<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Spire.DocViewer.Wpf</name>
    </assembly>
    <members>
        <member name="M:Spire.License.LicenseProvider.UnbindDevelopmentOrTestingLicenses(System.Type,System.String@)">
            <summary>
            Unbinds development or testing licenses of the specified type.
            The approach to lifting development or testing licenses does not allow frequent invocation by the same machine code,
            mandating a two-hour wait period before it can be invoked again.
            </summary>
            <param name="type">The type of the license to unbind.</param>
            <param name="errorMsg">The error message in case of failure.</param>
            <returns>True if at least one development or test license was unbound, otherwise false.</returns>
        </member>
        <member name="T:Spire.License.Blacklist">
            <summary> 
            Authorization Blacklist
            </summary>
        </member>
        <member name="F:Spire.License.Blacklist.BlacklistData">
            <summary> 
            The serial number or the MD5 code of the key that is entered into the authorization blacklist. 
            </summary> 
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.UseNewEngine">
            <summary>
            Use new engine.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.EnableHandTools">
            <summary>
            Gets or Sets hand tool
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.HiddenText">
            <summary>
            Set parameters are show hidden text
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.ToPdfParameterList">
            <summary>
            Gets or sets the convertors parameter.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.PrintDialog">
            <summary>
            Set print parnameters
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.PrintDocument">
            <summary>
            Gets the PrintDocument
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.HorizontalScrollBarVisibility">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.VerticalScrollBarVisibility">
            <summary>
            
            </summary>
        </member>
        <member name="E:Spire.DocViewer.Wpf.DocDocumentViewer.PageNumberChanged">
            <summary>
            Occurs current page number changed.
            </summary>
        </member>
        <member name="E:Spire.DocViewer.Wpf.DocDocumentViewer.DocLoaded">
            <summary>
            Provides document loaded events.
            </summary>
        </member>
        <member name="E:Spire.DocViewer.Wpf.DocDocumentViewer.DocClosed">
            <summary>
            Provides document closed events.
            </summary>
        </member>
        <member name="E:Spire.DocViewer.Wpf.DocDocumentViewer.ZoomChanged">
            <summary>
            Occurs zoom changed.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.FileName">
            <summary>
            Get opened Doc file name.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.LoadedDocument">
            <summary>
            Gets/Sets loaded document.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.ProgressControl">
            <summary>
            Get according to the progress control.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.AltPageCount">
            <summary>
            Gets/Set page count.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.Pages">
            <summary>
            Gets pages array.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.CurrentPageNumber">
            <summary>
            Gets the page number for the currently displayed page.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.PageCount">
            <summary>
            Gets the current number of display pages for the content.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocDocumentViewer.ZoomMode">
            <summary>
            Gets or Sets the Zoom mode.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.GoToFirstPage">
            <summary>
            Go to first page.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.GoToLastPage">
            <summary>
            Go to last page.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.GoToNextPage">
            <summary>
            Go to next page.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.GoToPage(System.Int32)">
            <summary>
            Go to specific page.
            </summary>
            <param name="pageNumber">page number</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.GoToPreviousPage">
            <summary>
            Go to previous page
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.CloseDocument">
            <summary>
            Closes the Doc document.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.Close(System.Boolean,System.Boolean)">
            <summary>
            Closes the DOC document.
            </summary>
            <param name="cleartoPdfParameter"></param>
            <param name="disposing"></param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.Close(System.Boolean)">
            <summary>
            Closes the DOC document.
            </summary>
            <param name="cleartoPdfParameter"></param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadFromFile(System.String)">
            <summary>
             Opens doc file.
            </summary>
            <param name="filePath"></param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadFromFile(System.String,Spire.Doc.FileFormat)">
            <summary>
            Opens the document from a file in word format.
            </summary>
            <param name="filePath">Name of the file.</param>
            <param name="fileFormat">Type of the format.</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadFromFile(System.String,Spire.Doc.FileFormat,Spire.Doc.Documents.XHTMLValidationType)">
            <summary>
            Opens the HTML document from a file.
            </summary>
            <param name="filePath">Name of the file.</param>
            <param name="fileFormat">Type of the format.</param>
            <param name="valiadationType">Type of the validation.</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadFromFile(System.String,System.String)">
            <summary>
            Opens the document from a file in word format.
            </summary>
            <param name="filePath">Name of the file.</param>       
            <param name="password">The password.</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadFromFile(System.String,Spire.Doc.FileFormat,System.String,Spire.Doc.Documents.XHTMLValidationType)">
            <summary>
             Opens the document from a file in word format.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="fileFormat">Type of the format.</param>
            <param name="password">The password.</param>
            <param name="valiadationType">Type of the validation.</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadFromStream(System.IO.Stream,Spire.Doc.FileFormat)">
            <summary>
            Opens the document in word format from the stream.
            </summary>
            <param name="stream">The stream.</param>
            <param name="formatType">Type of the format.</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadFromStream(System.IO.Stream,System.String)">
            <summary>
            Opens the document in word format from the stream.
            </summary>
            <param name="stream">The stream.</param>
            <param name="password">The password.</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadFromStream(System.IO.Stream,Spire.Doc.FileFormat,System.String)">
            <summary>
            Opens the document in word format from the stream.
            </summary>
            <param name="stream">The stream.</param>
            <param name="formatType">Type of the format.</param>
            <param name="password">The password.</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadDocument(Spire.Doc.Document)">
            <summary>
            Loading Document examples
            </summary>
            <param name="document">Document examples for Spire.Doc</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.SaveAs(System.String)">
            <summary>
            File format to save another
            </summary>
            <param name="fileName"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.SaveAs(System.String,Spire.Doc.FileFormat)">
            <summary>
            File format to save another
            </summary>
            <param name="fileName"></param>
            <param name="fileFormat"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.SaveImage(System.UInt16)">
            <summary>
            Saves DOC document page as image
            </summary>
            <param name="startPage">Page with start page to save as image</param>
            <returns>Returns  page as Image</returns>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.SaveImage(System.UInt16,System.UInt16)">
            <summary>
            Exports the specified pages as Images
            </summary>
            <param name="startPage">The starting page</param>
            <param name="endPage">The ending page</param>
            <returns>Returns the specified pages as Images</returns>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.OnScrollChanged(System.Windows.Controls.ScrollChangedEventArgs)">
            <summary>
            Page Zoom than listed change
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.GetCharBounds(Spire.Doc.ILineText)">
            <summary>
            获取某一行中所有字符的位置和尺寸.
            </summary>
            <param name="line"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.FindNext">
            <summary>
            Findes the next.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.FindPrevious">
            <summary>
            Finds the previous.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.ShowProgreesForm">
            <summary>
            显示进度条窗口
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.CloseProgressForm">
            <summary>
             关闭进度条窗口
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.OnLoaded(System.EventArgs)">
            <summary>
            加载页面数据完成事件
            </summary>
            <param name="eventArgs"></param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocDocumentViewer.LoadPages">
            <summary>
            加载页面数据
            </summary>
        </member>
        <member name="T:Spire.DocViewer.Wpf.SearchStringStruct">
             <summary>
            Find the search string character information.
             </summary>
        </member>
        <member name="F:Spire.DocViewer.Wpf.SearchStringStruct.FindChar">
            <summary>
            Need to search strings.
            </summary>
        </member>
        <member name="F:Spire.DocViewer.Wpf.SearchStringStruct.CaseSensitive">
            <summary>
            Is Case-sensitive.
            </summary>
        </member>
        <member name="F:Spire.DocViewer.Wpf.SearchStringStruct.StringPosition">
            <summary>
            Find a string position.
            </summary>
        </member>
        <member name="T:Spire.DocViewer.Wpf.StringPosition">
            <summary>
            The string in the page number,location within the page,border.
            </summary>
        </member>
        <member name="F:Spire.DocViewer.Wpf.StringPosition.PageNum">
             <summary>
            String in the page number.
             </summary>
        </member>
        <member name="F:Spire.DocViewer.Wpf.StringPosition.Bounds">
            <summary>
            String in position within the page,and borders.
            </summary>
        </member>
        <member name="T:Spire.DocViewer.Wpf.FoundCharEventHandler">
            <summary>
            Find character events entrust treatment.
            </summary>
            <param name="serder"></param>
            <param name="e"></param>
        </member>
        <member name="P:Spire.DocViewer.Wpf.FindStringPanel.SearchChar">
            <summary>
            Search String
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.FindStringPanel.CaseSensitive">
            <summary>
            Case sensitive
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.FindStringPanel.ActiveView">
            <summary>
            Active Doc DocumentViewer
            </summary>
        </member>
        <member name="F:Spire.DocViewer.Wpf.ProgressForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.ProgressForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.VirtualizingPagePanel.GetPageIndexAtPosition(System.Double,System.Double)">
            <summary>
            
            </summary>
            <param name="vScrollValue"></param>
            <param name="zoomFactor"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DocViewer.Wpf.VirtualizingPagePanel.GetPageIndexesAtPosition(System.Single,System.Single)">
            <summary>
            根据PdfDocumentViewer的高度，当前滚动条的位置，文档的缩放比列，页面的显示模式决定应该绘制多少页
            </summary>
            <param name="vScrollPosition">当前滚动条的位置</param>
            <param name="zoomFactor">文档的缩放比列</param>
            <param name="pageMode">页面的显示模式</param>
            <returns></returns>
        </member>
        <member name="F:Spire.DocViewer.Wpf.ZoomMode.Default">
            <summary>
            
            </summary>
        </member>
        <member name="F:Spire.DocViewer.Wpf.ZoomMode.FitPage">
            <summary>
            
            </summary>
        </member>
        <member name="F:Spire.DocViewer.Wpf.ZoomMode.FitWidth">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DocViewer.Wpf.Properties.Resource_WPF">
            <summary>
              一个强类型的资源类，用于查找本地化的字符串等。
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.Properties.Resource_WPF.ResourceManager">
            <summary>
              返回此类使用的缓存的 ResourceManager 实例。
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.Properties.Resource_WPF.Culture">
            <summary>
              重写当前线程的 CurrentUICulture 属性，对
              使用此强类型资源类的所有资源查找执行重写。
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.Properties.Resource_WPF.ArrowCursor">
            <summary>
              查找 System.Byte[] 类型的本地化资源。
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.Properties.Resource_WPF.DragHandCursor">
            <summary>
              查找 System.Byte[] 类型的本地化资源。
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.Properties.Resource_WPF.HandCursor">
            <summary>
              查找 System.Byte[] 类型的本地化资源。
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.Properties.Resource_WPF.HyperlinksHandCursor">
            <summary>
              查找 System.Byte[] 类型的本地化资源。
            </summary>
        </member>
        <member name="T:Spire.DocViewer.Wpf.DocumentToolbar">
            <summary>
            DocumentToolbar
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocumentToolbar.EnableControl(System.Boolean)">
            <summary>
            是否起用控件
            </summary>
            <param name="enable"></param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocumentToolbar.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="E:Spire.DocViewer.Wpf.DocViewer.DocumentLoaded">
            <summary>
            Occurs when the Doc document is loaded
            </summary>
        </member>
        <member name="E:Spire.DocViewer.Wpf.DocViewer.CurrentPageChange">
            <summary>
            Occurs when the Current Page is Change
            </summary>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.OnLoaded(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="eventArgs"></param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.SaveToFile(System.String)">
            <summary>
            Save Doc documetns
            </summary>
            <param name="filePath"></param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.SaveToFile(System.IO.Stream,Spire.Doc.FileFormat)">
            <summary>
            Stream to save Doc document
            </summary>
            <param name="stream"></param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.SaveAsImage(System.UInt16)">
            <summary>
            Exports the specified page as Image
            </summary>
            <param name="startPage">The start page to be converted into image</param>
            <returns>Returns the specified page as BitmapSource</returns>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.SaveAsImage(System.UInt16,System.UInt16)">
            <summary>
            Saves the specified pages as Images
            </summary>
            <param name="startPage">The starting page</param>
            <param name="endPage">The ending page</param>
            <returns>Returns the specified pages as Images</returns>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.GoToPage(System.Int32)">
            <summary>
            Navigates to the specified page.
            </summary>
            <param name="index">The page index</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.LoadFromFile(System.String)">
            <summary>
            Loads a Doc document in the Doc viewer
            </summary>
            <param name="filePath">The path for the Doc document to display in the Doc viewer</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.LoadFromFile(System.String,System.String)">
            <summary>
            Loads a Doc document in the Doc viewer
            </summary>
            <param name="filePath">The path for the Doc document to display in the Doc viewer</param>
            <param name="password">The password for opening the document.</param>
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.LoadFromStream(System.IO.Stream)">
            <summary>
            Opens the document from stream in Xml or Microsoft Word format.
            </summary>
            <param name="stream">The stream.</param>    
        </member>
        <member name="M:Spire.DocViewer.Wpf.DocViewer.CloseDocument">
            <summary>
            Closes the Doc document
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocViewer.CurrentPageNumber">
            <summary>
            Returns the number of the current page displayed in the Viewer
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocViewer.PageCount">
            <summary>
            Gets the page count
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocViewer.PrintDocument">
            <summary>
            Gets the Print document
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocViewer.PrintDialog">
            <summary>
            Set print parnameters
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocViewer.ShowToolbar">
            <summary>
            Gets and sets the visibility of the toolbar
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocViewer.HorizontalScrollBarVisibility">
            <summary>
            Gets and sets the HorizontalScrollBar Visibility of the DocDocmentViewer
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocViewer.VerticalScrollBarVisibility">
            <summary>
            Gets and sets the VerticalScrollBar Visibility of the DocDocmentViewer
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocViewer.UseNewEngine">
            <summary>
            Use new engine.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.DocViewer.ToPdfParameterList">
            <summary>
            Get or set convertors parameter.
            </summary>
        </member>
        <member name="P:Spire.DocViewer.Wpf.Graphics.WpfFontFamily.Families">
            Properties
        </member>
        <member name="M:Spire.DocViewer.Wpf.Graphics.WPFGraphics.DrawImage(System.Windows.Media.Imaging.BitmapSource,System.Int32,System.Int32)">
            public void DrawImage(XImage image, Point[] destPoints);
            public void DrawImage(XImage image, PointF[] destPoints);
            public void DrawImage(XImage image, XPoint[] destPoints);
        </member>
        <member name="P:Spire.DocViewer.HyperlinksLabel.LinkData">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DocViewer.HyperlinksLabel.Image">
            <summary>
            get/set transparent image
            </summary>
        </member>
        <member name="M:Spire.DocViewer.HyperlinksLabel.SetToolTipInfo">
            <summary>
            Set tooltip pop info.
            </summary>
        </member>
        <member name="T:Spire.DocViewer.License.LicenseProvider">
            <summary>
            Class Spire.DocViewer.LicenseProvider.
            </summary>  
        </member>
        <member name="M:Spire.DocViewer.License.LicenseProvider.SetLicense(System.String)">
            <summary>
            Provides a license by a license file path, which will be used for loading license.
            </summary>
            <param name="licenseFileFullPath">License file full path.</param>
        </member>
        <member name="M:Spire.DocViewer.License.LicenseProvider.SetLicense(System.IO.Stream)">
            <summary>
            Provides a license by a license stream, which will be used for loading license.
            </summary>
            <param name="licenseFileStream">License data stream.</param>
        </member>
        <member name="M:Spire.DocViewer.License.LicenseProvider.SetLicenseKey(System.String)">
            <summary>
            Provides a license by a license key, which will be used for loading license.
            </summary>
            <param name="key">The value of the Key attribute of the element License of you license xml file.</param>
        </member>
        <member name="M:Spire.DocViewer.License.LicenseProvider.SetLicenseKey(System.String,System.Boolean)">
            <summary>
            Sets the license key required for license loading, and specifies whether to use a development or test license.
            </summary>    
            <param name="key">The value of the Key attribute of the element License of you license xml file.</param>
            <param name="useDevOrTestLicense">Indicates whether to apply a development or test license.</param>
        </member>
        <member name="M:Spire.DocViewer.License.LicenseProvider.UnbindDevelopmentOrTestingLicenses">
            <summary>
            Unbind development or testing licenses. 
            Only development or testing licenses can be unbound, deployment licenses cannot be unbound.
            The approach to lifting development or testing licenses does not allow frequent invocation by the same machine code,
            mandating a two-hour wait period before it can be invoked again.
            </summary>
            <returns>Returns true if the unbinding operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Spire.DocViewer.License.LicenseProvider.SetLicenseFileName(System.String)">
            <summary>
             Sets the license file name, which will be used for loading license.
            </summary>
            <param name="licenseFileName">License file name.</param>
        </member>
        <member name="M:Spire.DocViewer.License.LicenseProvider.ClearLicense">
            <summary>
            Clear all cached license.
            </summary>
        </member>
        <member name="M:Spire.DocViewer.License.LicenseProvider.LoadLicense">
            <summary>
            Load the license provided by current setting to the license cache.
            </summary>
        </member>
        <member name="T:Spire.DocViewer.DocumentOpenedEventHandler">
            <summary>
            Provides document opened events
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="T:Spire.DocViewer.DocumentClosedEventHandler">
            <summary>
            Provides document closed events
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="T:Spire.DocViewer.PageNumberChangedEventHandler">
            <summary>
            Provides page number changed events.
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="T:Spire.DocViewer.NavigationButtonStatesChangedEventHandler">
            <summary>
            
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="T:Spire.DocViewer.ZoomChangedEventHandler">
            <summary>
            Provides zoom changed events
            </summary>
            <param name="sender"></param>
            <param name="zoomFactor"></param>
        </member>
    </members>
</doc>
