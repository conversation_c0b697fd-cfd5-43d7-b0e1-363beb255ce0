<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Spire.Barcode</name>
    </assembly>
    <members>
        <member name="M:Spire.License.LicenseProvider.UnbindDevelopmentOrTestingLicenses(System.Type,System.String@)">
            <summary>
            Unbinds development or testing licenses of the specified type.
            The approach to lifting development or testing licenses does not allow frequent invocation by the same machine code,
            mandating a two-hour wait period before it can be invoked again.
            </summary>
            <param name="type">The type of the license to unbind.</param>
            <param name="errorMsg">The error message in case of failure.</param>
            <returns>True if at least one development or test license was unbound, otherwise false.</returns>
        </member>
        <member name="T:Spire.License.Blacklist">
            <summary> 
            Authorization Blacklist
            </summary>
        </member>
        <member name="F:Spire.License.Blacklist.BlacklistData">
            <summary> 
            The serial number or the MD5 code of the key that is entered into the authorization blacklist. 
            </summary> 
        </member>
        <member name="F:Spire.Barcode.CheckSumMode.Auto">
            <summary>
            Enable or disable checksum according barcode type.
            </summary>
        </member>
        <member name="F:Spire.Barcode.CheckSumMode.ForceEnable">
            <summary>
            Force enable checksum.
            </summary>
        </member>
        <member name="F:Spire.Barcode.CheckSumMode.ForceDisable">
            <summary>
            Force disable checksum.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Common.SupportClass.URShift(System.Int32,System.Int32)">
            <summary>
            Performs an unsigned bitwise right shift with the specified number
            </summary>
            <param name="number">Number to operate on</param>
            <param name="bits">Ammount of bits to shift</param>
            <returns>The resulting number from the shift operation</returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Common.SupportClass.Identity(System.Double)">
            <summary>
            This method returns the literal value received
            </summary>
            <param name="literal">The literal to return</param>
            <returns>The received value</returns>
        </member>
        <member name="T:Spire.Barcode.Implementation.Dimension">
            <summary>
            Simply encapsulates a width and height.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Dimension.#ctor(System.Int32,System.Int32)">
            <summary>
            initializing constructor
            </summary>
            <param name="width"></param>
            <param name="height"></param>
        </member>
        <member name="P:Spire.Barcode.Implementation.Dimension.Width">
            <summary>
            the width
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Dimension.Height">
            <summary>
            the height
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Dimension.Equals(System.Object)">
            <summary>
            
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Dimension.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Dimension.ToString">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.DataMatrix.Encoder.SymbolInfo.overrideSymbolSet(Spire.Barcode.Implementation.Generator.Encoder.DataMatrix.Encoder.SymbolInfo[])">
             Overrides the symbol info set used by this class. Used for testing purposes.
            
             @param override the symbol info set to use
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.#ctor(System.Int32)">
             Create a representation of GF(256) using the given primitive polynomial.
            
             @param primitive irreducible polynomial whose coefficients are represented by
              the bits of an int, where the least-significant bit represents the constant
              coefficient
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.BuildMonomial(System.Int32,System.Int32)">
            @return the monomial representing coefficient * x^degree
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.AddOrSubtract(System.Int32,System.Int32)">
             Implements both addition and subtraction -- they are the same in GF(256).
            
             @return sum/difference of a and b
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.Exp(System.Int32)">
            @return 2 to the power of a in GF(256)
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.Log(System.Int32)">
            @return base 2 log of a in GF(256)
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.Inverse(System.Int32)">
            @return multiplicative inverse of a
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.Multiply(System.Int32,System.Int32)">
            @param a
            @param b
            @return product of a and b in GF(256)
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.Codes.NewRowToSeparator">
            For RSS.When 2D part finished and need to change to draw the separator part
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.#ctor(System.Int32[])">
            @param field the {@link GF256} instance representing the field to use
            to perform computations
            @param coefficients coefficients as ints representing elements of GF(256), arranged
            from most significant (highest-power term) coefficient to least significant
            @throws IllegalArgumentException if argument is null or empty,
            or if leading coefficient is 0 and this is not a
            constant polynomial (that is, it is not the monomial "0")
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.evaluateAt(System.Int32)">
            @return evaluation of this polynomial at a given point
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.Degree">
            @return degree of this polynomial
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.IsZero">
            @return true iff this polynomial is the monomial "0"
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.GetCoefficient(System.Int32)">
            @return coefficient of x^degree term in this polynomial
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Generation.BarcodeCreator.AztecLayers">
            <summary>
            Layers of Aztect type of barcode. value should between -4 to 32.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Spire.Barcode.Implementation.Scanner.Reader.DataMatrix.Utils.NFAUtils.RectangleImprove(Spire.Barcode.Implementation.Generator.Types.BinaryBitmap.FloatBitmap)" -->
        <member name="M:Spire.Barcode.Implementation.Scanner.Reader.DataMatrix.Utils.NFAUtils.CalculateNFA(System.Int32,System.Int32,System.Double)">
            Computes -log10(NFA).
            
                        NFA stands for Number of False Alarms:
                        @f[
                            \mathrm{NFA} = NT \cdot B(n,k,p)
                        @f]
            
                        - NT       - number of tests
                        - B(n,k,p) - tail of binomial distribution with parameters n,k and p:
                        @f[
                            B(n,k,p) = \sum_{j=k}^n
                                       \left(\begin{array}{c}n\\j\end{array}\right)
                                       p^{j} (1-p)^{n-j}
                        @f]
            
                        The value -log10(NFA) is equivalent but more intuitive than NFA:
                        - -1 corresponds to 10 mean false alarms
                        -  0 corresponds to 1 mean false alarm
                        -  1 corresponds to 0.1 mean false alarms
                        -  2 corresponds to 0.01 mean false alarms
                        -  ...
            
                        Used this way, the bigger the value, better the detection,
                        and a logarithmic scale is used.
            
                        @param n,k,p binomial parameters.
                        @param logNT logarithm of Number of Tests
            
                        The computation is based in the gamma function by the following
                        relation:
                        @f[
                            \left(\begin{array}{c}n\\k\end{array}\right)
                            = \frac{ \Gamma(n+1) }{ \Gamma(k+1) \cdot \Gamma(n-k+1) }.
                        @f]
                        We use efficient algorithms to compute the logarithm of
                        the gamma function.
            
                        To make the computation faster, not all the sum is computed, part
                        of the terms are neglected based on a bound to the error obtained
                        (an error of 10% in the result is accepted).
        </member>
        <member name="M:Spire.Barcode.Implementation.Scanner.Reader.DataMatrix.Utils.NFAUtils.InterpolateToLowValue(System.Double,System.Double,System.Double,System.Double,System.Double)">
            Interpolate y value corresponding to 'x' value given 
        </member>
        <member name="M:Spire.Barcode.Implementation.Scanner.Reader.DataMatrix.Utils.NFAUtils.InterpolateToHighValue(System.Double,System.Double,System.Double,System.Double,System.Double)">
            Interpolate y value corresponding to 'x' value given 
        </member>
        <member name="T:Spire.Barcode.IDataEncoder">
            <summary>
            Encoder interface for barcode symbols.
            </summary>
            <remarks>
            Classes that encode barcode data into arrays of bits (<see cref="T:System.Collections.BitArray"/>) should
            implement this interface.
            </remarks>
        </member>
        <member name="M:Spire.Barcode.IDataEncoder.Encode(System.String)">
            <summary>
            Encodes a string of barcode data.
            </summary>
            <param name="data">The string of data to be encoded.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="M:Spire.Barcode.IDataEncoder.Encode(System.Char)">
            <summary>
            Encodes a character of barcode data.
            </summary>
            <param name="datum">The character of data to be encoded.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="T:Spire.Barcode.TableEncoder">
            <summary>
            Encoder base class for table-lookup like encoders.
            </summary>
        </member>
        <member name="M:Spire.Barcode.TableEncoder.LookUp(System.Int32)">
            <summary>
            Looks up the sought value and returns its encoded equivalent.
            Should be properly overriden in derived classes.
            </summary>
            <param name="index">Index of the value to look up.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="M:Spire.Barcode.TableEncoder.Encode(System.String)">
            <summary>
            Encodes a string of barcode data.
            </summary>
            <param name="data">The string of data to be encoded.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="M:Spire.Barcode.TableEncoder.Encode(System.Char)">
            <summary>
            Encodes a character of barcode data. 
            </summary>
            <remarks>
            This method uses the <see cref="M:Spire.Barcode.TableEncoder.LookUp(System.Int32)"/> method to resolve 
            the character of data to an encoded entity (<see cref="T:System.Collections.BitArray"/>).
            It assumes the datum passed represents a number and converts it before
            looking it up.
            </remarks>
            <param name="datum">The character of data to be encoded.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="T:Spire.Barcode.BarCodeFormatException">
            <summary>
            The exception thrown when the barcode has an incorrect format.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BarCodeFormatException.#ctor">
            <overloads>
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeFormatException"/> class.
            </summary>
            </overloads>
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeFormatException"/> class.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BarCodeFormatException.#ctor(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeFormatException"/> class with the given message.
            </summary>
            <param name="message">Error message of the exception.</param>
        </member>
        <member name="M:Spire.Barcode.BarCodeFormatException.#ctor(System.String,System.Exception)">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeFormatException"/> class with the given message and 
            the given inner exception.
            </summary>
            <param name="message">Error message of the exception.</param>
            <param name="innerException">Inner exception.</param>
        </member>
        <member name="T:Spire.Barcode.BarCodeType">
            <summary>
            Identifies the barcode types.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Codabar">
            <summary>
            Code bar Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code11">
            <summary>
            Code 1 of 1 Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code25">
            <summary>
            Standard 2 of 5 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Interleaved25">
            <summary>
            Interleaved 2 of 5 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code39">
            <summary>
            Code 3 of 9 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code39Extended">
            <summary>
            Extended Code 3 of 9 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code93">
            <summary>
            Code 9 of 3 Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code93Extended">
            <summary>
            Extended Code 9 of 3 Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code128">
            <summary>
            Code 128 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.EAN8">
            <summary>
            EAN-8 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.EAN13">
            <summary>
            EAN-13 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.EAN128">
            <summary>
            EAN-128 barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.EAN14">
            <summary>
            EAN-14 barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.SCC14">
            <summary>
            SCC14 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.SSCC18">
            <summary>
            SSCC18 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.ITF14">
            <summary>
            ITF14 Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.ITF6">
            <summary>
            ITF-6 Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.UPCA">
            <summary>
            UPCA barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.UPCE">
            <summary>
            UPCE barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.PostNet">
            <summary>
            Postnet barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Planet">
            <summary>
            Planet barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.MSI">
            <summary>
            MSI barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.QRCode">
            <summary>
            QR Code barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.DataMatrix">
            <summary>
            2D Barcode DataMatrix
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Pdf417">
            <summary>
            Pdf417 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Pdf417Macro">
            <summary>
            Pdf417 Macro barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RSS14">
            <summary>
            RSS14 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RSS14Truncated">
            <summary>
            RSS-14 Truncated barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RSSLimited">
            <summary>
            RSS Limited Barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RSSExpanded">
            <summary>
            RSS Expanded Barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.USPS">
            <summary>
            USPS OneCode barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.SwissPostParcel">
            <summary>
            Swiss Post Parcel Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.PZN">
            <summary>
            PZN Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.OPC">
            <summary>
            OPC(Optical Product Code) Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.DeutschePostIdentcode">
            <summary>
            Deutschen Post Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.DeutschePostLeitcode">
            <summary>
            Deutsche Post Leitcode Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RoyalMail4State">
            <summary>
            Royal Mail 4-state Customer Code Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.SingaporePost4State">
            <summary>
            Singapore Post Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Aztec">
            
              Aztec Barcode
            
        </member>
        <member name="F:Spire.Barcode.BarCodeType.MicroQR">
            <summary>
            Micro QRCode
            </summary>
        </member>
        <member name="T:Spire.Barcode.IChecksum">
            <summary>
            Interface to calculate checksums on barcode data.
            </summary>
            <remarks>
            Classes that calculate checksums on barcode data should implement this interface.
            It makes for a pluggable checksum architecture.
            </remarks>
        </member>
        <member name="M:Spire.Barcode.IChecksum.Calculate(System.String)">
            <summary>
            Calculates the checksum of a string of data.
            </summary>
            <param name="data">The data to calculate the checksum for.</param>
            <returns>The calculated checksum.</returns>
        </member>
        <member name="T:Spire.Barcode.IOptionalChecksum">
            <summary>
            Determines whether the checksum calculation in a barcode is optional.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IOptionalChecksum.UseChecksum">
            <summary>
            Determines whether the barcode will calculate a checksum on its data.
            </summary>
        </member>
        <member name="F:Spire.Barcode.ITF14BorderType.None">
            <summary>
            No border enclosing the Barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.ITF14BorderType.Frame">
            <summary>
            Frame enclosing the Barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.ITF14BorderType.Bar">
            <summary>
            Tow horizontal bars enclosing the Barcode
            </summary>
        </member>
        <member name="T:Spire.Barcode.BarCodeGenerator">
            <summary>
            Operates on <see cref="T:Spire.Barcode.IBarcodeSettings"/>
            to provide barcode rendering and conversion services.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.#ctor(Spire.Barcode.IBarcodeSettings)">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeGenerator"/> class.
            </summary>
            <param name="settings">The settings to use.</param>
            <exception cref="T:System.ArgumentNullException">
            If the settings passed are <c>null</c>.
            </exception>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.GenerateImage">
            <summary>
            Generates an image with the rendered barcode based on the settings (<see cref="T:Spire.Barcode.IBarcodeSettings"/>).
            </summary>
            <returns>The generated barcode image.</returns>
            <exception cref="T:Spire.Barcode.BarCodeFormatException">
            If the data in the settings can't be rendered by the selected barcode.
            </exception>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.GenerateImage(System.Drawing.Size)">
            <summary>
            Generates an image with the rendered barcode based on the settings (<see cref="T:Spire.Barcode.IBarcodeSettings"/>).
            </summary>
            <returns>The generated barcode image.</returns>
            <exception cref="T:Spire.Barcode.BarCodeFormatException">
            If the data in the settings can't be rendered by the selected barcode.
            </exception>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.GetBarCodeType(Spire.Barcode.BarCodeType)">
            <summary>
            Instantiates the barcode type asked by the settings.
            </summary>
            <returns>The instantiated barcode type.</returns>
            <exception cref="T:System.InvalidOperationException">
            If the settings contain an invalid barcode type.
            </exception>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.AssembleBarCode">
            <summary>
            Instantiates and sets the barcode properties based on the settings.
            </summary>
            <returns>Instantiated and set up barcode.</returns>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.CopySettings(Spire.Barcode.IBarcodeSettings,Spire.Barcode.IBarcodeSettings)">
            <summary>
            Copy settings between <see cref="T:Spire.Barcode.IBarcodeSettings"/>.
            </summary>
            <param name="srcSetting">Settings to copy from.</param>
            <param name="settingsCopyTo">Settings to copy to.</param>
        </member>
        <member name="P:Spire.Barcode.Settings.BarcodeInfo.BarCodeReadType">
            <summary>
            Get barcode type
            </summary>
        </member>
        <member name="P:Spire.Barcode.Settings.BarcodeInfo.Vertexes">
            <summary>
            Get barcode position,return the four vertices of the rectangle
            </summary>
        </member>
        <member name="P:Spire.Barcode.Settings.BarcodeInfo.Angle">
            <summary>
            Get barcode angle
            </summary>
        </member>
        <member name="P:Spire.Barcode.Settings.BarcodeInfo.DataString">
            <summary>
            Get barcode data
            </summary>
        </member>
        <member name="T:Spire.Barcode.BarcodeScanner">
            <summary>
             Provide barcode scanning services.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanInfo(System.String)">
             Scan barcode from image file.
            
             @param fileName Image file path.
             @return Barcode info array.
             @throws Exception
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanInfo(System.String,Spire.Barcode.BarCodeType)">
             Scan barcode from image file.
            
             @param fileName Image file path.
             @param barcodeType barcode type
             @return Barcode info array.
             @throws Exception
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.String,Spire.Barcode.BarCodeType)">
            <summary>
            Scans barcode from image file.
            </summary>
            <param name="fileName">file Name</param>
            <param name="barcodeType">barcode type</param>
            <returns>barcode text list</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.Drawing.Bitmap,Spire.Barcode.BarCodeType)">
            <summary>
            Scan barcode from image.
            </summary>
            <param name="image">image object</param>
            <param name="barcodeType">barcode type</param>
            <returns>Barcode text list</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.String)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.String,System.Boolean)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.String,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <param name="barcodeType">The barcode type.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.String)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.String,System.Boolean)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.String,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <param name="barcodeType">The barcode type.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.IO.Stream)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.IO.Stream,System.Boolean)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.IO.Stream,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <param name="barcodeType">The barcode type.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.IO.Stream)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.IO.Stream,System.Boolean)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.IO.Stream,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <param name="barcodeType">The barcode type.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.Drawing.Bitmap)">
            <summary>
            Scan barcode from bitmap.
            </summary>
            <param name="bitmap">Bitmap object.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.Drawing.Bitmap)">
            <summary>
            Scan barcode from bitmap.
            </summary>
            <param name="bitmap">Bitmap object.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.Drawing.Bitmap,System.Drawing.Rectangle,Spire.Barcode.BarCodeType)">
            <summary>
            Scans barcode from bitmap.
            </summary>
            <param name="bitmap">Bitmap object.</param>
            <param name="rect">Scan rectangle</param>
            <param name="barcodeType">Barcode Type</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.Drawing.Bitmap,System.Drawing.Rectangle,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scans barcode from bitmap.
            </summary>
            <param name="bitmap">Bitmap object.</param>
            <param name="rect">Scan rectangle</param>
            <param name="barcodeType">Barcode Type</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="T:Spire.Barcode.BarcodeSettings">
            <summary>
            Packages settings for barcodes.
            Canonical implementation of <see cref="T:Spire.Barcode.IBarcodeSettings"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Type">
            <summary>
            The type of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Data">
            <summary>
            The data to render with the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BackColor">
            <summary>
            The back color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextColor">
            <summary>
            The color of the bar of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BarHeight">
            <summary>
            The height of the barcode. Affected by <see cref="P:Spire.Barcode.BarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.FontColor">
            <summary>
            The color of the font to render text in the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopMargin">
            <summary>
            The vertical top  offset height of the barcode to the border.
            Affected by <see cref="P:Spire.Barcode.BarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.LeftMargin">
            <summary>
            The horizontal (left and right) offset width of the barcode to the border.
            Affected by <see cref="P:Spire.Barcode.BarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopTextMargin">
            <summary>
            The top margin of top text
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextFont">
            <summary>
            The font used to render the text inside the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.UseChecksum">
            <summary>
            Whether the barcode will use an (optional) checksum.
            Not every barcode requires a checksum, and others mandate it.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.AutoResize">
            <summary>
            Gets or sets adjust size of barcode image automatically.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Data2D">
            <summary>
            Gets or sets text of 2D barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopText">
            <summary>
            Top text of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopTextColor">
            <summary>
            Top text color of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ITF14BearerBars">
            <summary>
            ITF14 Bearer bar
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopTextFont">
            <summary>
            Top text font of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowTopText">
            <summary>
            Indicates whether displays top text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Unit">
            <summary>
            Measurement unit.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextRenderingHint">
            <summary>
            Gets or sets quality of barcode text rendering.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Rotate">
            <summary>
            Gets or set rotation angle of BarCode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ForeColor">
            <summary>
            Gets or sets foreground color of the barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowText">
            <summary>
            Indicates whether display barcode's text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowTextOnBottom">
            <summary>
            Indicates whether display barcode's text on bottom.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BottomMargin">
            <summary>
            The vertical bottom offset height of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextMargin">
            <summary>
            Space between barcode and text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.RightMargin">
            <summary>
            The horizontal right offset width of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextAlignment">
            <summary>
            The position of the text rendered in the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.UseAntiAlias">
            <summary>
            Inidcates whether use anti alias mode to render image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ImageHeight">
            <summary>
             Height of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ImageWidth">
            <summary>
            Width of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ColumnCount">
            <summary>
            Columns of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.RowCount">
            <summary>
            Rows of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.DpiX">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.DpiY">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ResolutionType">
            <summary>
            Gets or sets the resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowCheckSumChar">
            <summary>
            Indicates whether shows checksum digit in Code128 and EAN128 Barcodes.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.CodabarStartChar">
            <summary>
            Start character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.CodabarStopChar">
            <summary>
            Stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowStartCharAndStopChar">
            <summary>
            Show start character and stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.SupData">
            <summary>
            Supplement data.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.SupSpace">
            <summary>
            Space between main and supplement Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.WideNarrowRatio">
            <summary>
            Wide/narrow ratio.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.HasBorder">
            <summary>
            Indicates whether has border;
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BorderWidth">
            <summary>
            Borders's width of barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BorderColor">
            <summary>
            Border's color
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BorderDashStyle">
            <summary>
            Border's Dash style.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.X">
            <summary>
            Width of barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Y">
            <summary>
            Height of 2D barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.XYRatio">
            <summary>
            height/width ratio of 2D Barcode's module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Code128SetMode">
            <summary>
            Gets or sets code set of Barcode128 barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Pdf417DataMode">
            <summary>
            Gets or sets data mode of Pdf417 barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Pdf417ECL">
            <summary>
            Error correction level of pdf417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Pdf417Truncated">
            <summary>
            Indicates wheter has been truncated of pdf 417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.AztecLayers">
            <summary>
            Gets or sets a value specifies the required number of layers for an Aztec code.
            A negative number(-1, -2, -3, -4) specifies a compact Aztec code
            0 indicates to use the minimum nuber for layers (the default)
            A positive number (1, 2, ... 32) specifies a normal (non-compact) Aztec code.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.AztecErrorCorrection">
            <summary>
            Gets or sets a value specifies what degree of error correction. the default is 23.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.DataMatrixSymbolShape">
            <summary>
            Gets or sets a value specifies the symbol shape hint for DataMatrix barcode. the default is Auto.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.MacroFileIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's file index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.MacroSegmentIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's segment index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.QRCodeDataMode">
            <summary>
            Gets or sets of QRCode Barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.QRCodeECL">
            <summary>
            Error correction level of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.QRCodeLogoImage">
            <summary>
            Gets or sets logo image of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopTextAligment">
            <summary>
            Indicate top text aligment.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BottomTextColor">
            <summary>
            Bottom text color of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BottomTextFont">
            <summary>
            Bottom text font of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowBottomText">
            <summary>
            Indicates whether displays bottom text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BottomTextAligment">
            <summary>
            Indicate bottom text aligment.
            </summary>
        </member>
        <member name="T:Spire.Barcode.IBarcodeSettings">
            <summary>
            Defines barcode settings.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.AutoResize">
            <summary>
            The size of image automatically.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Type">
            <summary>
            The type of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Data">
            <summary>
            The data to render with the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Data2D">
            <summary>
            The data to render with the 2D barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopText">
            <summary>
            Top text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopTextColor">
            <summary>
            Text color of top text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopTextFont">
            <summary>
            Text font of top text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowTopText">
            <summary>
            Indicates whether shows top text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopTextMargin">
            <summary>
            Indicate top text margin.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Unit">
            <summary>
            The unit of measure of the barcode's measurable properties.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextRenderingHint">
            <summary>
            Quality of text rendering. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BorderDashStyle">
            <summary>
            Border's Dash style.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextAlignment">
            <summary>
            The position of the text rendered in the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopTextAligment">
            <summary>
            Indicate top text aligment.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomTextAligment">
            <summary>
            Indicate Bottom text aligment.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Rotate">
            <summary>
            Rotation angle of Barcode image. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ITF14BearerBars">
            <summary>
            ITF14 barcode Bearer Bars
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BackColor">
            <summary>
            The back color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ForeColor">
            <summary>
            The fore color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowText">
            <summary>
            Indicates whether display barcode data text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowTextOnBottom">
            <summary>
            Indicates whether display barcode data text on bottom.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextColor">
            <summary>
            The color of the bar of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BarHeight">
            <summary>
            The height of the barcode. Affected by <see cref="P:Spire.Barcode.IBarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopMargin">
            <summary>
            The vertical top  offset height of the barcode to the border.
            Affected by <see cref="P:Spire.Barcode.IBarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomMargin">
            <summary>
            The vertical bottom offset height of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextMargin">
            <summary>
            Space between barcode and text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.LeftMargin">
            <summary>
            The horizontal left offset width of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.RightMargin">
            <summary>
            The horizontal right offset width of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextFont">
            <summary>
            The font used to render the text inside the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.UseChecksum">
            <summary>
            Whether the barcode will use an (optional) checksum.
            Not every barcode requires a checksum, and others mandate it.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.UseAntiAlias">
            <summary>
            Inidcates whether use anti alias mode to render image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ImageHeight">
            <summary>
            Height of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ImageWidth">
            <summary>
            Width of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ColumnCount">
            <summary>
            Columns of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.RowCount">
            <summary>
            Rows of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.DpiX">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.DpiY">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ResolutionType">
            <summary>
            Gets or sets the resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowCheckSumChar">
            <summary>
            Indicates whether shows checksum digit in Code128 and EAN128 Barcodes.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.CodabarStartChar">
            <summary>
            Start character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.CodabarStopChar">
            <summary>
            Stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowStartCharAndStopChar">
            <summary>
            Show start character and stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.SupData">
            <summary>
            Supplement data.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.SupSpace">
            <summary>
            Space between main and supplement Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.WideNarrowRatio">
            <summary>
            Wide/narrow ratio.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.HasBorder">
            <summary>
            Indicates whether has border;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BorderWidth">
            <summary>
            Borders's width of barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BorderColor">
            <summary>
            Border's color
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.X">
            <summary>
            Width of barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Y">
            <summary>
            Height of 2D barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.XYRatio">
            <summary>
             height/width ratio of 2D Barcode's module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Pdf417DataMode">
            <summary>
             Gets or sets data mode of Pdf417 barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Pdf417ECL">
            <summary>
            Error correction level of pdf417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Pdf417Truncated">
            <summary>
            Indicates wheter has been truncated of pdf 417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.MacroFileIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's file index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.MacroSegmentIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's segment index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.QRCodeDataMode">
            <summary>
             Gets or sets of QRCode Barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.QRCodeECL">
            <summary>
            Error correction level of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.QRCodeLogoImage">
            <summary>
            Gets or sets logo image of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.AztecErrorCorrection">
            <summary>
            Gets or sets a value specifies what degree of error correction. the default is 23.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.AztecLayers">
            <summary>
            Gets or sets a value specifies the required number of layers for an Aztec code.
            A negative number(-1, -2, -3, -4) specifies a compact Aztec code
            0 indicates to use the minimum nuber for layers (the default)
            A positive number (1, 2, ... 32) specifies a normal (non-compact) Aztec code.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.DataMatrixSymbolShape">
            <summary>
            Gets or sets a value specifies the symbol shape hint for DataMatrix barcode. the default is Auto.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomText">
            <summary>
            Bottom text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomTextColor">
            <summary>
            Text color of Bottom text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomTextFont">
            <summary>
            Text font of Bottom text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowBottomText">
            <summary>
            Indicates whether shows Bottom text.
            </summary>
        </member>
        <member name="T:Spire.Barcode.BitArrayHelper">
            <summary>
            Utility class for BitArray operations.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BitArrayHelper.PopFront(System.Collections.BitArray[]@)">
            <summary>
            Removes the first <see cref="T:System.Collections.BitArray"/> of an array of
            BitArrays and returns it.
            </summary>
            <param name="bits">Array of bit arrays to work on. On return, will lack the first element.</param>
            <returns>The first element of the BitArray array parameter.</returns>
        </member>
        <member name="M:Spire.Barcode.BitArrayHelper.PopBack(System.Collections.BitArray[]@)">
            <summary>
            Removes the last <see cref="T:System.Collections.BitArray"/> of an array of
            BitArrays and returns it.
            </summary>
            <param name="bits">Array of bit arrays to work on. On return, will lack the last element.</param>
            <returns>The last element of the BitArray array parameter.</returns>
        </member>
        <member name="M:Spire.Barcode.BitArrayHelper.ToBitArray(System.String)">
            <summary>
            Converts a string of data consisting of '1's and '0's
            into a <see cref="T:System.Collections.BitArray"/>.
            </summary>
            <param name="data">Input data.</param>
            <returns>BitArray of input data.</returns>
        </member>
        <member name="M:Spire.Barcode.BitArrayHelper.ToBitMatrix(System.String[])">
            <summary>
            Converts an array of strings of data consisting of '1's and '0's
            into an array of corresponding <see cref="T:System.Collections.BitArray"/>s.
            </summary>
            <param name="data">Input strings.</param>
            <returns>Bit matrix (array of BitArrays) created.</returns>
        </member>
        <member name="M:Spire.Barcode.MathMethods.CompareFloat(System.Single,System.Single,System.Single)">
            <summary>
            Compares the floating number.
            </summary>
            <param name="val1">The value1.</param>
            <param name="val2">The value2.</param>
            <param name="accuracy">The accuracy.</param>
            <returns><c>return 0,val1 equal val2;return 1,val1 greater than val2;return -1,val1 less than val2;</c></returns>
        </member>
        <member name="M:Spire.Barcode.MathMethods.CompareDouble(System.Double,System.Double,System.Double)">
            <summary>
            Compares the double number.
            </summary>
            <param name="val1">The value1.</param>
            <param name="val2">The value2.</param>
            <param name="accuracy">The accuracy.</param>
            <returns><c>return 0,val1 equal val2;return 1,val1 greater than val2;return -1,val1 less than val2;</c></returns>
        </member>
        <member name="T:Spire.Barcode.DebugStream">
            <summary>
            A <see cref="T:System.IO.Stream"/> decorator used to trace stream reads and writes.
            </summary>
        </member>
        <member name="M:Spire.Barcode.DebugStream.#ctor(System.String,System.IO.Stream)">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.DebugStream"/> class.
            </summary>
            <param name="title">A name to distinguish this stream.</param>
            <param name="baseStream">The stream to be decorated by this stream.</param>
            <exception cref="T:System.ArgumentNullException">
            If the title or the base stream are null or the title is empty.
            </exception>
        </member>
        <member name="M:Spire.Barcode.DebugStream.Flush">
            <summary>
            Flushes the base stream's contents.
            </summary>
        </member>
        <member name="M:Spire.Barcode.DebugStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a chunk from the base stream.
            </summary>
            <param name="buffer">Buffer to store bytes read.</param>
            <param name="offset">Offset into buffer to begin storing bytes read.</param>
            <param name="count">Number of bytes to read.</param>
            <returns>Number of bytes actually read.</returns>
        </member>
        <member name="M:Spire.Barcode.DebugStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the base stream.
            </summary>
            <param name="offset">Size of the position seek operation.</param>
            <param name="origin">Where to seek from.</param>
            <returns>The position set on the base stream.</returns>
        </member>
        <member name="M:Spire.Barcode.DebugStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the base stream.
            </summary>
            <param name="value">New length to set.</param>
        </member>
        <member name="M:Spire.Barcode.DebugStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a number of bytes to the base stream.
            </summary>
            <param name="buffer">Buffer with bytes for writing.</param>
            <param name="offset">Offset into the buffer.</param>
            <param name="count">Number of bytes to read from the buffer and write to the stream.</param>
        </member>
        <member name="P:Spire.Barcode.DebugStream.CanRead">
            <summary>Determines whether the stream supports reading.</summary>
        </member>
        <member name="P:Spire.Barcode.DebugStream.CanSeek">
            <summary>Determines whether the stream supports seeking.</summary>
        </member>
        <member name="P:Spire.Barcode.DebugStream.CanWrite">
            <summary>Determines whether the stream supports writing.</summary>
        </member>
        <member name="P:Spire.Barcode.DebugStream.Length">
            <summary>The length of the stream.</summary>
        </member>
        <member name="P:Spire.Barcode.DebugStream.Position">
            <summary>The current position of the stream.</summary>
        </member>
        <member name="M:Spire.Barcode.DebugStream.LogMessage(System.String,System.Object[])">
            <summary>
            Logs a message to the <see cref="T:System.Diagnostics.Debug"/> class.
            </summary>
            <param name="message">Message to log, in the format of <see cref="M:System.String.Format(System.String,System.Object)"/>.</param>
            <param name="parameters">Parameters to the positional elements of the message.</param>
        </member>
        <member name="F:Spire.Barcode.DebugStream._baseStream">
            <summary>The decorated base stream.</summary>
        </member>
        <member name="F:Spire.Barcode.DebugStream._title">
            <summary>The title of this stream.</summary>
        </member>
        <member name="T:Spire.Barcode.StringHelper">
            <summary>
            Utility class for common string operations.
            </summary>
        </member>
        <member name="M:Spire.Barcode.StringHelper.IsNullOrEmpty(System.String)">
            <summary>
            Determines if a string is null of empty. It's emptiness is
            determined by first trimming it.
            </summary>
            <param name="stringToCheck">String to check for nullness or emptiness.</param>
            <returns><c>True</c> if the string is null or empty, <c>false</c> otherwise.</returns>
        </member>
        <member name="T:Spire.Barcode.License.LicenseProvider">
            <summary>
            Class Spire.Barcode.LicenseProvider.
            </summary>  
        </member>
        <member name="M:Spire.Barcode.License.LicenseProvider.SetLicense(System.String)">
            <summary>
            Provides a license by a license file path, which will be used for loading license.
            </summary>
            <param name="licenseFileFullPath">License file full path.</param>
        </member>
        <member name="M:Spire.Barcode.License.LicenseProvider.SetLicense(System.IO.Stream)">
            <summary>
            Provides a license by a license stream, which will be used for loading license.
            </summary>
            <param name="licenseFileStream">License data stream.</param>
        </member>
        <member name="M:Spire.Barcode.License.LicenseProvider.SetLicenseKey(System.String)">
            <summary>
            Provides a license by a license key, which will be used for loading license.
            </summary>
            <param name="key">The value of the Key attribute of the element License of you license xml file.</param>
        </member>
        <member name="M:Spire.Barcode.License.LicenseProvider.SetLicenseKey(System.String,System.Boolean)">
            <summary>
            Sets the license key required for license loading, and specifies whether to use a development or test license.
            </summary>    
            <param name="key">The value of the Key attribute of the element License of you license xml file.</param>
            <param name="useDevOrTestLicense">Indicates whether to apply a development or test license.</param>
        </member>
        <member name="M:Spire.Barcode.License.LicenseProvider.UnbindDevelopmentOrTestingLicenses">
            <summary>
            Unbind development or testing licenses. 
            Only development or testing licenses can be unbound, deployment licenses cannot be unbound.
            The approach to lifting development or testing licenses does not allow frequent invocation by the same machine code,
            mandating a two-hour wait period before it can be invoked again.
            </summary>
            <returns>Returns true if the unbinding operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Spire.Barcode.License.LicenseProvider.SetLicenseFileName(System.String)">
            <summary>
             Sets the license file name, which will be used for loading license.
            </summary>
            <param name="licenseFileName">License file name.</param>
        </member>
        <member name="M:Spire.Barcode.License.LicenseProvider.ClearLicense">
            <summary>
            Clear all cached license.
            </summary>
        </member>
        <member name="M:Spire.Barcode.License.LicenseProvider.LoadLicense">
            <summary>
            Load the license provided by current setting to the license cache.
            </summary>
        </member>
        <member name="T:Spire.Properties.Resources">
            <summary>
              一个强类型的资源类，用于查找本地化的字符串等。
            </summary>
        </member>
        <member name="P:Spire.Properties.Resources.ResourceManager">
            <summary>
              返回此类使用的缓存的 ResourceManager 实例。
            </summary>
        </member>
        <member name="P:Spire.Properties.Resources.Culture">
            <summary>
              重写当前线程的 CurrentUICulture 属性，对
              使用此强类型资源类的所有资源查找执行重写。
            </summary>
        </member>
    </members>
</doc>
