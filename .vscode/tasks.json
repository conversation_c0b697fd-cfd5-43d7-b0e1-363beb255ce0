{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "C#: PandaServer.UpdateExam",
            "command": "dotnet",
            "type": "process",
            "args": [
                "build",
                "${workspaceFolder}/PandaServer.UpdateExam/PandaServer.UpdateExam.csproj"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "C#: PandaServer.Web.Entry",
            "command": "dotnet",
            "type": "process",
            "args": [
                "build",
                "${workspaceFolder}/PandaServer.Web.Entry/PandaServer.Web.Entry.csproj"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "C#: PandaServer.UpdateExam Publish",
            "type": "process",
            "command": "dotnet",
            "args": [
                "publish",
                "${workspaceFolder}/PandaServer.UpdateExam/PandaServer.UpdateExam.csproj",
                "-c",
                "Release",
                "-o",
                "${workspaceFolder}/PandaServer.UpdateExam/bin/Release/net9.0/publish/"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "C#: PandaServer.UpdateExam rsync",
            "type": "shell",
            "command": "rsync",
            "args": [
                "-avz",
                "--update",
                "-e",
                "ssh -o StrictHostKeyChecking=no",
                "/Users/<USER>/MyCode/PandaServer/PandaServer.UpdateExam/bin/Release/net9.0/publish/*",
                "<EMAIL>:/app/UpdateExam/"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "reveal": "always",
                "panel": "dedicated"
            },
            "problemMatcher": [],
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "C#: PandaServer.Web.Entry Build and Deploy",
            "dependsOrder": "sequence",
            "dependsOn": [
                "C#: PandaServer.Web.Entry Publish",
                "C#: PandaServer.Web.Entry rsync"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            }
        },
        {
            "label": "C#: PandaServer.Web.Entry Publish",
            "type": "shell",
            "command": "bash",
            "args": [
                "-c",
                "rm -rf ${workspaceFolder}/PandaServer.Web.Entry/bin/Release/net9.0/publish/ && dotnet publish ${workspaceFolder}/PandaServer.Web.Entry/PandaServer.Web.Entry.csproj -c Release -o ${workspaceFolder}/PandaServer.Web.Entry/bin/Release/net9.0/publish/"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "C#: PandaServer.Web.Entry rsync",
            "type": "shell",
            "command": "rsync",
            "args": [
                "-avz",
                "--update",
                "-e",
                "ssh -o StrictHostKeyChecking=no",
                "/Users/<USER>/MyCode/PandaServer/PandaServer.Web.Entry/bin/Release/net9.0/publish/*",
                "<EMAIL>:/app/PandaWeb/"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "reveal": "always",
                "panel": "dedicated"
            },
            "problemMatcher": [],
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Git Push Daily",
            "type": "shell",
            "command": "git",
            "args": [
                "add",
                ".",
                "&&",
                "git",
                "commit",
                "-m",
                "daily",
                "&&",
                "git",
                "push",
                "-u",
                "origin",
                "master",
                "-f"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": []
        },
        {
            "type": "dotnet",
            "task": "build /Users/<USER>/MyCode/PandaServer/PandaServer.System/PandaServer.System.csproj",
            "file": "/Users/<USER>/MyCode/PandaServer/PandaServer.System/PandaServer.System.csproj",
            "group": "build",
            "problemMatcher": [],
            "label": "dotnet: build /Users/<USER>/MyCode/PandaServer/PandaServer.System/PandaServer.System.csproj"
        },
    ]
}