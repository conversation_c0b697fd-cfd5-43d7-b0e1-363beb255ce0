using PandaServer.Core;
using PandaServer.System.Entity;
using PandaServer.System.Services.Student.Dto.Contract;

namespace PandaServer.System.Services;

/// <summary>
/// 学生合同服务接口
/// </summary>
public interface IJxStudentContractService
{
    /// <summary>
    /// 根据学生ID获取合同信息
    /// </summary>
    /// <param name="studentId">学生ID</param>
    /// <returns>合同信息列表</returns>
    Task<List<JxStudentContractEntity>> GetByStudentIdAsync(string studentId);

    /// <summary>
    /// 上传合同PDF文件
    /// </summary>
    /// <param name="input">合同上传输入参数</param>
    /// <returns>上传结果</returns>
    Task<JxStudentContractEntity> UploadContractFileAsync(JxStudentContractUploadInput input);

    /// <summary>
    /// 上传签名视频
    /// </summary>
    /// <param name="input">视频上传输入参数</param>
    /// <returns>上传结果</returns>
    Task<JxStudentContractEntity> UploadSignVideoAsync(JxStudentContractVideoInput input);

    /// <summary>
    /// 上传签名笔迹
    /// </summary>
    /// <param name="input">笔迹上传输入参数</param>
    /// <returns>上传结果</returns>
    Task<JxStudentContractEntity> UploadSignAsync(JxStudentContractSignInput input);
}