﻿namespace PandaServer.System.Entity;

/// <summary>
///     车辆实体的表
/// </summary>
[SugarTable("car_car")]
[Description("车辆实体的表")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class CarEntity : DataEntityBase
{
    /// <summary>
    ///     车辆牌照
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarNumber", ColumnDescription = "车辆牌照")]
    public string CarNumber { get; set; }

    /// <summary>
    ///     随车电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarPhone", ColumnDescription = "随车电话", DefaultValue = "")]
    public string CarPhone { get; set; }

    /// <summary>
    ///     车辆昵称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_NickName", ColumnDescription = "车辆昵称", DefaultValue = "")]
    public string NickName { get; set; }

    /// <summary>
    ///     准驾车型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarType", ColumnDescription = "准驾车型", DefaultValue = "")]
    public string CarType { get; set; }

    /// <summary>
    ///     车辆型号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarModelId", ColumnDescription = "车辆型号")]
    public Guid CarModelId { get; set; }

    /// <summary>
    ///     最后一次的的经度
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Longitude", ColumnDescription = "最后一次的的经度")]
    public double Longitude { get; set; }

    /// <summary>
    ///     最后一次的纬度
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Latitude", ColumnDescription = "最后一次的纬度")]
    public double Latitude { get; set; }

    /// <summary>
    ///     车辆有效期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ValidDate", ColumnDescription = "车辆有效期",
        DefaultValue = "2000-01-01")]
    public DateTime ValidDate { get; set; }

    /// <summary>
    ///     车辆检测日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TestingDate", ColumnDescription = "车辆检测日期", DefaultValue = "2000-01-01")]
    public DateTime TestingDate { get; set; }

    /// <summary>
    ///     强制保险有效期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompulsoryInsuranceValidDate", ColumnDescription = "强制保险有效期", DefaultValue = "2000-01-01")]
    public DateTime CompulsoryInsuranceValidDate { get; set; }

    /// <summary>
    ///     保险有效期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InsuranceValidDate", ColumnDescription = "保险有效期",
        DefaultValue = "2000-01-01")]
    public DateTime InsuranceValidDate { get; set; }

    /// <summary>
    ///     安全技术检测日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SecurityTestingDate", ColumnDescription = "安全技术检测日期", DefaultValue = "2000-01-01")]
    public DateTime SecurityTestingDate { get; set; }

    /// <summary>
    ///     发动机号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_EngineNumber", ColumnDescription = "发动机号码")]
    public string EngineNumber { get; set; }

    /// <summary>
    ///     车架识别码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Vin", ColumnDescription = "车架识别码")]
    public string Vin { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500,
        DefaultValue = "")]
    public string Remark { get; set; }

    /// <summary>
    ///     品牌型号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Model", ColumnDescription = "型号", DefaultValue = "")]
    public string Model { get; set; }

    /// <summary>
    ///     品牌型号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Brand", ColumnDescription = "品牌", DefaultValue = "")]
    public string Brand { get; set; }

    /// <summary>
    ///     注册日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RegisterDate", ColumnDescription = "注册日期",
        DefaultValue = "2000-01-01")]
    public DateTime RegisterDate { get; set; }

    /// <summary>
    ///     物联卡  Sim 卡号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SimCardNo", ColumnDescription = "物联卡  Sim 卡号", DefaultValue = "")]
    public string SimCardNo { get; set; }

    /// <summary>
    ///     车辆状态
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Status", ColumnDescription = "车辆状态", DefaultValue = "0")]
    public CarStatusEnum Status { get; set; }

    /// <summary>
    ///     状态描述
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StatusDetail", ColumnDescription = "状态描述", DefaultValue = " ")]
    public string StatusDetail { get; set; }


    /// <summary>
    ///     计时设备状态
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudyDeviceStatus", ColumnDescription = "计时设备状态", DefaultValue = "0")]
    public StudyDeviceStatusEnum StudyDeviceStatus { get; set; }

    /// <summary>
    ///     计时设备状态说明
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudyDeviceStatusDetail", ColumnDescription = "计时设备状态说明",
        DefaultValue = " ")]
    public string StudyDeviceStatusDetail { get; set; }

    /// <summary>
    ///     资料完成度  百分比的 分值
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Completeness", ColumnDescription = "资料完成度  百分比的 分值", DefaultValue = "0")]
    public int Completeness { get; set; }

    /// <summary>
    ///     交通的备案号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JtNum", ColumnDescription = "交通的备案号", DefaultValue = "0")]
    public long JtNum { get; set; }

    /// <summary>
    ///     科目二使用的预约模板
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderCarKeMu2", ColumnDescription = "科目二使用的预约模板", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid OrderCarKeMu2 { get; set; }

    /// <summary>
    ///     科目三使用的预约模板
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderCarKeMu3", ColumnDescription = "科目三使用的预约模板", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid OrderCarKeMu3 { get; set; }

    /// <summary>
    ///     车辆分组
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarGroupId", ColumnDescription = "车辆分组", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid CarGroupId { get; set; }

    /// <summary>
    ///     考场场次
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FieldId", ColumnDescription = "考场场次", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid FieldId { get; set; }

    /// <summary>
    ///     驾校场地
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxFieldId", ColumnDescription = "驾校场地", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid JxFieldId { get; set; }

    /// <summary>
    ///     驾校报名场地
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxDeptId", ColumnDescription = "驾校报名场地", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid JxDeptId { get; set; }

    /// <summary>
    ///     证书编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CertificateNumber", ColumnDescription = "证书编号", DefaultValue = "''")]
    public string CertificateNumber { get; set; }

    /// <summary>
    ///     车辆转出日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TransferOutDate", ColumnDescription = "车辆转出日期", DefaultValue = "2000-01-01")]
    public DateTime TransferOutDate { get; set; }



    /// <summary>
    /// 科目二 排班模板Id
    /// </summary>
    [SugarColumn(ColumnName = "F_RankClassId_2", IsNullable = false, ColumnDescription = "科目二 排班模板Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid RankClassId_2 { get; set; }


    /// <summary>
    /// 科目三 排班模板Id
    /// </summary>
    [SugarColumn(ColumnName = "F_RankClassId_3", IsNullable = false, ColumnDescription = "科目三 排班模板Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid RankClassId_3 { get; set; }
}