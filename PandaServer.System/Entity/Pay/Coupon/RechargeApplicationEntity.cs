namespace PandaServer.System.Entity;

/// <summary>
///     充值申请
/// </summary>
[SugarTable("pay_recharge_application")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class RechargeApplicationEntity : DataEntityBase
{
    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "********-0000-0000-0000-************")]
    public override Guid TenantId { get; set; }

    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号",
        IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true,
        ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     充值金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Amount", ColumnDescription = "充值金额", DecimalDigits = 4, Length = 18)]
    public decimal Amount { get; set; }

    /// <summary>
    ///     充值账户名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AccountName", ColumnDataType = "nvarchar(100)", ColumnDescription = "充值账户名称")]
    public string AccountName { get; set; }

    /// <summary>
    ///     申请时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ApplyTime", ColumnDescription = "申请时间")]
    public DateTime ApplyTime { get; set; }

    /// <summary>
    ///     审核状态 (0: 待审核, 1: 审核通过, 2: 审核拒绝)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuditStatus", ColumnDescription = "审核状态")]
    public AuditEnum AuditStatus { get; set; }

    /// <summary>
    ///     审核人ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuditUserId", ColumnDescription = "审核人ID", DefaultValue = "********-0000-0000-0000-************")]
    public Guid AuditUserId { get; set; }

    /// <summary>
    ///     审核时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuditTime", ColumnDescription = "审核时间", DefaultValue = "1900-01-01")]
    public DateTime AuditTime { get; set; }

    /// <summary>
    ///     审核备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuditRemark", ColumnDataType = "nvarchar(500)", ColumnDescription = "审核备注")]
    public string AuditRemark { get; set; }

    /// <summary>
    ///     关联的现金券ID
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "F_CashCouponId", ColumnDescription = "关联的现金券ID")]
    public Guid? CashCouponId { get; set; }

    /// <summary>
    ///     申请备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDataType = "nvarchar(500)", ColumnDescription = "申请备注")]
    public string Remark { get; set; }
}