﻿namespace PandaServer.System.Entity;

/// <summary>
///     红包
/// </summary>
//[SplitTable(SplitType.Year, typeof(DateSplitTableService))]
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("pay_cash_coupon_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class CashCouponEntity : DataEntityBase
{
    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public override Guid TenantId { get; set; }

    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号",
        IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true,
        ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     收款用户的微信 OpenId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OpenId", ColumnDescription = "收款用户的微信 OpenId")]
    public string OpenId { get; set; }

    /// <summary>
    ///     收款用户的UserId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UserId", ColumnDescription = "收款用户的UserId")]
    public Guid UserId { get; set; }

    /// <summary>
    ///     shop_sale 的编号 或者 其他关联表的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleId", ColumnDescription = "shop_sale 的编号 或者 其他关联表的编号")]
    public Guid SaleId { get; set; }

    /// <summary>
    ///     shop_item 的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ItemId", ColumnDescription = "shop_item 的编号")]
    public Guid ItemId { get; set; }


    /// <summary>
    ///     付款的金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PayMoney", ColumnDescription = "付款的金额", DecimalDigits = 4, Length = 18)]
    public decimal PayMoney { get; set; }

    /// <summary>
    ///     是否付款
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PayMark", ColumnDescription = "是否付款")]
    public bool PayMark { get; set; }


    /// <summary>
    ///     付款时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PayTime", ColumnDescription = "付款时间")]
    public DateTime PayTime { get; set; }

    /// <summary>
    ///     是否收款
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ReceiveMark", ColumnDescription = "是否收款")]
    public bool ReceiveMark { get; set; }

    /// <summary>
    ///     收款时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ReceiveTime", ColumnDescription = "收款时间")]
    public DateTime ReceiveTime { get; set; }


    /// <summary>
    ///     逻辑审核标记
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfirmMark", ColumnDescription = "逻辑审核标记", DefaultValue = "0")]
    public bool ConfirmMark { get; set; }

    /// <summary>
    ///     审核实体的用户
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfirmUserId", ColumnDescription = "审核实体的用户", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid ConfirmUserId { get; set; }

    /// <summary>
    ///     审核时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfirmTime", ColumnDescription = "审核时间", DefaultValue = "1900-01-01")]
    public DateTime ConfirmTime { get; set; }

    /// <summary>
    ///     交易备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDataType = "nvarchar(500)", ColumnDescription = "交易备注")]
    public string Remark { get; set; }

    /// <summary>
    ///     唯一单号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TradeNo", ColumnDataType = "nvarchar(50)", ColumnDescription = "唯一单号")]
    public string TradeNo { get; set; }

    /// <summary>
    ///     交易订单号  由 微信系统 返回
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PaymentNo", ColumnDataType = "nvarchar(50)", ColumnDescription = "交易订单号")]
    public string PaymentNo { get; set; }

    /// <summary>
    ///     返利方式  TRANSFERS  由 微信系统 返回
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SendType", ColumnDataType = "nvarchar(50)", ColumnDescription = "返利方式")]
    public string SendType { get; set; }

    /// <summary>
    ///     是否取消
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsCancel", ColumnDescription = "是否取消")]
    public bool IsCancel { get; set; }

    /// <summary>
    ///     是否是系统红包账户
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsSystemAccount", ColumnDescription = "是否是系统红包账户", DefaultValue = "1")]
    public bool IsSystemAccount { get; set; }
}
