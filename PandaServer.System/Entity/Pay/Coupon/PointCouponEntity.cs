﻿namespace PandaServer.System.Entity;

/// <summary>
///     点数 交易的明细
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("pay_point_coupon_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class PointCouponEntity : DataEntityBase
{
    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public override Guid TenantId { get; set; }

    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号",
        IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true,
        ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     收款用户的微信 OpenId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OpenId", ColumnDescription = "收款用户的微信 OpenId")]
    public string OpenId { get; set; }

    /// <summary>
    ///     收款用户的UserId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UserId", ColumnDescription = "收款用户的UserId")]
    public Guid UserId { get; set; }

    /// <summary>
    ///     shop_sale 的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleId", ColumnDescription = "shop_sale 的编号")]
    public Guid SaleId { get; set; }

    /// <summary>
    ///     shop_item 的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ItemId", ColumnDescription = "shop_item 的编号")]
    public Guid ItemId { get; set; }

    /// <summary>
    ///     交易的点数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Point", ColumnDescription = "交易的点数", DecimalDigits = 4,
        Length = 18)]
    public decimal Point { get; set; }

    /// <summary>
    ///     逻辑审核标记
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfirmMark", ColumnDescription = "逻辑审核标记")]
    private bool ConfirmMark { get; set; }

    /// <summary>
    ///     审核实体的用户
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfirmUserId", ColumnDescription = "审核实体的用户")]
    private string ConfirmUserId { get; set; }

    /// <summary>
    ///     审核时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfirmTime", ColumnDescription = "审核时间")]
    private DateTime ConfirmTime { get; set; }

    /// <summary>
    ///     交易备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDataType = "nvarchar(500)",
        ColumnDescription = "交易备注")]
    public string Remark { get; set; }

    /// <summary>
    ///     考场的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FieldId", ColumnDescription = "考场的编号")]
    public Guid FieldId { get; set; }

    /// <summary>
    ///     关联的学员姓名
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_xm", ColumnDescription = "关联的学员姓名")]
    public string xm { get; set; }

    /// <summary>
    ///     关联的学员证件号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_sfzmhm", ColumnDescription = "关联的学员证件号")]
    public string sfzmhm { get; set; }

    /// <summary>
    ///     来源用户的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FromUserId", ColumnDescription = "来源用户的编号")]
    public Guid FromUserId { get; set; }

    /// <summary>
    ///     来源点数券的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FromPointCouponId", ColumnDescription = "来源点数券的编号")]
    public Guid FromPointCouponId { get; set; }

    /// <summary>
    ///     目标用户的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ToUserId", ColumnDescription = "目标用户的编号", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid ToUserId { get; set; }
}