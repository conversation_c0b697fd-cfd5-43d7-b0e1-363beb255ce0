namespace PandaServer.System.Entity;

/// <summary>
///     发票实体
/// </summary>
[SugarTable("pay_invoice")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class InvoiceEntity : DataEntityBase
{
    /// <summary>
    ///     纳税人识别号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Nsrsbh", ColumnDescription = "纳税人识别号")]
    public string Nsrsbh { get; set; }

    /// <summary>
    ///     用户电票平台账号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Username", ColumnDescription = "用户电票平台账号")]
    public string Username { get; set; }

    /// <summary>
    ///     用户电票平台密码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Password", ColumnDescription = "用户电票平台密码")]
    public string Password { get; set; }

    /// <summary>
    ///     销方识别号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Xhdwsbh", ColumnDescription = "销方识别号")]
    public string Xhdwsbh { get; set; }

    /// <summary>
    ///     销方名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Xhdwmc", ColumnDescription = "销方名称")]
    public string Xhdwmc { get; set; }

    /// <summary>
    ///     销方地址电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Xhdwdzdh", ColumnDescription = "销方地址电话")]
    public string Xhdwdzdh { get; set; }

    /// <summary>
    ///     销方银行账户
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Xhdwyhzh", ColumnDescription = "销方银行账户")]
    public string Xhdwyhzh { get; set; }
}