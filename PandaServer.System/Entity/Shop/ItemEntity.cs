﻿namespace PandaServer.System.Entity;

/// <summary>
///     商品列表
/// </summary>
[SugarTable("shop_item")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class ItemEntity : DataEntityBase
{
    /// <summary>
    ///     商品名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ItemName", ColumnDescription = "商品名称")]
    public string ItemName { get; set; }

    /// <summary>
    ///     收款账户
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AccountId", ColumnDescription = "收款账户")]
    public Guid AccountId { get; set; }

    /// <summary>
    ///     商品售价
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Price", ColumnDescription = "商品售价", DecimalDigits = 4, Length = 18)]
    public decimal Price { get; set; }

    /// <summary>
    ///     入账金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InComeMoney", ColumnDescription = "入账金额", DecimalDigits = 4, Length = 18)]
    public decimal InComeMoney { get; set; }

    /// <summary>
    ///     返利金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Coupon", ColumnDescription = "返利金额", DecimalDigits = 4, Length = 18)]
    public decimal Coupon { get; set; }

    /// <summary>
    ///     返利积分
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CouponPoint", ColumnDescription = "返利积分", DecimalDigits = 4, Length = 18)]
    public decimal CouponPoint { get; set; }

    /// <summary>
    ///     商品备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDataType = "nvarchar(500)", ColumnDescription = "商品备注")]
    public string Remark { get; set; }

    /// <summary>
    ///     职员购买的价格
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ForUserPrice", ColumnDescription = "职员购买的价格", DecimalDigits = 4, Length = 18)]
    public decimal ForUserPrice { get; set; }

    /// <summary>
    ///     管理员购买的价格
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ForAdminPrice", ColumnDescription = "管理员购买的价格", DecimalDigits = 4, Length = 18)]
    public decimal ForAdminPrice { get; set; }

    /// <summary>
    ///     关联场地
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FieldId", ColumnDescription = "关联场地")]
    public Guid FieldId { get; set; }

    /// <summary>
    ///     前置商品
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FirstItemId", ColumnDescription = "前置商品")]
    public Guid FirstItemId { get; set; }

    /// <summary>
    ///     前置商品购买时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FirstDays", ColumnDescription = "前置商品购买时间")]
    public int FirstDays { get; set; }

    /// <summary>
    ///     可以使用的时间长度
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TimeLength", ColumnDescription = "可以使用的时间长度")]
    public int TimeLength { get; set; }

    /// <summary>
    ///     可以使用的圈数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Circles", ColumnDescription = "可以使用的圈数")]
    public int Circles { get; set; }

    /// <summary>
    ///     购买以后的有效期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ValidDays", ColumnDescription = "购买以后的有效期")]
    public int ValidDays { get; set; }

    /// <summary>
    ///     排除数据来源的名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ExcludeCompanyName", ColumnDescription = "排除数据来源的名称", Length = 500)]
    public string ExcludeCompanyName { get; set; }

    /// <summary>
    ///     数据来源必须要包含的名字
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IncludeCompanyName", ColumnDescription = "数据来源必须要包含的名字", Length = 500)]
    public string IncludeCompanyName { get; set; }

    /// <summary>
    ///     关联公司的学员信息
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IncludeTenantIds", ColumnDescription = "关联公司的学员信息", Length = 500)]
    public string IncludeTenantIds { get; set; }

    /// <summary>
    ///     上架销售的开始时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StartTime", ColumnDescription = "上架销售的开始时间")]
    public DateTime StartTime { get; set; }

    /// <summary>
    ///     上架销售的结束时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_EndTime", ColumnDescription = "上架销售的结束时间")]
    public DateTime EndTime { get; set; }

    /// <summary>
    ///     是否需要打印
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_NeedPrint", ColumnDescription = "是否需要打印")]
    public bool NeedPrint { get; set; }

    /// <summary>
    ///     打印 Mini 票的 张数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PrintMiniTicket", ColumnDescription = "打印 Mini 票的 张数", DefaultValue = "0")]
    public int PrintMiniTicket { get; set; }

    /// <summary>
    ///     支持的车型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarTypes", ColumnDescription = "支持的车型", Length = 100)]
    public string CarTypes { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SortCode", ColumnDescription = "排序", DefaultValue = "999")]
    public int SortCode { get; set; }

    /// <summary>
    ///     可以预约考试的场次
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderExamNumber", ColumnDescription = "可以预约考试的场次", DefaultValue = "0")]
    public bool OrderExamNumber { get; set; }

    /// <summary>
    ///     当次考试只能购买一次
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OnlyOnce", ColumnDescription = "当次考试只能购买一次", DefaultValue = "0")]
    public bool OnlyOnce { get; set; }

    /// <summary>
    ///     票面上要打印的内容
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PrintText", ColumnDescription = "票面上要打印的内容", DefaultValue = " ", Length = 5000)]
    public string PrintText { get; set; }

    /// <summary>
    ///     是否打印购买的序号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_NeedPrintBuyOrderNumber", ColumnDescription = "是否打印购买的序号", DefaultValue = "0")]
    public bool NeedPrintBuyOrderNumber { get; set; }

    /// <summary>
    ///     生成 Sale 的张数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleCount", ColumnDescription = "生成 Sale 的张数", DefaultValue = "1")]
    public int SaleCount { get; set; }

    /// <summary>
    ///     只允许手机购买
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OnlyMobileBuy", ColumnDescription = "只允许手机购买", DefaultValue = "0")]
    public bool OnlyMobileBuy { get; set; }

    /// <summary>
    ///     需要审核
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_NeedAudit", ColumnDescription = "需要审核", DefaultValue = "0")]
    public bool NeedAudit { get; set; }

    /// <summary>
    ///     训练完成才能领钱
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_NeedTrainingComplete", ColumnDescription = "训练完成才能领钱", DefaultValue = "0")]
    public bool NeedTrainingComplete { get; set; }

    /// <summary>
    ///     返利有效期(天)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CouponValidDays", ColumnDescription = "返利有效期(天)", DefaultValue = "180")]
    public int CouponValidDays { get; set; }

    /// <summary>
    ///     发票配置ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceId", ColumnDescription = "发票配置ID", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid InvoiceId { get; set; }


    /// <summary>
    ///     发票商品编码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceSpbm", ColumnDescription = "发票商品编码", Length = 50, DefaultValue = "")]
    public string InvoiceSpbm { get; set; }



    /// <summary>
    ///     发票商品编码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceSl", ColumnDescription = "发票税率", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal InvoiceSl { get; set; }



    /// <summary>
    ///     发票商品编码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceSs", ColumnDescription = "发票税率", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal InvoiceSs { get; set; }
}