﻿namespace PandaServer.System.Entity;

/// <summary>
///     用户实体
/// </summary>
[SugarTable("sys_user")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class UserEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号", IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true, ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     账户
    /// </summary>
    // [Required(ErrorMessage = "账户不能为空")]
    [SugarColumn(IsNullable = false, ColumnName = "F_Account", ColumnDescription = "账户", UniqueGroupNameList = new[] { "sys_user" })]
    public string Account { get; set; }

    /// <summary>
    ///     姓名
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RealName", ColumnDescription = "姓名")]
    public string RealName { get; set; }

    /// <summary>
    ///     昵称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_NickName", ColumnDescription = "昵称")]
    public string NickName { get; set; }

    /// <summary>
    ///     头像
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Avatar", ColumnDescription = "头像", Length = 500)]
    public string Avatar { get; set; }

    /// <summary>
    ///     性别
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Sex", ColumnDescription = "性别 1. 男 2. 女")]
    public int Sex { get; set; }

    /// <summary>
    ///     生日
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Birthday", ColumnDescription = "生日")]
    public DateTime Birthday { get; set; }

    /// <summary>
    ///     手机
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Phone", ColumnDataType = "nvarchar(20)", ColumnDescription = "手机")]
    public string Phone { get; set; }

    /// <summary>
    ///     电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Tel", ColumnDataType = "nvarchar(20)", ColumnDescription = "电话")]
    public string Tel { get; set; }

    /// <summary>
    ///     证件号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IdCard", ColumnDescription = "证件号码")]
    public string IdCard { get; set; }

    /// <summary>
    ///     邮箱
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Email", ColumnDescription = "邮箱")]
    public string Email { get; set; }

    /// <summary>
    ///     微信号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_WeChat", ColumnDescription = "微信号")]
    public string WeChat { get; set; }

    /// <summary>
    ///     管理Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ManagerId", ColumnDescription = "管理Id")]
    public string ManagerId { get; set; }

    /// <summary>
    ///     安全级别
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SecurityLevel", ColumnDescription = "安全级别")]
    public int SecurityLevel { get; set; }

    /// <summary>
    ///     个性签名
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Signature", ColumnDataType = "nvarchar(4000)", ColumnDescription = "个性签名")]
    public string Signature { get; set; }


    /// <summary>
    ///     部门Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DepartmentId", ColumnDescription = "部门Id")]
    public Guid DepartmentId { get; set; }


    /// <summary>
    ///     岗位Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DutyId", ColumnDescription = "岗位Id")]
    public int DutyId { get; set; }


    /// <summary>
    ///     是否管理员
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsAdmin", ColumnDescription = "是否管理员")]
    public bool IsAdmin { get; set; }


    /// <summary>
    ///     有效标记
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_EnabledMark", ColumnDescription = "有效标记")]
    public bool IsEnabled { get; set; }


    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Description", ColumnDataType = "nvarchar(4000)", ColumnDescription = "备注")]
    public string Description { get; set; }


    // 拓展字段，2019-03-03
    /// <summary>
    ///     钉钉用户Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DingTalkUserId", ColumnDescription = "钉钉用户Id")]
    public string DingTalkUserId { get; set; }


    /// <summary>
    ///     钉钉用户名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DingTalkUserName", ColumnDescription = "钉钉用户名称")]
    public string DingTalkUserName { get; set; }


    /// <summary>
    ///     钉钉头像
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DingTalkAvatar", ColumnDataType = "nvarchar(100)", ColumnDescription = "钉钉头像")]
    public string DingTalkAvatar { get; set; }


    /// <summary>
    ///     微信开放Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_WxOpenId", ColumnDescription = "微信开放Id")]
    public string WxOpenId { get; set; }


    /// <summary>
    ///     微信头像
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_HeadImgUrl", ColumnDescription = "微信头像", Length = 500)]
    public string HeadImgUrl { get; set; }


    /// <summary>
    ///     拼音首字母
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PinYin", ColumnDescription = "拼音首字母", Length = 50, DefaultValue = "")]
    public string PinYin { get; set; }


    /// <summary>
    ///     导航 UserLogOnEntity 的表
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(Id))]
    public UserLogOnEntity UserLogOn { get; set; }

    /// <summary>
    ///     公司名称
    /// </summary>
    // [Required(ErrorMessage = "公司名称")]
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyName", ColumnDescription = "公司名称", DefaultValue = "  ")]
    public string CompanyName { get; set; }

    /// <summary>
    ///     入职时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_EntryTime", ColumnDescription = "入职时间", DefaultValue = "1900-01-01")]
    public DateTime EntryTime { get; set; }

    /// <summary>
    ///     禁止微信登录
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DisableWxLogin", ColumnDescription = "禁止微信登录", DefaultValue = "0")]
    public bool DisableWxLogin { get; set; }

    /// <summary>
    /// 禁止收到微信消息
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DisableWxMessage", ColumnDescription = "禁止收到微信消息", DefaultValue = "0")]
    public bool DisableWxMessage { get; set; }

    /// <summary>
    /// 主微信 OpenId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_MainOpenId", ColumnDescription = "主微信 OpenId", DefaultValue = " ")]
    public string MainOpenId { get; set; }


    /// <summary>
    ///     分润费率
    /// </summary>
    [SugarColumn(ColumnName = "F_CommissionRate", IsNullable = false, ColumnDescription = "分润费率", DefaultValue = "1")]
    public decimal CommissionRate { get; set; }



    /// <summary>
    /// 科目二 排班模板Id
    /// </summary>
    [SugarColumn(ColumnName = "F_RankClassId_2", IsNullable = false, ColumnDescription = "科目二 排班模板Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid RankClassId_2 { get; set; }


    /// <summary>
    /// 科目三 排班模板Id
    /// </summary>
    [SugarColumn(ColumnName = "F_RankClassId_3", IsNullable = false, ColumnDescription = "科目三 排班模板Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid RankClassId_3 { get; set; }


    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        var entity = this;
        entity.Id = Guid.NewGuid();
        entity.CreateUserId = UserManager.UserId;
        entity.CreateTime = DateTime.Now;
        entity.Avatar = "";
        entity.NickName = "";
        entity.Phone = "";
        entity.Tel = "";
        entity.Email = "";
        entity.WeChat = "";
        entity.ManagerId = "";
        entity.SecurityLevel = 0;
        entity.Signature = "";
        entity.DepartmentId = Guid.Empty;
        entity.DutyId = 0;
        entity.Description = "";
        entity.DingTalkUserId = "";
        entity.DingTalkUserName = "";
        entity.DingTalkAvatar = "";
        entity.WxOpenId = "";
        entity.HeadImgUrl = "";
        entity.IdCard = "";
        entity.DeleteTime = "2000-01-01".ParseToDateTime();
        entity.DeleteUserId = Guid.Empty;
        entity.UpdateTime = "2000-01-01".ParseToDateTime();
        entity.UpdateUserId = Guid.Empty;
        entity.CompanyName = "";
        entity.MainOpenId = "";
        entity.PinYin = "";
    }
}