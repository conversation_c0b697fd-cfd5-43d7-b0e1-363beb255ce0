using SqlSugar;

namespace PandaServer.System.Entity;

/// <summary>
///     合同模板控件
/// </summary>
[SugarTable("student_jxContractTemplateControls")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxContractTemplateControlsEntity
{
    /// <summary>
    ///     主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, ColumnName = "F_Id", ColumnDescription = "主键ID")]
    public Guid Id { get; set; }

    /// <summary>
    ///     模板ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TemplateId", ColumnDescription = "模板ID")]
    public Guid TemplateId { get; set; }

    /// <summary>
    ///     租户ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TenantId", ColumnDescription = "租户ID")]
    public Guid TenantId { get; set; }

    /// <summary>
    ///     控件名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Field", ColumnDescription = "控件名称")]
    public string Field { get; set; }

    /// <summary>
    ///     页码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PageNo", ColumnDescription = "页码")]
    public int PageNo { get; set; }

    /// <summary>
    ///     X坐标
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_X", ColumnDescription = "X坐标")]
    public float X { get; set; }

    /// <summary>
    ///     Y坐标
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Y", ColumnDescription = "Y坐标")]
    public float Y { get; set; }

    /// <summary>
    ///     字体大小
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FontSize", ColumnDescription = "字体大小")]
    public float FontSize { get; set; }
}