﻿namespace PandaServer.System.Entity;

/// <summary>
///     学生的合同
/// </summary>
[SugarTable("student_jxContract")]
[CodeGen]
public class JxStudentContractEntity : DataEntityBase
{
    /// <summary>
    ///     学员Id
    /// </summary>
    [Required(ErrorMessage = "学员Id不能为空")]
    [SugarColumn(IsNullable = false, ColumnName = "F_StudentId", ColumnDescription = "学员Id")]
    public Guid StudentId { get; set; }

    /// <summary>
    ///     模板编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TemplateId", ColumnDescription = "模板编号")]
    public Guid TemplateId { get; set; }


    /// <summary>
    ///     签署的微信
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OpenId", ColumnDescription = "OpenID")]
    public string OpenId { get; set; }


    /// <summary>
    ///     签署的合同文件
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SignFile", ColumnDescription = "签署的文件,分号隔开", Length = 4000)]
    public string SignFile { get; set; }


    /// <summary>
    ///     签署时候的视频
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SignVideo", ColumnDescription = "签署时候的视频,分号隔开", Length = 4000)]
    public string SignVideo { get; set; }


    /// <summary>
    ///     笔迹文件
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Sign", ColumnDescription = "笔迹文件", Length = 4000)]
    public string Sign { get; set; }


    /// <summary>
    ///     签署时候的图片
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SignImages", ColumnDescription = "签署时候的图片,分号隔开", Length = 4000)]
    public string SignImages { get; set; }


    /// <summary>
    ///     签署时候的定位信息
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_LocationInfo", ColumnDescription = "签署时候的定位信息", Length = 1000)]
    public string LocationInfo { get; set; }
}