﻿using System.Threading.Tasks;
using PandaServer.System.Services.Student.Dto;
using PandaServer.System.Services.Student.Dto.Pay;



namespace PandaServer.System.Entity.Student.Pay;

/// <summary>
///     学员缴费的实体
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_pay_********_0000_0000_0000_************")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
[SugarIndex("index1",
    nameof(JxShouldPayId), OrderByType.Asc,
    nameof(PayTypeId), OrderByType.Asc,
    nameof(PayTime), OrderByType.Asc,
    nameof(PayMoney), OrderByType.Asc,
    nameof(IsDelete), OrderByType.Asc,
    nameof(StudentId), OrderByType.Asc,
    nameof(CreateTime), OrderByType.Asc,
    nameof(AccountId), OrderByType.Asc,
    nameof(CreateJxDeptId), OrderByType.Asc,
    nameof(SysId), OrderByType.Asc)]
[SugarIndex("index2",
    nameof(IsDelete), OrderByType.Asc,
    nameof(StudentId), OrderByType.Asc)]
[SugarIndex("index3",
    nameof(StudentId), OrderByType.Asc)]

public class JxPayEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号", IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true, ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "********-0000-0000-0000-************")]
    [SplitField]
    public new Guid TenantId { get; set; }

    /// <summary>
    ///     批次的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_BatchId", ColumnDescription = "批次的 Id 当不同的支付方式 来付款的时候 会生成多条记录 但是 这些记录 批次Id 是一样的", DefaultValue = "********-0000-0000-0000-************")]
    public Guid BatchId { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间", ColumnName = "F_CreatorTime", IsNullable = false)]
    public override DateTime CreateTime { get; set; }

    /// <summary>
    ///     学员的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudentId", ColumnDescription = "学员的编号")]
    public Guid StudentId { get; set; }

    /// <summary>
    ///     员工的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UserId", ColumnDescription = "员工的编号")]
    public Guid UserId { get; set; }

    /// <summary>
    ///     车辆的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarId", ColumnDescription = "车辆的编号")]
    public Guid CarId { get; set; }

    /// <summary>
    ///     挂账的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ShouldPayId", ColumnDescription = "挂账的编号")]
    public Guid JxShouldPayId { get; set; }

    /// <summary>
    ///     费用类型编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CostTypeId", ColumnDescription = "费用类型编号")]
    public Guid CostTypeId { get; set; }

    /// <summary>
    ///     付款方式
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PayTypeId", ColumnDescription = "付款方式")]
    public Guid PayTypeId { get; set; }

    /// <summary>
    ///     缴费的金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PayMoney", ColumnDescription = "缴费的金额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal PayMoney { get; set; }


    /// <summary>
    ///     缴费时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PayTime", ColumnDescription = "缴费时间", DefaultValue = "2000-01-01")]
    public DateTime PayTime { get; set; }

    /// <summary>
    ///     付款账号  PayAccountEntity 的 Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AccountId", ColumnDescription = "付款账号  PayAccountEntity 的 Id", DefaultValue = "********-0000-0000-0000-************")]
    public Guid AccountId { get; set; }

    /// <summary>
    ///     OrderEntity 订单编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderId", ColumnDescription = "OrderEntity 订单编号", DefaultValue = "********-0000-0000-0000-************")]
    public Guid OrderId { get; set; }

    /// <summary>
    ///     OrderDetailEntity 订单编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderDetailId", ColumnDescription = "OrderDetailEntity 订单编号", DefaultValue = "********-0000-0000-0000-************")]
    public Guid OrderDetailId { get; set; }

    /// <summary>
    ///     入账的门店  一般作为 推荐人的小区
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CreateJxDeptId", ColumnDescription = "录入的门店", DefaultValue = "********-0000-0000-0000-************")]
    public Guid CreateJxDeptId { get; set; }

    /// <summary>
    ///     收款的门店
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxPayDeptId", ColumnDescription = "收款的门店", DefaultValue = "********-0000-0000-0000-************")]
    public Guid JxPayDeptId { get; set; }

    /// <summary>
    ///     缴费的备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "缴费的备注", Length = 2000, DefaultValue = "")]
    public string Remark { get; set; }


    /// <summary>
    ///     商户订单号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_MerchantOrderSn", ColumnDescription = "商户订单号", DefaultValue = "")]
    public string MerchantOrderSn { get; set; }


    /// <summary>
    ///     付呗等三方系统订单号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderSn", ColumnDescription = "付呗等三方系统订单号", DefaultValue = "")]
    public string OrderSn { get; set; }


    /// <summary>
    ///     通道订单号，微信订单号、支付宝订单号等
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ChannelOrderSn", ColumnDescription = "通道订单号，微信订单号、支付宝订单号等", DefaultValue = "")]
    public string ChannelOrderSn { get; set; }


    /// <summary>
    ///     收单机构订单号(微信、支付宝、银行)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InsOrderSn", ColumnDescription = "收单机构订单号(微信、支付宝、银行)", DefaultValue = "")]
    public string InsOrderSn { get; set; }

    /// <summary>
    ///     优惠活动
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleId", ColumnDescription = "优惠活动", DefaultValue = "********-0000-0000-0000-************")]
    public Guid SaleId { get; set; }

    /// <summary>
    ///     结算账户
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ComputerAccountId", ColumnDescription = "结算账户", DefaultValue = "********-0000-0000-0000-************")]
    public Guid ComputerAccountId { get; set; }


    /// <summary>
    ///     优惠金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DiscountMoney", ColumnDescription = "优惠金额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal DiscountMoney { get; set; }


    /// <summary>
    ///     未付款的金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_NoPay", ColumnDescription = "未付款的金额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal NoPay { get; set; }


    /// <summary>
    ///     实际收入
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxInCome", ColumnDescription = "实际收入", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal JxInCome { get; set; }

    /// <summary>
    ///     是否已退款
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsRefund", ColumnDescription = "是否已退款", DefaultValue = "0")]
    public bool IsRefund { get; set; }

    /// <summary>
    ///     退款金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RefundAmount", ColumnDescription = "退款金额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal RefundAmount { get; set; }

    /// <summary>
    ///     原始支付记录的ID（如果这是退款记录）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OriginalJxPayId", ColumnDescription = "原始支付记录的ID", DefaultValue = "********-0000-0000-0000-************")]
    public Guid OriginalJxPayId { get; set; }

    /// <summary>
    ///     退款记录的ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RefundId", ColumnDescription = "退款记录的ID", DefaultValue = "********-0000-0000-0000-************")]
    public Guid RefundId { get; set; }

    /// <summary>
    ///     应缴的金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ShouldPayMoney", ColumnDescription = "应缴的金额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal ShouldPayMoney { get; set; }


    /// <summary>
    ///     外部编号，导入时候导入
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ImportOutId", ColumnDescription = "外部编号，导入时候导入", DefaultValue = "")]
    public string ImportOutId { get; set; }


    /// <summary>
    ///     票据单号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_NoteNumber", ColumnDescription = "票据单号", DefaultValue = " ", Length = 500)]
    public string NoteNumber { get; set; }


    /// <summary>
    ///     付款人员
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PayeePerson", ColumnDescription = "付款人员", DefaultValue = " ", Length = 500)]
    public string PayeePerson { get; set; }


    /// <summary>
    ///     提成总和
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PushMoney", ColumnDescription = "提成总和", DefaultValue = "0", DecimalDigits = 4, Length = 18)]
    public decimal PushMoney { get; set; }


    // /// <summary>
    // ///     附加 关联 的人员
    // /// </summary>
    // [Navigate(NavigateType.OneToOne, nameof(UserId))]
    // public UserEntity AttachUser { get; set; }

    // /// <summary>
    // ///     附加 关联 的车辆
    // /// </summary>
    // [Navigate(NavigateType.OneToOne, nameof(CarId))]
    // public CarEntity AttachCar { get; set; }


    // /// <summary>
    // /// </summary>
    // [Navigate(NavigateType.OneToOne, nameof(PayTypeId))]
    // public PayTypeEntity PayType { get; set; }

    // /// <summary>
    // /// </summary>
    // [Navigate(NavigateType.OneToOne, nameof(CostTypeId))]
    // public CostTypeEntity CostType { get; set; }

    // /// <summary>
    // /// </summary>
    // [Navigate(NavigateType.OneToOne, nameof(CreateUserId))]
    // public UserEntity CreateUser { get; set; }

    // /// <summary>
    // /// </summary>
    // [Navigate(NavigateType.OneToOne, nameof(JxPayDeptId))]
    // public JxDeptEntity JxPayDept { get; set; }

    // /// <summary>
    // /// </summary>
    // [Navigate(NavigateType.OneToOne, nameof(CreateJxDeptId))]
    // public JxDeptEntity CreateJxDept { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(JxPushMoneyEntity.PayId))]
    public List<JxPushMoneyEntity> JxPushMoneys { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(Id), nameof(JxShouldPayId))]
    public JxShouldPayOutPut JxShouldPay { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Id), nameof(JxShouldPayId))]
    public List<JxShouldPayEntity> JxShouldPays { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(Id), nameof(StudentId))]
    public JxStudentOutPut JxStudent { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Id), nameof(StudentId))]
    public List<JxStudentEntity> JxStudents { get; set; }

    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;
        // if (ClaimConst.UserId.ParseToGuid() != Guid.Empty) this .CreateUser = ClaimConst.RealName.ToString();

        CreateTime = DateTime.Now;

        NoteNumber = "";
        PayeePerson = "";
    }

    public static async Task<bool> CreateIndex3_IfNotExists(ISqlSugarClient db, string tableName, ISimpleCacheService simpleCacheService)
    {
        string indexName = "index_3";

        var indexExists = await simpleCacheService.HashGetOne<bool>(CacheConst.Cache_DataBaseIndex, tableName + "-" + indexName);
        if (indexExists)
        {
            return true;
        }

        try
        {
            // 检查表是否存在
            string checkTableSql = @"
SELECT COUNT(1) 
FROM sys.objects 
WHERE object_id = OBJECT_ID(@TableName) AND type = 'U'";

            var tableExists = db.Ado.GetInt(checkTableSql, new { TableName = tableName }) > 0;
            if (!tableExists)
            {
                Console.WriteLine($"表 {tableName} 不存在");
                return false;
            }

            // 检查索引是否存在
            string checkIndexSql = @"
SELECT COUNT(1) 
FROM sys.indexes 
WHERE name = @IndexName 
AND object_id = OBJECT_ID(@TableName)";

            indexExists = db.Ado.GetInt(checkIndexSql, new { IndexName = indexName, TableName = tableName }) > 0;

            if (indexExists)
            {
                await simpleCacheService.HashAdd(CacheConst.Cache_DataBaseIndex, tableName + "-" + indexName, true);
                Console.WriteLine($"索引 {indexName} 已存在");
                return true;
            }

            // 创建索引
            string createIndexSql = $@"
CREATE NONCLUSTERED INDEX [{indexName}]
ON [dbo].[{tableName}] ([F_StudentId])";

            db.Ado.CommandTimeOut = 9999999;
            db.Ado.ExecuteCommand(createIndexSql);
            Console.WriteLine($"索引 {indexName} 创建成功");

            await simpleCacheService.HashAdd(CacheConst.Cache_DataBaseIndex, tableName + "-" + indexName, true);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"创建索引失败: {ex.Message}");
            return false;
        }
    }
}