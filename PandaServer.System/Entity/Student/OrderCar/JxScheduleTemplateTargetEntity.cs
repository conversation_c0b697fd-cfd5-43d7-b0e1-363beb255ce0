using SqlSugar;

namespace PandaServer.System.Entity;

/// <summary>
///     时间模板目标关联表
/// </summary>
[SugarTable("student_jxScheduleTemplateTarget")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxScheduleTemplateTargetEntity
{
    /// <summary>
    ///     主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, ColumnName = "F_Id", ColumnDescription = "主键ID")]
    public Guid Id { get; set; }

    /// <summary>
    ///     模板ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TemplateId", ColumnDescription = "模板ID")]
    public Guid TemplateId { get; set; }

    /// <summary>
    ///     目标ID（教练ID或车辆ID）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TargetId", ColumnDescription = "目标ID（教练ID或车辆ID）")]
    public Guid TargetId { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CreateTime", ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    ///     租户ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TenantId", ColumnDescription = "租户ID")]
    public Guid TenantId { get; set; }

    /// <summary>
    ///     是否删除
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsDelete", ColumnDescription = "是否删除")]
    public bool IsDelete { get; set; }
}