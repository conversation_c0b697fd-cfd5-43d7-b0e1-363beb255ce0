using SqlSugar;

namespace PandaServer.System.Entity;

/// <summary>
///     时间模板详情表
/// </summary>
[SugarTable("student_jxScheduleTemplateDetail")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxScheduleTemplateDetailEntity
{
    /// <summary>
    ///     主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, ColumnName = "F_Id", ColumnDescription = "主键ID")]
    public Guid Id { get; set; }

    /// <summary>
    ///     模板ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TemplateId", ColumnDescription = "模板ID")]
    public Guid TemplateId { get; set; }

    /// <summary>
    ///     星期几（1=周一, 7=周日）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_WeekDay", ColumnDescription = "星期几（1=周一, 7=周日）")]
    public int WeekDay { get; set; }

    /// <summary>
    ///     开始时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StartTime", ColumnDescription = "开始时间")]
    public TimeSpan StartTime { get; set; }

    /// <summary>
    ///     结束时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_EndTime", ColumnDescription = "结束时间")]
    public TimeSpan EndTime { get; set; }

    /// <summary>
    ///     最大学员数量
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_MaxStudentCount", ColumnDescription = "最大学员数量")]
    public int MaxStudentCount { get; set; }

    /// <summary>
    ///     科目ID列表（JSON格式存储）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMuIds", ColumnDescription = "科目ID列表（JSON格式存储）")]
    public string KeMuIds { get; set; }

    /// <summary>
    ///     车型列表（JSON格式存储）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarTypes", ColumnDescription = "车型列表（JSON格式存储）")]
    public string CarTypes { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CreateTime", ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    ///     租户ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TenantId", ColumnDescription = "租户ID")]
    public Guid TenantId { get; set; }

    /// <summary>
    ///     是否删除
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsDelete", ColumnDescription = "是否删除")]
    public bool IsDelete { get; set; }
}