using SqlSugar;

namespace PandaServer.System.Entity;

/// <summary>
///     时间模板表（周期性时间设置）
/// </summary>
[SugarTable("student_jxScheduleTemplate")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxScheduleTemplateEntity
{
    /// <summary>
    ///     主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, ColumnName = "F_Id", ColumnDescription = "主键ID")]
    public Guid Id { get; set; }

    /// <summary>
    ///     模板名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Name", ColumnDescription = "模板名称")]
    public string Name { get; set; }

    /// <summary>
    ///     是否公开
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsPublic", ColumnDescription = "是否公开")]
    public bool IsPublic { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CreateTime", ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    ///     租户ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TenantId", ColumnDescription = "租户ID")]
    public Guid TenantId { get; set; }

    /// <summary>
    ///     是否删除
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsDelete", ColumnDescription = "是否删除")]
    public bool IsDelete { get; set; }
}