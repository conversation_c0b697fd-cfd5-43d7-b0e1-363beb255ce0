using SqlSugar;

namespace PandaServer.System.Entity;

/// <summary>
///     预约记录表
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_jxOrder_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxOrderEntity
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号",
        IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true,
        ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     租户ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TenantId", ColumnDescription = "租户ID")]
    [SplitField]
    public Guid TenantId { get; set; }

    /// <summary>
    ///     主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, ColumnName = "F_Id", ColumnDescription = "主键ID")]
    public Guid Id { get; set; }

    /// <summary>
    ///     学员ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudentId", ColumnDescription = "学员ID")]
    public Guid StudentId { get; set; }

    /// <summary>
    ///     类型（1:教练预约 2:车辆预约）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Type", ColumnDescription = "类型（1:教练预约 2:车辆预约）")]
    public int Type { get; set; }

    /// <summary>
    ///     目标ID（教练ID或车辆ID）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TargetId", ColumnDescription = "目标ID（教练ID或车辆ID）")]
    public Guid TargetId { get; set; }

    /// <summary>
    ///     时间模板详情ID
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "F_JxScheduleTemplateDetailId", ColumnDescription = "时间模板详情ID")]
    public Guid? JxScheduleTemplateDetailId { get; set; }

    /// <summary>
    ///     时间模板ID
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "F_JxScheduleTemplateId", ColumnDescription = "时间模板ID")]
    public Guid? JxScheduleTemplateId { get; set; }

    /// <summary>
    ///     车辆ID
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "F_CarId", ColumnDescription = "车辆ID")]
    public Guid? CarId { get; set; }

    /// <summary>
    ///     用户ID
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "F_UserId", ColumnDescription = "用户ID")]
    public Guid? UserId { get; set; }

    /// <summary>
    ///     日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Date", ColumnDescription = "日期")]
    public DateTime Date { get; set; }

    /// <summary>
    ///     开始时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StartTime", ColumnDescription = "开始时间")]
    public TimeSpan StartTime { get; set; }

    /// <summary>
    ///     结束时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_EndTime", ColumnDescription = "结束时间")]
    public TimeSpan EndTime { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CreateTime", ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    ///     状态（0=待上课, 1=已完成, 2=取消）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Status", ColumnDescription = "状态（0=待上课, 1=已完成, 2=取消）")]
    public int Status { get; set; }

    /// <summary>
    ///     是否删除
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsDelete", ColumnDescription = "是否删除")]
    public bool IsDelete { get; set; }
}