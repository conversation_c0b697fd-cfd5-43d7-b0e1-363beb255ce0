namespace PandaServer.System.Entity;

/// <summary>
///     驾校资质
/// </summary>
[SugarTable("student_jxCompany")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxCompanyEntity : DataEntityBase
{
    /// <summary>
    ///     公司简称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Name", ColumnDescription = "公司简称")]
    public string Name { get; set; }

    /// <summary>
    ///     资质公司名字
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyName", ColumnDescription = "资质公司名字", Length = 500)]
    public string CompanyName { get; set; }

    /// <summary>
    ///     所属区域
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CityCode", ColumnDescription = "所属区域")]
    public string CityCode { get; set; }

    /// <summary>
    ///     费用的排序
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderNumber", ColumnDescription = "显示排序")]
    public int SortCode { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500,
        DefaultValue = " ")]
    public string Remark { get; set; }

    /// <summary>
    ///     地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Address", ColumnDescription = "地址", Length = 200,
        DefaultValue = " ")]
    public string Address { get; set; }

    /// <summary>
    ///     统一社会信用代码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyCode", ColumnDescription = "统一社会信用代码", DefaultValue = " ")]
    public string CompanyCode { get; set; }

    /// <summary>
    ///     道路运输备案号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TransportCode", ColumnDescription = "道路运输备案号", DefaultValue = " ")]
    public string TransportCode { get; set; }

    /// <summary>
    ///     公司联系电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyPhone", ColumnDescription = "公司联系电话", DefaultValue = " ")]
    public string CompanyPhone { get; set; }

    /// <summary>
    ///     开业时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OpenTime", ColumnDescription = "开业时间",
        DefaultValue = "2000-01-01")]
    public DateTime OpenTime { get; set; }

    /// <summary>
    ///     法人
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_LegalPerson", ColumnDescription = "法人", DefaultValue = " ")]
    public string LegalPerson { get; set; }

    /// <summary>
    ///     法人身份证号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_LegalPersonaIdCard", ColumnDescription = "法人身份证号码",
        DefaultValue = " ")]
    public string LegalPersonaIdCard { get; set; }

    /// <summary>
    ///     法人电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_LegalPersonPhone", ColumnDescription = "法人电话", DefaultValue = " ")]
    public string LegalPersonPhone { get; set; }

    /// <summary>
    ///     负责人
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ChargePerson", ColumnDescription = "负责人", DefaultValue = " ")]
    public string ChargePerson { get; set; }

    /// <summary>
    ///     负责人身份证号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ChargePersonaIdCard", ColumnDescription = "负责人身份证号码",
        DefaultValue = " ")]
    public string ChargePersonaIdCard { get; set; }

    /// <summary>
    ///     负责人电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ChargePersonPhone", ColumnDescription = "负责人电话",
        DefaultValue = " ")]
    public string ChargePersonPhone { get; set; }

    /// <summary>
    ///     联络人
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ContactPerson", ColumnDescription = "联络人", DefaultValue = " ")]
    public string ContactPerson { get; set; }

    /// <summary>
    ///     联络人身份证号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ContactPersonIdCard", ColumnDescription = "联络人身份证号码",
        DefaultValue = " ")]
    public string ContactPersonIdCard { get; set; }

    /// <summary>
    ///     联络人电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ContactPersonPhone", ColumnDescription = "联络人电话",
        DefaultValue = " ")]
    public string ContactPersonPhone { get; set; }

    /// <summary>
    ///     培训能力
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TrainingAbility", ColumnDescription = "培训能力", DefaultValue = "0")]
    public int TrainingAbility { get; set; }

    /// <summary>
    ///     培训车型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TrainingCarType", ColumnDescription = "培训车型", DefaultValue = " ")]
    public string TrainingCarType { get; set; }

    /// <summary>
    ///     公章图片
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SealFilePath", ColumnDescription = "公章图片", DefaultValue = " ", Length = 5000)]
    public string SealFilePath { get; set; }

    /// <summary>
    ///     法人签字图片
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_LegalPersonSignFilePath", ColumnDescription = "法人签字图片",
        DefaultValue = " ")]
    public string LegalPersonSignFilePath { get; set; }
}