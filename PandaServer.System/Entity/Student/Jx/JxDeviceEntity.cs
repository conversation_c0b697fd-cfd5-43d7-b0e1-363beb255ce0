namespace PandaServer.System.Entity;

/// <summary>
///     驾校 设备 的实体
/// </summary>
[SugarTable("student_jxDevice")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxDeviceEntity : DataEntityBase
{
    /// <summary>
    ///     区域名字
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Name", ColumnDescription = "区域名字")]
    public string Name { get; set; }

    /// <summary>
    ///     关联的所属区域
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxAreaId", ColumnDescription = "所属区域", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid JxAreaId { get; set; }

    /// <summary>
    ///     关联的考场的场地
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FieldId", ColumnDescription = "关联的考场的场地", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid FieldId { get; set; }

    /// <summary>
    ///     关联的驾校训练场
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxFieldId", ColumnDescription = "关联的驾校训练场", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid JxFieldId { get; set; }

    /// <summary>
    ///     设备的序列号 JT808 里面用 IMEI 来代替
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SerialNumber", ColumnDescription = "设备的序列号 JT808 里面用 IMEI 来代替")]
    public string SerialNumber { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注")]
    public string Remark { get; set; }

    /// <summary>
    ///     设备的型号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DeviceModel", ColumnDescription = "设备的型号")]
    public string DeviceModel { get; set; }

    /// <summary>
    ///     设备 类型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DeviceType", ColumnDescription = "设备 类型", DefaultValue = "0")]
    public JxDeviceTypeEnum JxDeviceType { get; set; }

    /// <summary>
    ///     设备 类型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DoorDirection", ColumnDescription = "闸机的进出方向 1.入口  2.出口", DefaultValue = "0")]
    public JxDoorDirectionEnum JxDoorDirection { get; set; }

    /// <summary>
    ///     关联的 车 Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarId", ColumnDescription = "关联的 车 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid CarId { get; set; }

    /// <summary>
    ///     绑定的手机号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Phone", ColumnDescription = "绑定的手机号码", DefaultValue = " ")]
    public string Phone { get; set; }

    /// <summary>
    ///     物联卡的卡号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SimNo", ColumnDescription = "物联卡的卡号", DefaultValue = " ")]
    public string SimNo { get; set; }

    /// <summary>
    ///     设备 Ip 地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Ip", ColumnDescription = "设备 Ip 地址", DefaultValue = " ")]
    public string Ip { get; set; }

    /// <summary>
    ///     设备 端口号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Port", ColumnDescription = "设备 端口号", DefaultValue = " ")]
    public string Port { get; set; }

    /// <summary>
    ///     设备激活码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ActivationCode", ColumnDescription = "设备激活码", DefaultValue = " ", Length = 200)]
    public string ActivationCode { get; set; }

    /// <summary>
    ///     管理员证件号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AdminIdNumber", ColumnDescription = "管理员证件号", DefaultValue = " ", Length = 500)]
    public string AdminIdNumber { get; set; }

    /// <summary>
    ///     是否有打印
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_HasPrinter", ColumnDescription = "是否有打印", DefaultValue = "0")]
    public bool HasPrinter { get; set; }

    /// <summary>
    ///     设备品跑型号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BrandModel", ColumnDescription = "设备品跑型号", DefaultValue = " ", Length = 200)]
    public JxDoorBrandModelEnum BrandModel { get; set; }


    /// <summary>
    ///     关联 公司的 Id 列表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IncludeTenantIds", ColumnDescription = "关联 公司的 Id 列表", DefaultValue = " ", Length = 5000)]
    public string IncludeTenantIds { get; set; }
}