﻿namespace PandaServer.System.Entity;

/// <summary>
///     驾校 报名点的实体
/// </summary>
[SugarTable("student_jxClass")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxClassEntity : DataEntityBase
{
    /// <summary>
    ///     班别名字
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Name", ColumnDescription = "班别名字")]
    public string Name { get; set; }

    /// <summary>
    ///     班型备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "班型备注", Length = 4000)]
    public string Remark { get; set; }


    /// <summary>
    ///     生效时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StartTime", ColumnDescription = "生效时间",
        DefaultValue = "2000-01-01")]
    public DateTime StartTime { get; set; }


    /// <summary>
    ///     失效时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_EndTime", ColumnDescription = "失效时间", DefaultValue = "2099-01-01")]
    public DateTime EndTime { get; set; }

    /// <summary>
    ///     排序数字
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderNumber", ColumnDescription = "排序数字", DefaultValue = "0")]
    public int SortCode { get; set; }

    /// <summary>
    ///     打印备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PrintRemark", ColumnDescription = "打印备注", DefaultValue = " ",
        Length = 5000)]
    public string PrintRemark { get; set; }

    /// <summary>
    ///     待培打印备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PrintWaitStudyRemark", ColumnDescription = "待培打印备注",
        DefaultValue = " ", Length = 5000)]
    public string PrintWaitStudyRemark { get; set; }

    /// <summary>
    ///     最小年纪
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_MinAge", ColumnDescription = "最小年纪", DefaultValue = "1")]
    public int MinAge { get; set; }

    /// <summary>
    ///     最大年纪
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_MaxAge", ColumnDescription = "最大年纪", DefaultValue = "100")]
    public int MaxAge { get; set; }

    /// <summary>
    ///     e签保机构编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ESign_OrgId", ColumnDescription = "e签保机构编号", DefaultValue = " ")]
    public string ESignOrgId { get; set; }

    /// <summary>
    ///     e签保模板编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ESign_TemplateId", ColumnDescription = "e签保模板编号", DefaultValue = " ")]
    public string ESignTemplateId { get; set; }

    /// <summary>
    ///     自助申请教练
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AllowSelfApplyCoach", ColumnDescription = "自助申请教练", DefaultValue = "0")]
    public bool AllowSelfApplyCoach { get; set; }

    /// <summary>
    ///     自助申请换教练
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AllowSelfChangeCoach", ColumnDescription = "自助申请换教练", DefaultValue = "0")]
    public bool AllowSelfChangeCoach { get; set; }
}