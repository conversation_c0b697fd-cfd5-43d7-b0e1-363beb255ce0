namespace PandaServer.System.Entity;

/// <summary>
///     考试成绩的 公共表
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_exam_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
[SugarIndex("index1", nameof(StudentId), OrderByType.Asc, nameof(IsDelete), OrderByType.Asc)]
public class JxStudentExamEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号",
        IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true,
        ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public new Guid TenantId { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间", ColumnName = "F_CreatorTime", IsNullable = false)]
    public override DateTime CreateTime { get; set; }

    /// <summary>
    ///     学员的编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudentId", ColumnDescription = "学员的编号")]
    public Guid StudentId { get; set; }

    /// <summary>
    ///     约考日期
    /// </summary>
    [SugarColumn(ColumnName = "F_ykrq", IsNullable = false, ColumnDescription = "约考日期")]
    public DateTime ykrq { get; set; }

    /// <summary>
    ///     考试日期
    /// </summary>
    [SugarColumn(ColumnName = "F_ksrq", IsNullable = false, ColumnDescription = "考试日期")]
    [SplitField]
    public DateTime ksrq { get; set; }

    /// <summary>
    ///     考场名称
    /// </summary>
    [SugarColumn(ColumnName = "F_kc", IsNullable = false, ColumnDescription = "考场名称")]
    public string kc { get; set; }

    /// <summary>
    ///     考试场次
    /// </summary>
    [SugarColumn(ColumnName = "F_cc", IsNullable = false, ColumnDescription = "考试场次")]
    public string cc { get; set; }

    /// <summary>
    ///     准驾车型
    /// </summary>
    [SugarColumn(ColumnName = "F_zjcx", IsNullable = false, ColumnDescription = "准驾车型")]
    public string zjcx { get; set; }

    /// <summary>
    ///     考试分数
    /// </summary>
    [SugarColumn(ColumnName = "F_cj", IsNullable = false, ColumnDescription = "考试分数")]
    public int cj { get; set; }

    /// <summary>
    ///     考试次数（12123 里面的考试次数）
    /// </summary>
    [SugarColumn(ColumnName = "F_kscs", IsNullable = false, ColumnDescription = "考试次数（12123 里面的考试次数）", DefaultValue = "0")]
    public int kscs { get; set; }

    /// <summary>
    ///     考试成绩
    /// </summary>
    [SugarColumn(ColumnName = "F_ResultId", IsNullable = false, ColumnDescription = "考试成绩")]
    public JxExamResultEnum ResultId { get; set; }

    /// <summary>
    ///     驾校名称
    /// </summary>
    [SugarColumn(ColumnName = "F_jxmc", IsNullable = false, ColumnDescription = "驾校名称")]
    public string jxmc { get; set; }

    /// <summary>
    ///     考试科目
    /// </summary>
    [SugarColumn(ColumnName = "F_KeMuId", IsNullable = false, ColumnDescription = "考试科目")]
    public JxExamKeMuEnum KeMuId { get; set; }

    /// <summary>
    ///     已经取消
    /// </summary>
    [SugarColumn(ColumnName = "F_IsCancel", IsNullable = false, ColumnDescription = "已经取消")]
    public bool IsCancel { get; set; }


    /// <summary>
    ///     关联关账的编号
    /// </summary>
    [SugarColumn(ColumnName = "F_ShouldPayId", IsNullable = false, ColumnDescription = "关联关账的编号")]
    public Guid ShouldPayId { get; set; }

    /// <summary>
    ///     记录备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "记录备注", Length = 4000)]
    public string Remark { get; set; }

    /// <summary>
    ///     考试教练
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TeachUserId", ColumnDescription = "考试教练")]
    public Guid TeachUserId { get; set; }


    /// <summary>
    ///     考试次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Times", ColumnDescription = "考试次数", DefaultValue = "0")]
    public int Times { get; set; }


    /// <summary>
    ///     上次发送的成绩
    /// </summary>
    [SugarColumn(ColumnName = "F_LastSendResultId", IsNullable = false, ColumnDescription = "上次发送的成绩", DefaultValue = "7")]
    public JxExamResultEnum LastSendResultId { get; set; }


    /// <summary>
    ///     上次发送的成绩
    /// </summary>
    [SugarColumn(ColumnName = "F_LastSendCj", IsNullable = false, ColumnDescription = "上次发送的成绩", DefaultValue = "0")]
    public int LastSendCj { get; set; }


    /// <summary>
    ///     上次更新的时间
    /// </summary>
    [SugarColumn(ColumnName = "F_LastUpdateTime", IsNullable = false, ColumnDescription = "上次更新的时间", DefaultValue = "1900-01-01")]
    public DateTime LastUpdateTime { get; set; }


    /// <summary>
    ///     上次发送预约信息的时间
    /// </summary>
    [SugarColumn(ColumnName = "F_LastSendOrderTime", IsNullable = false, ColumnDescription = "上次发送预约信息的时间", DefaultValue = "1900-01-01")]
    public DateTime LastSendOrderTime { get; set; }


    /// <summary>
    ///     上次发送结果信息的时间
    /// </summary>
    [SugarColumn(ColumnName = "F_LastSendResultTime", IsNullable = false, ColumnDescription = "上次发送结果信息的时间",
        DefaultValue = "1900-01-01")]
    public DateTime LastSendResultTime { get; set; }


    /// <summary>
    ///     最后一次更新信息的Ukey
    /// </summary>
    [SugarColumn(ColumnName = "F_UKey", IsNullable = false, ColumnDescription = "最后一次更新信息的Ukey", DefaultValue = "''")]
    public string UKey { get; set; }

    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;
        // if (ClaimConst.UserId.ParseToGuid() != Guid.Empty) this .CreateUser = ClaimConst.RealName.ToString();

        CreateTime = DateTime.Now;
    }
}