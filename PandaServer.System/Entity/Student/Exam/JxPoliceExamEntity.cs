namespace PandaServer.System.Entity;

/// <summary>
///     考试成绩的 公共表
/// </summary>
[SplitTable(SplitType.Year, typeof(DateSplitTableService))]
[SugarTable("student_police_exam_{year}{month}{day}")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxPoliceExamEntity
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [SugarColumn(ColumnName = "F_Id", IsPrimaryKey = true, ColumnDescription = "主键Id")]
    public Guid Id { get; set; }

    /// <summary>
    ///     约考日期
    /// </summary>
    [SugarColumn(ColumnName = "F_ykrq", IsNullable = false, ColumnDescription = "约考日期")]
    public DateTime ykrq { get; set; }

    /// <summary>
    ///     考试日期
    /// </summary>
    [SugarColumn(ColumnName = "F_ksrq", IsNullable = false, ColumnDescription = "考试日期")]
    [SplitField]
    public DateTime ksrq { get; set; }

    /// <summary>
    ///     考场名称
    /// </summary>
    [SugarColumn(ColumnName = "F_kc", IsNullable = false, ColumnDescription = "考场名称", Length = 100)]
    public string kc { get; set; }

    /// <summary>
    ///     考试场次
    /// </summary>
    [SugarColumn(ColumnName = "F_cc", IsNullable = false, ColumnDescription = "考试场次", Length = 100)]
    public string cc { get; set; }

    /// <summary>
    ///     准驾车型
    /// </summary>
    [SugarColumn(ColumnName = "F_zjcx", IsNullable = false, ColumnDescription = "准驾车型", Length = 10)]
    public string zjcx { get; set; }

    /// <summary>
    ///     考试分数
    /// </summary>
    [SugarColumn(ColumnName = "F_cj", IsNullable = false, ColumnDescription = "考试分数")]
    public int cj { get; set; }

    /// <summary>
    ///     考试成绩
    /// </summary>
    [SugarColumn(ColumnName = "F_ResultId", IsNullable = false, ColumnDescription = "考试成绩")]
    public JxExamResultEnum ResultId { get; set; }

    /// <summary>
    ///     驾校名称
    /// </summary>
    [SugarColumn(ColumnName = "F_jxmc", IsNullable = false, ColumnDescription = "驾校名称", Length = 50)]
    public string Jxmc { get; set; }

    /// <summary>
    ///     考试科目
    /// </summary>
    [SugarColumn(ColumnName = "F_KeMuId", IsNullable = false, ColumnDescription = "考试科目")]
    public JxExamKeMuEnum KeMuId { get; set; }

    /// <summary>
    ///     业务流水号
    /// </summary>
    [SugarColumn(ColumnName = "F_lsh", IsNullable = false, ColumnDescription = "业务流水号", Length = 50)]
    public string lsh { get; set; }

    /// <summary>
    ///     业务序号
    /// </summary>
    [SugarColumn(ColumnName = "F_xh", IsNullable = false, ColumnDescription = "业务序号", Length = 50)]
    public string xh { get; set; }

    /// <summary>
    ///     准考证编号
    /// </summary>
    [SugarColumn(ColumnName = "F_zkzmbh", IsNullable = false, ColumnDescription = "准考证编号", Length = 100)]
    public string zkzmbh { get; set; }

    /// <summary>
    ///     已经取消
    /// </summary>
    [SugarColumn(ColumnName = "F_IsCancel", IsNullable = false, ColumnDescription = "已经取消")]
    public bool IsCancel { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CreatorTime", ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    ///     学员姓名
    /// </summary>
    [SugarColumn(ColumnName = "F_xm", IsNullable = false, ColumnDescription = "学员姓名", Length = 30)]
    public string xm { get; set; }

    /// <summary>
    ///     证件号码
    /// </summary>
    [SugarColumn(ColumnName = "F_sfzmhm", IsNullable = false, ColumnDescription = "证件号码", Length = 30)]
    public string sfzmhm { get; set; }

    /// <summary>
    ///     学员姓名
    /// </summary>
    [SugarColumn(ColumnName = "F_real_xm", IsNullable = false, ColumnDescription = "学员姓名", Length = 30, DefaultValue = "''")]
    public string real_xm { get; set; }

    /// <summary>
    ///     证件号码
    /// </summary>
    [SugarColumn(ColumnName = "F_real_sfzmhm", IsNullable = false, ColumnDescription = "证件号码", Length = 30, DefaultValue = "''")]
    public string real_sfzmhm { get; set; }


    /// <summary>
    /// </summary>
    /// <param name="km"></param>
    /// <returns></returns>
    public int GetKeMuID(string km)
    {
        if (km == "科目一" || km == "1") return 1;
        if (km == "科目二" || km == "2") return 2;
        if (km == "科目三" || km == "3") return 3;
        if (km == "科目二三" || km == "9") return 9;
        if (km == "科目三 道路驾驶技能考试") return 3;
        if (km == "科目三 道路驾驶技能") return 3;
        if (km.Contains("科目三") && km.Contains("道路驾驶技能")) return 3;
        if (km == "科目四" || km == "安全文明考试" || km == "4") return 4;
        if (km == "科目三 安全文明常识考试") return 4;
        if (km == "科目三 安全文明常识") return 4;
        if (km.Contains("科目三") && km.Contains("安全文明考试")) return 4;
        return -1;
    }

    /// <summary>
    ///     根据 成绩 和 科目 和 车型 转换成 ResultID
    /// </summary>
    /// <param name="cj"></param>
    /// <param name="KeMuID"></param>
    /// <param name="zjcx"></param>
    /// <returns></returns>
    public JxExamResultEnum GetResultId(int cj, int KeMuID, string zjcx)
    {
        if (cj < 0) return JxExamResultEnum.NoExam;

        if (KeMuID == 1 && cj == 0) return JxExamResultEnum.Absent;

        if (KeMuID == 2 && !string.IsNullOrEmpty(zjcx) && zjcx.Substring(0, 1) != "A" && zjcx.Substring(0, 1) != "B")
        {
            if (cj >= 80)
                return JxExamResultEnum.Pass;
            return JxExamResultEnum.NoPass;
        }

        if (cj >= 90)
            return JxExamResultEnum.Pass;
        return JxExamResultEnum.NoPass;
    }

    public bool CreateUniqueIndex(string tableName)
    {
        try
        {
            string sql = $@"
            IF EXISTS (
                SELECT * FROM sys.indexes 
                WHERE name='idx_unique_student' 
                AND object_id = OBJECT_ID('{tableName}')
            )
            BEGIN
                DROP INDEX idx_unique_student ON {tableName};
            END

            CREATE UNIQUE INDEX idx_unique_student 
            ON {tableName} (F_xm, F_sfzmhm, F_ksrq, F_KeMuId, F_lsh, F_xh);";

            DbContext.Db.Ado.CommandTimeOut = 9999999;
            DbContext.Db.Ado.ExecuteCommand(sql);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"创建索引时发生错误: {ex.Message}");
            return false;
        }
    }
}