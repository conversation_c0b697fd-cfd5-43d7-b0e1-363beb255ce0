using SqlSugar;

namespace PandaServer.System.Entity.Student.Tenant;

/// <summary>
///     租户使用记录实体
/// </summary>
[SugarTable("sys_tenant_usage")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class TenantUsageRecordEntity : BaseEntity
{
    /// <summary>
    ///     租户ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TenantId", ColumnDescription = "租户ID")]
    public Guid TenantId { get; set; }

    /// <summary>
    ///     学生ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudentId", ColumnDescription = "学生ID")]
    public Guid StudentId { get; set; }

    /// <summary>
    ///     充值记录ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RechargeId", ColumnDescription = "充值记录ID")]
    public Guid RechargeId { get; set; }

    /// <summary>
    ///     使用次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UsageCount", ColumnDescription = "使用次数", DefaultValue = "0")]
    public int UsageCount { get; set; }

    /// <summary>
    ///     剩余次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RemainingCount", ColumnDescription = "剩余次数", DefaultValue = "0")]
    public int RemainingCount { get; set; }

    /// <summary>
    ///     使用时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UsageTime", ColumnDescription = "使用时间", DefaultValue = "GETDATE()")]
    public DateTime UsageTime { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDataType = "nvarchar(500)", ColumnDescription = "备注", DefaultValue = "")]
    public string Remark { get; set; }
}