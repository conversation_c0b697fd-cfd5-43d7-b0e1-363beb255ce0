using System;
using SqlSugar;
using PandaServer.System.Entity.Student.Student;

namespace PandaServer.System.Entity.Student.Student;

/// <summary>
/// 微信退考试费申请流程
/// </summary>
[SugarTable("wechat_refund_application")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class WeChatRefundApplicationEntity : DataEntityBase
{
    /// <summary>
    /// 学员ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudentId", ColumnDescription = "学员ID", DefaultValue = "********-0000-0000-0000-************")]
    public Guid StudentId { get; set; } = Guid.Empty;

    /// <summary>
    /// 学员手机号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Phone", ColumnDescription = "学员手机号", Length = 20)]
    public string Phone { get; set; }

    /// <summary>
    /// 微信OpenId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OpenId", ColumnDescription = "微信OpenId", Length = 50)]
    public string OpenId { get; set; }

    /// <summary>
    /// 退款金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RefundAmount", ColumnDescription = "退款金额", ColumnDataType = "decimal(18,2)")]
    public decimal RefundAmount { get; set; }

    /// <summary>
    /// 退款科目
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMuId", ColumnDescription = "退款科目", Length = 50)]
    public JxExamKeMuEnum KeMuId { get; set; }

    /// <summary>
    /// 考场名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_kcmc", ColumnDescription = "考场名称")]
    public string kcmc { get; set; }

    /// <summary>
    /// 考试场次
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_kscc", ColumnDescription = "考试场次")]
    public string kscc { get; set; }

    /// <summary>
    /// 准驾车型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_zjcx", ColumnDescription = "准驾车型")]
    public string zjcx { get; set; }

    /// <summary>
    /// 流水号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_lsh", ColumnDescription = "流水号")]
    public string lsh { get; set; }

    /// <summary>
    /// 预约考试时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ksrq", ColumnDescription = "预约考试时间")]
    public DateTime ksrq { get; set; }

    /// <summary>
    /// 银行卡号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BankCardNo", ColumnDescription = "银行卡号", Length = 30)]
    public string BankCardNumber { get; set; }

    /// <summary>
    /// 开户银行
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BankName", ColumnDescription = "开户银行", Length = 50)]
    public string BankName { get; set; }

    /// <summary>
    /// 开户支行
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BankBranch", ColumnDescription = "开户支行", Length = 100)]
    public string BankBranchName { get; set; }

    /// <summary>
    /// 开户人姓名
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AccountHolderName", ColumnDescription = "开户人姓名", Length = 50)]
    public string BankAccountName { get; set; }

    /// <summary>
    /// 申请状态（0：待审核，1：已通过，2：已拒绝）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Status", ColumnDescription = "申请状态")]
    public AuditEnum Status { get; set; }

    /// <summary>
    /// 审核人ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuditorUserId", ColumnDescription = "审核人ID", DefaultValue = "********-0000-0000-0000-************")]
    public Guid AuditorUserId { get; set; } = Guid.Empty;

    /// <summary>
    /// 审核时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuditTime", ColumnDescription = "审核时间")]
    public DateTime AuditTime { get; set; }

    /// <summary>
    /// 审核备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuditRemark", ColumnDescription = "审核备注", Length = 500)]
    public string AuditRemark { get; set; }

    /// <summary>
    /// 退款人ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RefundOperatorId", ColumnDescription = "退款人ID", DefaultValue = "********-0000-0000-0000-************")]
    public Guid RefundOperatorId { get; set; } = Guid.Empty;

    /// <summary>
    /// 退款时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RefundTime", ColumnDescription = "退款时间")]
    public DateTime RefundTime { get; set; }

    /// <summary>
    /// 退款单号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RefundNo", ColumnDescription = "退款单号", Length = 50)]
    public string RefundNo { get; set; }

    /// <summary>
    /// 退款备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RefundRemark", ColumnDescription = "退款备注", Length = 500)]
    public string RefundRemark { get; set; }

    /// <summary>
    /// 上传的截图图片地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ScreenshotUrl", ColumnDescription = "上传的截图图片地址", Length = 500)]
    public string ScreenshotUrl { get; set; }

    /// <summary>
    /// 上传的银行卡截图地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BankCardScreenshotUrl", ColumnDescription = "上传的银行卡截图地址", Length = 500)]
    public string BankCardScreenshotUrl { get; set; }

    /// <summary>
    /// 关联的现金券ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CashCouponId", ColumnDescription = "关联的现金券ID", DefaultValue = "********-0000-0000-0000-************")]
    public Guid CashCouponId { get; set; }

    /// <summary>
    /// 驾校应支付ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxShouldPayId", ColumnDescription = "驾校应支付ID", DefaultValue = "********-0000-0000-0000-************")]
    public Guid JxShouldPayId { get; set; }

    /// <summary>
    /// 驾校支付ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxPayId", ColumnDescription = "驾校支付ID", DefaultValue = "********-0000-0000-0000-************")]
    public Guid JxPayId { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500)]
    public string Remark { get; set; }

    /// <summary>
    /// 是否付款
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsPaid", ColumnDescription = "是否付款", DefaultValue = "0")]
    public bool IsPaid { get; set; }

    // /// <summary>
    // /// 付款时间
    // /// </summary>
    // [SugarColumn(IsNullable = false, ColumnName = "F_PaymentTime", ColumnDescription = "付款时间", DefaultValue = "1900-01-01")]
    // public DateTime PaymentTime { get; set; } = DateTime.MinValue;
}