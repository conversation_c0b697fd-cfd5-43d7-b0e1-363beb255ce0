﻿using PandaServer.System.Entity.Student.Pay;

namespace PandaServer.System.Entity;

/// <summary>
///     学员的实体
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_student_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxStudentEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号",
        IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true,
        ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public Guid TenantId { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间", ColumnName = "F_CreatorTime",
        IsNullable = false)]
    public override DateTime CreateTime { get; set; }

    /// <summary>
    ///     学员证件号码
    /// </summary>
    // [Required(ErrorMessage = "学员姓名不能为空")]
    [SugarColumn(IsNullable = false, ColumnName = "F_xm", ColumnDescription = "学员姓名")]
    public string xm { get; set; }

    /// <summary>
    ///     学员证件类型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_sfzmmc", ColumnDescription = "学员证件类型", Length = 1)]
    public string sfzmmc { get; set; }

    /// <summary>
    ///     学员证件号码
    /// </summary>
    // [Required(ErrorMessage = "学员证件号不能为空")]
    [SugarColumn(IsNullable = false, ColumnName = "F_sfzmhm", ColumnDescription = "学员证件号码")]
    public string sfzmhm { get; set; }

    /// <summary>
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Status", ColumnDescription = "当前状态")]
    public JxStudentStatusEnum Status { get; set; }

    /// <summary>
    ///     证件有效期开始
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_sfzyxqs", ColumnDescription = "证件有效期开始")]
    public DateTime sfzyxqs { get; set; }

    /// <summary>
    ///     证件有效期结束
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_sfzyxqz", ColumnDescription = "证件有效期结束")]
    public DateTime sfzyxqz { get; set; }

    /// <summary>
    ///     学员性别
    /// </summary>
    // [Required(ErrorMessage = "学员性别")]
    [SugarColumn(IsNullable = false, ColumnName = "F_xb", ColumnDescription = "学员性别")]
    public XbEnum xb { get; set; }

    /// <summary>
    ///     学员电话
    /// </summary>
    // [Required(ErrorMessage = "学员电话不能为空")]
    [SugarColumn(IsNullable = false, ColumnName = "F_yddh", ColumnDescription = "学员电话")]
    public string yddh { get; set; }

    /// <summary>
    ///     固定电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_gddh", ColumnDescription = "固定电话")]
    public string gddh { get; set; }

    /// <summary>
    ///     出生日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_csrq", ColumnDescription = "出生日期")]
    public DateTime csrq { get; set; }

    /// <summary>
    ///     学员国籍
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_gj", ColumnDescription = "学员国籍")]
    public int gj { get; set; }

    /// <summary>
    ///     登记住所行政区划
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_djzsxzqh", ColumnDescription = "登记住所行政区划")]
    public int djzsxzqh { get; set; }

    /// <summary>
    ///     登记住所地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_djzsxxdz", ColumnDescription = "登记住所地址", Length = 500)]
    public string djzsxxdz { get; set; }

    /// <summary>
    ///     电子邮箱
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_dzyx", ColumnDescription = "电子邮箱", Length = 100)]
    public string dzyx { get; set; }

    /// <summary>
    ///     联系住所行政区划
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_lxzsxzqh", ColumnDescription = "联系住所行政区划")]
    public int lxzsxzqh { get; set; }

    /// <summary>
    ///     联系住所地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_lxzsxxdz", ColumnDescription = "联系住所地址", Length = 500)]
    public string lxzsxxdz { get; set; }

    /// <summary>
    ///     来源
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ly", ColumnDescription = "来源")]
    public string ly { get; set; }

    /// <summary>
    ///     邮寄地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_yjdz", ColumnDescription = "邮寄地址", Length = 500)]
    public string yjdz { get; set; }

    /// <summary>
    ///     邮政编码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_yzbm", ColumnDescription = "邮政编码")]
    public string yzbm { get; set; }

    /// <summary>
    ///     行政区划
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_xzqh", ColumnDescription = "行政区划")]
    public int xzqh { get; set; }


    /// <summary>
    ///     优惠活动
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleId", ColumnDescription = "优惠活动")]
    public Guid SaleId { get; set; }


    /// <summary>
    ///     优惠活动 优惠金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleDiscountMoney", ColumnDescription = "优惠活动 优惠金额",
        DefaultValue = "0", DecimalDigits = 4, Length = 18)]
    public decimal SaleDiscountMoney { get; set; }


    /// <summary>
    ///     优惠活动（叠加）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleId2", ColumnDescription = "优惠活动（叠加）")]
    public Guid SaleId2 { get; set; }


    /// <summary>
    ///     优惠活动（叠加） 优惠金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleDiscountMoney2", ColumnDescription = "优惠活动（叠加） 优惠金额",
        DefaultValue = "0", DecimalDigits = 4, Length = 18)]
    public decimal SaleDiscountMoney2 { get; set; }


    /// <summary>
    ///     准驾车型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxClassId", ColumnDescription = "准驾车型")]
    public Guid JxClassId { get; set; }


    /// <summary>
    ///     报名场地
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxDeptId", ColumnDescription = "报名场地")]
    public Guid JxDeptId { get; set; }


    /// <summary>
    ///     训练场地
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxFieldId", ColumnDescription = "训练场地")]
    public Guid JxFieldId { get; set; }


    /// <summary>
    ///     受理单位的Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxCompanyId", ColumnDescription = "受理单位的Id",
        DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid JxCompanyId { get; set; }

    /// <summary>
    ///     准驾车型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CarType", ColumnDescription = "准驾车型", Length = 10)]
    public string CarType { get; set; }

    /// <summary>
    ///     现准驾车型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_xzjcx", ColumnDescription = "现准驾车型")]
    public string xzjcx { get; set; }

    /// <summary>
    ///     档案编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_dabh", ColumnDescription = "档案编号")]
    public string dabh { get; set; }

    /// <summary>
    ///     学员备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "学员备注", Length = 4000)]
    public string Remark { get; set; }


    /// <summary>
    ///     应缴学费
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Tuition", ColumnDescription = "应缴学费", DefaultValue = "0",
        DecimalDigits = 4, Length = 18)]
    public decimal Tuition { get; set; }


    /// <summary>
    ///     已缴学费
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TuitionPay", ColumnDescription = "已缴学费", DefaultValue = "0",
        DecimalDigits = 4, Length = 18)]
    public decimal TuitionPay { get; set; }


    /// <summary>
    ///     培训费欠费金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TuitionNoPay", ColumnDescription = "培训费欠费金额", DefaultValue = "0",
        DecimalDigits = 4, Length = 18)]
    public decimal TuitionNoPay { get; set; }


    /// <summary>
    ///     培训费的优惠金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TuitionDiscountMoney", ColumnDescription = "培训费的优惠金额", DefaultValue = "0", DecimalDigits = 4, Length = 18)]
    public decimal TuitionDiscountMoney { get; set; }


    /// <summary>
    ///     全部的挂账金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ShouldPayMoney", ColumnDescription = "全部的挂账金额", DefaultValue = "0",
        DecimalDigits = 4, Length = 18)]
    public decimal ShouldPayMoney { get; set; }


    /// <summary>
    ///     欠费金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_NoPay", ColumnDescription = "欠费金额", DefaultValue = "0",
        DecimalDigits = 4, Length = 18)]
    public decimal NoPay { get; set; }


    /// <summary>
    ///     校入总金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxInCome", ColumnDescription = "校入总金额", DefaultValue = "0",
        DecimalDigits = 4, Length = 18)]
    public decimal JxInCome { get; set; }


    /// <summary>
    ///     提成总和
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PushMoney", ColumnDescription = "提成总和", DefaultValue = "0",
        DecimalDigits = 4, Length = 18)]
    public decimal PushMoney { get; set; }


    /// <summary>
    ///     销售人员
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleUserId", ColumnDescription = "销售人员")]
    public Guid SaleUserId { get; set; }


    /// <summary>
    ///     销售门店
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleJxDeptId", ColumnDescription = "销售门店")]
    public Guid SaleJxDeptId { get; set; }


    /// <summary>
    ///     协单人员
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleUserId2", ColumnDescription = "协单人员")]
    public Guid SaleUserId2 { get; set; }


    /// <summary>
    ///     责任人员
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SaleUserId3", ColumnDescription = "责任人员")]
    public Guid SaleUserId3 { get; set; }


    /// <summary>
    ///     科目一教练
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TeachOneUserId", ColumnDescription = "科目一教练")]
    public Guid TeachOneUserId { get; set; }


    /// <summary>
    ///     科目二教练
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TeachTwoUserId", ColumnDescription = "科目二教练")]
    public Guid TeachTwoUserId { get; set; }


    /// <summary>
    ///     科目三教练
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TeachThreeUserId", ColumnDescription = "科目三教练")]
    public Guid TeachThreeUserId { get; set; }



    /// <summary>
    ///     科二带训的车牌
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TeachTwoCar", ColumnDescription = "科二带训的车牌",
        DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid TeachTwoCar { get; set; }


    /// <summary>
    ///     科三带训的车牌
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TeachThreeCar", ColumnDescription = "科三带训的车牌",
        DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid TeachThreeCar { get; set; }


    /// <summary>
    ///     现场拍照 是否已经上传
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Image2", ColumnDescription = "现场拍照 是否已经上传", DefaultValue = "0")]
    public bool Image2 { get; set; }

    /// <summary>
    ///     身份证明正反面 是否已经上传
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Image4", ColumnDescription = "身份证明正反面 是否已经上传", DefaultValue = "0")]
    public bool Image4 { get; set; }

    /// <summary>
    ///     驾驶证申请表 是否已经上传
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Image6", ColumnDescription = "驾驶证申请表 是否已经上传", DefaultValue = "0")]
    public bool Image6 { get; set; }

    /// <summary>
    ///     体检表 是否已经上传
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Image7", ColumnDescription = "体检表 是否已经上传", DefaultValue = "0")]
    public bool Image7 { get; set; }

    /// <summary>
    ///     学员业务状态
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Ywzt", ColumnDescription = "学员业务状态")]
    public string Ywzt { get; set; }


    /// <summary>
    ///     报名日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RegistrationDate", ColumnDescription = "报名日期",
        DefaultValue = "1900-01-01")]
    public DateTime RegistrationDate { get; set; }


    /// <summary>
    ///     注册时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RegisterTime", ColumnDescription = "注册时间",
        DefaultValue = "1900-01-01")]
    public DateTime RegisterTime { get; set; }


    /// <summary>
    ///     注册公司名字
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RegisterSchoolName", ColumnDescription = "注册公司名字",
        DefaultValue = "")]
    public string RegisterSchoolName { get; set; }


    /// <summary>
    ///     注册失败的原因
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RegisterErrorReason", ColumnDescription = "注册失败的原因", Length = 200,
        DefaultValue = "")]
    public string RegisterErrorReason { get; set; }


    /// <summary>
    ///     科目一成绩
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu1", ColumnDescription = "科目一成绩", DefaultValue = "-1")]
    public JxExamResultEnum KeMu1 { get; set; }


    /// <summary>
    ///     科目一考试次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu1Times", ColumnDescription = "科目一考试次数", DefaultValue = "0")]
    public int KeMu1Times { get; set; }


    /// <summary>
    ///     科目一日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu1Date", ColumnDescription = "科目一日期",
        DefaultValue = "1900-01-01")]
    public DateTime KeMu1Date { get; set; }


    /// <summary>
    ///     科目二成绩
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu2", ColumnDescription = "科目二成绩", DefaultValue = "-1")]

    public JxExamResultEnum KeMu2 { get; set; }


    /// <summary>
    ///     科目二考试次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu2Times", ColumnDescription = "科目二考试次数", DefaultValue = "0")]
    public int KeMu2Times { get; set; }

    /// <summary>
    ///     科目二日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu2Date", ColumnDescription = "科目二日期",
        DefaultValue = "1900-01-01")]
    public DateTime KeMu2Date { get; set; }


    /// <summary>
    ///     科目三成绩
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu3", ColumnDescription = "科目三成绩", DefaultValue = "-1")]
    public JxExamResultEnum KeMu3 { get; set; }


    /// <summary>
    ///     科目三考试次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu3Times", ColumnDescription = "科目三考试次数", DefaultValue = "0")]
    public int KeMu3Times { get; set; }


    /// <summary>
    ///     科目三日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu3Date", ColumnDescription = "科目三日期",
        DefaultValue = "1900-01-01")]
    public DateTime KeMu3Date { get; set; }


    /// <summary>
    ///     科目四成绩
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu4", ColumnDescription = "科目四成绩", DefaultValue = "-1")]
    public JxExamResultEnum KeMu4 { get; set; }


    /// <summary>
    ///     科目四考试次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu4Times", ColumnDescription = "科目四考试次数", DefaultValue = "0")]
    public int KeMu4Times { get; set; }


    /// <summary>
    ///     科目四日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu4Date", ColumnDescription = "科目四日期",
        DefaultValue = "1900-01-01")]
    public DateTime KeMu4Date { get; set; }


    /// <summary>
    ///     科目一学时完成时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu1StudyCompleteDate", ColumnDescription = "科目一学时完成时间",
        DefaultValue = "1900-01-01")]
    public DateTime KeMu1StudyCompleteDate { get; set; }


    /// <summary>
    ///     科目二学时完成时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu2StudyCompleteDate", ColumnDescription = "科目二学时完成时间",
        DefaultValue = "1900-01-01")]
    public DateTime KeMu2StudyCompleteDate { get; set; }


    /// <summary>
    ///     科目二已学学时长度
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu2StudyTimeLength", ColumnDescription = "科目二已学学时长度",
        DefaultValue = "0")]
    public double KeMu2StudyTimeLength { get; set; }


    /// <summary>
    ///     科目三学时完成时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu3StudyCompleteDate", ColumnDescription = "科目三学时完成时间",
        DefaultValue = "1900-01-01")]
    public DateTime KeMu3StudyCompleteDate { get; set; }


    /// <summary>
    ///     科目三已学学时长度
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_KeMu3StudyTimeLength", ColumnDescription = "科目三已学学时长度",
        DefaultValue = "0")]
    public double KeMu3StudyTimeLength { get; set; }


    /// <summary>
    ///     线上报名标记
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsOnline", ColumnDescription = "线上报名标记", DefaultValue = "0")]
    public bool IsOnline { get; set; }


    /// <summary>
    ///     线上报名的 OpenId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OpenId", ColumnDescription = "线上报名的 OpenId", DefaultValue = "")]
    public string OpenId { get; set; }


    /// <summary>
    ///     档案审核状态
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DocSatus", ColumnDescription = "档案审核状态", DefaultValue = "0")]

    public JxStudentDocSatusEnum DocSatus { get; set; }

    /// <summary>
    ///     档案审核的备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DocRemark", ColumnDescription = "档案审核的备注", DefaultValue = "",
        Length = 4000)]
    public string DocRemark { get; set; }


    /// <summary>
    ///     文档受理状态
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DocStatusId", ColumnDescription = "文档受理状态", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid DocStatusId { get; set; }


    /// <summary>
    ///     合同编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ContractNumber", ColumnDescription = "合同编号", DefaultValue = " ")]
    public string ContractNumber { get; set; }

    /// <summary>
    ///     合同签署时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ContractSignTime", ColumnDescription = "合同签署时间", DefaultValue = "2000-01-01")]
    public DateTime ContractSignTime { get; set; }


    /// <summary>
    ///     第一次缴费时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PayTime", ColumnDescription = "缴费日期", DefaultValue = "1900-01-01")]
    public DateTime PayTime { get; set; }


    /// <summary>
    ///     培训费缴费完成时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PayCompleteTime", ColumnDescription = "完费时间", DefaultValue = "1900-01-01")]
    public DateTime PayCompleteTime { get; set; }


    /// <summary>
    ///     退学时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DropOutTime", ColumnDescription = "退学时间", DefaultValue = "1900-01-01")]
    public DateTime DropOutTime { get; set; }

    /// <summary>
    ///     学员业务状态  冗余数据备份用
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "F__Ywzt", ColumnDescription = "学员业务状态  冗余数据备份用", Length = 100)]
    public string _Ywzt { get; set; }


    /// <summary>
    ///     资料状态的Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JxStudentImformationStatusId", ColumnDescription = "资料状态的Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid JxStudentImformationStatusId { get; set; }


    /// <summary>
    ///     来源Id
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SourceId", ColumnDescription = "来源Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid SourceId { get; set; }


    /// <summary>
    ///     外部编号，导入时候导入
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OutId", ColumnDescription = "外部编号，导入时候导入", DefaultValue = "")]
    public string OutId { get; set; }


    /// <summary>
    ///     拼音
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PinYin", ColumnDescription = "拼音", DefaultValue = "")]
    public string PinYin { get; set; }

    /// <summary>
    ///     交通的备案号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_JtNum", ColumnDescription = "交通的备案号", DefaultValue = " ", Length = 50)]
    public string JtNum { get; set; }


    /// <summary>
    ///     约车的科目，默认是 0 按照当前的流程来预约
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderCarSubject", ColumnDescription = "约车的科目，默认是 0 按照当前的流程来预约", DefaultValue = "0")]
    public int OrderCarSubject { get; set; }


    /// <summary>
    ///     交通的备案号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PickUpAddress", ColumnDescription = "接送地址", DefaultValue = " ", Length = 50)]
    public string PickUpAddress { get; set; }


    /// <summary>
    ///     班别车型价格配置
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "F_JxClassCarTypePriceId", ColumnDescription = "班别车型价格配置", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid JxClassCarTypePriceId { get; set; }


    /// <summary>
    ///     流水号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Lsh", ColumnDescription = "流水号", DefaultValue = "''")]
    public string Lsh { get; set; }




    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;

        // if (ClaimConst.UserId.ParseToGuid() != Guid.Empty) this .CreateUser = ClaimConst.RealName.ToString();

        CreateTime = DateTime.Now;
    }

    /// <summary>
    /// </summary>
    public void FullNullData()
    {
        djzsxxdz = string.IsNullOrEmpty(djzsxxdz) ? "" : djzsxxdz;
        lxzsxxdz = string.IsNullOrEmpty(lxzsxxdz) ? "" : lxzsxxdz;
        yjdz = string.IsNullOrEmpty(yjdz) ? "" : yjdz;
        yzbm = string.IsNullOrEmpty(yzbm) ? "" : yzbm;
        Remark = string.IsNullOrEmpty(Remark) ? "" : Remark;
        dzyx = string.IsNullOrEmpty(dzyx) ? "" : dzyx;
        xzjcx = string.IsNullOrEmpty(xzjcx) ? "" : xzjcx;
        dabh = string.IsNullOrEmpty(dabh) ? "" : dabh;
        gddh = string.IsNullOrEmpty(gddh) ? "" : gddh;
        ly = string.IsNullOrEmpty(ly) ? "" : ly;
        OpenId = string.IsNullOrEmpty(OpenId) ? "" : OpenId;

        DocRemark = string.IsNullOrEmpty(DocRemark) ? "" : DocRemark;

        Ywzt = string.IsNullOrEmpty(Ywzt) ? DateTime.Now.ToString("yyyy-MM-dd") + " 报名" : Ywzt;
        RegisterErrorReason = string.IsNullOrEmpty(RegisterErrorReason) ? "" : RegisterErrorReason;
        RegisterSchoolName = string.IsNullOrEmpty(RegisterSchoolName) ? "" : RegisterSchoolName;

        PayTime = Convert.ToDateTime("1900-01-01");

        OutId = "";
        JtNum = "";
        Lsh = "";
        OrderCarSubject = 0;
        PickUpAddress = "";
        ContractNumber = "";
        PinYin = PingYinHelper.GetFirstSpell(xm);



        // JxStudentEntity student = new JxStudentEntity();
        // student.xm = "";
        // student.sfzmhm = "";
        // student.sfzmmc = "";
        // student.CarType = "";
        // student.Remark = "";
        // student.yddh = "";
        // student.gddh = "";
        // student.djzsxxdz = "";
        // student.lxzsxxdz = "";
        // student.dzyx = "";
        // student.ly = "";
        // student.yjdz = "";
        // student.yzbm = "";
        // student.xzjcx = "";
        // student.dabh = "";
        // student.Ywzt = "";
        // student.RegisterSchoolName = "";
        // student.RegisterErrorReason = "";
        // student.OpenId = "";
        // student.DocRemark = "";
        // student.ContractNumber = "";
        // student.OutId = "";
        // student.PinYin = "";
        // student.JtNum = "";
        // student.PickUpAddress = "";
        // student.TenantId = Guid.Empty;


        // await DbContext.Db.Insertable(student).SplitTable().ExecuteCommandAsync();
        // await DbContext.Db.Deleteable(student).SplitTable().ExecuteCommandAsync();

    }


    public static bool CreateIndexIfNotExists(ISqlSugarClient db, string tableName)
    {
        string indexName = "index_1";
        try
        {
            // 检查表是否存在
            string checkTableSql = @"
SELECT COUNT(1) 
FROM sys.objects 
WHERE object_id = OBJECT_ID(@TableName) AND type = 'U'";

            var tableExists = db.Ado.GetInt(checkTableSql, new { TableName = tableName }) > 0;
            if (!tableExists)
            {
                Console.WriteLine($"表 {tableName} 不存在");
                return false;
            }

            // 检查索引是否存在
            string checkIndexSql = @"
SELECT COUNT(1) 
FROM sys.indexes 
WHERE name = @IndexName 
AND object_id = OBJECT_ID(@TableName)";

            var indexExists = db.Ado.GetInt(checkIndexSql, new { IndexName = indexName, TableName = tableName }) > 0;

            if (indexExists)
            {
                Console.WriteLine($"索引 {indexName} 已存在");
                return true;
            }

            // 创建索引
            string createIndexSql = $@"
CREATE NONCLUSTERED INDEX [{indexName}]
ON [dbo].[{tableName}] ([F_TenantId],[F_DeleteMark],[F_CreatorTime])
INCLUDE ([F_JxDeptId],[F_JxFieldId],[F_SaleUserId],[F_SaleUserId2],[F_SaleUserId3])";

            db.Ado.CommandTimeOut = 9999999;
            db.Ado.ExecuteCommand(createIndexSql);
            Console.WriteLine($"索引 {indexName} 创建成功");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"创建索引失败: {ex.Message}");
            return false;
        }
    }
}