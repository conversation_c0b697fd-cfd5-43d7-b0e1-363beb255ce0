using System;

namespace PandaServer.System.Entity.Student.Student;

[SugarTable("student_slzt")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxStudentSlztEntity
{
    /// <summary>
    /// 证件类型名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_sfzmmc", ColumnDescription = "证件类型名称", Length = 5)]
    public string sfzmmc { get; set; }

    /// <summary>
    /// 身份证号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_sfzmhm", ColumnDescription = "身份证号码")]
    public string sfzmhm { get; set; }

    /// <summary>
    /// 更新状态
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_zt", ColumnDescription = "更新状态", Length = 5000)]
    public string zt { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_LastUpdateTime", ColumnDescription = "最后更新时间")]
    public DateTime LastUpdateTime { get; set; }
}
