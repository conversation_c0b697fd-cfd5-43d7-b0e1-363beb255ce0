namespace PandaServer.System.Entity.Student.Invoice;

/// <summary>
///     发票授权管理实体
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_invoice_auth_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxInvoiceAuthEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号", IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true, ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public new Guid TenantId { get; set; }

    /// <summary>
    ///     企业纳税人识别号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TaxpayerNo", ColumnDescription = "企业纳税人识别号", Length = 100, DefaultValue = "")]
    public string TaxpayerNo { get; set; }

    /// <summary>
    ///     企业名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyName", ColumnDescription = "企业名称", Length = 200, DefaultValue = "")]
    public string CompanyName { get; set; }

    /// <summary>
    ///     授权Token
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuthToken", ColumnDescription = "授权Token", Length = 1000, DefaultValue = "")]
    public string AuthToken { get; set; }

    /// <summary>
    ///     Token过期时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TokenExpireTime", ColumnDescription = "Token过期时间", DefaultValue = "2000-01-01")]
    public DateTime TokenExpireTime { get; set; }

    /// <summary>
    ///     是否已登录平台
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsLoginPlatform", ColumnDescription = "是否已登录平台", DefaultValue = "0")]
    public bool IsLoginPlatform { get; set; }

    /// <summary>
    ///     登录平台时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_LoginPlatformTime", ColumnDescription = "登录平台时间", DefaultValue = "2000-01-01")]
    public DateTime LoginPlatformTime { get; set; }

    /// <summary>
    ///     平台登录状态
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PlatformStatus", ColumnDescription = "平台登录状态", Length = 50, DefaultValue = "")]
    public string PlatformStatus { get; set; }

    /// <summary>
    ///     授权配置JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuthConfig", ColumnDescription = "授权配置JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string AuthConfig { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500, DefaultValue = "")]
    public string Remark { get; set; }

    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;
        CreateTime = DateTime.Now;
    }
}