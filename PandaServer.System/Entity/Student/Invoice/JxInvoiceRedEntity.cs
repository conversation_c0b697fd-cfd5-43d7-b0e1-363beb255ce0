namespace PandaServer.System.Entity.Student.Invoice;

/// <summary>
///     红字发票处理实体
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_invoice_red_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxInvoiceRedEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号", IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true, ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public new Guid TenantId { get; set; }

    /// <summary>
    ///     关联的授权实体ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuthId", ColumnDescription = "关联的授权实体ID")]
    public Guid AuthId { get; set; }

    /// <summary>
    ///     关联的蓝票实体ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BlueInvoiceId", ColumnDescription = "关联的蓝票实体ID")]
    public Guid BlueInvoiceId { get; set; }

    /// <summary>
    ///     原蓝票发票代码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OriginalInvoiceCode", ColumnDescription = "原蓝票发票代码", Length = 50, DefaultValue = "")]
    public string OriginalInvoiceCode { get; set; }

    /// <summary>
    ///     原蓝票发票号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OriginalInvoiceNumber", ColumnDescription = "原蓝票发票号码", Length = 50, DefaultValue = "")]
    public string OriginalInvoiceNumber { get; set; }

    /// <summary>
    ///     红字信息表编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInfoNumber", ColumnDescription = "红字信息表编号", Length = 100, DefaultValue = "")]
    public string RedInfoNumber { get; set; }

    /// <summary>
    ///     红字信息表状态 (0-待申请, 1-申请中, 2-申请成功, 3-申请失败)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInfoStatus", ColumnDescription = "红字信息表状态", DefaultValue = "0")]
    public int RedInfoStatus { get; set; }

    /// <summary>
    ///     红票发票代码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInvoiceCode", ColumnDescription = "红票发票代码", Length = 50, DefaultValue = "")]
    public string RedInvoiceCode { get; set; }

    /// <summary>
    ///     红票发票号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInvoiceNumber", ColumnDescription = "红票发票号码", Length = 50, DefaultValue = "")]
    public string RedInvoiceNumber { get; set; }

    /// <summary>
    ///     红票金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedAmount", ColumnDescription = "红票金额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal RedAmount { get; set; }

    /// <summary>
    ///     红票税额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedTaxAmount", ColumnDescription = "红票税额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal RedTaxAmount { get; set; }

    /// <summary>
    ///     红票价税合计
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedTotalAmount", ColumnDescription = "红票价税合计", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal RedTotalAmount { get; set; }

    /// <summary>
    ///     红票开具日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInvoiceDate", ColumnDescription = "红票开具日期", DefaultValue = "2000-01-01")]
    public DateTime RedInvoiceDate { get; set; }

    /// <summary>
    ///     红票状态 (0-待开票, 1-开票中, 2-开票成功, 3-开票失败)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInvoiceStatus", ColumnDescription = "红票状态", DefaultValue = "0")]
    public int RedInvoiceStatus { get; set; }

    /// <summary>
    ///     红字原因
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedReason", ColumnDescription = "红字原因", Length = 500, DefaultValue = "")]
    public string RedReason { get; set; }

    /// <summary>
    ///     申请红字信息表请求JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInfoRequestJson", ColumnDescription = "申请红字信息表请求JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string RedInfoRequestJson { get; set; }

    /// <summary>
    ///     申请红字信息表响应JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInfoResponseJson", ColumnDescription = "申请红字信息表响应JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string RedInfoResponseJson { get; set; }

    /// <summary>
    ///     开具红票请求JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInvoiceRequestJson", ColumnDescription = "开具红票请求JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string RedInvoiceRequestJson { get; set; }

    /// <summary>
    ///     开具红票响应JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RedInvoiceResponseJson", ColumnDescription = "开具红票响应JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string RedInvoiceResponseJson { get; set; }

    /// <summary>
    ///     红票PDF文件路径
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PdfFilePath", ColumnDescription = "红票PDF文件路径", Length = 500, DefaultValue = "")]
    public string PdfFilePath { get; set; }

    /// <summary>
    ///     失败原因
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FailReason", ColumnDescription = "失败原因", Length = 1000, DefaultValue = "")]
    public string FailReason { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500, DefaultValue = "")]
    public string Remark { get; set; }

    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;
        CreateTime = DateTime.Now;
    }
}