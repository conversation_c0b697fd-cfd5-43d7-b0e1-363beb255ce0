namespace PandaServer.System.Entity.Student.Invoice;

/// <summary>
///     发票操作日志实体
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_invoice_log_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxInvoiceLogEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号", IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true, ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public new Guid TenantId { get; set; }

    /// <summary>
    ///     关联的发票ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceId", ColumnDescription = "关联的发票ID", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid InvoiceId { get; set; }

    /// <summary>
    ///     关联的授权ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuthId", ColumnDescription = "关联的授权ID", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid AuthId { get; set; }

    /// <summary>
    ///     操作类型 (1-获取授权, 2-登录平台, 3-人脸认证, 4-开蓝票, 5-申请红字, 6-开红票, 7-获取PDF, 8-查询额度, 9-切换账号)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OperationType", ColumnDescription = "操作类型", DefaultValue = "1")]
    public int OperationType { get; set; }

    /// <summary>
    ///     操作名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OperationName", ColumnDescription = "操作名称", Length = 200, DefaultValue = "")]
    public string OperationName { get; set; }

    /// <summary>
    ///     API接口URL
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ApiUrl", ColumnDescription = "API接口URL", Length = 500, DefaultValue = "")]
    public string ApiUrl { get; set; }

    /// <summary>
    ///     HTTP方法
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_HttpMethod", ColumnDescription = "HTTP方法", Length = 20, DefaultValue = "")]
    public string HttpMethod { get; set; }

    /// <summary>
    ///     请求参数JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RequestJson", ColumnDescription = "请求参数JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string RequestJson { get; set; }

    /// <summary>
    ///     响应结果JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ResponseJson", ColumnDescription = "响应结果JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string ResponseJson { get; set; }

    /// <summary>
    ///     操作状态 (0-失败, 1-成功, 2-处理中)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Status", ColumnDescription = "操作状态", DefaultValue = "2")]
    public int Status { get; set; }

    /// <summary>
    ///     错误码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ErrorCode", ColumnDescription = "错误码", Length = 50, DefaultValue = "")]
    public string ErrorCode { get; set; }

    /// <summary>
    ///     错误信息
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ErrorMessage", ColumnDescription = "错误信息", Length = 1000, DefaultValue = "")]
    public string ErrorMessage { get; set; }

    /// <summary>
    ///     处理耗时(毫秒)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ProcessTime", ColumnDescription = "处理耗时(毫秒)", DefaultValue = "0")]
    public long ProcessTime { get; set; }

    /// <summary>
    ///     操作开始时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StartTime", ColumnDescription = "操作开始时间", DefaultValue = "2000-01-01")]
    public DateTime StartTime { get; set; }

    /// <summary>
    ///     操作结束时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_EndTime", ColumnDescription = "操作结束时间", DefaultValue = "2000-01-01")]
    public DateTime EndTime { get; set; }

    /// <summary>
    ///     IP地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IpAddress", ColumnDescription = "IP地址", Length = 50, DefaultValue = "")]
    public string IpAddress { get; set; }

    /// <summary>
    ///     用户代理
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UserAgent", ColumnDescription = "用户代理", Length = 1000, DefaultValue = "")]
    public string UserAgent { get; set; }

    /// <summary>
    ///     重试次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RetryCount", ColumnDescription = "重试次数", DefaultValue = "0")]
    public int RetryCount { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500, DefaultValue = "")]
    public string Remark { get; set; }

    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;
        CreateTime = DateTime.Now;
    }
}