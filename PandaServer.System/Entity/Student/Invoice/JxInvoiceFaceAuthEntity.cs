namespace PandaServer.System.Entity.Student.Invoice;

/// <summary>
///     发票人脸认证管理实体
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_invoice_face_auth_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxInvoiceFaceAuthEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号", IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true, ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public new Guid TenantId { get; set; }

    /// <summary>
    ///     关联的授权实体ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuthId", ColumnDescription = "关联的授权实体ID")]
    public Guid AuthId { get; set; }

    /// <summary>
    ///     企业纳税人识别号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TaxpayerNo", ColumnDescription = "企业纳税人识别号", Length = 100, DefaultValue = "")]
    public string TaxpayerNo { get; set; }

    /// <summary>
    ///     人脸认证二维码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FaceQRCode", ColumnDescription = "人脸认证二维码", ColumnDataType = "TEXT", DefaultValue = "")]
    public string FaceQRCode { get; set; }

    /// <summary>
    ///     二维码获取时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_QRCodeGetTime", ColumnDescription = "二维码获取时间", DefaultValue = "2000-01-01")]
    public DateTime QRCodeGetTime { get; set; }

    /// <summary>
    ///     二维码过期时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_QRCodeExpireTime", ColumnDescription = "二维码过期时间", DefaultValue = "2000-01-01")]
    public DateTime QRCodeExpireTime { get; set; }

    /// <summary>
    ///     认证状态 (0-未认证, 1-认证中, 2-认证成功, 3-认证失败)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuthStatus", ColumnDescription = "认证状态", DefaultValue = "0")]
    public int AuthStatus { get; set; }

    /// <summary>
    ///     认证完成时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuthCompleteTime", ColumnDescription = "认证完成时间", DefaultValue = "2000-01-01")]
    public DateTime AuthCompleteTime { get; set; }

    /// <summary>
    ///     是否需要人脸认证
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsNeedFaceAuth", ColumnDescription = "是否需要人脸认证", DefaultValue = "1")]
    public bool IsNeedFaceAuth { get; set; }

    /// <summary>
    ///     认证失败原因
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FailReason", ColumnDescription = "认证失败原因", Length = 500, DefaultValue = "")]
    public string FailReason { get; set; }

    /// <summary>
    ///     认证响应JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuthResponse", ColumnDescription = "认证响应JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string AuthResponse { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500, DefaultValue = "")]
    public string Remark { get; set; }

    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;
        CreateTime = DateTime.Now;
    }
}