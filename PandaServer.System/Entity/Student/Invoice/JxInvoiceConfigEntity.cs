namespace PandaServer.System.Entity.Student.Invoice;

/// <summary>
///     发票配置管理实体
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_invoice_config_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxInvoiceConfigEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号", IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true, ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public new Guid TenantId { get; set; }

    /// <summary>
    ///     配置类型 (1-账号配置, 2-授信额度, 3-税率配置, 4-其他)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfigType", ColumnDescription = "配置类型", DefaultValue = "1")]
    public int ConfigType { get; set; }

    /// <summary>
    ///     配置名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfigName", ColumnDescription = "配置名称", Length = 200, DefaultValue = "")]
    public string ConfigName { get; set; }

    /// <summary>
    ///     配置键
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfigKey", ColumnDescription = "配置键", Length = 100, DefaultValue = "")]
    public string ConfigKey { get; set; }

    /// <summary>
    ///     配置值
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ConfigValue", ColumnDescription = "配置值", ColumnDataType = "TEXT", DefaultValue = "")]
    public string ConfigValue { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsEnabled", ColumnDescription = "是否启用", DefaultValue = "1")]
    public bool IsEnabled { get; set; }

    /// <summary>
    ///     排序号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SortNum", ColumnDescription = "排序号", DefaultValue = "0")]
    public int SortNum { get; set; }

    /// <summary>
    ///     API基础地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ApiBaseUrl", ColumnDescription = "API基础地址", Length = 500, DefaultValue = "")]
    public string ApiBaseUrl { get; set; }

    /// <summary>
    ///     AppId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AppId", ColumnDescription = "AppId", Length = 100, DefaultValue = "")]
    public string AppId { get; set; }

    /// <summary>
    ///     AppSecret
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AppSecret", ColumnDescription = "AppSecret", Length = 500, DefaultValue = "")]
    public string AppSecret { get; set; }

    /// <summary>
    ///     企业纳税人识别号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TaxpayerNo", ColumnDescription = "企业纳税人识别号", Length = 100, DefaultValue = "")]
    public string TaxpayerNo { get; set; }

    /// <summary>
    ///     企业名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyName", ColumnDescription = "企业名称", Length = 200, DefaultValue = "")]
    public string CompanyName { get; set; }

    /// <summary>
    ///     企业地址
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyAddress", ColumnDescription = "企业地址", Length = 500, DefaultValue = "")]
    public string CompanyAddress { get; set; }

    /// <summary>
    ///     企业电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyPhone", ColumnDescription = "企业电话", Length = 100, DefaultValue = "")]
    public string CompanyPhone { get; set; }

    /// <summary>
    ///     企业开户行
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyBank", ColumnDescription = "企业开户行", Length = 200, DefaultValue = "")]
    public string CompanyBank { get; set; }

    /// <summary>
    ///     企业银行账号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CompanyBankAccount", ColumnDescription = "企业银行账号", Length = 100, DefaultValue = "")]
    public string CompanyBankAccount { get; set; }

    /// <summary>
    ///     默认税率
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DefaultTaxRate", ColumnDescription = "默认税率", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal DefaultTaxRate { get; set; }

    /// <summary>
    ///     授信额度
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CreditQuota", ColumnDescription = "授信额度", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal CreditQuota { get; set; }

    /// <summary>
    ///     已用额度
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UsedQuota", ColumnDescription = "已用额度", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal UsedQuota { get; set; }

    /// <summary>
    ///     配置描述
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Description", ColumnDescription = "配置描述", Length = 1000, DefaultValue = "")]
    public string Description { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500, DefaultValue = "")]
    public string Remark { get; set; }

    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;
        CreateTime = DateTime.Now;
    }
}