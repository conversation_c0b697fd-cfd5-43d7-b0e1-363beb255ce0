namespace PandaServer.System.Entity.Student.Invoice;

/// <summary>
///     发票明细实体
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_invoice_detail_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxInvoiceDetailEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号", IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true, ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public new Guid TenantId { get; set; }

    /// <summary>
    ///     关联的发票ID (蓝票或红票)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceId", ColumnDescription = "关联的发票ID")]
    public Guid InvoiceId { get; set; }

    /// <summary>
    ///     发票类型 (1-蓝票, 2-红票)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceType", ColumnDescription = "发票类型", DefaultValue = "1")]
    public int InvoiceType { get; set; }

    /// <summary>
    ///     商品或服务名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_GoodsName", ColumnDescription = "商品或服务名称", Length = 500, DefaultValue = "")]
    public string GoodsName { get; set; }

    /// <summary>
    ///     规格型号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Specification", ColumnDescription = "规格型号", Length = 200, DefaultValue = "")]
    public string Specification { get; set; }

    /// <summary>
    ///     计量单位
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Unit", ColumnDescription = "计量单位", Length = 50, DefaultValue = "")]
    public string Unit { get; set; }

    /// <summary>
    ///     数量
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Quantity", ColumnDescription = "数量", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal Quantity { get; set; }

    /// <summary>
    ///     单价
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UnitPrice", ColumnDescription = "单价", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal UnitPrice { get; set; }

    /// <summary>
    ///     金额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Amount", ColumnDescription = "金额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal Amount { get; set; }

    /// <summary>
    ///     税率
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TaxRate", ColumnDescription = "税率", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal TaxRate { get; set; }

    /// <summary>
    ///     税额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TaxAmount", ColumnDescription = "税额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal TaxAmount { get; set; }

    /// <summary>
    ///     价税合计
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TotalAmount", ColumnDescription = "价税合计", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    ///     商品编码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_GoodsCode", ColumnDescription = "商品编码", Length = 100, DefaultValue = "")]
    public string GoodsCode { get; set; }

    /// <summary>
    ///     税收分类编码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TaxClassificationCode", ColumnDescription = "税收分类编码", Length = 100, DefaultValue = "")]
    public string TaxClassificationCode { get; set; }

    /// <summary>
    ///     优惠政策标识 (0-无优惠, 1-免税, 2-减税等)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PreferentialPolicyFlag", ColumnDescription = "优惠政策标识", DefaultValue = "0")]
    public int PreferentialPolicyFlag { get; set; }

    /// <summary>
    ///     零税率标识 (空或0-非零税率, 1-免税, 2-不征税, 3-普通零税率)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ZeroTaxRateFlag", ColumnDescription = "零税率标识", DefaultValue = "0")]
    public int ZeroTaxRateFlag { get; set; }

    /// <summary>
    ///     扣除额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_DeductionAmount", ColumnDescription = "扣除额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal DeductionAmount { get; set; }

    /// <summary>
    ///     行号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_LineNumber", ColumnDescription = "行号", DefaultValue = "1")]
    public int LineNumber { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500, DefaultValue = "")]
    public string Remark { get; set; }

    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;
        CreateTime = DateTime.Now;
    }
}