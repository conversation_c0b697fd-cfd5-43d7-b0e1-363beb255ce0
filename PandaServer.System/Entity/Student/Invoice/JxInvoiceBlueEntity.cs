namespace PandaServer.System.Entity.Student.Invoice;

/// <summary>
///     数电蓝票开具实体
/// </summary>
[SplitTable(SplitType._Custom01, typeof(CustomSplitService))]
[SugarTable("student_invoice_blue_00000000_0000_0000_0000_000000000000")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class JxInvoiceBlueEntity : DataEntityBase
{
    /// <summary>
    ///     系统的数字编号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SysId", ColumnDescription = "系统的数字编号", IsDisabledAlterColumn = true, IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true, ColumnDataType = " BIGINT IDENTITY(1,1) ")]
    public long SysId { get; set; }

    /// <summary>
    ///     所在的租户的 Id
    /// </summary>
    [SugarColumn(ColumnName = "F_TenantId", ColumnDescription = "所在的租户的 Id", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    [SplitField]
    public new Guid TenantId { get; set; }

    /// <summary>
    ///     关联的授权实体ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AuthId", ColumnDescription = "关联的授权实体ID")]
    public Guid AuthId { get; set; }

    /// <summary>
    ///     关联的订单ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OrderId", ColumnDescription = "关联的订单ID", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid OrderId { get; set; }

    /// <summary>
    ///     关联的学员ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudentId", ColumnDescription = "关联的学员ID", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid StudentId { get; set; }

    /// <summary>
    ///     发票代码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceCode", ColumnDescription = "发票代码", Length = 50, DefaultValue = "")]
    public string InvoiceCode { get; set; }

    /// <summary>
    ///     发票号码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceNumber", ColumnDescription = "发票号码", Length = 50, DefaultValue = "")]
    public string InvoiceNumber { get; set; }

    /// <summary>
    ///     购买方名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BuyerName", ColumnDescription = "购买方名称", Length = 200, DefaultValue = "")]
    public string BuyerName { get; set; }

    /// <summary>
    ///     购买方纳税人识别号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BuyerTaxNo", ColumnDescription = "购买方纳税人识别号", Length = 100, DefaultValue = "")]
    public string BuyerTaxNo { get; set; }

    /// <summary>
    ///     购买方地址电话
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BuyerAddressPhone", ColumnDescription = "购买方地址电话", Length = 500, DefaultValue = "")]
    public string BuyerAddressPhone { get; set; }

    /// <summary>
    ///     购买方开户行及账号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_BuyerBankAccount", ColumnDescription = "购买方开户行及账号", Length = 500, DefaultValue = "")]
    public string BuyerBankAccount { get; set; }

    /// <summary>
    ///     发票金额(不含税)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceAmount", ColumnDescription = "发票金额(不含税)", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal InvoiceAmount { get; set; }

    /// <summary>
    ///     税额
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TaxAmount", ColumnDescription = "税额", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal TaxAmount { get; set; }

    /// <summary>
    ///     价税合计
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TotalAmount", ColumnDescription = "价税合计", DecimalDigits = 4, Length = 18, DefaultValue = "0")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    ///     开票日期
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceDate", ColumnDescription = "开票日期", DefaultValue = "2000-01-01")]
    public DateTime InvoiceDate { get; set; }

    /// <summary>
    ///     发票状态 (0-待开票, 1-开票中, 2-开票成功, 3-开票失败, 4-已作废)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_InvoiceStatus", ColumnDescription = "发票状态", DefaultValue = "0")]
    public int InvoiceStatus { get; set; }

    /// <summary>
    ///     开票请求JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_RequestJson", ColumnDescription = "开票请求JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string RequestJson { get; set; }

    /// <summary>
    ///     开票响应JSON
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_ResponseJson", ColumnDescription = "开票响应JSON", ColumnDataType = "TEXT", DefaultValue = "")]
    public string ResponseJson { get; set; }

    /// <summary>
    ///     发票PDF文件路径
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_PdfFilePath", ColumnDescription = "发票PDF文件路径", Length = 500, DefaultValue = "")]
    public string PdfFilePath { get; set; }

    /// <summary>
    ///     失败原因
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_FailReason", ColumnDescription = "失败原因", Length = 1000, DefaultValue = "")]
    public string FailReason { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Remark", ColumnDescription = "备注", Length = 500, DefaultValue = "")]
    public string Remark { get; set; }

    /// <summary>
    ///     初始化
    /// </summary>
    public new void Create()
    {
        Id = Guid.NewGuid();
        if (UserManager.UserId != Guid.Empty)
            CreateUserId = UserManager.UserId;
        CreateTime = DateTime.Now;
    }
}