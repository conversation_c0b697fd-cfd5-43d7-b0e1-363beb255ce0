﻿namespace PandaServer.System.Entity;

[SugarTable("wx_LongTicket")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
public class WxLongTicketEntity
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [SugarColumn(ColumnName = "F_Id", IsPrimaryKey = true, ColumnDescription = "主键Id")]
    public Guid Id { get; set; }

    /// <summary>
    ///     数据创建的时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CreatorTime", ColumnDescription = "数据创建的时间", DefaultValue = "1900-01-01")]
    public DateTime CreateTime { get; set; }


    /// <summary>
    ///     AppId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AppId", ColumnDescription = "相关联的微信公众号或者小程序的 AppId", Length = 18)]
    public string AppId { get; set; }


    /// <summary>
    ///     Ticket
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Ticket", ColumnDescription = "Ticket", Length = 500)]
    public string Ticket { get; set; }


    /// <summary>
    ///     Url
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_Url", ColumnDescription = "Url", Length = 500, DefaultValue = "''")]
    public string Url { get; set; }


    /// <summary>
    ///     外部关联的表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OutTable", ColumnDescription = "外部关联的表")]
    public OuTableEnum OutTable { get; set; }


    /// <summary>
    ///     外部关联的主键
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_OutId", ColumnDescription = "外部关联的主键")]
    public string OutId { get; set; }


    /// <summary>
    ///     场景值ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SceneId", ColumnDescription = "场景值ID，临时二维码时为32位非0整型，永久二维码时最大值为100000（目前参数只支持1--100000）", Length = 5000)]
    public int SceneId { get; set; }


    /// <summary>
    ///     场景值ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SceneStr", ColumnDescription = "场景值ID（字符串形式的ID），字符串类型，长度限制为1到64", Length = 64)]
    public string SceneStr { get; set; }
}


public enum OuTableEnum
{

    [Description("空白")]
    White = 1000,

    [Description("考场买票")]
    FieldItem = 0,

    [Description("公司买票")]
    TenantItem = 1,

    [Description("需要审核的注册")]
    NeedAuditRegister = 2,

    [Description("无需审核的注册")]
    NoAuditRegister = 3,

    [Description("老系统注册账户")]
    OldSystemCreateUser = 43,
}