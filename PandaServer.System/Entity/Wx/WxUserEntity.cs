﻿namespace PandaServer.System.Entity;

/// <summary>
///     微信用户表
/// </summary>
// [SplitTable(SplitType.Year, typeof(DateSplitTableService))]
[SugarTable("wx_user")]
[Tenant(SqlsugarConst.DB_Default)]
[CodeGen]
// [SugarIndex("index1", nameof(UnionId), OrderByType.Asc, nameof(OpenId), OrderByType.Asc, nameof(UserId), OrderByType.Asc)]
// [SugarIndex("index2", nameof(UnionId), OrderByType.Asc, nameof(OpenId), OrderByType.Asc, nameof(StudentId), OrderByType.Asc)]
public class WxUserEntity
{
    /// <summary>
    ///     OpenId
    /// </summary>
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, ColumnName = "F_OpenId", ColumnDescription = "微信用户的 OpenId", Length = 28)]
    public string OpenId { get; set; }


    /// <summary>
    ///     UnionId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UnionId", ColumnDescription = "微信用户的 UnionId", Length = 29)]
    public string UnionId { get; set; } = "";

    /// <summary>
    ///     AppId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_AppId", ColumnDescription = "相关联的微信公众号或者小程序的 AppId", Length = 18)]
    public string AppId { get; set; }

    // /// <summary>
    // ///     昵称
    // /// </summary>
    // [SugarColumn(IsNullable = false, ColumnName = "F_NickName", ColumnDescription = "用户微信 昵称")]
    // public string NickName { get; set; }

    // /// <summary>
    // ///     头像
    // /// </summary>
    // [SugarColumn(IsNullable = false, ColumnName = "F_AvatarUrl", ColumnDescription = "用户微信 头像", Length = 500)]
    // public string AvatarUrl { get; set; }

    // /// <summary>
    // ///     电话
    // /// </summary>
    // [SugarColumn(IsNullable = false, ColumnName = "F_Phone", ColumnDescription = "用户微信关联的 手机号码")]
    // public string Phone { get; set; } = "";

    // /// <summary>
    // ///     用户国家
    // /// </summary>
    // [SugarColumn(IsNullable = false, ColumnName = "F_Country", ColumnDescription = "用户国家")]
    // public string Country { get; set; } = "";

    // /// <summary>
    // ///     用户省份
    // /// </summary>
    // [SugarColumn(IsNullable = false, ColumnName = "F_Province", ColumnDescription = "用户省份")]
    // public string Province { get; set; } = "";

    // /// <summary>
    // ///     用户城市
    // /// </summary>
    // [SugarColumn(IsNullable = false, ColumnName = "F_City", ColumnDescription = "用户城市")]
    // public string City { get; set; } = "";

    // /// <summary>
    // ///     用户微信性别
    // /// </summary>
    // [SugarColumn(IsNullable = false, ColumnName = "F_Gender", ColumnDescription = "用户微信性别")]
    // public int Gender { get; set; } = 0;

    /// <summary>
    ///     信息最后一次更新时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_LastUpdateTime", ColumnDescription = "信息最后一次更新时间", DefaultValue = "1900-01-01")]
    public DateTime LastUpdateTime { get; set; }


    /// <summary>
    ///     当前登录的用户 UserId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UserId", ColumnDescription = "当前登录的用户 UserId", DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid UserId { get; set; }


    /// <summary>
    ///     当前登录的用户 所属的公司
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_TenantId", ColumnDescription = "当前登录的用户 所属的公司",
        DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid TenantId { get; set; }


    /// <summary>
    ///     当前登录的学员 StudentId
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudentId", ColumnDescription = "当前登录的学员 StudentId",
        DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid StudentId { get; set; }

    /// <summary>
    ///     当前登录的学员 所属的公司
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_StudentTenantId", ColumnDescription = "当前登录的学员 所属的公司",
        DefaultValue = "00000000-0000-0000-0000-000000000000")]
    public Guid StudentTenantId { get; set; }


    /// <summary>
    ///     用户登录的时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UserLoginTime", ColumnDescription = "用户登录的时间",
        DefaultValue = "1900-01-01")]
    public DateTime UserLoginTime { get; set; }


    /// <summary>
    ///     数据创建的时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_CreatorTime", ColumnDescription = "数据创建的时间",
        DefaultValue = "1900-01-01")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    ///     是否关注
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_IsSubscribe", ColumnDescription = "是否关注", DefaultValue = "1")]
    public bool IsSubscribe { get; set; } = false;

    /// <summary>
    ///     关注时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_SubscribeTime", ColumnDescription = "关注时间", DefaultValue = "1900-01-01")]
    public DateTime SubscribeTime { get; set; } = new DateTime(1900, 1, 1);

    /// <summary>
    ///     取消关注时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "F_UnsubscribeTime", ColumnDescription = "取消关注时间", DefaultValue = "1900-01-01")]
    public DateTime UnsubscribeTime { get; set; } = new DateTime(1900, 1, 1);
}