using PandaServer.System.Pay.JlPay.Models;
using Newtonsoft.Json;

namespace PandaServer.System.Pay.JlPay;

/// <summary>
/// 嘉联支付收银托管服务
/// </summary>
public partial class JlPayService : JlPayServiceBase, IJlPayService, ITransient
{
    #region 收银托管
    /// <summary>
    /// 预下单
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="order">订单信息</param>
    /// <param name="notifyUrl">异步通知地址，默认为空</param>
    /// <param name="returnUrl">同步跳转地址，默认为空</param>
    /// <returns>预下单响应</returns>
    public async Task<PreOrderResponse> PreOrder(
        PayAccountEntity account,
        OrderEntity order,
        string notifyUrl = "",
        string returnUrl = "")
    {
        try
        {
            string url = "https://openapi.jlpay.com/open/cashier/trans/trade/pre-order";
            var requestBody = BuildPreOrderRequestBody(account, order, notifyUrl, returnUrl);
            var headers = await PrepareRequestHeaders(url, requestBody);
            var response = await SendRequest(url, headers, requestBody);

            // Console.WriteLine("requestData:" + JsonConvert.SerializeObject(requestBody));
            // Console.WriteLine("================================================");
            // Console.WriteLine("requestData:" + JsonConvert.SerializeObject(response));
            // Console.WriteLine("================================================");


            return HandlePreOrderResponse(response);
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"预下单失败: {ex.Message}");
        }
    }

    private string BuildPreOrderRequestBody(
        PayAccountEntity account,
        OrderEntity order,
        string notifyUrl,
        string returnUrl)
    {
        var requestData = new PreOrderRequest
        {
            merch_no = account.MerchantId,
            term_no = account.DeviceId,
            out_trade_no = order.MerchantOrderSn,
            description = order.Desc,
            attach = order.Desc,
            product_name = order.Desc,
            total_amount = (order.PayMoney * 100).ParseToInt().ToString(),
            merch_appid = HttpNewUtil.GetHeader("appId"),
            env_version = "develop",//release/trial/develop
            notify_url = notifyUrl,
            return_url = returnUrl
        };

        return JsonConvert.SerializeObject(requestData);
    }

    private PreOrderResponse HandlePreOrderResponse(string responseData)
    {
        return HandleBaseResponse<PreOrderResponse>(responseData);
    }
    #endregion
}