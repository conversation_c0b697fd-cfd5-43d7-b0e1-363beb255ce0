using PandaServer.System.Pay.JlPay.Models;
using Newtonsoft.Json;
using PandaServer.System.Pay.JlPay.Utils;

namespace PandaServer.System.Pay.JlPay;

/// <summary>
/// 嘉联支付扫码支付服务
/// </summary>
public partial class JlPayService : JlPayServiceBase, IJlPayService, ITransient
{
    #region 扫码支付
    public async Task<MicroPayResponse> PayByBarCode(PayAccountEntity account, OrderEntity order, string barCode)
    {
        string org_code = await GetConfig(DevConfigConst.SYS_JLPAY_ORG_CODE);
        string url = "https://qrcode.jlpay.com/api/pay/micropay";

        var request = BuildMicroPayRequest(account, order, barCode, org_code);
        var requestData = JsonConvert.SerializeObject(request);
        string requestParamStr = await BuildRequestParamsAsync(requestData);
        string responseData = await HttpHelper.SendRequestAsync(requestParamStr, url);

        if (!string.IsNullOrEmpty(responseData))
        {
            var microPayResponse = JsonConvert.DeserializeObject<MicroPayResponse>(responseData);

            Console.WriteLine("microPayResponse ==== >>>" + JsonConvert.SerializeObject(microPayResponse));
            return await ValidateMicroPayResponseAsync(microPayResponse, order);
        }
        return null;
    }

    private MicroPayRequest BuildMicroPayRequest(PayAccountEntity account, OrderEntity order, string barCode, string orgCode)
    {
        return new MicroPayRequest
        {
            attach = order.Desc,
            body = order.Desc,
            device_info = account.DeviceId,
            latitude = "28.228209",
            longitude = "112.938814",
            mch_create_ip = "************",
            mch_id = account.MerchantId,
            nonce_str = CreateNonce(32),
            notify_url = "",
            org_code = orgCode,
            out_trade_no = order.MerchantOrderSn,
            pay_type = "alipay",
            payment_valid_time = "20",
            remark = order.Desc,
            term_no = account.DeviceId,
            total_fee = (order.PayMoney * 100).ParseToInt().ToString(),
            sign_type = "RSA256",
            auth_code = barCode
        };
    }

    private async Task<MicroPayResponse> ValidateMicroPayResponseAsync(MicroPayResponse response, OrderEntity order)
    {
        if (string.IsNullOrEmpty(response.ret_code) || response.ret_code != "00" || string.IsNullOrEmpty(response.sign))
        {
            throw Oops.Bah(response.ret_msg);
        }
        if (string.IsNullOrEmpty(order.OrderSn) && !string.IsNullOrEmpty(response.transaction_id))
        {
            order.OrderSn = response.transaction_id;
            await DbContext.Db.Updateable(order).SplitTable().ExecuteCommandAsync();

            var tenantOrder = order.Adapt<OrderTenantEntity>();
            await DbContext.Db.Updateable(tenantOrder).SplitTable().ExecuteCommandAsync();
        }
        return response;
    }
    #endregion
}