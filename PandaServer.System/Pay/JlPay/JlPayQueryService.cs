using System;
using Newtonsoft.Json;
using PandaServer.System.Pay.JlPay.Models;
using Polly;

namespace PandaServer.System.Pay.JlPay;

public partial class JlPayService : JlPayServiceBase, IJlPayService, ITransient
{
    /// <inheritdoc />
    public async Task<OrderQueryResponse> QueryOrder(string outTradeNo)
    {
        var orderTable = "pay_order_" + DateTime.Now.ToString("yyyy0101");
        var order = await DbContext.Db.Queryable<OrderEntity>().AS(orderTable).Where(m => m.MerchantOrderSn == outTradeNo).SingleAsync();

        var account = await DbContext.Db.Queryable<PayAccountEntity>().Where(m => m.Id == order.AccountId).SingleAsync();

        return await QueryOrder(account, outTradeNo);
    }

    /// <inheritdoc />
    public async Task<OrderQueryResponse> QueryOrder(PayAccountEntity account, string outTradeNo = null, string transactionId = null, string sourceChannel = null)
    {
        if (string.IsNullOrEmpty(outTradeNo) && string.IsNullOrEmpty(transactionId))
        {
            throw Oops.Bah("商户订单号和平台订单号不能同时为空");
        }

        // Console.WriteLine("sourceChannel:" + sourceChannel);
        // Console.WriteLine("transactionId:" + transactionId);
        // Console.WriteLine("outTradeNo:" + outTradeNo);

        string url = "https://openapi.jlpay.com/open/trans/chnquery";
        if (sourceChannel == "wxapp" && string.IsNullOrEmpty(transactionId))
        {
            url = "https://openapi.jlpay.com/open/cashier/trans/trade/query";
        }

        // Console.WriteLine("url:" + url);

        var requestBody = BuildOrderQueryRequestBody(account, outTradeNo, transactionId);

        var headers = await PrepareRequestHeaders(url, requestBody);

        var response = await SendRequest(url, headers, requestBody);

        Console.WriteLine("response:" + response);

        var result = HandleOrderQueryResponse(response);

        if (result.status == "2")
        {
            if (string.IsNullOrEmpty(transactionId))
            {
                return await QueryOrder(account, outTradeNo, result.transaction_id, sourceChannel);
            }
            else
            {
                return result;
            }
        }
        else
        {
            return result;
        }
    }

    private string BuildOrderQueryRequestBody(PayAccountEntity account, string outTradeNo, string transactionId)
    {
        var request = new OrderQueryRequest
        {
            merch_no = account.MerchantId,
            mch_id = account.MerchantId,
            out_trade_no = outTradeNo,
            transaction_id = transactionId
        };

        return JsonConvert.SerializeObject(request);
    }

    private OrderQueryResponse HandleOrderQueryResponse(string responseData)
    {
        return HandleBaseResponse<OrderQueryResponse>(responseData);
    }

    /// <summary>
    /// 轮询查询订单状态
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="outTradeNo">商户订单号</param>
    /// <param name="maxAttempts">最大尝试次数</param>
    /// <param name="interval">查询间隔(毫秒)</param>
    /// <returns>订单查询响应</returns>
    public async Task<OrderQueryResponse> PollOrderStatus(
        PayAccountEntity account,
        string outTradeNo,
        int maxAttempts = 4,
        int interval = 5000)
    {
        OrderQueryResponse? response = null;
        var attempts = 0;

        while (attempts < maxAttempts)
        {
            attempts++;
            response = await QueryOrder(account, outTradeNo);

            if (response?.status == "2")
            {
                return response;
            }
            else if (response?.status is "3" or "4")
            {
                throw Oops.Bah($"支付失败: {response.ret_msg}");
            }

            if (attempts < maxAttempts)
            {
                await Task.Delay(interval);
            }
        }

        throw Oops.Bah("订单查询超时，请稍后重试");
    }
}