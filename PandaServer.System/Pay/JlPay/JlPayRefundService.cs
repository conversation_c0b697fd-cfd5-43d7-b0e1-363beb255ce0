using System;
using System.Threading.Tasks;
using PandaServer.System.Entity.JiaXiaoOA;
using PandaServer.System.Pay.JlPay.Models;
using Newtonsoft.Json;

namespace PandaServer.System.Pay.JlPay
{
    public partial class JlPayService : JlPayServiceBase, IJlPayService, ITransient
    {

        /// <summary>
        /// 执行退款操作
        /// </summary>
        /// <param name="account">支付账户信息</param>
        /// <param name="order">订单信息</param>
        /// <param name="refund">退款信息</param>
        /// <param name="notifyUrl">退款通知地址</param>
        /// <returns>退款响应结果</returns>
        public async Task<RefundResponse> Refund(PayAccountEntity account, OrderEntity order, RefundEntity refund, string notifyUrl)
        {
            try
            {
                if (order.SourceChannel == "wxapp")
                {
                    string url = "https://openapi.jlpay.com/open/cashier/trans/trade/refund";
                    var requestBody = BuildRefundRequestBody(account, order, refund);

                    var headers = await PrepareRequestHeaders(url, requestBody);

                    var response = await SendRequest(url, headers, requestBody);
                    var result = HandleRefundResponse(response);

                    if (result.ret_code == "00000")
                    {
                        return result;
                    }
                    else
                    {
                        throw Oops.Bah(result.ret_msg);
                    }
                }
                if (order.SourceChannel == "web")
                {
                    // web端退款逻辑，如果需要的话可以在这里实现
                    throw Oops.Bah("暂不支持web端退款");
                }
                else
                {
                    throw Oops.Bah("不支持的订单来源");
                }
            }
            catch (Exception ex)
            {
                throw Oops.Bah($"退款失败: {ex.Message}");
            }
        }

        private string BuildRefundRequestBody(PayAccountEntity account, OrderEntity order, RefundEntity refund)
        {
            Console.WriteLine(JsonConvert.SerializeObject(order));

            var requestData = new Dictionary<string, object>
            {
                ["merch_no"] = account.MerchantId,                // 嘉联分配的商户号
                ["out_trade_no"] = refund.MerchantOrderSn,        // 退款订单号
                ["ori_out_trade_no"] = order.MerchantOrderSn,             // 原支付订单号
                ["total_amount"] = Convert.ToInt32(refund.RefundMoney * 100),            // 退款金额(分)
                ["remark"] = "退款"                                // 备注信息
            };

            return JsonConvert.SerializeObject(requestData);
        }

        private RefundResponse HandleRefundResponse(string responseData)
        {
            return HandleBaseResponse<RefundResponse>(responseData);
        }
    }
}