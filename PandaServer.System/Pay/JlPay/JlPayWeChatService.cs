using PandaServer.System.Pay.JlPay.Models;
using Newtonsoft.Json;

namespace PandaServer.System.Pay.JlPay;

/// <summary>
/// 嘉联支付微信支付服务
/// </summary>
public partial class JlPayService : JlPayServiceBase, IJlPayService, ITransient
{
    #region 微信支付
    public async Task<AuthBindResponse> AuthBind(PayAccountEntity account)
    {
        string url = "https://openapi.jlpay.com/open/trans/authbind";
        var requestBody = BuildAuthBindRequestBody(account);
        var headers = await PrepareRequestHeaders(url, requestBody);
        var response = await SendRequest(url, headers, requestBody);

        Console.WriteLine(response);
        var result = HandleAuthBindResponse(response);


        if (result.ret_code == "00")
        {
            return result;
        }
        else
        {
            throw Oops.Bah(result.ret_msg);
        }
    }

    public async Task<OfficialPayResponse> OfficialPay(PayAccountEntity account, OrderEntity order, string notifyUrl)
    {
        try
        {
            string url = "https://openapi.jlpay.com/open/trans/officialpay";
            var requestBody = BuildOfficialPayRequestBody(account, order, notifyUrl);
            var headers = await PrepareRequestHeaders(url, requestBody);
            var response = await SendRequest(url, headers, requestBody);
            return HandleOfficialPayResponse(response);
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"支付失败: {ex.Message}");
        }
    }

    private string BuildAuthBindRequestBody(PayAccountEntity account)
    {
        var requestData = new OfficialPayRequest
        {
            mch_id = account.MerchantId,
            pay_type = "wxpay",
            sub_appid = "wxf417317222ee37a4", //嘉联收银托管
            mch_create_ip = "*************"
        };
        return JsonConvert.SerializeObject(requestData);
    }

    private string BuildOfficialPayRequestBody(PayAccountEntity account, OrderEntity order, string notifyUrl)
    {
        var requestData = new OfficialPayRequest
        {
            mch_id = account.MerchantId,
            term_no = account.DeviceId,
            pay_type = "wxpay",
            open_id = HttpNewUtil.GetHeader("openId"),
            out_trade_no = $"ORDER{DateTime.Now:yyyyMMddHHmmssfff}",
            body = order.Desc,
            attach = order.Desc,
            total_fee = (order.PayMoney * 100).ParseToInt().ToString(),
            payment_valid_time = "20",
            sub_appid = HttpNewUtil.GetHeader("appId"),
            notify_url = notifyUrl,
            remark = "该订单单流通知走此卡号",
            latitude = "28.22778",
            longitude = "112.93886"
        };
        return JsonConvert.SerializeObject(requestData);
    }

    private AuthBindResponse HandleAuthBindResponse(string responseData)
    {
        return HandleBaseResponse<AuthBindResponse>(responseData);
    }

    private OfficialPayResponse HandleOfficialPayResponse(string responseData)
    {
        return HandleBaseResponse<OfficialPayResponse>(responseData);
    }
    #endregion
}