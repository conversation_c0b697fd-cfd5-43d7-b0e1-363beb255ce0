using System;
using PandaServer.System.Entity.JiaXiaoOA;
using PandaServer.System.Pay.JlPay.Models;

namespace PandaServer.System.Pay.JlPay;

public interface IJlPayService
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="payAccount"></param>
    /// <returns></returns>
    Task<bool> AddDevice(PayAccountEntity payAccount);

    /// <summary>
    /// 微信公众号、小程序通过该接口下预支付订单。
    /// </summary>
    /// <param name="account"></param>
    /// <param name="order"></param>
    /// <param name="notifyUrl"></param>
    /// <returns></returns>
    Task<OfficialPayResponse> OfficialPay(PayAccountEntity account, OrderEntity order, string notifyUrl);

    /// <summary>
    ///     付款码 支付
    /// </summary>
    /// <param name="account"></param>
    /// <param name="order"></param>
    /// <param name="barCode"></param>
    /// <returns></returns>
    Task<MicroPayResponse> PayByBarCode(PayAccountEntity account, OrderEntity order, string barCode);

    /// <summary>
    /// 收银托管 预下单 用于小程序的 支付
    /// </summary>
    /// <param name="account"></param>
    /// <param name="order"></param>
    /// <param name="notifyUrl"></param>
    /// <param name="returnUrl"></param>
    /// <returns></returns>
    Task<PreOrderResponse> PreOrder(PayAccountEntity account, OrderEntity order, string notifyUrl = "", string returnUrl = "");


    /// <summary>
    /// 微信 绑定支付目录
    /// </summary>
    /// <param name="account"></param>
    /// <returns></returns>
    Task<AuthBindResponse> AuthBind(PayAccountEntity account);


    /// <summary>
    /// 查询订单状态
    /// </summary>
    /// <param name="outTradeNo">商户订单号</param>
    /// <returns>订单查询响应</returns>
    Task<OrderQueryResponse> QueryOrder(string outTradeNo);

    /// <summary>
    /// 查询订单状态
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="outTradeNo">商户订单号</param>
    /// <param name="transactionId">平台订单号（与商户订单号二选一）</param>
    /// <param name="sourceChannel"></param>
    /// <returns>订单查询响应</returns>
    Task<OrderQueryResponse> QueryOrder(PayAccountEntity account, string outTradeNo = null, string transactionId = null, string sourceChannel = null);

    /// <summary>
    /// 订单退款
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="order">订单信息</param>
    /// <param name="refundEntity">退款信息</param>
    /// <param name="notifyUrl">退款结果通知地址</param>
    /// <returns>退款响应结果</returns>
    Task<RefundResponse> Refund(PayAccountEntity account, OrderEntity order, RefundEntity refundEntity, string notifyUrl = "");

    /// <summary>
    /// 下载账单
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="billType">账单类型</param>
    /// <param name="billDate">账单日期</param>
    /// <returns>账单下载结果</returns>
    Task<DownloadBillResponse> DownloadBill(PayAccountEntity account, string billType, string billDate);
}
