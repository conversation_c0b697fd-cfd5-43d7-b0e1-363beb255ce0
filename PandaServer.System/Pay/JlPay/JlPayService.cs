using PandaServer.System.Pay.JlPay.Models;
using Newtonsoft.Json;
using PandaServer.System.Services.Config;
using PandaServer.System.Services.Pay.Base;

namespace PandaServer.System.Pay.JlPay;

/// <summary>
/// 嘉联支付设备管理服务
/// </summary>
public partial class JlPayService : JlPayServiceBase, IJlPayService, ITransient
{
    private readonly IPayAccountService _payAccountService;

    public JlPayService(IConfigService configService, IPayAccountService payAccountService)
        : base(configService)  // 正确传递参数给基类构造函数
    {
        _payAccountService = payAccountService;
    }


    #region 设备管理
    public async Task<bool> AddDevice(PayAccountEntity account)
    {
        ValidatePayAccount(account);

        var url = "https://openapi.jlpay.com/open/merch/access/device/add";
        var requestBody = BuildAddDeviceRequestBody(account.MerchantId);
        var headers = await PrepareRequestHeaders(url, requestBody);
        var response = await SendRequest(url, headers, requestBody);


        Console.WriteLine(response);

        return await HandleAddDeviceResponse(account, response);
    }

    private void ValidatePayAccount(PayAccountEntity payAccount)
    {
        if (string.IsNullOrEmpty(payAccount.MerchantId))
        {
            throw Oops.Bah("请先填写商户编号");
        }
        if (!string.IsNullOrEmpty(payAccount.DeviceId))
        {
            throw Oops.Bah("请勿重复获取设备编号");
        }
    }

    private string BuildAddDeviceRequestBody(string merchantId)
    {
        var requestData = new { merch_no = merchantId, device_type = "qrcode" };
        return JsonConvert.SerializeObject(requestData);
    }

    private async Task<bool> HandleAddDeviceResponse(PayAccountEntity payAccount, string responseData)
    {
        var response = HandleBaseResponse<DeviceAddResponse>(responseData);

        if (response.ret_code != "00000" || string.IsNullOrEmpty(response.term_no))
        {
            throw Oops.Bah($"添加设备失败: {response.ret_msg}");
        }

        payAccount.DeviceId = response.term_no;
        await _payAccountService.Update(payAccount);
        return true;
    }
    #endregion

    public async Task<DownloadBillResponse> DownloadBill(PayAccountEntity account, string billType, string billDate)
    {
        if (account == null)
            throw Oops.Bah("未找到支付账户");
        if (string.IsNullOrEmpty(billType) || string.IsNullOrEmpty(billDate))
            throw Oops.Bah("账单类型和账单日期不能为空");

        var url = "https://openapi.jlpay.com/open/bill/download/query";
        var requestBody = new
        {
            bill_type = billType,
            dt = billDate
        };
        var requestJson = JsonConvert.SerializeObject(requestBody);
        var headers = await PrepareRequestHeaders(url, requestJson);
        var responseStr = await SendRequest(url, headers, requestJson);

        Console.WriteLine(responseStr);

        var responseObj = JsonConvert.DeserializeObject<DownloadBillResponse>(responseStr);
        return responseObj;
    }
}