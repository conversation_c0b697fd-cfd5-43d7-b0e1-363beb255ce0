using Newtonsoft.Json;
using PandaServer.System.Pay.FuBei.Models.Parameter;
using PandaServer.System.Entity.Student.Pay;
using PandaServer.System.Services.Student.Pay;
using PandaServer.System.Services.Student.Dto.Pay;
using System.Text;
using System.Security.Cryptography;
using PandaServer.System.Pay.FuBei.Models;

namespace PandaServer.System.Pay.FuBei;

/// <summary>
///     <inheritdoc cref="IJxCostTypeCallBackService" />
/// </summary>
public class JxCostTypeCallBackService : IJxCostTypeCallBackService, ITransient
{
    private readonly ISimpleCacheService _simpleCacheService;
    private readonly IJxStudentPayService _jxStudentPayService;

    public JxCostTypeCallBackService(ISimpleCacheService simpleCacheService, IJxStudentPayService jxStudentPayService)
    {
        _simpleCacheService = simpleCacheService;
        _jxStudentPayService = jxStudentPayService;
    }

    /// <inheritdoc />
    public async Task<List<JxPayEntity>> CallBack(FubeiNotificationParam p, string sign)
    {
        if (p == null || string.IsNullOrEmpty(p.Data))
            throw Oops.Bah("传入参数为空");

        var data = JsonConvert.DeserializeObject<FuBeiOrderResult>(p.Data);
        if (data == null)
            throw Oops.Bah("解析数据失败");

        var order = await DbContext.Db.Queryable<OrderEntity>().SplitTable(st => st.Take(2)).Where(m => m.MerchantOrderSn == data.MerchantOrderSn).SingleAsync();
        if (order == null)
            throw Oops.Bah("未找到对应订单");

        var account = await DbContext.Db.Queryable<PayAccountEntity>().Where(m => m.Id == order.AccountId).SingleAsync();
        if (account == null)
            throw Oops.Bah("未找到支付账户");

        // 构建签名验证
        var signStr = new StringBuilder();
        signStr.Append($"data={p.Data}&");
        signStr.Append($"result_code={p.ResultCode}&");
        signStr.Append($"result_message={p.ResultMessage}");
        signStr.Append(account.AppSecret); // 添加密钥

        string calculatedSign = MD5(signStr.ToString()).ToUpper();
        bool valid = calculatedSign.Equals(sign, StringComparison.OrdinalIgnoreCase);

        if (!valid)
            throw Oops.Bah("签名验证失败");

        var startTime = DateTime.Now;

        if (!data.OrderStatus.Equals("SUCCESS", StringComparison.OrdinalIgnoreCase))
            throw Oops.Bah($"订单状态异常: {data.OrderStatus}");

        var jxPays = await UpdateOrder(order, account, data.FinishTime.ParseToDateTime(),
                data.TotalAmount.ParseToDecimal(), data.PayType,
                data.MerchantOrderSn, data.OrderSn, data.ChannelOrderSn, data.InsOrderSn);

        if (jxPays != null)
        {
            order.PayTime = data.FinishTime.ParseToDateTime();
            order.ActPayMoney = data.TotalAmount.ParseToDecimal();
            order.MerchantOrderSn = data.MerchantOrderSn;
            order.OrderSn = data.OrderSn;
            order.ChannelOrderSn = data.ChannelOrderSn;
            order.InsOrderSn = data.InsOrderSn;

            await _simpleCacheService.Set($"{CacheConst.Cache_PayOrder_Single}:{order.Id}", order);

            return jxPays;
        }

        return null;
    }

    private static string MD5(string input)
    {
        using var md5 = global::System.Security.Cryptography.MD5.Create();
        var bytes = Encoding.UTF8.GetBytes(input);
        var hash = md5.ComputeHash(bytes);
        return BitConverter.ToString(hash).Replace("-", "").ToLower();
    }

    /// <inheritdoc />
    public async Task<List<JxPayEntity>> UpdateOrder(OrderEntity order, PayAccountEntity account, DateTime payTime,
        decimal actPayMoney, string payChannel,
        string merchantOrderSn = "", string orderSn = "", string channelOrderSn = "", string insOrderSn = "")
    {

        var payTypeId = Guid.Empty;
        string payTypeName = "";
        switch (account.PayMethod)
        {
            case PayMethodEnum.FuBei:
                payTypeName = "付呗支付";
                break;
            default:
                throw new Exception("不支持的支付方式");
        }

        var payTypes = await DbContext.Db.Queryable<PayTypeEntity>().Where(m => m.Name == payTypeName && m.IsDelete == false && m.TenantId == order.TenantId).ToListAsync();

        if (payTypes.Count > 0)
        {
            payTypeId = payTypes[0].Id;
        }
        else
        {
            var payType = new PayTypeEntity()
            {
                Id = Guid.NewGuid(),
                Name = payTypeName,
                SortCode = 1,
                TenantId = order.TenantId,
                CreateTime = DateTime.Now,
                CreateUserId = order.CreateUserId,
                UpdateTime = DateTime.Now,
                UpdateUserId = order.UpdateUserId,
                IsDelete = false,
                Remark = ""
            };

            await DbContext.Db.Insertable(payType).ExecuteCommandAsync();

            payTypeId = payType.Id;
        }

        var details = await DbContext.Db.Queryable<OrderDetailEntity>().SplitTable(order.CreateTime.AddDays(-1), DateTime.Now)
            .Where(m => m.OrderId == order.Id)
            .ToListAsync();

        Console.WriteLine($"总共 有 {details.Count} 条数据  {order.CreateTime.AddDays(-1)} {DateTime.Now}");
        Console.WriteLine($"order.Id : {order.Id}");

        var jxPays = new List<JxPayEntity>();

        foreach (var item in details)
            switch (item.OutTable)
            {
                case PayOutTableEnum.JxCostType:
                    {
                        var pay = await JxCostTypeCompleteCallBack(order, item, payTime, payTypeId, account.Id, merchantOrderSn, orderSn, channelOrderSn, insOrderSn);
                        if (pay != null && pay.Count > 0)
                        {
                            jxPays.AddRange(pay);
                        }
                    }
                    break;
                default:
                    throw Oops.Bah("回调页面错误");
            }


        order.ActPayMoney = actPayMoney;
        order.PayTime = payTime;

        order.OrderStatus = OrderStatusEnum.Success;

        if (payChannel.Equals("wxPay", StringComparison.OrdinalIgnoreCase))
            order.PayChannel = PayChannelEnum.WxPay;
        if (payChannel.Equals("aliPay", StringComparison.OrdinalIgnoreCase))
            order.PayChannel = PayChannelEnum.AliPay;
        if (payChannel.Equals("unionPay", StringComparison.OrdinalIgnoreCase))
            order.PayChannel = PayChannelEnum.UnionPay;

        if (!string.IsNullOrEmpty(merchantOrderSn))
            order.MerchantOrderSn = merchantOrderSn;
        if (!string.IsNullOrEmpty(orderSn))
            order.OrderSn = orderSn;
        if (!string.IsNullOrEmpty(channelOrderSn))
            order.ChannelOrderSn = channelOrderSn;
        if (!string.IsNullOrEmpty(insOrderSn))
            order.InsOrderSn = insOrderSn;

        await DbContext.Db.Updateable(order).SplitTable().ExecuteCommandAsync();
        var orderTenant = order.Adapt<OrderTenantEntity>();
        await DbContext.Db.Updateable(orderTenant).SplitTable().ExecuteCommandAsync();


        return jxPays;
    }


    /// <inheritdoc />
    public async Task<List<JxPayEntity>> JxCostTypeCompleteCallBack(OrderEntity order, OrderDetailEntity detail, DateTime payTime, Guid payTypeId, Guid accountId, string merchantOrderSn = "", string orderSn = "", string channelOrderSn = "", string insOrderSn = "")
    {
        var payTable = "student_pay_" + order.TenantId.ToString().Replace("-", "_").ToLower();
        if (await DbContext.Db.Queryable<JxPayEntity>().AS(payTable).Where(m => m.Id == detail.Id && m.TenantId == order.TenantId && m.IsDelete == false).CountAsync() > 0)
        {
            Console.WriteLine("JxCostTypeCompleteCallBack 有重复记录 不执行");
            return null;
        }

        var pay = new JxPayInPut();
        pay.Id = detail.Id;
        pay.TenantId = order.TenantId;
        pay.CreateTime = DateTime.Now;

        pay.CostTypeId = detail.OutId;

        pay.PayMoney = detail.PayMoney;

        pay.PayTypeId = payTypeId;
        pay.AccountId = accountId;
        pay.StudentId = order.StudentId;
        pay.PayTime = payTime;
        pay.IsDelete = false;

        pay.UserId = order.UserId;
        pay.CarId = order.CarId;
        pay.PayeePerson = order.PayeePerson;
        pay.NoteNumber = order.NoteNumber;

        pay.MerchantOrderSn = merchantOrderSn;
        pay.OrderSn = orderSn;
        pay.ChannelOrderSn = channelOrderSn;
        pay.InsOrderSn = insOrderSn;

        pay.OrderDetailId = detail.Id;
        pay.OrderId = detail.OrderId;

        pay.Remark += $" 支付订单号：{(!string.IsNullOrEmpty(insOrderSn) ? insOrderSn : (!string.IsNullOrEmpty(merchantOrderSn) ? merchantOrderSn : orderSn))}  {detail.Remark}";


        if (pay.PayTime < Convert.ToDateTime("2000-01-01"))
            pay.PayTime = DateTime.Now;

        if (string.IsNullOrEmpty(pay.ImportOutId))
            pay.ImportOutId = "";


        var jxPay = await _jxStudentPayService.Pay(pay);
        return jxPay;
    }
}
