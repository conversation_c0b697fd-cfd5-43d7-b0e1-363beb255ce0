using System;
using PandaServer.System.Entity;
using PandaServer.System.Pay.FuBei.Models.Response;

namespace PandaServer.System.Pay.FuBei;

public interface IFuBeiService
{

    /// <summary>
    /// 订单退款
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="order">订单信息</param>
    /// <param name="refund">退款信息</param>
    /// <returns>退款响应结果</returns>
    Task<RefundResponse> Refund(PayAccountEntity account, OrderEntity order, RefundEntity refund);

    /// <summary>
    /// 订单退款
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="order">订单信息</param>
    /// <param name="refund">退款信息</param>
    /// <returns>退款响应结果</returns>
    Task<RefundResponse> Refund(PayAccountEntity account, OrderEntity order, ChildRefundEntity refund);

    /// <summary>
    /// 订单退款
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="order">订单信息</param>
    /// <param name="refund">退款信息</param>
    /// <returns>退款响应结果</returns>
    Task<RefundResponse> Refund(PayAccountEntity account, OrderEntity order, HnjxRefundEntity refund);

    /// <summary>
    /// 退款查询
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="refundSn">付呗退款单号</param>
    /// <param name="merchantRefundSn">外部系统退款单号</param>
    /// <returns>退款查询响应结果</returns>
    Task<RefundQueryResponse> RefundQuery(PayAccountEntity account, string refundSn = null, string merchantRefundSn = null);

    /// <summary>
    /// 创建支付订单
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="order">订单信息</param>
    /// <param name="payType">支付方式：wxpay-微信支付，alipay-支付宝，unionpay-银联云闪付</param>
    /// <param name="notifyUrl">通知地址</param>
    Task<OrderCreateResponse> CreateOrder(PayAccountEntity account, OrderEntity order, string payType, string notifyUrl);

    /// <summary>
    /// 获取微信支付参数配置
    /// </summary>
    /// <param name="account">支付账号</param>
    /// <param name="subAppid">子appid（可选）</param>
    /// <param name="accountType">账号类型（可选）</param>
    /// <param name="jsapiPath">支付授权目录（可选）</param>
    /// <returns>微信参数配置</returns>
    Task<WxConfigResponse> WxConfig(
        PayAccountEntity account,
        string subAppid = null,
        string accountType = null,
        string jsapiPath = null);

    /// <summary>
    /// 条码支付
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="order">订单信息</param>
    /// <param name="barCode">支付条码</param>
    /// <param name="notifyUrl">通知地址</param>
    /// <returns>支付响应结果</returns>
    Task<BarCodePayResponse> PayByBarCode(PayAccountEntity account, OrderEntity order, string barCode, string notifyUrl);

    /// <summary>
    /// 订单查询
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="merchantOrderSn">商户订单号</param>
    /// <returns>订单查询响应结果</returns>
    Task<OrderQueryResponse> Query(PayAccountEntity account, string merchantOrderSn);

    /// <summary>
    /// 关闭订单
    /// </summary>
    /// <param name="account">支付账户</param>
    /// <param name="merchantOrderSn">商户订单号</param>
    /// <returns>关闭订单响应结果</returns>
    Task<OrderCloseResponse> Close(PayAccountEntity account, string merchantOrderSn);
}
