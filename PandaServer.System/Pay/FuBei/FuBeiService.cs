using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using PandaServer.System.Pay.FuBei.Models.Response;

namespace PandaServer.System.Pay.FuBei;

public class FuBeiService : IFuBeiService, ITransient
{
    private readonly HttpClient _httpClient;
    private const string API_URL = "https://shq-api.51fubei.com/gateway/agent";
    private const string API_VERSION = "2.0";

    public FuBeiService()
    {
        _httpClient = new HttpClient();
    }

    #region 订单退款
    /// <summary>
    /// 订单退款
    /// </summary>
    public async Task<RefundResponse> Refund(PayAccountEntity account, OrderEntity order, RefundEntity refund)
    {
        var bizContent = new SortedDictionary<string, object>
        {
            { "merchant_id", int.Parse(account.Uid) },
            { "order_sn", order.OrderSn },
            { "merchant_order_sn", order.MerchantOrderSn },
            { "ins_order_sn", order.InsOrderSn },
            { "merchant_refund_sn", refund.MerchantOrderSn },
            { "refund_amount", refund.RefundMoney.ToString("F2") },
            { "refund_reason", refund.Remark }
        };

        var result = await SendRequestAsync<RefundResponse>(account, "fbpay.order.refund", bizContent);

        if (!result.Success)
            throw Oops.Bah($"请求失败: {result.ResultMessage}");

        return result.Data;
    }

    /// <summary>
    /// 订单退款
    /// </summary>
    public async Task<RefundResponse> Refund(PayAccountEntity account, OrderEntity order, ChildRefundEntity refund)
    {
        var bizContent = new SortedDictionary<string, object>
        {
            { "merchant_id", int.Parse(account.Uid) },
            { "order_sn", order.OrderSn },
            { "merchant_order_sn", order.MerchantOrderSn },
            { "ins_order_sn", order.InsOrderSn },
            { "merchant_refund_sn", refund.MerchantOrderSn },
            { "refund_amount", refund.RefundMoney.ToString("F2") },
            { "refund_reason", refund.Remark }
        };

        var result = await SendRequestAsync<RefundResponse>(account, "fbpay.order.refund", bizContent);

        if (!result.Success)
            throw Oops.Bah($"请求失败: {result.ResultMessage}");

        return result.Data;
    }
    /// <summary>
    /// 湖南驾协 的 退款查询
    /// </summary>
    /// <returns></returns>
    public async Task<RefundResponse> Refund(PayAccountEntity account, OrderEntity order, HnjxRefundEntity refund)
    {
        var bizContent = new SortedDictionary<string, object>
        {
            { "merchant_id", int.Parse(account.Uid) },
            { "order_sn", order.OrderSn },
            { "merchant_order_sn", order.MerchantOrderSn },
            { "ins_order_sn", order.InsOrderSn },
            { "merchant_refund_sn", refund.MerchantOrderSn },
            { "refund_amount", refund.RefundMoney.ToString("F2") },
            { "refund_reason", refund.Remark }
        };

        var result = await SendRequestAsync<RefundResponse>(account, "fbpay.order.refund", bizContent);

        if (!result.Success)
            throw Oops.Bah($"请求失败: {result.ResultMessage}");

        return result.Data;
    }

    /// <summary>
    /// 退款查询
    /// </summary>
    public async Task<RefundQueryResponse> RefundQuery(PayAccountEntity account, string refundSn = null, string merchantRefundSn = null)
    {
        if (string.IsNullOrEmpty(account.Uid))
        {
            throw Oops.Bah("请先配置支付账号 的 Uid（三方系统 编号）");
        }
        var bizContent = new SortedDictionary<string, object>
        {
            { "merchant_id", int.Parse(account.Uid) },
            { "refund_sn", string.IsNullOrEmpty(refundSn) ? "" : refundSn },
            { "merchant_refund_sn", string.IsNullOrEmpty(merchantRefundSn) ? "" : merchantRefundSn }
        };
        if (string.IsNullOrEmpty(refundSn) && string.IsNullOrEmpty(merchantRefundSn))
            throw Oops.Bah("refundSn和merchantRefundSn不能同时为空");

        var result = await SendRequestAsync<RefundQueryResponse>(account, "fbpay.order.refund.query", bizContent);
        return result.Data;
    }

    #endregion 订单退款

    /// <summary>
    /// 创建支付订单
    /// </summary>
    public async Task<OrderCreateResponse> CreateOrder(PayAccountEntity account, OrderEntity order, string payType, string notifyUrl)
    {
        var bizContent = new SortedDictionary<string, object>
        {
            { "merchant_order_sn", order.MerchantOrderSn },
            { "merchant_id", int.Parse(account.Uid) },
            {"sub_appid", HttpNewUtil.GetHeader("appId") },
            { "pay_type", payType },
            { "total_amount", order.PayMoney.ToString("F2") },
            { "user_id", HttpNewUtil.GetHeader("openId") },
            { "notify_url", notifyUrl },
            { "body", order.Desc },
        };

        var result = await SendRequestAsync<OrderCreateResponse>(account, "fbpay.order.create", bizContent);

        if (!result.Success)
            throw Oops.Bah($"请求失败: {result.ResultMessage}");

        return result.Data;
    }

    /// <summary>
    /// 获取微信支付参数配置
    /// </summary>
    /// <param name="account">支付账号</param>
    /// <param name="subAppid">子appid（可选）</param>
    /// <param name="accountType">账号类型（可选）</param>
    /// <param name="jsapiPath">支付授权目录（可选）</param>
    /// <returns>微信参数配置</returns>
    public async Task<WxConfigResponse> WxConfig(
        PayAccountEntity account,
        string subAppid = null,
        string accountType = null,
        string jsapiPath = null)
    {
        var bizContent = new SortedDictionary<string, object>
        {
            { "store_id", account.StoreId }
        };
        if (!string.IsNullOrEmpty(subAppid))
            bizContent.Add("sub_appid", subAppid);
        if (!string.IsNullOrEmpty(accountType))
            bizContent.Add("account_type", accountType);
        if (!string.IsNullOrEmpty(jsapiPath))
            bizContent.Add("jsapi_path", jsapiPath);

        var result = await SendRequestAsync<WxConfigResponse>(
            account, "fbpay.order.wxconfig", bizContent);

        if (result == null || !result.Success)
            throw Oops.Bah($"请求失败: {result?.ResultMessage}");

        return result.Data;
    }

    /// <summary>
    /// 条码支付
    /// </summary>
    public async Task<BarCodePayResponse> PayByBarCode(PayAccountEntity account, OrderEntity order, string barCode, string notifyUrl)
    {
        if (string.IsNullOrEmpty(account.Uid))
            throw Oops.Bah("请先配置支付账号 的 Uid（三方系统 编号）");

        var bizContent = new SortedDictionary<string, object>
        {
            { "merchant_order_sn", order.MerchantOrderSn },
            { "merchant_id", int.Parse(account.Uid) },
            { "pay_type", "barcode" },
            { "total_amount", order.PayMoney.ToString("F2") },
            { "notify_url", notifyUrl },
            { "body", order.Desc },
            { "auth_code", barCode }
        };

        var result = await SendRequestAsync<BarCodePayResponse>(account, "fbpay.order.pay", bizContent);

        if (!result.Success)
            throw Oops.Bah($"请求失败: {result.ResultMessage}");


        if (result.Data.OrderStatus != "USERPAYING" && result.Data.OrderStatus != "SUCCESS")
            throw Oops.Bah(result.ResultMessage);


        return result.Data;
    }


    /// <summary>
    /// 订单查询
    /// </summary>
    public async Task<OrderQueryResponse> Query(PayAccountEntity account, string merchantOrderSn)
    {
        var bizContent = new SortedDictionary<string, object>
        {
            { "merchant_id", account.Uid },
            { "merchant_order_sn", merchantOrderSn }
        };

        var result = await SendRequestAsync<OrderQueryResponse>(account, "fbpay.order.query", bizContent);

        if (!result.Success)
            throw Oops.Bah($"请求失败: {result.ResultMessage}");

        return result.Data;
    }



    /// <summary>
    /// 关闭订单
    /// </summary>
    public async Task<OrderCloseResponse> Close(PayAccountEntity account, string merchantOrderSn)
    {
        var bizContent = new SortedDictionary<string, object>
        {
            { "merchant_id", account.Uid },
            { "merchant_order_sn", merchantOrderSn }
        };

        var result = await SendRequestAsync<OrderCloseResponse>(account, "fbpay.order.close", bizContent);

        if (!result.Success)
            throw Oops.Bah($"请求失败: {result.ResultMessage}");

        return result.Data;
    }

    #region 发送API请求
    /// <summary>
    /// 发送API请求
    /// </summary>
    private async Task<FuBeiApiResponse<T>> SendRequestAsync<T>(PayAccountEntity account, string method, object bizContent)
    {
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();

        Console.WriteLine("account:" + JsonConvert.SerializeObject(account));

        var requestObj = new SortedDictionary<string, object>
        {
            { "vendor_sn", account.VendorSn },
            { "app_id", account.AppId },
            { "method", method },
            { "format", "json" },
            { "sign_method", "md5" },
            { "nonce", DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString() },
            { "version", API_VERSION },
            { "biz_content", JsonConvert.SerializeObject(bizContent) }
        };

        var signStr = new StringBuilder();
        foreach (var kv in requestObj)
        {
            signStr.Append($"{kv.Key}={kv.Value}&");
        }
        signStr.Length--; // 去掉最后一个&
        signStr.Append(account.AppSecret); // 加密钥
        string sign = MD5(signStr.ToString()).ToUpper();
        requestObj.Add("sign", sign);

        var json = JsonConvert.SerializeObject(requestObj);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Add required headers
        _httpClient.DefaultRequestHeaders.Clear();
        _httpClient.DefaultRequestHeaders.Add("X-API-KEY", account.AppId);
        _httpClient.DefaultRequestHeaders.Add("X-API-SIGN", sign);
        _httpClient.DefaultRequestHeaders.Add("X-API-TIMESTAMP", timestamp);

        var response = await _httpClient.PostAsync(API_URL, content);

        if (!response.IsSuccessStatusCode)
            throw Oops.Bah($"API请求失败: {response.StatusCode}");

        var responseContent = await response.Content.ReadAsStringAsync();

        Console.WriteLine(responseContent);

        var result = JsonConvert.DeserializeObject<FuBeiApiResponse<T>>(responseContent);

        if (result == null)
            throw Oops.Bah("响应解析失败");

        // if (!result.Success)
        //     throw Oops.Bah($"请求失败: {result.ResultMessage}");

        // Console.WriteLine(JsonConvert.SerializeObject(result));
        return result;
    }

    private static string MD5(string input)
    {
        using var md5 = global::System.Security.Cryptography.MD5.Create();
        var bytes = Encoding.UTF8.GetBytes(input);
        var hash = md5.ComputeHash(bytes);
        return BitConverter.ToString(hash).Replace("-", "").ToLower();
    }
    #endregion 发送API请求
}
