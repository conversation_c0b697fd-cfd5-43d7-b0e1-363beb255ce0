
using PandaServer.System.Pay.FuBei.Models.Parameter;
using PandaServer.System.Entity.Student.Pay;


namespace PandaServer.System.Pay.FuBei;

public interface IJxCostTypeCallBackService
{
    /// <summary>
    ///     支付成功以后 回调的 方法
    /// </summary>
    /// <param name="p"></param>
    /// <param name="sign"></param>
    /// <returns></returns>
    Task<List<JxPayEntity>> CallBack(FubeiNotificationParam p, string sign);
}
