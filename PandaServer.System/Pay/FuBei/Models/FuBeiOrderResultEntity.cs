using System;
using Newtonsoft.Json;

namespace PandaServer.System.Pay.FuBei.Models;

/// <summary>
/// 付呗订单结果实体
/// </summary>
public class FuBeiOrderResult
{
    /// <summary>
    /// 附加数据
    /// </summary>
    [JsonProperty("attach")]
    public string Attach { get; set; }

    /// <summary>
    /// 买家支付金额
    /// </summary>
    [JsonProperty("buyer_pay_amount")]
    public decimal BuyerPayAmount { get; set; }

    /// <summary>
    /// 收银员ID
    /// </summary>
    [JsonProperty("cashier_id")]
    public int CashierId { get; set; }

    /// <summary>
    /// 渠道订单号
    /// </summary>
    [JsonProperty("channel_order_sn")]
    public string ChannelOrderSn { get; set; }

    /// <summary>
    /// 渠道标签
    /// </summary>
    [JsonProperty("channel_tag")]
    public string ChannelTag { get; set; }

    /// <summary>
    /// 设备号
    /// </summary>
    [JsonProperty("device_no")]
    public string DeviceNo { get; set; }

    /// <summary>
    /// 手续费
    /// </summary>
    [JsonProperty("fee")]
    public decimal Fee { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    [JsonProperty("finish_time")]
    public string FinishTime { get; set; }

    /// <summary>
    /// 机构订单号
    /// </summary>
    [JsonProperty("ins_order_sn")]
    public string InsOrderSn { get; set; }

    /// <summary>
    /// 是否可部分退款
    /// </summary>
    [JsonProperty("is_can_part_refund")]
    public int IsCanPartRefund { get; set; }

    /// <summary>
    /// 商户订单号
    /// </summary>
    [JsonProperty("merchant_order_sn")]
    public string MerchantOrderSn { get; set; }

    /// <summary>
    /// 商户费率
    /// </summary>
    [JsonProperty("merchant_rate")]
    public decimal MerchantRate { get; set; }

    /// <summary>
    /// 净金额
    /// </summary>
    [JsonProperty("net_amount")]
    public decimal NetAmount { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    [JsonProperty("order_sn")]
    public string OrderSn { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    [JsonProperty("order_status")]
    public string OrderStatus { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    [JsonProperty("pay_type")]
    public string PayType { get; set; }

    /// <summary>
    /// 支付列表
    /// </summary>
    [JsonProperty("payment_list")]
    public List<PaymentItem> PaymentList { get; set; }

    /// <summary>
    /// 店铺ID
    /// </summary>
    [JsonProperty("store_id")]
    public int StoreId { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    [JsonProperty("total_amount")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [JsonProperty("uid")]
    public int Uid { get; set; }

    /// <summary>
    /// 用户标识
    /// </summary>
    [JsonProperty("user_id")]
    public string UserId { get; set; }

    /// <summary>
    /// 支付宝扩展参数
    /// </summary>
    [JsonProperty("alipay_extend_params")]
    public AlipayExtendParamsEntity AlipayExtendParams { get; set; }
}

/// <summary>
/// 支付宝扩展参数实体
/// </summary>
public class AlipayExtendParamsEntity
{
    /// <summary>
    /// 花呗分期期数
    /// </summary>
    [JsonProperty("hb_fq_instalment")]
    public int? HbFqInstalment { get; set; }

    /// <summary>
    /// 花呗分期数
    /// </summary>
    [JsonProperty("hb_fq_num")]
    public int? HbFqNum { get; set; }

    /// <summary>
    /// 花呗分期卖家承担比例
    /// </summary>
    [JsonProperty("hb_fq_seller_percent")]
    public int? HbFqSellerPercent { get; set; }
}

/// <summary>
/// 支付项
/// </summary>
public class PaymentItem
{
    /// <summary>
    /// 金额
    /// </summary>
    [JsonProperty("amount")]
    public decimal Amount { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; }
}