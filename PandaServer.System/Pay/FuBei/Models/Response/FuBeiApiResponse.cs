using Newtonsoft.Json;

namespace PandaServer.System.Pay.FuBei.Models.Response;

/// <summary>
/// 付呗API通用响应包装类
/// </summary>
/// <typeparam name="T">响应数据类型</typeparam>
public class FuBeiApiResponse<T>
{
    /// <summary>
    /// 响应码
    /// </summary>
    [JsonProperty("result_code")]
    public int ResultCode { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    [JsonProperty("result_message")]
    public string ResultMessage { get; set; }

    /// <summary>
    /// 响应数据
    /// </summary>
    [JsonProperty("data")]
    public T Data { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonProperty("success")]
    public bool Success { get; set; }
}