using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace PandaServer.System.Pay.FuBei.Models.Response;

public class OrderQueryResponse
{
    /// <summary>
    /// 商户订单号
    /// </summary>
    [JsonProperty("merchant_order_sn")]
    public string MerchantOrderSn { get; set; }

    /// <summary>
    /// 付呗订单号
    /// </summary>
    [JsonProperty("order_sn")]
    public string OrderSn { get; set; }

    /// <summary>
    /// 内部订单号
    /// </summary>
    [JsonProperty("ins_order_sn")]
    public string InsOrderSn { get; set; }

    /// <summary>
    /// 渠道订单号
    /// </summary>
    [JsonProperty("channel_order_sn")]
    public string ChannelOrderSn { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    [JsonProperty("order_status")]
    public string OrderStatus { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    [JsonProperty("pay_type")]
    public string PayType { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    [JsonProperty("total_amount")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 净额
    /// </summary>
    [JsonProperty("net_amount")]
    public decimal NetAmount { get; set; }

    /// <summary>
    /// 买家支付金额
    /// </summary>
    [JsonProperty("buyer_pay_amount")]
    public decimal BuyerPayAmount { get; set; }

    /// <summary>
    /// 费用
    /// </summary>
    [JsonProperty("fee")]
    public decimal? Fee { get; set; }

    /// <summary>
    /// 商店ID
    /// </summary>
    [JsonProperty("store_id")]
    public long StoreId { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [JsonProperty("user_id")]
    public string UserId { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    [JsonProperty("finish_time")]
    public string FinishTime { get; set; }

    /// <summary>
    /// 设备号
    /// </summary>
    [JsonProperty("device_no")]
    public string DeviceNo { get; set; }

    /// <summary>
    /// 附件
    /// </summary>
    [JsonProperty("attach")]
    public string Attach { get; set; }

    /// <summary>
    /// 支付列表
    /// </summary>
    [JsonProperty("payment_list")]
    public List<FuBeiPaymentItem> PaymentList { get; set; }
}

public class FuBeiPaymentItem
{
    /// <summary>
    /// 类型
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    [JsonProperty("amount")]
    public decimal Amount { get; set; }
}