using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace PandaServer.System.Pay.FuBei.Models.Response;

public class FuBeiResponse
{
    /// <summary>
    /// 结果码
    /// </summary>
    [JsonProperty("result_code")]
    public int ResultCode { get; set; }

    /// <summary>
    /// 结果消息
    /// </summary>
    [JsonProperty("result_message")]
    public string ResultMessage { get; set; }

    /// <summary>
    /// 响应数据
    /// </summary>
    [JsonProperty("data")]
    public BarCodePayResponse Data { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonProperty("success")]
    public bool Success { get; set; }
}

public class BarCodePayResponse
{
    /// <summary>
    /// 商户订单号
    /// </summary>
    [JsonProperty("merchant_order_sn")]
    public string MerchantOrderSn { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    [JsonProperty("order_sn")]
    public string OrderSn { get; set; }

    /// <summary>
    /// 机构订单号
    /// </summary>
    [JsonProperty("ins_order_sn")]
    public string InsOrderSn { get; set; }

    /// <summary>
    /// 渠道订单号
    /// </summary>
    [JsonProperty("channel_order_sn")]
    public string ChannelOrderSn { get; set; }

    /// <summary>
    /// 商户ID
    /// </summary>
    [JsonProperty("merchant_id")]
    public int MerchantId { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    [JsonProperty("order_status")]
    public string OrderStatus { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    [JsonProperty("pay_type")]
    public string PayType { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    [JsonProperty("total_amount")]
    public decimal? TotalAmount { get; set; }

    /// <summary>
    /// 实收金额
    /// </summary>
    [JsonProperty("net_amount")]
    public decimal? NetAmount { get; set; }

    /// <summary>
    /// 买家支付金额
    /// </summary>
    [JsonProperty("buyer_pay_amount")]
    public decimal? BuyerPayAmount { get; set; }

    /// <summary>
    /// 手续费
    /// </summary>
    [JsonProperty("fee")]
    public decimal? Fee { get; set; }

    /// <summary>
    /// 门店ID
    /// </summary>
    [JsonProperty("store_id")]
    public long? StoreId { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [JsonProperty("user_id")]
    public string UserId { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    [JsonProperty("finish_time")]
    public string FinishTime { get; set; }

    /// <summary>
    /// 设备号
    /// </summary>
    [JsonProperty("device_no")]
    public string DeviceNo { get; set; }

    /// <summary>
    /// 附加数据
    /// </summary>
    [JsonProperty("attach")]
    public string Attach { get; set; }

    /// <summary>
    /// 支付列表
    /// </summary>
    [JsonProperty("payment_list")]
    public List<PaymentItem> PaymentList { get; set; }
}

/// <summary>
/// 支付项
/// </summary>
public class PaymentItem
{
    /// <summary>
    /// 支付类型
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    [JsonProperty("amount")]
    public decimal? Amount { get; set; }
}