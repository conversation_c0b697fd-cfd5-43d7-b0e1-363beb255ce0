using Newtonsoft.Json;

namespace PandaServer.System.Pay.FuBei.Models.Response;

/// <summary>
/// 退款查询响应
/// </summary>
public class RefundQueryResponse
{
    /// <summary>
    /// 付呗订单号
    /// </summary>
    [JsonProperty("order_sn")]
    public string OrderSn { get; set; }

    /// <summary>
    /// 付呗退款单号
    /// </summary>
    [JsonProperty("refund_sn")]
    public string RefundSn { get; set; }

    /// <summary>
    /// 外部系统订单号
    /// </summary>
    [JsonProperty("merchant_order_sn")]
    public string MerchantOrderSn { get; set; }

    /// <summary>
    /// 外部系统退款单号
    /// </summary>
    [JsonProperty("merchant_refund_sn")]
    public string MerchantRefundSn { get; set; }

    /// <summary>
    /// 退款状态：
    /// REFUND_PROCESSING--退款中
    /// REFUND_SUCCESS--退款成功
    /// REFUND_FAIL--退款失败
    /// </summary>
    [JsonProperty("refund_status")]
    public string RefundStatus { get; set; }

    /// <summary>
    /// 退款金额
    /// </summary>
    [JsonProperty("refund_amount")]
    public decimal RefundAmount { get; set; }

    /// <summary>
    /// 退款完成时间，格式为yyyyMMddHHmmss，当退款成功或退款失败时返回
    /// </summary>
    [JsonProperty("finish_time")]
    public string FinishTime { get; set; }

    /// <summary>
    /// 处理人ID
    /// </summary>
    [JsonProperty("handler")]
    public int Handler { get; set; }

    /// <summary>
    /// 硬件设备号
    /// </summary>
    [JsonProperty("device_no")]
    public string DeviceNo { get; set; }

    /// <summary>
    /// 退款原因
    /// </summary>
    [JsonProperty("refund_reason")]
    public string RefundReason { get; set; }

    /// <summary>
    /// 退款手续费
    /// </summary>
    [JsonProperty("refund_fee")]
    public decimal RefundFee { get; set; }

    /// <summary>
    /// 补贴金额
    /// </summary>
    [JsonProperty("subsidy_amount")]
    public decimal? SubsidyAmount { get; set; }

    /// <summary>
    /// 失败原因代码
    /// </summary>
    [JsonProperty("failure_code")]
    public string FailureCode { get; set; }
}