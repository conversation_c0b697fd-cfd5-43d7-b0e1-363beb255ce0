using System;
using Newtonsoft.Json;

namespace PandaServer.System.Pay.FuBei.Models.Response;

public class OrderCreateResponse
{
    /// <summary>
    /// 商户订单号
    /// </summary>
    [JsonProperty("merchant_order_sn")]
    public string MerchantOrderSn { get; set; }

    /// <summary>
    /// 商户ID
    /// </summary>
    [JsonProperty("merchant_id")]
    public string MerchantId { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    [JsonProperty("order_sn")]
    public string OrderSn { get; set; }

    /// <summary>
    /// 预支付ID
    /// </summary>
    [JsonProperty("prepay_id")]
    public string PrepayId { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    [JsonProperty("pay_type")]
    public string PayType { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    [JsonProperty("total_amount")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 门店ID
    /// </summary>
    [JsonProperty("store_id")]
    public long StoreId { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [JsonProperty("user_id")]
    public string UserId { get; set; }

    /// <summary>
    /// 附加数据
    /// </summary>
    [JsonProperty("attach")]
    public string Attach { get; set; }

    /// <summary>
    /// 签名包
    /// </summary>
    [JsonProperty("sign_package")]
    public SignPackage SignPackage { get; set; }
}

public class SignPackage
{
    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonProperty("timeStamp")]
    public string TimeStamp { get; set; }

    /// <summary>
    /// 随机字符串
    /// </summary>
    [JsonProperty("nonceStr")]
    public string NonceStr { get; set; }

    /// <summary>
    /// 订单详情扩展字符串
    /// </summary>
    [JsonProperty("package")]
    public string Package { get; set; }

    /// <summary>
    /// 签名方式
    /// </summary>
    [JsonProperty("signType")]
    public string SignType { get; set; }

    /// <summary>
    /// 签名
    /// </summary>
    [JsonProperty("paySign")]
    public string PaySign { get; set; }
}