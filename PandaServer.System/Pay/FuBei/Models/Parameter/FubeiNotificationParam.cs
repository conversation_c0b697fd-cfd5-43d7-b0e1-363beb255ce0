﻿using Newtonsoft.Json;


namespace PandaServer.System.Pay.FuBei.Models.Parameter;

public class FubeiNotificationParam : BaseEntity
{
    /// <summary>
    /// </summary>
    /// <value></value>
    [JsonProperty("result_code")]
    [FromForm(Name = "result_code")]
    public string ResultCode { get; set; }


    /// <summary>
    /// </summary>
    /// ///
    /// <value></value>
    [JsonProperty("result_message")]
    [FromForm(Name = "result_message")]
    public string ResultMessage { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    [JsonProperty("data")]
    [FromForm(Name = "data")]
    public string Data { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    [JsonIgnore]
    public string AppSecret { get; set; }
}