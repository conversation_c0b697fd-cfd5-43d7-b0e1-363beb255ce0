// using Com.Fubei.OpenApi.Sdk.Models;
// using Microsoft.AspNetCore.Mvc;
// using Newtonsoft.Json;

// namespace PandaServer.System.Pay.FuBei.Models.Parameter;

// public class FubeiRequestParam : Com.Fubei.OpenApi.Sdk.Models.BaseEntity
// {

//     [JsonProperty("app_id")]
//     public string AppId { get; set; }

//     [JsonProperty("method")]
//     public string Method { get; set; }

//     [JsonProperty("format")]
//     public string Format
//     {
//         get { return "json"; }
//     }

//     [JsonProperty("sign_method")]
//     public string SignMethod
//     {
//         get { return "md5"; }
//     }

//     [JsonProperty("nonce")]
//     public string Nonce { get; set; }

//     [JsonProperty("version")]
//     public string Version
//     {
//         get { return "1.0"; }
//     }

//     [JsonProperty("biz_content")]
//     public string BizContent { get; set; }

//     [JsonProperty("sign")]
//     public string Sign { get; set; }

//     [JsonIgnore]
//     public string AppSecret { get; set; }
// }

