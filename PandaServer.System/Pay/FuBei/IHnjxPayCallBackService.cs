using System;
using PandaServer.System.Pay.FuBei.Models.Parameter;

namespace PandaServer.System.Pay.FuBei;


/// <summary>
/// 主要是 支付成功以后 根据 OutTable  来判断相关的
/// </summary>
public interface IHnjxPayCallBackService
{

    /// <summary>
    ///     支付成功以后 回调的 方法
    /// </summary>
    /// <param name="p"></param>
    /// <param name="sign"></param>
    /// <returns></returns>
    Task<bool> CallBack(FubeiNotificationParam p, string sign);
}
