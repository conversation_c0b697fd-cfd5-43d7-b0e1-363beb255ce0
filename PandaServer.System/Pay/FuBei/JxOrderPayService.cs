﻿using PandaServer.System.Services.Config;
using PandaServer.System.Services.Pay.Dto;
using Yitter.IdGenerator;
using Newtonsoft.Json;
using PandaServer.System.Entity.Student.Pay;
using PandaServer.System.Services.Student.Dto.Pay;
using PandaServer.System.Pay.FuBei;

namespace PandaServer.System.Pay.FuBei;

/// <summary>
///     <inheritdoc cref="IJxOrderPayService" />
/// </summary>
public class JxOrderPayService : IJxOrderPayService, ITransient
{
    private readonly IFuBeiService _fuBeiService;

    public JxOrderPayService(IFuBeiService fuBeiService)
    {
        _fuBeiService = fuBeiService;
    }

    #region 创建订单

    /// <inheritdoc />
    public async Task<PayOutPut> CreateOrder(JxPayInPut inPut)
    {
        var data = await PreparePaymentData(inPut);
        var order = await CreateOrderEntity(inPut, data);
        await CreateOrderDetail(order, data);
        return await ProcessPayment(inPut, order);
    }

    /// <inheritdoc />
    public async Task<PayOutPut> OnlinePay(JxPayInPut inPut)
    {
        var account = await DbContext.Db.Queryable<PayAccountEntity>()
            .Where(m => m.Id == inPut.AccountId)
            .FirstAsync();

        if (account == null)
            throw Oops.Bah("当前收款账户数据异常");

        var order = new OrderEntity();
        order.Create();
        order.TenantId = UserManager.TenantId;
        order.PayMoney = inPut.PayMoney;
        order.UserId = inPut.UserId;
        order.StudentId = inPut.StudentId;
        order.MerchantOrderSn = YitIdHelper.NextId().ToString();
        order.AppId = HttpNewUtil.GetHeader("appId");
        order.OpenId = HttpNewUtil.GetHeader("openId");
        order.AccountId = account.Id;
        order.CreateJxDeptId = inPut.CreateJxDeptId;

        order.PayChannel = PayChannelEnum.NoPay;
        order.OrderSn = "";
        order.InsOrderSn = "";
        order.ChannelOrderSn = "";
        order.Desc = inPut.Remark;

        order.OrderStatus = OrderStatusEnum.WaitPay;
        order.CouponId = inPut.CouponId;
        order.ActPayMoney = order.PayMoney - order.DiscountMoney;

        return await ProcessOnlinePayment(order, account);
    }

    /// <summary>
    /// 准备支付数据
    /// </summary>
    private async Task<PayInPut> PreparePaymentData(JxPayInPut inPut)
    {
        var data = new PayInPut();
        var student = await GetStudentInfo(inPut.StudentId);

        var accountId = await GetAccountId(student, inPut.CreateJxDeptId, string.IsNullOrEmpty(inPut.BarCode), UserManager.TenantId);
        await ValidateAndSetAccount(data, accountId, inPut.CreateJxDeptId);

        await ProcessPaymentDetails(data, inPut, student);

        data.PayMoney = inPut.PayMoney;
        data.JxPayDeptId = inPut.CreateJxDeptId;
        data.JxShouldPayId = inPut.JxShouldPayId;
        data.CostTypeId = inPut.CostTypeId;
        data.BarCode = inPut.BarCode;
        data.Remark = inPut.Remark;

        return data;
    }

    /// <summary>
    /// 获取学员信息
    /// </summary>
    private async Task<JxStudentEntity> GetStudentInfo(Guid studentId)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        return await DbContext.Db.Queryable<JxStudentEntity>().AS(studentTable)
            .Where(m => m.Id == studentId)
            .SingleAsync();
    }

    /// <summary>
    /// 验证并设置账户信息
    /// </summary>
    private async Task ValidateAndSetAccount(PayInPut data, Guid accountId, Guid createJxDeptId)
    {
        if (createJxDeptId == Guid.Empty)
            throw Oops.Bah("请选择正确的缴费门店!");

        if (accountId == Guid.Empty)
            throw Oops.Bah("请先设置相应的店面的或者训练场的收款账户");

        data.AccountId = accountId;

        var account = await DbContext.Db.Queryable<PayAccountEntity>()
            .Where(m => m.Id == accountId)
            .SingleAsync();

        if (account == null)
            throw Oops.Bah("当前收款账户数据异常");
    }

    /// <summary>
    /// 处理支付详情
    /// </summary>
    private async Task ProcessPaymentDetails(PayInPut data, JxPayInPut inPut, JxStudentEntity student)
    {
        if (inPut.JxShouldPayId == Guid.Empty)
        {
            ProcessDirectPayment(data, inPut);
        }
        else
        {
            await ProcessShouldPayPayment(data, inPut, student);
        }
    }

    /// <summary>
    /// 处理直接支付
    /// </summary>
    private void ProcessDirectPayment(PayInPut data, JxPayInPut inPut)
    {
        data.StudentId = inPut.StudentId;
        data.OutTable = PayOutTableEnum.JxCostType;
        data.OutId = inPut.CostTypeId;
    }

    /// <summary>
    /// 处理应付账款支付
    /// </summary>
    private async Task ProcessShouldPayPayment(PayInPut data, JxPayInPut inPut, JxStudentEntity student)
    {
        var shouldPayTable = "student_ShouldPay_" + student.TenantId.ToString().Replace("-", "_").ToLower();
        var shouldPay = await DbContext.Db.Queryable<JxShouldPayEntity>()
            .AS(shouldPayTable)
            .Where(m => m.Id == inPut.JxShouldPayId)
            .SingleAsync();

        if (shouldPay == null)
            throw Oops.Bah("挂账信息为空");

        data.OutTable = PayOutTableEnum.JxShouldPay;
        data.OutId = inPut.JxShouldPayId;

        await ValidatePaymentAmount(inPut, student, shouldPay);
        data.StudentId = shouldPay.StudentId;
    }

    /// <summary>
    /// 验证支付金额
    /// </summary>
    private async Task ValidatePaymentAmount(JxPayInPut inPut, JxStudentEntity student, JxShouldPayEntity shouldPay)
    {
        var payTable = "student_pay_" + student.TenantId.ToString().Replace("-", "_").ToLower();
        var shouldPayTable = "student_ShouldPay_" + student.TenantId.ToString().Replace("-", "_").ToLower();

        var noPay = await DbContext.Db.Queryable<JxShouldPayEntity>()
            .AS(shouldPayTable)
            .Where(m => m.Id == inPut.JxShouldPayId)
            .Select(m => m.PayMoney - m.DiscountMoney - SqlFunc.Subqueryable<JxPayEntity>()
                .AS(payTable)
                .Where(z => z.JxShouldPayId == m.Id && z.IsDelete == false)
                .Sum(z => z.PayMoney))
            .SingleAsync();

        if (noPay < inPut.PayMoney)
            throw Oops.Bah("缴费的金额不能大于欠费金额");
    }

    /// <summary>
    /// 创建订单实体
    /// </summary>
    private async Task<OrderEntity> CreateOrderEntity(JxPayInPut inPut, PayInPut data)
    {
        var order = new OrderEntity();
        order.Create();
        order.TenantId = UserManager.TenantId;
        order.PayMoney = inPut.PayMoney;
        order.UserId = inPut.UserId;
        order.StudentId = inPut.StudentId;
        order.MerchantOrderSn = YitIdHelper.NextId().ToString();
        order.AppId = HttpNewUtil.GetHeader("appId");
        order.OpenId = HttpNewUtil.GetHeader("openId");
        order.AccountId = data.AccountId;
        order.CreateJxDeptId = inPut.CreateJxDeptId;

        order.PayChannel = PayChannelEnum.NoPay;
        order.OrderSn = "";
        order.InsOrderSn = "";
        order.ChannelOrderSn = "";
        order.Desc = "线上直接付款";

        order.OrderStatus = OrderStatusEnum.WaitPay;
        order.CouponId = inPut.CouponId;
        order.ActPayMoney = order.PayMoney - order.DiscountMoney;

        if (order.ActPayMoney <= 0)
            throw Oops.Bah("请输入金额，金额不能为空");

        await DbContext.Db.Insertable(order).SplitTable().ExecuteCommandAsync();
        return order;
    }

    /// <summary>
    /// 创建订单详情
    /// </summary>
    private async Task CreateOrderDetail(OrderEntity order, PayInPut data)
    {
        var detail = new OrderDetailEntity
        {
            Id = Guid.NewGuid(),
            OrderId = order.Id,
            SortCode = 1,
            ActPayMoney = order.ActPayMoney,
            OutTable = data.OutTable,
            OutId = data.OutId,
            Remark = string.IsNullOrEmpty(data.Remark) ? "" : data.Remark
        };

        await DbContext.Db.Insertable(detail).SplitTable().ExecuteCommandAsync();
    }

    /// <summary>
    /// 处理支付
    /// </summary>
    private async Task<PayOutPut> ProcessPayment(JxPayInPut inPut, OrderEntity order)
    {
        var account = await DbContext.Db.Queryable<PayAccountEntity>()
            .Where(m => m.Id == order.AccountId)
            .SingleAsync();

        return string.IsNullOrEmpty(inPut.BarCode)
            ? await ProcessOnlinePayment(order, account)
            : await ProcessOfflinePayment(order, account, inPut.BarCode);
    }

    /// <summary>
    /// 处理线上支付
    /// </summary>
    private async Task<PayOutPut> ProcessOnlinePayment(OrderEntity order, PayAccountEntity account)
    {
        Console.WriteLine("处理线上支付 :" + order.Desc);

        var result = await _fuBeiService.CreateOrder(
            account,
            order,
            "wxpay",
            "https://api.51panda.com/Pay/CallBack/FuBei/JxShouldPay"
        );

        return new PayOutPut
        {
            OrderId = order.Id,
            SignData = result.SignPackage.Adapt<SignPackageByWeiXin>()
        };
    }

    /// <summary>
    /// 处理线下支付
    /// </summary>
    private async Task<PayOutPut> ProcessOfflinePayment(OrderEntity order, PayAccountEntity account, string barCode)
    {
        var result = await _fuBeiService.PayByBarCode(
            account,
            order,
            barCode,
            "https://api.51panda.com/Pay/CallBack/FuBei/JxShouldPay"
        );


        return new PayOutPut
        {
            OrderId = order.Id,
            SignData = new SignPackageByWeiXin()
        };
    }

    #endregion 创建订单

    #region 账户ID获取相关方法

    /// <summary>
    /// 根据学员ID获取支付账户ID
    /// </summary>
    /// <param name="studentId">学员ID</param>
    /// <param name="createJxDeptId">创建部门ID</param>
    /// <param name="isOnline">是否是线上支付</param>
    /// <param name="tenantId">租户ID</param>
    /// <returns>支付账户ID</returns>
    /// <remarks>
    /// 如果学员ID无效，将使用创建部门的支付账户
    /// </remarks>
    public async Task<Guid> GetAccountId(Guid studentId, Guid createJxDeptId, bool isOnline, Guid tenantId)
    {
        Console.WriteLine($"开始获取账户ID - 学员ID: {studentId}, 创建部门ID: {createJxDeptId}, 是否是线上: {isOnline}");
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await DbContext.Db.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == studentId).SingleAsync();
        return await GetAccountId(student, createJxDeptId, isOnline, tenantId);
    }

    /// <summary>
    /// 根据学员实体获取支付账户ID
    /// </summary>
    /// <param name="student">学员实体</param>
    /// <param name="createJxDeptId">创建部门ID</param>
    /// <param name="isOnline">是否是线上支付</param>
    /// <param name="tenantId">租户ID</param>
    /// <returns>支付账户ID</returns>
    /// <remarks>
    /// 根据配置的支付规则选择不同的支付账户:
    /// 1 - 学员报名点账户
    /// 2 - 学员销售门店账户
    /// 3 - 学员训练场账户
    /// 4 - 推荐人训练场账户
    /// 5 - 收款报名点账户
    /// 6 - 推荐人报名点账户
    /// </remarks>
    public async Task<Guid> GetAccountId(JxStudentEntity student, Guid createJxDeptId, bool isOnline, Guid tenantId)
    {
        Console.WriteLine($"根据学员信息获取账户ID - 创建部门ID: {createJxDeptId}, 是否是线上: {isOnline}");
        if (student == null)
        {
            Console.WriteLine("学员信息为空，使用创建部门账户");
            return await GetCreateDeptAccountId(createJxDeptId, isOnline);
        }

        var config = await DbContext.Db.Queryable<TenantConfigEntity>()
            .Where(m => m.TenantId == tenantId && m.ConfigKey == CateGoryConst.Config_JX_PAYID_RULE && m.IsDelete == false)
            .SingleAsync();

        string payIdRule = config?.ConfigValue ?? "1";
        Console.WriteLine($"获取到支付规则: {payIdRule}");

        switch (payIdRule)
        {
            case "1":
                Console.WriteLine("使用学员报名点账户");
                return await GetStudentDeptAccountId(student.JxDeptId, isOnline);
            case "2":
                Console.WriteLine("使用学员销售门店账户");
                return await GetSaleDeptAccountId(student.SaleJxDeptId, student.SaleUserId, isOnline);
            case "3":
                Console.WriteLine("使用学员训练场账户");
                return await GetStudentFieldAccountId(student.JxFieldId, isOnline);
            case "4":
                Console.WriteLine("使用推荐人训练场账户");
                return await GetSaleUserFieldAccountId(student.SaleUserId, isOnline);
            case "5":
                Console.WriteLine("使用收款报名点账户");
                return await GetCreateDeptAccountId(createJxDeptId, isOnline);
            case "6":
                Console.WriteLine("使用推荐人报名点账户");
                return await GetSaleUserDeptAccountId(student.SaleUserId, isOnline);
            default:
                throw Oops.Bah("请先配置选择收款账号调用规则");
        }
    }

    /// <summary>
    /// 获取创建部门的支付账户ID
    /// </summary>
    /// <param name="createJxDeptId">创建部门ID</param>
    /// <param name="isOnline">是否是线上支付</param>
    /// <returns>支付账户ID</returns>
    /// <exception cref="Exception">当部门为空或未配置支付账户时抛出异常</exception>
    /// <remarks>
    /// 根据是否是线上支付返回相应的支付账户ID
    /// </remarks>
    private async Task<Guid> GetCreateDeptAccountId(Guid createJxDeptId, bool isOnline)
    {
        Console.WriteLine($"获取创建部门账户ID - 部门ID: {createJxDeptId}, 是否是线上: {isOnline}");
        var createJxDept = await DbContext.Db.Queryable<JxDeptEntity>().Where(m => m.Id == createJxDeptId).SingleAsync();
        if (createJxDept == null)
            throw Oops.Bah("当前缴费的报名点为空");

        var accountId = isOnline ? createJxDept.OnlineAccountId : createJxDept.OfflineAccountId;
        Console.WriteLine($"获取到账户ID: {accountId}");
        if (accountId == Guid.Empty)
        {
            throw Oops.Bah($"请先配置 {createJxDept.Name} 报名点的 {(isOnline ? "线上" : "线下")}支付的账号");
        }
        return accountId;
    }

    /// <summary>
    /// 获取学员所属部门的支付账户ID
    /// </summary>
    /// <param name="jxDeptId">部门ID</param>
    /// <param name="isOnline">是否是线上支付</param>
    /// <returns>支付账户ID</returns>
    /// <exception cref="Exception">当部门为空或未配置支付账户时抛出异常</exception>
    /// <remarks>
    /// 根据是否是线上支付返回学员所属部门的支付账户ID
    /// </remarks>
    private async Task<Guid> GetStudentDeptAccountId(Guid jxDeptId, bool isOnline)
    {
        Console.WriteLine($"获取学员部门账户ID - 部门ID: {jxDeptId}, 是否是线上: {isOnline}");
        var jxDept = await DbContext.Db.Queryable<JxDeptEntity>().Where(m => m.Id == jxDeptId).SingleAsync();
        if (jxDept == null)
            throw Oops.Bah("当前学员的报名点为空");

        var accountId = isOnline ? jxDept.OnlineAccountId : jxDept.OfflineAccountId;
        Console.WriteLine($"获取到账户ID: {accountId}");
        if (accountId == Guid.Empty)
        {
            throw Oops.Bah($"请先配置 {jxDept.Name} 报名点的 {(isOnline ? "线上" : "线下")}支付的账号");
        }
        return accountId;
    }

    /// <summary>
    /// 获取销售部门的支付账户ID
    /// </summary>
    /// <param name="saleJxDeptId">销售部门ID</param>
    /// <param name="saleUserId">销售用户ID</param>
    /// <param name="isOnline">是否是线上支付</param>
    /// <returns>支付账户ID</returns>
    /// <exception cref="Exception">当销售部门为空时抛出异常</exception>
    /// <remarks>
    /// 优先使用销售部门的支付账户，如果未配置则尝试使用推荐人报名点账户
    /// </remarks>
    private async Task<Guid> GetSaleDeptAccountId(Guid saleJxDeptId, Guid saleUserId, bool isOnline)
    {
        Console.WriteLine($"获取销售部门账户ID - 销售部门ID: {saleJxDeptId}, 销售用户ID: {saleUserId}, 是否是线上: {isOnline}");
        var saleJxDept = await DbContext.Db.Queryable<JxDeptEntity>().Where(m => m.Id == saleJxDeptId).FirstAsync();

        if (saleJxDept == null)
        {
            Console.WriteLine("销售部门账户为空，使用推荐人报名点账户");
            return await GetSaleUserDeptAccountId(saleUserId, isOnline);
        }
        else
        {
            var accountId = isOnline ? saleJxDept.OnlineAccountId : saleJxDept.OfflineAccountId;
            Console.WriteLine($"获取到账户ID: {accountId}");
            if (accountId != Guid.Empty)
                return accountId;

            throw Oops.Bah($"请先配置 {saleJxDept.Name} 报名点的 {(isOnline ? "线上" : "线下")}支付的账号");
        }
    }

    /// <summary>
    /// 获取学员训练场的支付账户ID
    /// </summary>
    /// <param name="jxFieldId">训练场ID</param>
    /// <param name="isOnline">是否是线上支付</param>
    /// <returns>支付账户ID</returns>
    /// <exception cref="Exception">当训练场为空或未配置支付账户时抛出异常</exception>
    /// <remarks>
    /// 根据是否是线上支付返回学员所属训练场的支付账户ID
    /// </remarks>
    private async Task<Guid> GetStudentFieldAccountId(Guid jxFieldId, bool isOnline)
    {
        Console.WriteLine($"获取学员训练场账户ID - 训练场ID: {jxFieldId}, 是否是线上: {isOnline}");
        var jxField = await DbContext.Db.Queryable<JxFieldEntity>().Where(m => m.Id == jxFieldId).SingleAsync();
        if (jxField == null)
            throw Oops.Bah("当前学员的训练场为空");

        var accountId = isOnline ? jxField.OnlineAccountId : jxField.OfflineAccountId;
        Console.WriteLine($"获取到账户ID: {accountId}");
        if (accountId == Guid.Empty)
        {
            throw Oops.Bah($"请先配置 {jxField.Name} 训练场的 {(isOnline ? "线上" : "线下")}支付的账号");
        }
        return accountId;
    }

    /// <summary>
    /// 获取销售用户所属训练场的支付账户ID
    /// </summary>
    /// <param name="saleUserId">销售用户ID</param>
    /// <param name="isOnline">是否是线上支付</param>
    /// <returns>支付账户ID</returns>
    /// <exception cref="Exception">当销售用户信息不完整或训练场未配置支付账户时抛出异常</exception>
    /// <remarks>
    /// 获取销售用户(推荐人)所属训练场的支付账户ID
    /// </remarks>
    private async Task<Guid> GetSaleUserFieldAccountId(Guid saleUserId, bool isOnline)
    {
        Console.WriteLine($"获取销售用户训练场账户ID - 销售用户ID: {saleUserId}, 是否是线上: {isOnline}");
        var saleJxUser = await DbContext.Db.Queryable<JxUserEntity>().Where(m => m.Id == saleUserId).SingleAsync();
        if (saleJxUser == null)
            throw Oops.Bah("请先配置推荐人账号的相关信息");

        var saleJxField = await DbContext.Db.Queryable<JxFieldEntity>().Where(m => m.Id == saleJxUser.JxFieldId).SingleAsync();
        if (saleJxField == null)
            throw Oops.Bah("当前推荐人所属的训练场为空");

        var accountId = isOnline ? saleJxField.OnlineAccountId : saleJxField.OfflineAccountId;
        Console.WriteLine($"获取到账户ID: {accountId}");
        if (accountId == Guid.Empty)
        {
            throw Oops.Bah($"请先配置 {saleJxField.Name} 训练场的 {(isOnline ? "线上" : "线下")}支付的账号");
        }
        return accountId;
    }

    /// <summary>
    /// 获取推荐人报名点的支付账户ID
    /// </summary>
    /// <param name="saleUserId">推荐人用户ID</param>
    /// <param name="isOnline">是否是线上支付</param>
    /// <returns>支付账户ID</returns>
    /// <exception cref="Exception">当推荐人信息不完整或报名点未配置支付账户时抛出异常</exception>
    /// <remarks>
    /// 获取推荐人所属报名点的支付账户ID
    /// </remarks>
    private async Task<Guid> GetSaleUserDeptAccountId(Guid saleUserId, bool isOnline)
    {
        Console.WriteLine($"获取推荐人报名点账户ID - 推荐人ID: {saleUserId}, 是否是线上: {isOnline}");
        var saleJxUser = await DbContext.Db.Queryable<JxUserEntity>().Where(m => m.Id == saleUserId).SingleAsync();
        if (saleJxUser == null)
            throw Oops.Bah("请先配置推荐人账号的相关信息");

        var saleJxDept = await DbContext.Db.Queryable<JxDeptEntity>().Where(m => m.Id == saleJxUser.JxDeptId).SingleAsync();
        if (saleJxDept == null)
            throw Oops.Bah("当前推荐人所属的报名点为空");

        var accountId = isOnline ? saleJxDept.OnlineAccountId : saleJxDept.OfflineAccountId;
        Console.WriteLine($"获取到账户ID: {accountId}");
        if (accountId == Guid.Empty)
        {
            throw Oops.Bah($"请先配置 {saleJxDept.Name} 报名点的 {(isOnline ? "线上" : "线下")}支付的账号");
        }
        return accountId;
    }

    #endregion
}
