using System.Security.Cryptography;
using System.Text;

using Newtonsoft.Json;
using PandaServer.System.Pay.FuBei.Models;
using PandaServer.System.Pay.FuBei.Models.Parameter;
using PandaServer.System.Services.Hnjx;
using PandaServer.System.Services.Pay.Base;

namespace PandaServer.System.Pay.FuBei;

/// <summary>
///     <inheritdoc cref="IHnjxPayCallBackService" />
/// </summary>
public class HnjxPayCallBackService : IHnjxPayCallBackService, ITransient
{
    private readonly IHnjxPayService _hnjxPayService;
    private readonly IPayTypeService _payTypeService;
    private readonly ISimpleCacheService _simpleCacheService;

    public HnjxPayCallBackService(IHnjxPayService hnjxPayService, IPayTypeService payTypeService, ISimpleCacheService simpleCacheService)
    {
        _hnjxPayService = hnjxPayService;
        _payTypeService = payTypeService;
        _simpleCacheService = simpleCacheService;
    }

    /// <inheritdoc />
    public async Task<bool> CallBack(FubeiNotificationParam p, string sign)
    {
        var data = JsonConvert.DeserializeObject<FuBeiOrderResult>(p.Data);

        var order = await GetOrderByMerchantOrderSn(data.MerchantOrderSn);
        if (order == null)
        {
            return false;
        }

        var account = await GetPayAccount(order.AccountId);
        ValidateNotification(p, sign, account);

        if (!data.OrderStatus.Equals("SUCCESS", StringComparison.OrdinalIgnoreCase))
        {
            throw Oops.Bah("非法数据");
        }

        await UpdateOrder(order, account, data.FinishTime.ParseToDateTime(), data.TotalAmount.ParseToDecimal(), data.PayType, data.MerchantOrderSn, data.OrderSn, data.ChannelOrderSn, data.InsOrderSn);

        order.PayTime = data.FinishTime.ParseToDateTime();
        order.ActPayMoney = data.TotalAmount.ParseToDecimal();
        order.MerchantOrderSn = data.MerchantOrderSn;
        order.OrderSn = data.OrderSn;
        order.ChannelOrderSn = data.ChannelOrderSn;
        order.InsOrderSn = data.InsOrderSn;

        await _simpleCacheService.Set($"{CacheConst.Cache_PayOrder_Single}:{order.Id}", order);


        return true;
    }

    /// <summary>
    /// 获取订单信息
    /// </summary>
    private async Task<OrderEntity> GetOrderByMerchantOrderSn(string merchantOrderSn)
    {
        var currentYearTable = "pay_order_" + DateTime.Now.ToString("yyyy0101");
        var order = await DbContext.Db.Queryable<OrderEntity>()
            .AS(currentYearTable)
            .Where(m => m.MerchantOrderSn == merchantOrderSn)
            .SingleAsync();

        if (order == null)
        {
            var lastYearTable = "pay_order_" + DateTime.Now.AddYears(-1).ToString("yyyy0101");
            order = await DbContext.Db.Queryable<OrderEntity>()
                .AS(lastYearTable)
                .Where(m => m.MerchantOrderSn == merchantOrderSn)
                .SingleAsync();
        }

        return order;
    }

    /// <summary>
    /// 获取支付账户信息
    /// </summary>
    private async Task<PayAccountEntity> GetPayAccount(Guid accountId)
    {
        return await DbContext.Db.Queryable<PayAccountEntity>()
            .Where(m => m.Id == accountId)
            .SingleAsync();
    }

    /// <summary>
    /// 验证付呗通知
    /// </summary>
    private void ValidateNotification(FubeiNotificationParam p, string sign, PayAccountEntity account)
    {
        if (string.IsNullOrEmpty(p.Data) || string.IsNullOrEmpty(sign) || string.IsNullOrEmpty(account.AppSecret))
        {
            throw Oops.Bah("参数不完整");
        }

        var signStr = new StringBuilder();
        signStr.Append($"data={p.Data}&");
        signStr.Append($"result_code={p.ResultCode}&");
        signStr.Append($"result_message={p.ResultMessage}");
        signStr.Append(account.AppSecret);

        string calculatedSign = MD5(signStr.ToString()).ToUpper();

        bool valid = calculatedSign.Equals(sign, StringComparison.OrdinalIgnoreCase);

        if (!valid)
        {
            throw Oops.Bah("签名验证失败");
        }

        // 验证返回码
        if (p.ResultCode != "200")
        {
            throw Oops.Bah($"支付回调失败: {p.ResultMessage}");
        }
    }

    private static string MD5(string input)
    {
        using var md5 = global::System.Security.Cryptography.MD5.Create();
        var bytes = Encoding.UTF8.GetBytes(input);
        var hash = md5.ComputeHash(bytes);
        var result = BitConverter.ToString(hash).Replace("-", "").ToLower();
        return result;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateOrder(OrderEntity order, PayAccountEntity account, DateTime payTime,
        decimal actPayMoney, string payChannel, string merchantOrderSn, string orderSn, string channelOrderSn,
        string insOrderSn)
    {
        var details = await DbContext.Db.Queryable<OrderDetailEntity>().SplitTable(st => st)
            .Where(m => m.OrderId == order.Id)
            .ToListAsync();

        var payTypeId = Guid.Empty;
        switch (account.PayMethod)
        {
            case PayMethodEnum.FuBei:
                payTypeId = await _payTypeService.GetOrAddIdByName("付呗支付", order.TenantId);
                break;
            case PayMethodEnum.JlPay:
                payTypeId = await _payTypeService.GetOrAddIdByName("嘉联支付", order.TenantId);
                break;
        }

        var couponPayTypeId = await _payTypeService.GetOrAddIdByName("优惠", order.TenantId);

        foreach (var item in details)
            switch (item.OutTable)
            {
                case PayOutTableEnum.HnjxPayOrderDetail:
                    {
                        await _hnjxPayService.HnjxPayOrderDetailCompleteCallBack(item, payTime, payTypeId, merchantOrderSn,
                            orderSn, channelOrderSn, insOrderSn);
                    }
                    break;
                case PayOutTableEnum.HnjxPayOrder:
                    {
                        await _hnjxPayService.HnjxPayOrderCompleteCallBack(item, payTime, payTypeId, merchantOrderSn,
                            orderSn, channelOrderSn, insOrderSn);
                    }
                    break;
            }

        order.ActPayMoney = actPayMoney;
        order.PayTime = payTime;

        order.OrderStatus = OrderStatusEnum.Success;

        if (payChannel.Equals("wxPay", StringComparison.OrdinalIgnoreCase))
            order.PayChannel = PayChannelEnum.WxPay;
        if (payChannel.Equals("aliPay", StringComparison.OrdinalIgnoreCase))
            order.PayChannel = PayChannelEnum.AliPay;
        if (payChannel.Equals("unionPay", StringComparison.OrdinalIgnoreCase))
            order.PayChannel = PayChannelEnum.UnionPay;

        if (!string.IsNullOrEmpty(merchantOrderSn))
            order.MerchantOrderSn = merchantOrderSn;
        if (!string.IsNullOrEmpty(orderSn))
            order.OrderSn = orderSn;
        if (!string.IsNullOrEmpty(channelOrderSn))
            order.ChannelOrderSn = channelOrderSn;
        if (!string.IsNullOrEmpty(insOrderSn))
            order.InsOrderSn = insOrderSn;

        await DbContext.Db.Updateable(order).SplitTable().ExecuteCommandAsync();
        var orderTenant = order.Adapt<OrderTenantEntity>();
        await DbContext.Db.Updateable(orderTenant).SplitTable().ExecuteCommandAsync();

        // Cache the order ID for 30 minutes
        await _simpleCacheService.Set($"{CacheConst.Cache_PayOrder}:{order.Id}", order.Id, TimeSpan.FromMinutes(30));

        return true;
    }
}
