using System;
using System.Text;
using System.Security.Cryptography;
using Newtonsoft.Json;
using PandaServer.System.Services.Pay;
using PandaServer.System.Pay.FuBei.Dtos.Notify;
using PandaServer.System.Pay.FuBei.Models;
// using PandaServer.System.Services.Pay.Dto.FuBei.Parameter;
// using PandaServer.System.Services.Pay.Dto.FuBei.Notify;

namespace PandaServer.System.Pay.FuBei;

public class FuBeiCallBackService : IFuBeiCallBackService, ITransient
{
    private readonly IOrderService _orderService;
    private readonly ISimpleCacheService _simpleCacheService;

    public FuBeiCallBackService(IOrderService orderService, ISimpleCacheService simpleCacheService)
    {
        _orderService = orderService;
        _simpleCacheService = simpleCacheService;
    }

    /// <inheritdoc />
    public async Task<bool> CallBack(FubeiNotificationParam p, string sign)
    {
        var data = JsonConvert.DeserializeObject<FuBeiOrderResult>(p.Data);

        var order = await _orderService.GetQuickByMerchantOrderSn(data.MerchantOrderSn);

        var account = await DbContext.Db.Queryable<PayAccountEntity>().Where(m => m.Id == order.AccountId).SingleAsync();

        // 构建签名验证
        var signStr = new StringBuilder();
        signStr.Append($"data={p.Data}&");
        signStr.Append($"result_code={p.ResultCode}&");
        signStr.Append($"result_message={p.ResultMessage}");
        signStr.Append(account.AppSecret); // 添加密钥

        string calculatedSign = MD5(signStr.ToString()).ToUpper();
        bool valid = calculatedSign.Equals(sign, StringComparison.OrdinalIgnoreCase);

        if (!valid)
            throw Oops.Bah("非法数据");

        var startTine = DateTime.Now;

        if (!data.OrderStatus.Equals("SUCCESS", StringComparison.OrdinalIgnoreCase))
            throw Oops.Bah("非法数据");

        if (await _orderService.UpdateOrder(order, account, data.FinishTime.ParseToDateTime(), data.TotalAmount.ParseToDecimal(), data.PayType, data.MerchantOrderSn, data.OrderSn, data.ChannelOrderSn, data.InsOrderSn))
        {
            order.PayTime = data.FinishTime.ParseToDateTime();
            order.ActPayMoney = data.TotalAmount.ParseToDecimal();
            order.MerchantOrderSn = data.MerchantOrderSn;
            order.OrderSn = data.OrderSn;
            order.ChannelOrderSn = data.ChannelOrderSn;
            order.InsOrderSn = data.InsOrderSn;

            await _simpleCacheService.Set($"{CacheConst.Cache_PayOrder_Single}:{order.Id}", order);

            Console.WriteLine($"回调的执行耗时 {(DateTime.Now - startTine).TotalMilliseconds}");
            return true;
        }

        return false;
    }

    private static string MD5(string input)
    {
        using var md5 = global::System.Security.Cryptography.MD5.Create();
        var bytes = Encoding.UTF8.GetBytes(input);
        var hash = md5.ComputeHash(bytes);
        return BitConverter.ToString(hash).Replace("-", "").ToLower();
    }
}
