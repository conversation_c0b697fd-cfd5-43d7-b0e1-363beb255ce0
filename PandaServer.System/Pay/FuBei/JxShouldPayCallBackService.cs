using Newtonsoft.Json;
using PandaServer.System.Pay.FuBei.Models.Parameter;
using PandaServer.System.Services.Student;
using PandaServer.System.Services.Config;
using PandaServer.System.Services.SystemSecurity;
using PandaServer.System.Entity.Student.Pay;
using PandaServer.System.Services.Student.Pay;
using PandaServer.System.Services.Student.Dto.Pay;
using PandaServer.System.Pay.FuBei.Models;
using System;
using System.Text;
using System.Security.Cryptography;

namespace PandaServer.System.Pay.FuBei;

/// <summary>
/// 付呗支付回调服务
/// </summary>
public class JxShouldPayCallBackService : IJxShouldPayCallBackService, ITransient
{
    private readonly IJxStudentPayService _jxStudentPayService;
    private readonly ISimpleCacheService _simpleCacheService;

    public JxShouldPayCallBackService(ISimpleCacheService simpleCacheService, IJxStudentPayService jxStudentPayService)
    {
        _jxStudentPayService = jxStudentPayService;
        _simpleCacheService = simpleCacheService;
    }

    #region 支付回调处理

    /// <summary>
    /// 处理付呗支付回调
    /// </summary>
    public async Task<List<JxPayEntity>> CallBack(FubeiNotificationParam p, string sign)
    {
        var data = JsonConvert.DeserializeObject<FuBeiOrderResult>(p.Data);

        var order = await GetOrderByMerchantOrderSn(data.MerchantOrderSn);

        if (order == null)
        {
            return null;
        }

        var account = await GetPayAccount(order.AccountId);

        ValidateNotification(p, sign, account);

        if (!data.OrderStatus.Equals("SUCCESS", StringComparison.OrdinalIgnoreCase))
        {
            throw Oops.Bah("非法数据");
        }

        var jxPays = await UpdateOrder(order, account, data.FinishTime.ParseToDateTime(),
            data.TotalAmount.ParseToDecimal(), data.PayType, data.MerchantOrderSn,
            data.OrderSn, data.ChannelOrderSn, data.InsOrderSn);


        order.PayTime = data.FinishTime.ParseToDateTime();
        order.ActPayMoney = data.TotalAmount.ParseToDecimal();
        order.MerchantOrderSn = data.MerchantOrderSn;
        order.OrderSn = data.OrderSn;
        order.ChannelOrderSn = data.ChannelOrderSn;
        order.InsOrderSn = data.InsOrderSn;

        await _simpleCacheService.Set($"{CacheConst.Cache_PayOrder_Single}:{order.Id}", order);

        return jxPays;
    }

    /// <summary>
    /// 获取订单信息
    /// </summary>
    private async Task<OrderEntity> GetOrderByMerchantOrderSn(string merchantOrderSn)
    {
        var currentYearTable = "pay_order_" + DateTime.Now.ToString("yyyy0101");

        var order = await DbContext.Db.Queryable<OrderEntity>()
            .AS(currentYearTable)
            .Where(m => m.MerchantOrderSn == merchantOrderSn)
            .SingleAsync();

        if (order == null)
        {
            var lastYearTable = "pay_order_" + DateTime.Now.AddYears(-1).ToString("yyyy0101");

            order = await DbContext.Db.Queryable<OrderEntity>()
                .AS(lastYearTable)
                .Where(m => m.MerchantOrderSn == merchantOrderSn)
                .SingleAsync();
        }

        return order;
    }

    /// <summary>
    /// 获取支付账户信息
    /// </summary>
    private async Task<PayAccountEntity> GetPayAccount(Guid accountId)
    {
        var account = await DbContext.Db.Queryable<PayAccountEntity>()
            .Where(m => m.Id == accountId)
            .SingleAsync();
        return account;
    }

    /// <summary>
    /// 验证付呗通知
    /// </summary>
    private void ValidateNotification(FubeiNotificationParam p, string sign, PayAccountEntity account)
    {
        var signStr = new StringBuilder();
        signStr.Append($"data={p.Data}&");
        signStr.Append($"result_code={p.ResultCode}&");
        signStr.Append($"result_message={p.ResultMessage}");
        signStr.Append(account.AppSecret);

        string calculatedSign = MD5(signStr.ToString()).ToUpper();

        bool valid = calculatedSign.Equals(sign, StringComparison.OrdinalIgnoreCase);

        if (!valid)
        {
            throw Oops.Bah("非法数据");
        }
    }

    private static string MD5(string input)
    {
        using var md5 = global::System.Security.Cryptography.MD5.Create();
        var bytes = Encoding.UTF8.GetBytes(input);
        var hash = md5.ComputeHash(bytes);
        var result = BitConverter.ToString(hash).Replace("-", "").ToLower();
        return result;
    }

    #endregion

    #region 订单更新处理

    /// <summary>
    /// 更新订单信息
    /// </summary>
    public async Task<List<JxPayEntity>> UpdateOrder(OrderEntity order, PayAccountEntity account,
        DateTime payTime, decimal actPayMoney, string payChannel, string merchantOrderSn,
        string orderSn, string channelOrderSn, string insOrderSn)
    {
        var payTypeId = await CreateOrGetPayType(order, account);

        var jxPays = await ProcessOrderDetails(order, account, payTime, payTypeId,
            merchantOrderSn, orderSn, channelOrderSn, insOrderSn);

        await UpdateOrderStatus(order, actPayMoney, payTime, payChannel,
            merchantOrderSn, orderSn, channelOrderSn, insOrderSn);

        return jxPays;
    }

    /// <summary>
    /// 创建或获取支付类型
    /// </summary>
    private async Task<Guid> CreateOrGetPayType(OrderEntity order, PayAccountEntity account)
    {
        var payTypeName = GetPayTypeName(account.PayMethod);

        var payTypes = await DbContext.Db.Queryable<PayTypeEntity>()
            .Where(m => m.Name == payTypeName && m.IsDelete == false && m.TenantId == order.TenantId)
            .ToListAsync();

        if (payTypes.Count > 0)
        {
            return payTypes[0].Id;
        }

        return await CreateNewPayType(order, payTypeName);
    }

    /// <summary>
    /// 获取支付类型名称
    /// </summary>
    private string GetPayTypeName(PayMethodEnum payMethod)
    {
        var result = payMethod switch
        {
            PayMethodEnum.FuBei => "付呗支付",
            _ => throw new Exception("不支持的支付方式")
        };
        return result;
    }

    /// <summary>
    /// 创建新的支付类型
    /// </summary>
    private async Task<Guid> CreateNewPayType(OrderEntity order, string payTypeName)
    {
        var payType = new PayTypeEntity()
        {
            Id = Guid.NewGuid(),
            Name = payTypeName,
            SortCode = 1,
            TenantId = order.TenantId,
            CreateTime = DateTime.Now,
            CreateUserId = order.CreateUserId,
            UpdateTime = DateTime.Now,
            UpdateUserId = order.UpdateUserId,
            IsDelete = false,
            Remark = ""
        };

        await DbContext.Db.Insertable(payType).ExecuteCommandAsync();
        return payType.Id;
    }

    /// <summary>
    /// 处理订单明细
    /// </summary>
    private async Task<List<JxPayEntity>> ProcessOrderDetails(OrderEntity order, PayAccountEntity account,
        DateTime payTime, Guid payTypeId, string merchantOrderSn, string orderSn,
        string channelOrderSn, string insOrderSn)
    {
        var jxPays = new List<JxPayEntity>();
        string orderDetailTable = "pay_order_detial_" + order.CreateTime.ToString("yyyy0101");

        var details = await DbContext.Db.Queryable<OrderDetailEntity>()
            .AS(orderDetailTable)
            .Where(m => m.OrderId == order.Id)
            .ToListAsync();

        foreach (var detail in details)
        {
            if (detail.OutTable == PayOutTableEnum.JxShouldPay || detail.OutTable == PayOutTableEnum.JxCostType)
            {
                try
                {
                    var pay = await JxShouldPayCompleteCallBack(order, detail, account, payTime,
                        payTypeId, merchantOrderSn, orderSn, channelOrderSn, insOrderSn);
                    if (pay != null && pay.Count > 0)
                    {
                        jxPays.AddRange(pay);
                    }
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
            else
            {
                throw Oops.Bah("回调页面错误");
            }
        }

        return jxPays;
    }

    /// <summary>
    /// 更新订单状态
    /// </summary>
    private async Task UpdateOrderStatus(OrderEntity order, decimal actPayMoney, DateTime payTime,
        string payChannel, string merchantOrderSn, string orderSn, string channelOrderSn, string insOrderSn)
    {
        order.ActPayMoney = actPayMoney;
        order.PayTime = payTime;
        order.OrderStatus = OrderStatusEnum.Success;
        order.PayChannel = GetPayChannel(payChannel);

        UpdateOrderReferences(order, merchantOrderSn, orderSn, channelOrderSn, insOrderSn);

        await DbContext.Db.Updateable(order).SplitTable().ExecuteCommandAsync();

        var orderTenant = order.Adapt<OrderTenantEntity>();
        await DbContext.Db.Updateable(orderTenant).SplitTable().ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取支付渠道
    /// </summary>
    private PayChannelEnum GetPayChannel(string payChannel)
    {
        PayChannelEnum result;
        if (payChannel.Equals("wxPay", StringComparison.OrdinalIgnoreCase))
            result = PayChannelEnum.WxPay;
        else if (payChannel.Equals("aliPay", StringComparison.OrdinalIgnoreCase))
            result = PayChannelEnum.AliPay;
        else if (payChannel.Equals("unionPay", StringComparison.OrdinalIgnoreCase))
            result = PayChannelEnum.UnionPay;
        else
            result = PayChannelEnum.Unknown;

        return result;
    }

    /// <summary>
    /// 更新订单引用信息
    /// </summary>
    private void UpdateOrderReferences(OrderEntity order, string merchantOrderSn,
        string orderSn, string channelOrderSn, string insOrderSn)
    {
        if (!string.IsNullOrEmpty(merchantOrderSn))
        {
            order.MerchantOrderSn = merchantOrderSn;
        }
        if (!string.IsNullOrEmpty(orderSn))
        {
            order.OrderSn = orderSn;
        }
        if (!string.IsNullOrEmpty(channelOrderSn))
        {
            order.ChannelOrderSn = channelOrderSn;
        }
        if (!string.IsNullOrEmpty(insOrderSn))
        {
            order.InsOrderSn = insOrderSn;
        }
    }

    #endregion

    #region 支付完成回调处理

    /// <summary>
    /// 处理教学应付款支付完成回调
    /// </summary>
    public async Task<List<JxPayEntity>> JxShouldPayCompleteCallBack(OrderEntity order,
        OrderDetailEntity detail, PayAccountEntity account, DateTime payTime, Guid payTypeId,
        string merchantOrderSn = "", string orderSn = "", string channelOrderSn = "", string insOrderSn = "")
    {
        var payTable = "student_pay_" + order.TenantId.ToString().Replace("-", "_").ToLower();

        if (await IsPaymentExists(payTable, detail, order.TenantId))
        {
            return null;
        }

        var pay = CreatePayInput(order, detail, account, payTime, payTypeId,
            merchantOrderSn, orderSn, channelOrderSn, insOrderSn);

        var result = await _jxStudentPayService.Pay(pay);
        return result;
    }

    /// <summary>
    /// 检查支付是否已存在
    /// </summary>
    private async Task<bool> IsPaymentExists(string payTable, OrderDetailEntity detail, Guid tenantId)
    {
        var exists = await DbContext.Db.Queryable<JxPayEntity>()
            .AS(payTable)
            .Where(m => m.Id == detail.Id && m.TenantId == tenantId && m.IsDelete == false)
            .CountAsync() > 0;
        return exists;
    }

    /// <summary>
    /// 创建支付输入对象
    /// </summary>
    private JxPayInPut CreatePayInput(OrderEntity order, OrderDetailEntity detail, PayAccountEntity account,
        DateTime payTime, Guid payTypeId, string merchantOrderSn, string orderSn,
        string channelOrderSn, string insOrderSn)
    {
        var pay = new JxPayInPut
        {
            Id = detail.Id,
            TenantId = order.TenantId,
            CreateTime = DateTime.Now,
            CreateUserId = order.CreateUserId,
            JxShouldPayId = detail.OutTable == PayOutTableEnum.JxShouldPay ? detail.OutId : Guid.Empty,
            CostTypeId = detail.OutTable == PayOutTableEnum.JxCostType ? detail.OutId : Guid.Empty,
            CreateJxDeptId = order.CreateJxDeptId,
            PayMoney = detail.PayMoney,
            PayTypeId = payTypeId,
            StudentId = order.StudentId,
            PayTime = payTime < Convert.ToDateTime("2000-01-01") ? DateTime.Now : payTime,
            IsDelete = false,
            UserId = order.UserId,
            CarId = order.CarId,
            PayeePerson = order.PayeePerson,
            NoteNumber = order.NoteNumber,
            MerchantOrderSn = merchantOrderSn,
            OrderSn = orderSn,
            ChannelOrderSn = channelOrderSn,
            InsOrderSn = insOrderSn,
            OrderDetailId = detail.Id,
            OrderId = detail.OrderId,
            ImportOutId = string.Empty,
            Remark = $" 支付订单号：{(!string.IsNullOrEmpty(insOrderSn) ? insOrderSn : (!string.IsNullOrEmpty(merchantOrderSn) ? merchantOrderSn : orderSn))}  {detail.Remark}"
        };

        pay.JxPayIds = new List<Guid> { detail.Id };
        pay.ComputerAccountIds = new List<Guid> { account.ComputerAccountId };
        pay.PayTypeIds = new List<Guid> { payTypeId };
        pay.PayMoneys = new List<decimal> { detail.PayMoney };

        return pay;
    }

    #endregion
}
