﻿using PandaServer.System.Services.Pay.Dto;
using PandaServer.System.Services.Student.Dto.Pay;

namespace PandaServer.System.Pay.FuBei;

/// <summary>
/// 驾校相关的 订单支付 的服务
/// </summary>
public interface IJxOrderPayService
{
    /// <summary>
    /// 创建订单
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<PayOutPut> CreateOrder(JxPayInPut inPut);

    /// <summary>
    /// 不生成订单的 直接支付
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<PayOutPut> OnlinePay(JxPayInPut inPut);

    /// <summary>
    /// 通过学员信息 来获取支付的 Id
    /// </summary>
    /// <param name="studentId"></param>
    /// <param name="createJxDeptId">收款的报名点Id</param>
    /// <param name="isOnline"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<Guid> GetAccountId(Guid studentId, Guid createJxDeptId, bool isOnline, Guid tenantId);

    /// <summary>
    /// 通过学员信息 来获取支付的 Id
    /// </summary>
    /// <param name="student"></param>
    /// <param name="createJxDeptId">收款的报名点Id</param>
    /// <param name="isOnline"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<Guid> GetAccountId(JxStudentEntity student, Guid createJxDeptId, bool isOnline, Guid tenantId);
}
