﻿using PandaServer.System.Entity.Student.Pay;

namespace PandaServer.Core.Pay.Success;

public class UpdateData
{
    /// <summary>
    /// </summary>
    /// <param name="db"></param>
    /// <param name="order"></param>
    /// <param name="account"></param>
    /// <param name="payTime"></param>
    /// <param name="actPayMoney"></param>
    /// <param name="payChannel"></param>
    /// <param name="merchantOrderSn"></param>
    /// <param name="orderSn"></param>
    /// <param name="channelOrderSn"></param>
    /// <param name="insOrderSn"></param>
    /// <returns></returns>
    public async Task<bool> Update
    (
        ISqlSugarClient db,
        OrderEntity order,
        PayAccountEntity account,
        DateTime payTime,
        decimal actPayMoney,
        string payChannel,
        string merchantOrderSn,
        string orderSn,
        string channelOrderSn,
        string insOrderSn
    )
    {
        try
        {
            var details = db.Queryable<OrderDetailEntity>().SplitTable(st => st).Where(m => m.OrderId == order.Id)
                .ToList();

            var payTypeId = Guid.Empty;
            if (account.PayMethod == PayMethodEnum.FuBei) payTypeId = GetPayTypeId(db, "付呗支付", order.TenantId);

            var couponPayTypeId = GetPayTypeId(db, "优惠", order.TenantId);

            foreach (var item in details)
                if (item.OutTable == PayOutTableEnum.FreePay)
                {
                    var CostTypeId = GetCostTypeId(db, "自由付款", order.TenantId);

                    var jxFreePay = new JxFreePay();
                    jxFreePay.PayComplete(db, order, item, account, actPayMoney, payTime, payTypeId, CostTypeId,
                        order.TenantId, merchantOrderSn, orderSn, channelOrderSn, insOrderSn);
                }
                else if (item.OutTable == PayOutTableEnum.JxCostType)
                {
                    var jxCostType = new JxCostType();
                    jxCostType.PayComplete(db, order, item, item.Id, account, payTime, payTypeId, merchantOrderSn,
                        orderSn, channelOrderSn, insOrderSn);
                }
                else if (item.OutTable == PayOutTableEnum.JxShouldPay)
                {
                    var shouldPay = new StudentShouldPay();
                    await shouldPay.PayCompleteAsync(db, order, item, account, payTime, payTypeId, couponPayTypeId,
                        merchantOrderSn, orderSn, channelOrderSn, insOrderSn);
                }
                else if (item.OutTable == PayOutTableEnum.StudentPayOrder)
                {
                    var studentPayOrder = new StudentPayOrder();
                    await studentPayOrder.PayCompleteAsync(db, order, item, account, payTime, payTypeId,
                        merchantOrderSn, orderSn, channelOrderSn, insOrderSn);
                }
                else if (item.OutTable == PayOutTableEnum.HnjxPayOrderDetail)
                {
                    var hnjxPayOrder = new HnjxPayOrderDetail();
                    hnjxPayOrder.PayComplete(db, order, item, account, payTime, payTypeId, merchantOrderSn, orderSn,
                        channelOrderSn, insOrderSn);
                }
                else if (item.OutTable == PayOutTableEnum.HnjxPayOrder)
                {
                    var hnjxPayOrder = new HnjxPayOrder();
                    hnjxPayOrder.PayComplete(db, order, item, account, payTime, payTypeId, merchantOrderSn, orderSn,
                        channelOrderSn, insOrderSn);
                }
                else if (item.OutTable == PayOutTableEnum.ChildPayOrder)
                {
                    var childPayOrder = new ChildPayOrder();
                    if (item.ActPayMoney > 0)
                        await childPayOrder.PayCompleteAsync(db, order, item, item.Id, account, payTime, payTypeId,
                            item.ActPayMoney, merchantOrderSn, orderSn, channelOrderSn, insOrderSn);

                    //当前交易 有优惠  需要按照 优惠的方式录入系统
                    if (item.PayMoney - item.ActPayMoney > 0)
                        await childPayOrder.PayCompleteAsync(db, order, item, Guid.NewGuid(), account, payTime,
                            couponPayTypeId, item.PayMoney - item.ActPayMoney, merchantOrderSn, orderSn,
                            channelOrderSn, insOrderSn);
                }
                else if (item.OutTable == PayOutTableEnum.ChildCostType)
                {
                    var childCostType = new ChildCostType();
                    childCostType.PayComplete(db, order, item, item.Id, account, payTime, payTypeId,
                        item.ActPayMoney, merchantOrderSn, orderSn, channelOrderSn, insOrderSn);
                }

            order.ActPayMoney = actPayMoney;
            order.PayTime = payTime;

            if (payChannel.Equals("wxPay", StringComparison.OrdinalIgnoreCase))
                order.PayChannel = PayChannelEnum.WxPay;
            if (payChannel.Equals("aliPay", StringComparison.OrdinalIgnoreCase))
                order.PayChannel = PayChannelEnum.AliPay;
            if (payChannel.Equals("unionPay", StringComparison.OrdinalIgnoreCase))
                order.PayChannel = PayChannelEnum.UnionPay;

            order.OrderSn = orderSn;
            order.ChannelOrderSn = channelOrderSn;
            order.InsOrderSn = insOrderSn;

            db.Updateable(order).SplitTable(st => st).ExecuteCommand();

            return true;
        }
        catch (Exception ex)
        {
            var log = new EasyLogEntity();
            log.Id = Guid.NewGuid();
            log.CreateTime = DateTime.Now;
            log.Log = ex.Message + ex.StackTrace;

            db.Insertable(log).SplitTable().ExecuteCommand();

            throw Oops.Bah("更新数据失败:" + ex.Message);
        }
    }


    /// <summary>
    ///     更新缓存
    /// </summary>
    /// <param name="db"></param>
    /// <param name="StudentId"></param>
    public async Task UpdateStudentMoney(ISqlSugarClient db, Guid StudentId)
    {
        var shouldPayDatas = await db.Queryable<JxShouldPayEntity>().SplitTable(st => st)
            .Where(m => m.StudentId == StudentId).ToListAsync();

        decimal NoPay = 0;
        foreach (var shouldPayData in shouldPayDatas)
        {
            var PayMoney = db.Queryable<JxPayEntity>().SplitTable(st => st)
                .Where(m => m.JxShouldPayId == shouldPayData.Id && m.IsDelete == false).Sum(m => m.PayMoney);

            if (shouldPayData.DiscountMoney > 0 && !await db.Queryable<JxPayEntity>().SplitTable(st => st)
                    .AnyAsync(m => m.JxShouldPayId == shouldPayData.Id && m.IsDelete == false))
            {
                shouldPayData.NoPay = shouldPayData.PayMoney - shouldPayData.DiscountMoney;
                NoPay += shouldPayData.NoPay;
                await db.Updateable(shouldPayData).SplitTable(st => st).ExecuteCommandAsync();
            }
            else if (shouldPayData.NoPay != shouldPayData.PayMoney - PayMoney)
            {
                shouldPayData.NoPay = shouldPayData.PayMoney - PayMoney;
                NoPay += shouldPayData.NoPay;
                await db.Updateable(shouldPayData).SplitTable(st => st).ExecuteCommandAsync();
            }
        }

        var student = db.Queryable<JxStudentEntity>().SplitTable(st => st).Single(m => m.Id == StudentId);

        if (student != null)
        {
            student.NoPay = NoPay;

            db.Updateable(student).SplitTable().ExecuteCommand();
        }
    }

    /// <summary>
    ///     获取 支付方式的 编号
    /// </summary>
    /// <param name="db"></param>
    /// <param name="PayTypeName"></param>
    /// <param name="TenantId"></param>
    /// <returns></returns>
    public Guid GetPayTypeId
    (
        ISqlSugarClient db,
        string PayTypeName,
        Guid TenantId
    )
    {
        var PayTypeId = Guid.Empty;
        var payType = db.Queryable<PayTypeEntity>()
            .Single(m => m.Name == PayTypeName && m.TenantId == TenantId && m.IsDelete == false);

        if (payType == null)
        {
            payType = new PayTypeEntity();
            payType.Create();
            payType.TenantId = TenantId;
            payType.Name = PayTypeName;
            payType.ThirdPay = true;
            payType.Remark = "";

            if (db.Insertable(payType).ExecuteCommand() > 0) PayTypeId = payType.Id;
        }
        else
        {
            PayTypeId = payType.Id;
        }

        return PayTypeId;
    }

    /// <summary>
    ///     获取 费用类型的 编号
    /// </summary>
    /// <param name="db"></param>
    /// <param name="CostTypeName"></param>
    /// <param name="TenantId"></param>
    /// <returns></returns>
    public Guid GetCostTypeId
    (
        ISqlSugarClient db,
        string CostTypeName,
        Guid TenantId
    )
    {
        var CostTypeId = Guid.Empty;
        var costType = db.Queryable<CostTypeEntity>()
            .Single(m => m.Name == CostTypeName && m.TenantId == TenantId && m.IsDelete == false);

        if (costType == null)
        {
            costType = new CostTypeEntity();
            costType.Create();
            costType.TenantId = TenantId;
            costType.Name = CostTypeName;
            costType.Remark = "";

            if (db.Insertable(costType).ExecuteCommand() > 0) CostTypeId = costType.Id;
        }
        else
        {
            CostTypeId = costType.Id;
        }

        return CostTypeId;
    }
}