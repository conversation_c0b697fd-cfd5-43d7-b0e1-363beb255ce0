// AuthService.cs
using PandaServer.System.Services.Auth.Dto;
using PandaServer.System.Services.Config;
using PandaServer.System.Services.Hnjx;
using PandaServer.System.Services.Student;
using PandaServer.System.Services.SystemManage.Dtos;
using PandaServer.System.Services.SystemOrganize;
using PandaServer.System.Services.SystemSecurity;
using PandaServer.System.Services.Wx;

namespace PandaServer.System.Services.Auth;

/// <summary>
/// 认证服务主类
/// </summary>
public partial class AuthService : IAuthService, ITransient
{
    private readonly IConfigService _configService;
    // private readonly IFilterIpService _filterIpService;
    // private readonly IHnjxCompanyService _hnjxCompanyService;
    private readonly ISimpleCacheService _simpleCacheService;
    private readonly ITenantService _tenantService;
    private readonly IUserLogOnService _userLogOnService;

    private readonly IUserInfoService _userInfoService;
    private readonly IWxUserService _wxUserService;

    private readonly IJxStudentService _jxStudentService;

    /// <summary>
    /// 构造函数，注入所需服务
    /// </summary>
    public AuthService(
        ISimpleCacheService simpleCacheService,
        IConfigService configService,
        IUserLogOnService userLogOnService,
        IWxUserService wxUserService,
        IUserInfoService userInfoService,
        ITenantService tenantService,
        IJxStudentService jxStudentService
        )
    {
        _simpleCacheService = simpleCacheService;
        _configService = configService;
        _userLogOnService = userLogOnService;
        _wxUserService = wxUserService;
        _userInfoService = userInfoService;
        _tenantService = tenantService;
        _jxStudentService = jxStudentService;
    }

    /// <summary>
    /// 用户登录方法
    /// </summary>
    /// <param name="inPut">登录输入参数</param>
    /// <param name="loginClientType">登录客户端类型</param>
    /// <returns>登录输出结果</returns>
    public async Task<LoginOutPut> Login(LoginInput inPut, LoginClientTypeEnum loginClientType)
    {
        switch (loginClientType)
        {
            case LoginClientTypeEnum.B:
                return await ExecLoginB(inPut, loginClientType);
            case LoginClientTypeEnum.C:
                return await ExecLoginC(inPut, loginClientType);
            case LoginClientTypeEnum.D:
                return await ExecLoginD(inPut, loginClientType);
            case LoginClientTypeEnum.E:
                return await ExecLoginE(inPut, loginClientType);
            case LoginClientTypeEnum.W:
                return await ExecLoginW(inPut, loginClientType);
            default:
                throw Oops.Bah("系统不支持当前的登录状态");
        }
    }

    /// <summary>
    /// 获取当前登录用户信息
    /// </summary>
    /// <returns>登录用户详细信息</returns>
    public async Task<LoginUserOutput> GetLoginUser()
    {
        var user = await DbContext.Db.Queryable<UserEntity>().Where(ct => ct.Id == UserManager.UserId).FirstAsync();

        var loginOutput = new LoginUserOutput();
        loginOutput.Sex = user.Sex;
        loginOutput.Name = user.RealName;
        loginOutput.Account = user.Account;
        loginOutput.Birthday = user.Birthday;
        loginOutput.Id = user.Id;

        // 根据性别设置默认头像
        loginOutput.Avatar = loginOutput.Sex == 2
            ? "https://cdn.51panda.com/female_woman_person_people_avatar_user_white_tone_icon_159359.png"
            : "https://cdn.51panda.com/male_man_people_person_avatar_white_tone_icon_159363.png";

        // 更新最后登录时间
        loginOutput.LastLoginTime = user.UserLogOn.LastLoginTime = DateTime.Now;
        loginOutput.ip = WebHelper.Ip;

        // 获取租户信息
        var tenant = await DbContext.Db.Queryable<TenantEntity>().Where(ct => ct.Id == user.TenantId).FirstAsync();

        loginOutput.TenantType = tenant.TenantType;
        loginOutput.TenantName = tenant.TenantName;
        loginOutput.CityId = tenant.CityId;
        loginOutput.PlusEndTime = tenant.PlusEndTime;

        return loginOutput;
    }

    /// <summary>
    /// 刷新用户令牌
    /// </summary>
    /// <param name="inPut">登录输入参数</param>
    /// <param name="loginClientType">登录客户端类型</param>
    /// <returns>登录输出结果</returns>
    public async Task<LoginOutPut> RefreshToken(LoginInput inPut, LoginClientTypeEnum loginClientType)
    {
        // 生成新的访问令牌，包含用户信息
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, inPut.UserId },
            { ClaimConst.Avatar, inPut.Avatar },
            { ClaimConst.UserSysId, inPut.UserSysId },
            { ClaimConst.Phone, inPut.Phone },
            { ClaimConst.Account, inPut.Account },
            { ClaimConst.RealName, inPut.RealName },
            { ClaimConst.CompanyName, inPut.CompanyName },
            { ClaimConst.IsTenantAdmin, inPut.IsTenantAdmin },
            { ClaimConst.IsSuperAdmin, false },
            { ClaimConst.TenantId, inPut.TenantId },
            { ClaimConst.TenantName, inPut.TenantName },
            { ClaimConst.TenantType, inPut.TenantType },
            { ClaimConst.PlusEndTime, inPut.PlusEndTime },
            { ClaimConst.ServiceEndTime, inPut.ServiceEndTime },
            { ClaimConst.FieldId, inPut.FieldId },
        });

        // 获取令牌过期时间并生成刷新令牌
        var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, expire * 2);
        App.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);

        // 创建登录事件对象
        var logingEvent = new LoginEvent
        {
            Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
            Device = inPut.Device,
            Expire = expire,
            User = new LoginInput
            {
                UserId = inPut.UserId,
                TenantId = inPut.TenantId,
                OpenId = inPut.OpenId
            },
            Token = accessToken
        };

        // 将令牌写入Redis缓存
        await WriteTokenToRedis(logingEvent, loginClientType);

        return new LoginOutPut
        {
            UserId = inPut.UserId,
            StudentId = inPut.StudentId
        };
    }

    /// <summary>
    /// 当前用户登出
    /// </summary>
    /// <param name="loginClientType">登录客户端类型</param>
    public async Task LoginOut(LoginClientTypeEnum loginClientType)
    {
        var userId = UserManager.UserId;
        var token = HttpNewUtil.GetHeader("Authorization");
        await LoginOut(token, userId, loginClientType);
    }

    /// <summary>
    /// 指定用户登出
    /// </summary>
    /// <param name="token">用户令牌</param>
    /// <param name="userId">用户ID</param>
    /// <param name="loginClientType">登录客户端类型</param>
    public async Task LoginOut(string token, Guid userId, LoginClientTypeEnum loginClientType)
    {
        // if (string.IsNullOrEmpty(token))
        //     throw Oops.Bah("参数 Token 异常，请刷新网页重试");

        switch (loginClientType)
        {
            case LoginClientTypeEnum.B:
                await HandleBLogout(token, userId);
                break;
            case LoginClientTypeEnum.C:
            case LoginClientTypeEnum.W:
                await HandleCWLogout(token, userId);
                break;
            default:
                throw Oops.Bah("传入参数错误，请先传入登录类型");
        }
    }

    /// <summary>
    /// 处理B端用户登出
    /// </summary>
    /// <param name="token">用户令牌</param>
    /// <param name="userId">用户ID</param>
    private async Task HandleBLogout(string token, Guid userId)
    {
        var userinfo = await DbContext.Db.Queryable<UserEntity>().Where(ct => ct.Id == userId).FirstAsync();
        if (userinfo == null)
            throw Oops.Bah("会话已经超时");

        var loginEvent = new LoginEvent
        {
            Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
            User = userinfo.Adapt<LoginInput>(),
            Token = token
        };

        await RemoveTokenFromRedis(loginEvent, LoginClientTypeEnum.B);
    }

    /// <summary>
    /// 处理C端和微信端用户登出
    /// </summary>
    /// <param name="token">用户令牌</param>
    /// <param name="userId">用户ID</param>
    private async Task HandleCWLogout(string token, Guid userId)
    {
        var loginEvent = new LoginEvent
        {
            Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
            User = new LoginInput { UserId = userId },
            Token = token
        };

        // 更新微信用户ID为空
        await _wxUserService.UpdateUserId(HttpNewUtil.GetHeader("openId"), Guid.Empty, Guid.Empty);
        await RemoveTokenFromRedis(loginEvent, LoginClientTypeEnum.C);
    }

    /// <summary>
    /// 将令牌信息写入Redis缓存
    /// </summary>
    /// <param name="loginEvent">登录事件</param>
    /// <param name="loginClientType">登录客户端类型</param>
    private async Task WriteTokenToRedis(LoginEvent loginEvent, LoginClientTypeEnum loginClientType)
    {
        Console.WriteLine($"开始写入Token到Redis - UserId: {loginEvent.User.UserId}, OpenId: {loginEvent.User.OpenId}");
        var tokenInfo = new TokenInfo
        {
            Token = loginEvent.Token,
            TokenTimeout = DateTime.Now.AddMinutes(loginEvent.Expire),
            LoginClientType = loginClientType
        };

        var tokenInfos = await GetTokenInfos(loginEvent, loginClientType);
        // Console.WriteLine($"获取到的现有Token数量: {tokenInfos?.Count ?? 0}");

        // 如果是微信登录，只保留最新的token
        if (loginClientType == LoginClientTypeEnum.W)
        {
            tokenInfos = new List<TokenInfo> { tokenInfo };
        }
        else
        {
            if (tokenInfos != null)
            {
                // 移除同类型的旧token
                tokenInfos = tokenInfos.Where(t => t.LoginClientType != loginClientType).ToList();
                tokenInfos.Add(tokenInfo);
            }
            else
            {
                tokenInfos = new List<TokenInfo> { tokenInfo };
            }
        }

        var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
        string cacheKey = string.IsNullOrEmpty(loginEvent.User.OpenId)
            ? loginEvent.User.UserId.ToString()
            : loginEvent.User.OpenId.ToString();

        // Console.WriteLine($"准备写入Redis - Key: {cacheKey}, Token数量: {tokenInfos.Count}");

        await _simpleCacheService.HashAdd(CacheConst.Cache_UserToken, cacheKey, tokenInfos, TimeSpan.FromMinutes(expire));
        Console.WriteLine("Token写入Redis完成");
    }

    /// <summary>
    /// 从Redis缓存中移除令牌
    /// </summary>
    /// <param name="loginEvent">登录事件</param>
    /// <param name="loginClientType">登录客户端类型</param>
    private async Task RemoveTokenFromRedis(LoginEvent loginEvent, LoginClientTypeEnum loginClientType)
    {
        var tokenInfos = await GetTokenInfos(loginEvent, loginClientType);
        if (tokenInfos != null)
        {
            // 查找并移除指定的令牌
            var token = tokenInfos.FirstOrDefault(it =>
                it.Token == loginEvent.Token &&
                it.LoginClientType == loginClientType);

            if (token != null)
                tokenInfos.Remove(token);

            // 更新Redis缓存
            if (tokenInfos.Count > 0)
            {
                var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
                await _simpleCacheService.HashAdd(CacheConst.Cache_UserToken, loginEvent.User.UserId.ToString(), tokenInfos, TimeSpan.FromMinutes(expire));
            }
            else
                await _simpleCacheService.HashDel<List<TokenInfo>>(CacheConst.Cache_UserToken, loginEvent.User.UserId.ToString());
        }
    }

    /// <summary>
    /// 获取用户的令牌信息列表
    /// </summary>
    /// <param name="loginEvent">登录事件</param>
    /// <param name="loginClientType">登录客户端类型</param>
    /// <returns>令牌信息列表</returns>
    private async Task<List<TokenInfo>> GetTokenInfos(LoginEvent loginEvent, LoginClientTypeEnum loginClientType)
    {
        string userId = loginEvent.User.UserId.ToString();

        if (loginClientType == LoginClientTypeEnum.W && !string.IsNullOrEmpty(loginEvent.User.OpenId))
        {
            userId = loginEvent.User.OpenId;
        }

        Console.WriteLine("userId:" + userId);
        var tokenInfos = await _simpleCacheService.HashGetOne<List<TokenInfo>>(CacheConst.Cache_UserToken, userId.ToString());
        if (tokenInfos != null)
        {
            // 过滤掉已过期的令牌
            tokenInfos = tokenInfos.Where(it => it.TokenTimeout > DateTime.Now).ToList();
        }
        return tokenInfos;
    }
}