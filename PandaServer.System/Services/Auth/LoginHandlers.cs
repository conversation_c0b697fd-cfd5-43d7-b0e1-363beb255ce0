// LoginHandlers.cs
using System.Threading.Tasks;
using Newtonsoft.Json;
using PandaServer.System.Services.Auth.Dto;
using PandaServer.System.Services.SystemManage.Dtos;
using PandaServer.System.Services.SystemOrganize.Dtos;

namespace PandaServer.System.Services.Auth;

public partial class AuthService
{
    /// <summary>
    /// 处理B类型登录请求
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> ExecLoginB(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        Console.WriteLine($"开始执行B类型登录 - URL: {App.HttpContext.Request.Path}");
        Console.WriteLine($"登录账号: {input.Account}, 用户ID: {input.UserId}");
        // 处理已有UserId的情况
        if (input.UserId != Guid.Empty)
        {
            return await HandleExistingUserLogin(input, loginClientType);
        }

        // 根据租户类型分发处理
        return input.TenantId == TenantIdConst.HnjxTenantId
            ? await HandleHnjxLogin(input, loginClientType)
            : await HandleNormalLogin(input, loginClientType);
    }

    /// <summary>
    /// 处理已存在UserId的用户登录
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> HandleExistingUserLogin(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        // 获取用户信息
        var user = await DbContext.Db.Queryable<UserEntity>()
            .Where(ct => ct.Id == input.UserId)
            .FirstAsync();

        if (user == null)
            throw Oops.Bah("用户不存在");

        // 清除用户缓存
        await ClearUserCache(user.Id);

        input.UserId = user.Id;
        return await ExecuteBLoginCore(input, loginClientType);
    }

    /// <summary>
    /// 处理湖南驾协用户登录
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> HandleHnjxLogin(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        // 查询符合条件的湖南驾协用户
        var users = await DbContext.Db.Queryable<UserEntity>()
            .LeftJoin<TenantEntity>((user, tenant) => user.TenantId == tenant.Id)
            .LeftJoin<HnjxCompanyEntity>((user, tenant, company) => user.Id == company.AdminUserId)
            .Where((user, tenant) => user.IsEnabled == true && user.IsDelete == false && tenant.IsDelete == false)
            .WhereIF(input.UserId != Guid.Empty, (user, tenant) => user.Id == input.UserId)
            .WhereIF(input.UserId == Guid.Empty, (user, tenant) => user.Account == input.Account
                || (!string.IsNullOrEmpty(user.Phone) && user.Phone == input.Account))
            .Where((user, tenant) => tenant.Id == TenantIdConst.HnjxTenantId && user.TenantId == TenantIdConst.HnjxTenantId)
            .Select((user, tenant, company) => new
            {
                user.Id,
                user.Account,
                tenant.TenantName,
                company.FullName,
                company.ShortName,
                tenant.PlusEndTime,
                tenant.ServiceEndTime
            })
            .ToListAsync();

        return await HandleLoginResult(users, input, loginClientType, true);
    }

    /// <summary>
    /// 处理普通用户登录
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> HandleNormalLogin(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        // 查询符合条件的普通用户
        var users = await DbContext.Db.Queryable<UserEntity>()
            .LeftJoin<TenantEntity>((user, tenant) => user.TenantId == tenant.Id)
            .Where((user, tenant) => user.IsEnabled == true && user.IsDelete == false
                // && (tenant.ServiceEndTime > DateTime.Now || tenant.PlusEndTime > DateTime.Now)
                && tenant.IsDelete == false)
            .WhereIF(input.UserId != Guid.Empty, (user, tenant) => user.Id == input.UserId)
            .WhereIF(input.UserId == Guid.Empty, (user, tenant) => user.Account == input.Account
                || (!string.IsNullOrEmpty(user.Phone) && user.Phone == input.Account))
            .WhereIF(input.TenantId != Guid.Empty, (user, tenant) => tenant.Id == input.TenantId)
            .Select((user, tenant) => new UserOutPut
            {
                Id = user.Id,
                Account = user.Account,
                TenantName = tenant.TenantName,
                PlusEndTime = tenant.PlusEndTime,
                ServiceEndTime = tenant.ServiceEndTime
            })
            .ToListAsync();

        // 添加日志输出当前账号
        Console.WriteLine($"当前登录账号: {input.Account}");

        return await HandleLoginResult(users, input, loginClientType, false);
    }

    /// <summary>
    /// 处理登录查询结果
    /// </summary>
    /// <typeparam name="T">用户数据类型</typeparam>
    /// <param name="users">查询到的用户列表</param>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <param name="isHnjx">是否是湖南驾协用户</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> HandleLoginResult<T>(List<T> users, LoginInput input, LoginClientTypeEnum loginClientType, bool isHnjx)
    {
        if (users.Count == 0 || users[0] == null)
        {
            throw Oops.Bah("用户不存在 或者 未被启用");
        }

        if (users.Count > 1)
        {
            return CreateMultiUserOutput(users, isHnjx);
        }

        var user = users[0].Adapt<UserOutPut>();

        if (user.PlusEndTime < DateTime.Now && user.ServiceEndTime < DateTime.Now)
        {
            throw Oops.Bah("当前账户已到期");
        }

        await ClearUserCache(user.Id);

        input.UserId = user.Id;


        return await ExecuteBLoginCore(input, loginClientType);
    }

    /// <summary>
    /// 清除用户相关缓存
    /// </summary>
    /// <param name="userId">用户ID</param>
    private async Task ClearUserCache(Guid userId)
    {
        await _simpleCacheService.HashDel<UserEntity>(CacheConst.Cache_User, userId.ToString());
        await _simpleCacheService.HashDel<List<MenuOutPut<string>>>(CacheConst.Cache_UserMenu, userId.ToString());
        await _simpleCacheService.HashDel<LoginUserOutput>(CacheConst.Cache_LoginUser, userId.ToString());
    }

    /// <summary>
    /// 创建多用户选择输出
    /// </summary>
    /// <typeparam name="T">用户数据类型</typeparam>
    /// <param name="users">用户列表</param>
    /// <param name="isHnjx">是否是湖南驾协用户</param>
    /// <returns>登录输出结果</returns>
    private LoginOutPut CreateMultiUserOutput<T>(List<T> users, bool isHnjx)
    {
        var outPut = new LoginOutPut { Users = new List<UserEntity>() };

        foreach (var user in users)
        {
            if (isHnjx)
            {
                var dynamicUser = user.Adapt<UserOutPut>();
                outPut.Users.Add(new UserEntity
                {
                    Id = dynamicUser.Id,
                    Account = string.IsNullOrEmpty(dynamicUser.ShortName)
                        ? dynamicUser.FullName
                        : dynamicUser.ShortName
                });
            }
            else
            {
                outPut.Users.Add(user.Adapt<UserEntity>());
            }
        }

        return outPut;
    }

    /// <summary>
    /// B类型登录的核心处理方法
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> ExecuteBLoginCore(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        // 获取用户信息
        var user = await _userInfoService.GetById(input.UserId);

        // 验证用户状态
        if (user == null)
            throw Oops.Bah("用户数据为空，刷新页面重试");

        if (!user.IsEnabled)
            throw Oops.Bah("当前账户被禁用");

        if (user.IsDelete)
            throw Oops.Bah("当前账户信息被删除");

        if (user.UserLogOn == null)
            throw Oops.Bah("请联系管理员初始化登录数据");

        // 验证密码
        if (!string.IsNullOrEmpty(input.PassWord) && !_userLogOnService.ValidPassWord(user.UserLogOn, input.PassWord))
            throw Oops.Bah("账号密码错误");

        var ip = WebHelper.Ip;

        if (!user.IsEnabled)
            throw Oops.Bah("账号已停用");

        // 验证租户状态
        var tenant = await _tenantService.GetById(user.TenantId);

        if (tenant == null)
            throw Oops.Bah("当前用户所属公司账户异常");

        if (tenant.PlusEndTime < DateTime.Now && tenant.ServiceEndTime < DateTime.Now)
            throw Oops.Bah("所属公司账户已到期");

        // 生成JWT令牌
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, user.Id },
            { ClaimConst.UserSysId, user.SysId },
            { ClaimConst.Avatar, user.Avatar },
            { ClaimConst.Phone, user.Phone },
            { ClaimConst.Account, user.Account },
            { ClaimConst.RealName, user.RealName },
            { ClaimConst.CompanyName, user.CompanyName },
            { ClaimConst.IsTenantAdmin, user.IsAdmin },
            { ClaimConst.IsSuperAdmin, false },
            { ClaimConst.TenantId, tenant.Id },
            { ClaimConst.TenantName, tenant.TenantName },
            { ClaimConst.TenantType, tenant.TenantType },
            { ClaimConst.PlusEndTime, tenant.PlusEndTime },
            { ClaimConst.ServiceEndTime, tenant.ServiceEndTime },
            { ClaimConst.FieldId, input.FieldId },
        });

        // 设置令牌
        var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, expire * 2);
        App.HttpContext.SigninToSwagger(accessToken);
        App.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);

        // 记录登录事件
        var logingEvent = new LoginEvent
        {
            Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
            Device = input.Device,
            Expire = expire,
            User = input,
            Token = accessToken
        };

        await WriteTokenToRedis(logingEvent, loginClientType);

        var users = new List<UserEntity> { user };

        return new LoginOutPut
        {
            Users = users,
            Account = user.Account,
            RealName = user.RealName
        };
    }

    /// <summary>
    /// 处理C类型登录请求
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> ExecLoginC(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        Console.WriteLine($"开始执行C类型登录 - URL: {App.HttpContext.Request.Path}");
        Console.WriteLine($"用户ID: {input.UserId}, 租户ID: {input.TenantId}");
        // 获取并验证用户信息
        var user = await _userInfoService.GetById(input.UserId);
        if (user == null)
            throw Oops.Bah("用户数据为空，刷新页面重试");

        if (!user.IsEnabled)
            throw Oops.Bah("当前账户被禁用");

        if (user.IsDelete)
            throw Oops.Bah("当前账户信息被删除");

        // 验证租户状态
        var tenant = await _tenantService.GetById(user.TenantId);

        if (tenant == null)
            throw Oops.Bah("当前用户所属公司账户异常");

        if (tenant.PlusEndTime < DateTime.Now)
            throw Oops.Bah("所属公司账户已到期");

        // 生成JWT令牌
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, user.Id },
            { ClaimConst.UserSysId, user.SysId },
            { ClaimConst.Avatar, user.Avatar },
            { ClaimConst.Phone, user.Phone },
            { ClaimConst.Account, user.Account },
            { ClaimConst.RealName, user.RealName },
            { ClaimConst.CompanyName, user.CompanyName },
            { ClaimConst.IsTenantAdmin, user.IsAdmin },
            { ClaimConst.IsSuperAdmin, false },
            { ClaimConst.TenantId, user.TenantId },
            { ClaimConst.TenantName, tenant.TenantName },
            { ClaimConst.TenantType, tenant.TenantType },
            { ClaimConst.PlusEndTime, tenant.PlusEndTime },
            { ClaimConst.ServiceEndTime, tenant.ServiceEndTime },
            { ClaimConst.FieldId, input.FieldId },
        });

        // 设置令牌
        var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, expire * 2);
        App.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);

        // 记录登录事件
        var logingEvent = new LoginEvent
        {
            Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
            Device = input.Device,
            Expire = expire,
            User = input,
            Token = accessToken
        };

        await WriteTokenToRedis(logingEvent, loginClientType);

        // 更新微信用户信息
        if (!string.IsNullOrEmpty(input.OpenId))
            await _wxUserService.UpdateUserId(HttpNewUtil.GetHeader("openId"), input.UserId, input.TenantId);

        return new LoginOutPut
        {
            UserId = input.UserId,
            StudentId = input.StudentId
        };
    }

    /// <summary>
    /// 处理D类型登录请求
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> ExecLoginD(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        Console.WriteLine($"开始执行D类型登录 - URL: {App.HttpContext.Request.Path}");
        Console.WriteLine($"用户ID: {input.UserId}, 租户ID: {input.TenantId}, 真实姓名: {input.RealName}");
        // 验证租户状态
        var tenant = await _tenantService.GetById(input.TenantId);

        if (tenant == null)
            throw Oops.Bah("当前用户所属公司账户异常");

        if (tenant.PlusEndTime < DateTime.Now && tenant.ServiceEndTime < DateTime.Now)
            throw Oops.Bah("所属公司账户已到期");

        // 生成JWT令牌
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, input.UserId },
            { ClaimConst.RealName, input.RealName },
            { ClaimConst.IsTenantAdmin, false },
            { ClaimConst.IsSuperAdmin, false },
            { ClaimConst.TenantId, input.TenantId },
            { ClaimConst.TenantName, tenant.TenantName },
            { ClaimConst.TenantType, tenant.TenantType },
            { ClaimConst.PlusEndTime, tenant.PlusEndTime },
            { ClaimConst.ServiceEndTime, tenant.ServiceEndTime },
            { ClaimConst.FieldId, input.FieldId },
        });

        // 设置令牌
        var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, expire * 2);
        App.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);

        // 记录登录事件
        var logingEvent = new LoginEvent
        {
            Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
            Device = input.Device,
            Expire = expire,
            User = input,
            Token = accessToken
        };

        await WriteTokenToRedis(logingEvent, loginClientType);

        // 更新微信用户信息
        if (!string.IsNullOrEmpty(input.OpenId))
            await _wxUserService.UpdateUserId(HttpNewUtil.GetHeader("openId"), input.UserId, input.TenantId);

        return new LoginOutPut
        {
            UserId = input.UserId,
            StudentId = input.StudentId
        };
    }

    /// <summary>
    /// 处理E类型登录请求
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> ExecLoginE(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        Console.WriteLine($"开始执行E类型登录 - URL: {App.HttpContext.Request.Path}");
        Console.WriteLine($"用户ID: {input.UserId}, 租户ID: {input.TenantId}");
        // 验证租户状态
        var tenant = await _tenantService.GetById(input.TenantId);

        if (tenant == null)
            throw Oops.Bah("当前用户所属公司账户异常");

        Console.WriteLine("设备开始登录");
        Console.WriteLine("tenant.TenantName:" + tenant.TenantName);
        Console.WriteLine("UserId:" + input.UserId);

        // 生成JWT令牌
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, input.UserId },
            { ClaimConst.UserSysId, 0 },
            { ClaimConst.Avatar,  "" },
            { ClaimConst.Phone, "" },
            { ClaimConst.Account, "" },
            { ClaimConst.RealName, "" },
            { ClaimConst.CompanyName, "" },
            { ClaimConst.IsTenantAdmin, false },
            { ClaimConst.IsSuperAdmin, false },
            { ClaimConst.TenantId, input.TenantId },
            { ClaimConst.TenantName, tenant.TenantName },
            { ClaimConst.TenantType, tenant.TenantType },
            { ClaimConst.PlusEndTime, tenant.PlusEndTime },
            { ClaimConst.ServiceEndTime, tenant.ServiceEndTime },
            { ClaimConst.FieldId, input.FieldId },
        });

        // 设置令牌
        var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, expire * 2);
        App.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);

        // 记录登录事件
        var logingEvent = new LoginEvent
        {
            Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
            Device = input.Device,
            Expire = expire,
            User = input,
            Token = accessToken
        };

        await WriteTokenToRedis(logingEvent, loginClientType);

        // 更新微信用户信息
        if (!string.IsNullOrEmpty(input.OpenId))
            await _wxUserService.UpdateUserId(HttpNewUtil.GetHeader("openId"), input.UserId, input.TenantId);

        return new LoginOutPut
        {
            UserId = input.UserId,
            StudentId = input.StudentId
        };
    }

    /// <summary>
    /// 处理微信登录请求
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> ExecLoginW(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        // 获取微信用户信息
        var wxUser = await _wxUserService.GetByOpenId(input.OpenId);

        // 根据租户类型处理登录
        if (HttpNewUtil.GetHeader("appId") == "wx7065d977094dcbc4")
        {
            return await HandleHnjxWxLogin(input, loginClientType);
        }
        else
        {
            if (wxUser == null)
            {
                throw Oops.Bah("当前微信未登录");
            }
            else if (input.UserId != Guid.Empty)
            {
                return await HandleNormalWxLogin(wxUser, input, loginClientType);
            }
            else if (input.StudentId != Guid.Empty)
            {
                Console.WriteLine("学员微信用户的登录 : " + input.OpenId);
                return await HandleStudentWxLogin(wxUser, input, loginClientType);
            }
            else
            {
                Console.WriteLine("空微信用户的登录 : " + input.OpenId);
                return await HandleEmptyWxLogin(input, loginClientType);
            }
        }
    }

    /// <summary>
    /// 处理湖南驾协微信登录
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> HandleHnjxWxLogin(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        // 获取租户信息并生成令牌
        var tenant = await _tenantService.GetById(TenantIdConst.HnjxTenantId);

        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, input.UserId },
            { ClaimConst.Avatar, "" },
            { ClaimConst.UserSysId, 0 },
            { ClaimConst.Phone, "" },
            { ClaimConst.Account, "" },
            { ClaimConst.RealName, input.RealName },
            { ClaimConst.CompanyName, input.CompanyName },
            { ClaimConst.CompanyId, input.CompanyId },
            { ClaimConst.IsTenantAdmin, false },
            { ClaimConst.IsSuperAdmin, false },
            { ClaimConst.TenantId, tenant.Id },
            { ClaimConst.TenantName, tenant.TenantName },
            { ClaimConst.TenantType, tenant.TenantType },
            { ClaimConst.PlusEndTime, Convert.ToDateTime("2099-01-01") },
            { ClaimConst.ServiceEndTime, Convert.ToDateTime("2099-01-01") },
            { ClaimConst.FieldId, Guid.Empty },
            { ClaimConst.OpenId, input.OpenId },
            { ClaimConst.UnionId, input.UnionId },
        });

        // 设置令牌
        var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, expire * 2);
        App.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);
        App.HttpContext.Response.Headers["X-openId"] = input.OpenId;

        // 记录登录事件
        var logingEvent = new LoginEvent
        {
            Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
            Device = input.Device,
            Expire = expire,
            User = new LoginInput
            {
                UserId = input.UserId,
                TenantId = input.TenantId,
                OpenId = input.OpenId
            },
            Token = accessToken
        };

        await WriteTokenToRedis(logingEvent, loginClientType);

        return new LoginOutPut
        {
            OpenId = input.OpenId,
            UserId = input.UserId,
            RealName = input.RealName,
        };
    }

    /// <summary>   
    /// 处理学生微信登录
    /// </summary>
    /// <param name="wxUser">微信用户信息</param>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> HandleStudentWxLogin(WxUserEntity wxUser, LoginInput input, LoginClientTypeEnum loginClientType)
    {
        var student = input.student;
        if (input.student == null)
            student = await _jxStudentService.GetById(wxUser.StudentId, wxUser.StudentTenantId);

        if (student == null)
        {
            await _wxUserService.UpdateStudentId(input.OpenId, Guid.Empty, Guid.Empty);
            return await HandleEmptyWxLogin(input, loginClientType);
        }
        else
        {
            // 验证租户状态
            var tenant = await _tenantService.GetById(wxUser.StudentTenantId);

            if (tenant.PlusEndTime < DateTime.Now.AddDays(-14))
            {
                await _wxUserService.UpdateUserId(input.OpenId, Guid.Empty, Guid.Empty);
                throw Oops.Bah("当前学员所属驾校参数异常");
            }

            // 生成JWT令牌
            var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
            {
                { ClaimConst.UserId, Guid.Empty },
                { ClaimConst.StudentId, wxUser.StudentId },
                { ClaimConst.Avatar, "" },
                { ClaimConst.UserSysId, 0 },
                { ClaimConst.Phone, "" },
                { ClaimConst.Account, "" },
                { ClaimConst.RealName, student.xm },
                { ClaimConst.CompanyName, "" },
                { ClaimConst.IsTenantAdmin, false },
                { ClaimConst.IsSuperAdmin, false },
                { ClaimConst.TenantId, tenant.Id },
                { ClaimConst.TenantName, tenant.TenantName },
                { ClaimConst.TenantType, tenant.TenantType },
                { ClaimConst.PlusEndTime, tenant.PlusEndTime },
                { ClaimConst.ServiceEndTime, tenant.ServiceEndTime },
                { ClaimConst.FieldId, input.FieldId },
                { ClaimConst.OpenId, input.OpenId },
                { ClaimConst.UnionId, input.UnionId },
            });

            // 设置令牌
            var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
            var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, expire * 2);
            App.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);
            App.HttpContext.Response.Headers["X-openId"] = input.OpenId;

            // 记录登录事件
            var logingEvent = new LoginEvent
            {
                Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
                Device = input.Device,
                Expire = expire,
                User = new LoginInput
                {
                    UserId = Guid.Empty,
                    TenantId = student.TenantId,
                    OpenId = input.OpenId
                },
                Token = accessToken
            };

            await WriteTokenToRedis(logingEvent, loginClientType);

            return new LoginOutPut
            {
                OpenId = input.OpenId,
                UserId = wxUser.UserId,
                StudentId = wxUser.StudentId,
                RealName = student.xm,
                TenantName = tenant.TenantName,
                TenantId = tenant.Id
            };
        }
    }

    /// <summary>
    /// 处理普通用户微信登录
    /// </summary>
    /// <param name="wxUser">微信用户信息</param>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> HandleNormalWxLogin(WxUserEntity wxUser, LoginInput input, LoginClientTypeEnum loginClientType)
    {
        string openId = HttpNewUtil.GetHeader("openId");
        if (string.IsNullOrEmpty(openId))
        {
            openId = input.OpenId;
        }
        // 获取并验证用户信息
        var user = await _userInfoService.GetById(wxUser.UserId);
        if (user == null || user.IsEnabled == false || user.IsDelete == true || user.DisableWxLogin == true)
        {
            await _wxUserService.UpdateUserId(openId, Guid.Empty, Guid.Empty);
            return await HandleEmptyWxLogin(input, loginClientType);
        }
        else
        {
            // 验证租户状态
            var tenant = await _tenantService.GetById(user.TenantId);
            Console.WriteLine("tenant.TenantName:" + tenant.TenantName);

            if (tenant.PlusEndTime < DateTime.Now)
            {
                await _wxUserService.UpdateUserId(openId, Guid.Empty, Guid.Empty);
                throw Oops.Bah("当前登录用户所属公司账户已到期");
            }

            // 生成JWT令牌
            var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
            {
                { ClaimConst.UserId, user.Id },
                { ClaimConst.Avatar, user.Avatar },
                { ClaimConst.UserSysId, user.SysId },
                { ClaimConst.Phone, user.Phone },
                { ClaimConst.Account, user.Account },
                { ClaimConst.RealName, user.RealName },
                { ClaimConst.CompanyName, user.CompanyName },
                { ClaimConst.IsTenantAdmin, user.IsAdmin },
                { ClaimConst.IsSuperAdmin, false },
                { ClaimConst.TenantId, tenant.Id },
                { ClaimConst.TenantName, tenant.TenantName },
                { ClaimConst.TenantType, tenant.TenantType },
                { ClaimConst.PlusEndTime, tenant.PlusEndTime },
                { ClaimConst.ServiceEndTime, tenant.ServiceEndTime },
                { ClaimConst.FieldId, input.FieldId },
                { ClaimConst.OpenId, input.OpenId },
                { ClaimConst.UnionId, input.UnionId },
            });

            // 设置令牌
            var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
            var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, expire * 2);
            App.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);
            App.HttpContext.Response.Headers["X-openId"] = input.OpenId;

            // 记录登录事件
            var logingEvent = new LoginEvent
            {
                Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
                Device = input.Device,
                Expire = expire,
                User = new LoginInput
                {
                    UserId = user.Id,
                    TenantId = user.TenantId,
                    OpenId = input.OpenId
                },
                Token = accessToken
            };

            await WriteTokenToRedis(logingEvent, loginClientType);

            return new LoginOutPut
            {
                OpenId = input.OpenId,
                UserId = wxUser.UserId,
                StudentId = wxUser.StudentId,
                RealName = user.RealName,
            };
        }
    }

    /// <summary>
    /// 处理空微信用户登录
    /// </summary>
    /// <param name="input">登录输入参数</param>
    /// <param name="loginClientType">客户端类型</param>
    /// <returns>登录输出结果</returns>
    private async Task<LoginOutPut> HandleEmptyWxLogin(LoginInput input, LoginClientTypeEnum loginClientType)
    {
        // 生成JWT令牌
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, Guid.Empty },
            { ClaimConst.UserSysId, 0 },
            { ClaimConst.Avatar,  "" },
            { ClaimConst.Phone, "" },
            { ClaimConst.Account, "" },
            { ClaimConst.RealName, "" },
            { ClaimConst.CompanyName, "" },
            { ClaimConst.IsTenantAdmin, false },
            { ClaimConst.IsSuperAdmin, false },
            { ClaimConst.TenantId, input.TenantId },
            { ClaimConst.TenantName, "" },
            { ClaimConst.TenantType, TenantTypeEnum.JiaXiao },
            { ClaimConst.PlusEndTime, "1900-01-01"},
            { ClaimConst.ServiceEndTime, "1900-01-01"},
            { ClaimConst.FieldId, Guid.Empty },
            { ClaimConst.OpenId, input.OpenId },
            { ClaimConst.UnionId, input.UnionId },
        });

        // 设置令牌
        var expire = App.GetConfig<int>("JWTSettings:ExpiredTime");
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, expire * 2);
        App.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);
        App.HttpContext.Response.Headers["X-openId"] = input.OpenId;

        // 记录登录事件
        var logingEvent = new LoginEvent
        {
            Ip = App.HttpContext.GetRemoteIpAddressToIPv4(),
            Device = input.Device,
            Expire = expire,
            User = input,
            Token = accessToken
        };

        await WriteTokenToRedis(logingEvent, loginClientType);

        return new LoginOutPut
        {
            OpenId = input.OpenId,
            UnionId = input.UnionId
        };
    }
}