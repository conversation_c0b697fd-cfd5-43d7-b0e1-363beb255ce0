using Newtonsoft.Json;

namespace PandaServer.System.Services.Auth.Dto;

public class AuthOutPut
{
}

/// <summary>
///     验证码返回
/// </summary>
public class PicValidCodeOutPut
{
    /// <summary>
    ///     验证码图片，Base64
    /// </summary>
    public string ValidCodeBase64 { get; set; }

    /// <summary>
    ///     验证码请求号
    /// </summary>
    public string ValidCodeReqNo { get; set; }
}

/// <summary>
///     登录返回参数
/// </summary>
public class LoginOutPut
{
    /// <summary>
    /// 
    /// </summary>
    public string OpenId { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string UnionId { get; set; }

    /// <summary>
    ///     账号
    /// </summary>
    public string Account { get; set; }

    /// <summary>
    ///     姓名
    /// </summary>
    public string RealName { get; set; }

    /// <summary>
    ///     用户Id
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    ///     学员Id
    /// </summary>
    public Guid StudentId { get; set; }


    /// <summary>
    ///     通过查找检索到多个用户的，返回前台去查找。
    /// </summary>
    public List<UserEntity> Users { get; set; }

    /// <summary>
    ///     租户名称
    /// </summary> 
    public string TenantName { get; set; }

    /// <summary>
    ///     租户Id
    /// </summary>
    public Guid TenantId { get; set; }
}

/// <summary>
///     登录用互信息
/// </summary>
public class LoginUserOutput
{
    /// <summary>
    ///     主键
    /// </summary>
    [JsonProperty("id")]
    public Guid Id { get; set; }

    /// <summary>
    ///     账号
    /// </summary>
    [JsonProperty("account")]
    public string Account { get; set; }

    /// <summary>
    ///     昵称
    /// </summary>
    [JsonProperty("nickName")]
    public string NickName { get; set; }

    /// <summary>
    ///     姓名
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    ///     头像
    /// </summary>
    [JsonProperty("avatar")]
    public string Avatar { get; set; }

    /// <summary>
    ///     生日
    /// </summary>
    [JsonProperty("birthday")]
    public DateTime Birthday { get; set; }

    /// <summary>
    ///     性别(字典 1男 2女)
    /// </summary>
    [JsonProperty("sex")]
    public int Sex { get; set; }

    /// <summary>
    ///     邮箱
    /// </summary>
    [JsonProperty("email")]
    public string Email { get; set; }

    /// <summary>
    ///     手机
    /// </summary>
    [JsonProperty("phone")]
    public string Phone { get; set; }

    /// <summary>
    ///     电话
    /// </summary>
    [JsonProperty("tel")]
    public string Tel { get; set; }

    /// <summary>
    ///     管理员类型（0超级管理员 1非管理员）
    /// </summary>
    [JsonProperty("adminType")]
    public int AdminType { get; set; }

    /// <summary>
    ///     最后登陆IP
    /// </summary>
    [JsonProperty("lastLoginIp")]
    public string LastLoginIp { get; set; }

    /// <summary>
    ///     最后登陆时间
    /// </summary>
    [JsonProperty("lastLoginTime")]
    public DateTime LastLoginTime { get; set; }

    /// <summary>
    ///     最后登陆地址
    /// </summary>
    [JsonProperty("lastLoginAddress")]
    public string LastLoginAddress { get; set; }

    /// <summary>
    ///     最后登陆所用浏览器
    /// </summary>
    [JsonProperty("lastLoginBrowser")]
    public string LastLoginBrowser { get; set; }

    /// <summary>
    ///     最后登陆所用系统
    /// </summary>
    [JsonProperty("lastLoginOs")]
    public string LastLoginOs { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    [JsonProperty("ip")]
    public string ip { get; set; }

    /// <summary>
    ///     租户类型
    /// </summary>
    /// <value></value>
    [JsonProperty("tenantType")]
    public TenantTypeEnum TenantType { get; set; }

    /// <summary>
    ///     租户名称
    /// </summary>
    /// <value></value>
    [JsonProperty("tenantName")]
    public string TenantName { get; set; }

    /// <summary>
    ///     所属城市
    /// </summary>
    [JsonProperty("cityId")]
    public int CityId { get; set; }

    /// <summary>
    ///     Plus 服务的结束时间
    /// </summary>
    /// <value></value>
    [JsonProperty("plusEndTime")]
    public DateTime PlusEndTime { get; set; }
}