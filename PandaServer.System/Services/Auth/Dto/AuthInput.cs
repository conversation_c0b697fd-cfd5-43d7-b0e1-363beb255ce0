namespace PandaServer.System.Services.Auth.Dto;

/// <summary>
///     权限认证输入
/// </summary>
public class AuthInput
{
    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string OpenId { get; set; }
}

public class ValidCodeInput
{
    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string OpenId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string UnionId { get; set; }

    /// <summary>
    ///     验证码
    /// </summary>
    public string ValidCode { get; set; }

    /// <summary>
    ///     请求号
    /// </summary>
    public string ValidCodeReqNo { get; set; }
}

/// <summary>
///     登录输入参数
/// </summary>
public class LoginInput : ValidCodeInput
{
    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string xm { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string sfzmhm { get; set; }

    /// <summary>
    ///     账号
    /// </summary>
    /// <example>superAdmin</example>
    /// =
    public string Account { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    /// <example>04fc514b346f14b23d7cf5e6f64663b030512aa380a9e7d311288ed1e8be7b863ae5ee0bb570df2405fc9daff2b2d1ac627a0fbbd49ef2c6b8fac4fd5e4b9a1b7120999bdc0a8e425aa37abab3aec6f9f3570775ff901f2845e957b0c2d0542e21fbf1bbb65c04</example>
    public string PassWord { get; set; }


    /// <summary>
    ///     前台绑定的租户的 Id
    /// </summary>
    public Guid TenantId { get; set; }

    /// <summary>
    /// 系统内部 的 所属 公司的 Id 
    /// </summary>
    public Guid CompanyId { get; set; }


    /// <summary>
    ///     多用户选择传回 User Id
    /// </summary>
    public Guid UserId { get; set; }


    /// <summary>
    ///     用于考场的绑定的 考场的Id
    /// </summary>
    public Guid FieldId { get; set; }


    /// <summary>
    /// </summary>
    public Guid StudentId { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public JxStudentEntity student { get; set; }

    /// <summary>
    ///     设备类型，默认PC
    /// </summary>
    /// <example>0</example>
    public AuthDeviceTypeEumu Device { get; set; } = AuthDeviceTypeEumu.PC;

    /// <summary>
    /// </summary>
    public string Phone { get; set; }


    /// <summary>
    /// </summary>
    public string RealName { get; set; }

    /// <summary>
    ///     公司名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string Avatar { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public long UserSysId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public bool IsTenantAdmin { get; set; }


    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public bool IsSuperAdmin { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string TenantName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string TenantType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public DateTime PlusEndTime { get; set; } = Convert.ToDateTime("1900-01-01");

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public DateTime ServiceEndTime { get; set; } = Convert.ToDateTime("1900-01-01");
}
/// <summary>
///     登出输入参数
/// </summary>
public class LoginOutIput
{
    /// <summary>
    ///     token
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    ///     
    /// </summary>
    public string OpenId { get; set; }
}

/// <summary>
///     获取短信验证码输入
/// </summary>
public class GetPhoneValidCodeInput : ValidCodeInput
{
    /// <summary>
    ///     手机号
    /// </summary>
    public string Phone { get; set; }


    /// <summary>
    ///     如果 是手机关联多个账户  就需要先选择公司
    /// </summary>
    public Guid TenantId { get; set; }


    /// <summary>
    ///     如果 是手机关联多个账户  就需要先选择账户
    /// </summary>
    public Guid UserId { get; set; }
}

/// <summary>
///     手机号登录输入
/// </summary>
public class LoginByPhoneInput : GetPhoneValidCodeInput
{
    /// <summary>
    ///     设备类型，默认PC
    /// </summary>
    /// <example>0</example>
    public AuthDeviceTypeEumu Device { get; set; } = AuthDeviceTypeEumu.PC;
}