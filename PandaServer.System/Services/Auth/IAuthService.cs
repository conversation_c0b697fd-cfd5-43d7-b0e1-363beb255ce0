using PandaServer.System.Services.Auth.Dto;

namespace PandaServer.System.Services.Auth;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// 登录
    /// </summary>
    /// <param name="input">登录信息</param>
    /// <param name="loginClientType">登录类型</param>
    /// <returns></returns>
    Task<LoginOutPut> Login(LoginInput input, LoginClientTypeEnum loginClientType);

    /// <summary>
    /// 刷新Token
    /// </summary>
    /// <param name="input">登录信息</param>
    /// <param name="loginClientType">登录类型</param>
    /// <returns></returns>
    Task<LoginOutPut> RefreshToken(LoginInput input, LoginClientTypeEnum loginClientType);

    /// <summary>
    /// 获取登录用户信息
    /// </summary>
    /// <returns></returns>
    Task<LoginUserOutput> GetLoginUser();

    /// <summary>
    /// 登出
    /// </summary>
    /// <param name="loginClientType">登录类型</param>
    /// <returns></returns>
    Task LoginOut(LoginClientTypeEnum loginClientType);

    /// <summary>
    /// 登出(指定 Token)
    /// </summary>
    /// <param name="token">Token</param>
    /// <param name="userId">用户Id</param>
    /// <param name="loginClientType">登录类型</param>
    /// <returns></returns>
    Task LoginOut(string token, Guid userId, LoginClientTypeEnum loginClientType);
}