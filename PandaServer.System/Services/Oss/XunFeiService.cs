using System.Net.Http.Headers;
using System.Security.Cryptography;
using Newtonsoft.Json;
using PandaServer.System.Cloud;
using PandaServer.System.Services.Oss.Json.XunFei;

namespace PandaServer.System.Services.Oss;

/// <summary>
///     <inheritdoc cref="IXunFeiService" />
/// </summary>
public class XunFeiService : IXunFeiService, ITransient
{
    /// <inheritdoc />
    public async Task<XunFeiJson> GetSfz(byte[] fileBytes)
    {
        using (var client = new HttpClient())
        {
            var Param = Convert.ToBase64String(
                Encoding.ASCII.GetBytes("{\"engine_type\":\"idcard\",\"head_portrait\":\"1\",\"crop_image\":\"1\"}"));


            var CurTime = Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds)
                .ToString();
            var cryptoServiceProvider = new MD5CryptoServiceProvider();
            var CheckSum = Md5(string.Format("{0}{1}{2}", ApiConfig.XunFeiAPPSecret, CurTime, Param));

            var postData = "image=" + HttpUtility.UrlEncode(Convert.ToBase64String(fileBytes));

            HttpContent httpContent = new StringContent(postData);

            httpContent.Headers.Add("X-Appid", ApiConfig.XunFeiAPIKey);
            httpContent.Headers.Add("X-CurTime", CurTime);
            httpContent.Headers.Add("X-Param", Param);
            httpContent.Headers.Add("X-CheckSum", CheckSum);

            httpContent.Headers.ContentType =
                MediaTypeHeaderValue.Parse("application/x-www-form-urlencoded; charset=utf-8");


            var response = client.PostAsync("http://webapi.xfyun.cn/v1/service/v1/ocr/idcard", httpContent).Result;
            if (response.IsSuccessStatusCode)
            {
                var jsonContent = await response.Content.ReadAsStringAsync();

                var result = JsonConvert.DeserializeObject<XunFeiJson>(jsonContent);

                if (result.code == 10222)
                {
                    throw Oops.Bah("您上传的图片分辨率太大，超过系统能接受的程度，您可以直接使用裁剪功能上传");
                }
                else if (result.code != 0)
                {
                    Console.WriteLine(jsonContent);
                    throw Oops.Bah(result.desc);
                }

                return result;
            }

            return null;
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="s"></param>
    /// <returns></returns>
    private string Md5(string s)
    {
        MD5 md5 = new MD5CryptoServiceProvider();
        var bytes = Encoding.UTF8.GetBytes(s);
        var hash = md5.ComputeHash(bytes);
        md5.Clear();
        var result = "";
        for (var index = 0; index < hash.Length; ++index) result += Convert.ToString(hash[index], 16).PadLeft(2, '0');
        return result.PadLeft(32, '0');
    }
}