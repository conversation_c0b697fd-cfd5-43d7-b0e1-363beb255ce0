﻿using Baidu.Aip;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PandaServer.System.Cloud;

namespace PandaServer.System;

public class FaceV2 : AipServiceBase
{
    private readonly ISimpleCacheService _simpleCacheService;
    public FaceV2(string apiKey, string secretKey) : base(apiKey, secretKey)
    {
    }

    protected AipHttpRequest DefaultRequest(string uri)
    {
        return new AipHttpRequest(uri)
        {
            Method = "POST",
            BodyType = AipHttpRequest.BodyFormat.Json,
            ContentEncoding = Encoding.UTF8
        };
    }

    /// <inheritdoc />
    public string GetAccessToken()
    {
        var authHost = "https://aip.baidubce.com/oauth/2.0/token";
        var client = new HttpClient();
        var paraList = new List<KeyValuePair<string, string>>();
        paraList.Add(new KeyValuePair<string, string>("grant_type", "client_credentials"));
        paraList.Add(new KeyValuePair<string, string>("client_id", ApiConfig.BaiduFaceAPIKey));
        paraList.Add(new KeyValuePair<string, string>("client_secret", ApiConfig.BaiduFaceSecretKey));

        var response = client.PostAsync(authHost, new FormUrlEncodedContent(paraList)).Result;
        var result = response.Content.ReadAsStringAsync().Result;

        Console.WriteLine("GetAccessToken  " + result);
        var json = JObject.Parse(result);
        if (json == null || json["access_token"] == null)
        {
            return "";
        }

        var code = ((JValue)json["access_token"]).Value;

        return code.ToString();
    }

    public async Task<JObject> Search(string image, string imageType, string groupIdList,
        Dictionary<string, object> options = null)
    {
        var parameters = new Dictionary<string, string>
        {
            { "group_id", groupIdList },
            { "image", image },
            { "access_token", GetAccessToken() }
        };
        var client = new HttpClient();
        var response = await client.PostAsync("https://aip.baidubce.com/rest/2.0/face/v2/identify",
            new FormUrlEncodedContent(parameters));

        if (!response.IsSuccessStatusCode)
            throw new Exception($"Failed to call Baidu Face API. Status code: {response.StatusCode}");

        var responseBody = await response.Content.ReadAsStringAsync();

        // 返回响应
        return JsonConvert.DeserializeObject<JObject>(responseBody);
    }

    public async Task<JObject> UserAdd(string image, string imageType, string groupId, string userId,
        Dictionary<string, object> options = null)
    {
        var parameters = new Dictionary<string, string>
        {
            { "group_id", groupId },
            { "uid", userId },
            { "image", image },
            { "access_token", GetAccessToken() }
        };
        var client = new HttpClient();
        var response = await client.PostAsync("https://aip.baidubce.com/rest/2.0/face/v2/faceset/user/add",
            new FormUrlEncodedContent(parameters));

        if (!response.IsSuccessStatusCode)
            throw new Exception($"Failed to call Baidu Face API. Status code: {response.StatusCode}");

        var responseBody = await response.Content.ReadAsStringAsync();

        // Console.WriteLine("responseBody:" + responseBody);

        // 返回响应
        return JsonConvert.DeserializeObject<JObject>(responseBody);
    }
}