using Baidu.Aip.BodyAnalysis;
using Baidu.Aip.Face;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PandaServer.System.Cloud;
using PandaServer.System.Services.Config;
using PandaServer.System.Services.Oss.Json.Baidu;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using System.Text;

namespace PandaServer.System.Services.Oss;

/// <summary>
///     <inheritdoc cref="IBaiduService" />
/// </summary>
public class BaiduService : IBaiduService
{
    private readonly IConfigService _configService;
    private readonly ISimpleCacheService _simpleCacheService;

    public BaiduService(IConfigService configService, ISimpleCacheService simpleCacheService)
    {
        _configService = configService;
        _simpleCacheService = simpleCacheService;
    }

    #region 授权相关的

    /// <inheritdoc />
    public async Task<string> GetAccessToken()
    {
        var config = await _configService.GetByConfigKey("", DevConfigConst.SYS_UPDATE_BAIDU_FACEV2_APP_ID);

        if (config == null)
        {
            throw Oops.Bah("联系管理人员设置相关 Baidu 配置");
        }

        var appId = config.ConfigValue;

        config = await _configService.GetByConfigKey("", DevConfigConst.SYS_UPDATE_BAIDU_FACEV2_SECRET_KEY);
        if (config == null)
        {
            throw Oops.Bah("联系管理人员设置相关 Baidu 配置");
        }
        var secretKey = config.ConfigValue;

        var data = await _simpleCacheService.HashGetOne<Dictionary<string, DateTime>>(CacheConst.Cache_BaiduFaceV2AccessToken, appId);


        if (data == null || data.Count == 0 || data.First().Value < DateTime.Now.AddDays(-1))
        {
            var authHost = "https://aip.baidubce.com/oauth/2.0/token";
            var client = new HttpClient();
            var paraList = new List<KeyValuePair<string, string>>();
            paraList.Add(new KeyValuePair<string, string>("grant_type", "client_credentials"));
            paraList.Add(new KeyValuePair<string, string>("client_id", ApiConfig.BaiduFaceAPIKey));
            paraList.Add(new KeyValuePair<string, string>("client_secret", ApiConfig.BaiduFaceSecretKey));

            var response = client.PostAsync(authHost, new FormUrlEncodedContent(paraList)).Result;
            var result = response.Content.ReadAsStringAsync().Result;

            var json = JObject.Parse(result);
            return json["access_token"]?.ToString() ?? "";
        }
        else
        {
            return data.First().Key;
        }
    }

    #endregion 授权相关的

    #region 人脸相关的

    /// <inheritdoc />
    public async Task<FaceMatchV3Data> FaceMatchV3Async(byte[] idCard, byte[] face)
    {
        // 构造JSON数据
        var jsonData = new[]
        {
            new
            {
                image = Convert.ToBase64String(face),
                image_type = "BASE64",
                face_type = "LIVE",
                quality_control = "NORMAL",
                liveness_control = "NORMAL"
            },
            new
            {
                image = Convert.ToBase64String(idCard),
                image_type = "BASE64",
                face_type = "IDCARD",
                quality_control = "LOW",
                liveness_control = "NONE"
            }
        };

        var jsonString = JsonConvert.SerializeObject(jsonData);
        var accessToken = await GetAccessToken();
        var url = $"https://aip.baidubce.com/rest/2.0/face/v3/match?access_token={accessToken}";

        var client = new HttpClient();
        var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        var response = await client.PostAsync(url, content);

        if (!response.IsSuccessStatusCode)
            throw new Exception($"Failed to call Baidu Face API. Status code: {response.StatusCode}");

        var responseBody = await response.Content.ReadAsStringAsync();

        Console.WriteLine(responseBody);
        // 返回响应
        return JsonConvert.DeserializeObject<FaceMatchV3Data>(responseBody);
    }


    /// <inheritdoc />
    public async Task<FaceDetectV3Data> DetectAsync(byte[] faceData, bool mustLive)
    {
        var image = Convert.ToBase64String(faceData);
        var imageType = "BASE64";


        var livenessControl = "NONE";

        if (mustLive) livenessControl = "HIGH";

        var parameters = new Dictionary<string, string>
        {
            { "image_type", imageType },
            { "image", image },
            { "max_face_num", "10" },
            { "liveness_control", livenessControl },
            {
                "face_field",
                "angle,left,top,width,height,rotation,age,beauty,expression,face_shape,gender,glasses,landmark,landmark150,race,quality,eye_status,emotion,face_type"
            },
            { "access_token", await GetAccessToken() }
        };
        var client = new HttpClient();
        var response = await client.PostAsync("https://aip.baidubce.com/rest/2.0/face/v3/detect",
            new FormUrlEncodedContent(parameters));



        if (!response.IsSuccessStatusCode)
            throw new Exception($"Failed to call Baidu Face API. Status code: {response.StatusCode}");

        var responseBody = await response.Content.ReadAsStringAsync();
        // 返回响应
        return JsonConvert.DeserializeObject<FaceDetectV3Data>(responseBody);
    }

    /// <inheritdoc />
    public async Task<Image> GetInchImage(Image image, Color bgColor, bool mustLive = true)
    {
        if (image == null)
            throw Oops.Bah("图片参数错误，图片数据为空");


        if (image.Width * image.Height > 4000 * 4000)
            image.Mutate(x => x.Resize(image.Width / 3, image.Height / 3));
        else if (image.Width * image.Height > 2000 * 2000)
            image.Mutate(x => x.Resize(image.Width / 2, image.Height / 2));
        var fileBytes = image.ParseToBytes();

        var data = await DetectAsync(fileBytes, mustLive);


        if (data.ErrorMsg == "SUCCESS")
        {
            if (data.result.face_list.Count == 0)
                throw Oops.Bah("图片中为发现人脸图像");

            var face = data.result.face_list[0];

            // if (face.glasses.probability == 1 && face.glasses.type == "common")
            //     throw Oops.Bah("请不要带眼镜");

            if (face.glasses.probability == 1 && face.glasses.type == "sun")
                throw Oops.Bah("请不要带太阳眼镜");

            var x = Convert.ToInt32(face.location.left - face.location.width * 0.5M);
            var y = Convert.ToInt32(face.location.top -
                                    face.location.height * (face.location.width / face.location.height));
            var width = Convert.ToInt32(face.location.width * 2);
            var height = Convert.ToInt32(width / 2.5 * 3.5);

            if (x < 0)
                throw Oops.Bah("不要裁剪照片");

            if (x + width > image.Width)
                throw Oops.Bah("不要裁剪照片");

            if (y < 0 && y + height > image.Height)
                throw Oops.Bah("请人站退点再照");


            if (data.result.face_list[0].angle.roll < -80 && data.result.face_list[0].angle.roll > -110)
            {
                image.Mutate(x => x.RotateFlip(RotateMode.Rotate90, FlipMode.None));
                return await GetInchImage(image, bgColor);
            }

            if (data.result.face_list[0].angle.roll > 80 && data.result.face_list[0].angle.roll < 110)
            {
                image.Mutate(x => x.RotateFlip(RotateMode.Rotate270, FlipMode.None));
                return await GetInchImage(image, bgColor);
            }

            if (y < 0) y = 0;
            if (y + height > image.Height) height = image.Height - y;
            // Bitmap HeadImage = OriginalImage.Clone(new Rectangle(x, y, width, height), OriginalImage.PixelFormat);

            image.Mutate(a => a.Crop(new Rectangle(x, y, width, height)));
            var img = ClearBackGround(image, bgColor);


            return img;
        }

        if (data.ErrorCode == 222202)
            throw Oops.Bah("活体检测不通过，可能原因：【1】相机像素过低，【2】拍照时抖动，【3】照片为翻拍。");

        throw Oops.Bah(data.ErrorMsg);
    }

    /// <inheritdoc />
    public BodySegData BodySeg(byte[] fileBytes)
    {
        var body = new Body(ApiConfig.BaiduBodyAPIKey, ApiConfig.BaiduBodySecretKey);
        var result = body.BodySeg(fileBytes).ToString();

        var data = JsonConvert
            .DeserializeObject<BodySegData>(result);

        return data;
    }

    /// <inheritdoc />
    public Image ClearBackGround(Image image, Color bgColor)
    {
        var fileBytes = image.ParseToBytes();

        var data = BodySeg(fileBytes);

        if (data.ErrorCode != "0" && data.ErrorCode != null)
            throw new Exception(data.ErrorMsg);

        var filePath = Environment.CurrentDirectory + "\\" + Guid.NewGuid() + ".png";
        var foregroundTemp = data.Foreground.ParseToImage();

        foregroundTemp.SaveAsPng(filePath);

        var foreground = Image.Load(filePath);

        foregroundTemp.Dispose();
        global::System.IO.File.Delete(filePath);


        var brush = new RecolorBrush(Color.White, bgColor, 0.1F);

        var resultImage = new Image<Rgba32>(foreground.Width, foreground.Height, Rgba32.ParseHex(bgColor.ToHex()));

        resultImage.Mutate(x => x.DrawImage(foreground, 1));
        return resultImage;
    }


    /// <inheritdoc />
    public async Task<FaceSearchV2Data> Search(byte[] faceData, List<Guid> listGroupId)
    {
        var groupIdList = "";

        for (var i = 0; i < listGroupId.Count; i++)
        {
            groupIdList += "U_" + listGroupId[i].ToString().ToLower().Replace("-", "_") + ",";
            groupIdList += listGroupId[i].ToString().ToLower().Replace("-", "_");

            if (i < listGroupId.Count - 1) groupIdList += ",";
        }

        var result = await Search(faceData, groupIdList);

        if (!string.IsNullOrEmpty(result.ErrorMsg))
        {
            throw Oops.Bah(result.ErrorMsg);
        }

        return result;
    }


    /// <inheritdoc />
    public async Task<FaceSearchV2Data> Search(byte[] faceData, string groupIds)
    {
        groupIds = groupIds.ToLower().Replace("-", "_");

        var image = Convert.ToBase64String(faceData);

        var json = await Search(image, "BASE64", groupIds);

        var data = JsonConvert.DeserializeObject<FaceSearchV2Data>(json.ToString());

        return data;
    }


    /// <inheritdoc />
    public async Task<FaceSearchV2Data> Search(byte[] faceData, List<Guid> listGroupId, Guid tenantId)
    {
        var groupIdList = "";

        for (var i = 0; i < listGroupId.Count; i++)
        {
            groupIdList += "U_" + listGroupId[i].ToString().ToLower().Replace("-", "_") + ",";
            groupIdList += listGroupId[i].ToString().ToLower().Replace("-", "_");

            if (i < listGroupId.Count - 1) groupIdList += ",";
        }

        return await Search(faceData, groupIdList, tenantId);
    }

    /// <inheritdoc />
    public async Task<FaceSearchV2Data> Search(byte[] faceData, string groupIds, Guid tenantId)
    {
        return await Search(Convert.ToBase64String(faceData), groupIds, tenantId);
    }

    /// <inheritdoc />
    public async Task<FaceSearchV2Data> Search(string faceImage, string groupIds, Guid tenantId)
    {
        groupIds = groupIds.ToLower().Replace("-", "_");
        var options = new Dictionary<string, object>
        {
            { "max_face_num", 1 },
            { "match_threshold", 80 },
            { "quality_control", "NORMAL" },
            { "liveness_control", "NONE" },
            { "max_user_num", 3 }
        };
        var json = await Search(faceImage, "BASE64", groupIds, options);

        var data = JsonConvert.DeserializeObject<FaceSearchV2Data>(json.ToString());

        return data;
    }

    /// <inheritdoc />
    public bool DeleteFace(string uid, string groupId)
    {
        groupId = groupId.ToLower().Replace("-", "_");
        uid = uid.ToLower().Replace("-", "_");

        var face = new Face(ApiConfig.BaiduFaceAPIKey, ApiConfig.BaiduFaceSecretKey);
        var jObject = face.UserDelete(groupId, uid);

        if (string.IsNullOrWhiteSpace(jObject["log_id"].ParseToString()))
            return false;
        return true;
    }

    /// <inheritdoc />
    public async Task<bool> NewFace(string uid, string groupId, byte[] faceData)
    {
        groupId = groupId.ToLower().Replace("-", "_");
        uid = uid.ToLower().Replace("-", "_");

        var userinfo = new Dictionary<string, object>();
        var image = Convert.ToBase64String(faceData);

        var json = await UserAdd(image, "BASE64", groupId, uid, userinfo);

        if (string.IsNullOrWhiteSpace(json["log_id"].ParseToString()) || json["error_code"].ParseToInt() != 0)
            return false;
        return true;
    }

    #endregion 人脸相关的

    #region 方法


    public async Task<JObject> Search(string image, string imageType, string groupIdList,
        Dictionary<string, object> options = null)
    {
        var parameters = new Dictionary<string, string>
        {
            { "group_id", groupIdList },
            { "image", image },
            { "access_token", await GetAccessToken() }
        };
        var client = new HttpClient();
        var response = await client.PostAsync("https://aip.baidubce.com/rest/2.0/face/v2/identify",
            new FormUrlEncodedContent(parameters));

        if (!response.IsSuccessStatusCode)
            throw new Exception($"Failed to call Baidu Face API. Status code: {response.StatusCode}");

        var responseBody = await response.Content.ReadAsStringAsync();

        // 返回响应
        return JsonConvert.DeserializeObject<JObject>(responseBody);
    }

    public async Task<JObject> UserAdd(string image, string imageType, string groupId, string userId,
        Dictionary<string, object> options = null)
    {
        var parameters = new Dictionary<string, string>
        {
            { "group_id", groupId },
            { "uid", userId },
            { "image", image },
            { "access_token", await GetAccessToken() }
        };
        var client = new HttpClient();
        var response = await client.PostAsync("https://aip.baidubce.com/rest/2.0/face/v2/faceset/user/add",
            new FormUrlEncodedContent(parameters));

        if (!response.IsSuccessStatusCode)
            throw new Exception($"Failed to call Baidu Face API. Status code: {response.StatusCode}");

        var responseBody = await response.Content.ReadAsStringAsync();

        // Console.WriteLine("responseBody:" + responseBody);

        // 返回响应
        return JsonConvert.DeserializeObject<JObject>(responseBody);
    }

    #endregion 方法
}