using PandaServer.System.Entity.Student.Pay;
using PandaServer.System.Services.Pay.Dto;

namespace PandaServer.System.Services.Pay.Base;
/// <summary>
///     <inheritdoc cref="IPayTypeService" />
/// </summary>
public class PayTypeService : CustomDbRepository<PayTypeEntity>, IPayTypeService, ITransient
{
    private readonly ISimpleCacheService _simpleCacheService;
    public PayTypeService(ISimpleCacheService simpleCacheService)
    {
        _simpleCacheService = simpleCacheService;
    }
    #region 添加

    /// <inheritdoc />
    public async Task<PayTypeEntity> Add(PayTypePageInPut inPut)
    {
        if (string.IsNullOrEmpty(inPut.Name) || inPut.Name.Trim() == "")
            throw Oops.Bah("类型名称不能为空!");
        if (await ExistsPayTypeName(Guid.Empty, inPut.Name))
            throw Oops.Bah("名称重复，请重试");

        var payType = inPut.Adapt<PayTypeEntity>();
        payType.Create();
        payType.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;
        payType.TenantId = UserManager.TenantId;

        if (!await InsertAsync(payType))
            throw Oops.Bah("添加数据失败");

        await ClearPayTypeCache(UserManager.TenantId);
        return payType;
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<PayTypeEntity> GetById(Guid id)
    {
        var payTypes = await GetPayTypeCache(UserManager.TenantId);
        return payTypes.FirstOrDefault(m => m.Id == id);
    }

    /// <inheritdoc />
    public async Task<Guid> GetOrAddIdByName(string payTypeName, Guid tenantId)
    {
        var payTypes = await GetPayTypeCache(tenantId);
        var payTypeId = Guid.Empty;
        var payType = payTypes.FirstOrDefault(m => m.Name == payTypeName && m.IsDelete == false);

        if (payType == null)
        {
            if (string.IsNullOrEmpty(payTypeName)) return Guid.Empty;

            payType = new PayTypeEntity();
            payType.Create();
            payType.TenantId = tenantId;
            payType.Name = payTypeName;
            payType.ThirdPay = true;
            payType.Remark = "";

            if (await InsertAsync(payType))
            {
                payTypeId = payType.Id;
            }
            await ClearPayTypeCache(tenantId);
        }
        else
        {
            payTypeId = payType.Id;
        }

        return payTypeId;
    }

    /// <inheritdoc />
    public async Task<List<PayTypeEntity>> GetListByName(string name, Guid tenantId)
    {
        var payTypes = await GetPayTypeCache(tenantId);
        return payTypes.Where(m => m.Name == name && m.IsDelete == false).ToList();
    }

    /// <inheritdoc />
    public async Task<string> GetNameById(Guid id)
    {
        var payTypes = await GetPayTypeCache(UserManager.TenantId);
        return payTypes.FirstOrDefault(m => m.Id == id)?.Name;
    }


    /// <inheritdoc />
    public async Task<SqlSugarPagedList<PayTypeOutPut>> Page(PayTypePageInPut inPut)
    {
        var query = Context.Queryable<PayTypeEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .WhereIF(!string.IsNullOrEmpty(inPut.Name), m => m.Name.Contains(inPut.Name.Trim()))
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .OrderBy(m => m.SortCode, OrderByType.Desc)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n) => new PayTypeOutPut
            {
                Id = m.Id,
                Name = m.Name,
                SortCode = m.SortCode,
                CreateUserName = n.RealName,
                CreateTime = m.CreateTime
            });



        var data = await query.ToPagedListAsync(inPut.Current, inPut.Size);

        data.Records = data.Records.ToList().Select<PayTypeOutPut, PayTypeOutPut>((u, i) =>
            {
                u.RowIndex = (inPut.Current - 1) * inPut.Size + (i + 1);

                return u;
            });
        return data;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsPayTypeName(Guid payTypeId, string name)
    {
        return await IsAnyAsync(m =>
            m.Id != payTypeId && m.Name == name && m.TenantId == UserManager.TenantId && m.IsDelete == false);
    }


    /// <inheritdoc />
    public async Task<List<PayTypeEntity>> GetList(Guid tenantId)
    {
        return await GetPayTypeCache(tenantId);
    }

    #endregion 查询

    #region 更新

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data == null)
            throw Oops.Bah("未找到相关的数据，请刷新页面");

        var payTable = "student_pay_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();

        if (Context.Queryable<JxPayEntity>().AS(payTable).Where(m => m.PayTypeId == id && m.IsDelete == false).Count() >
            0)
            throw Oops.Bah("有相关联数据无法删除");

        data.Delete();

        if (!await UpdateAsync(data))
            throw Oops.Bah("删除失败");

        await ClearPayTypeCache(UserManager.TenantId);
        return true;
    }

    /// <inheritdoc />
    public async Task<bool> Edit(PayTypePageInPut inPut)
    {
        if (string.IsNullOrEmpty(inPut.Name) || inPut.Name.Trim() == "")
            throw Oops.Bah("名称不能为空!");

        var payType = await GetById(inPut.Id);

        payType.Name = inPut.Name;
        payType.SortCode = inPut.SortCode;
        payType.ThirdPay = inPut.ThirdPay;
        payType.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;

        payType.Modify();

        if (await ExistsPayTypeName(payType.Id, payType.Name))
            throw Oops.Bah("名称重复，请重试");
        if (!await UpdateAsync(payType))
            throw Oops.Bah("更新失败");

        await ClearPayTypeCache(UserManager.TenantId);
        return true;
    }

    #endregion 更新



    #region 缓存


    /// <summary>
    /// 清除 驾校的支付类型 的缓存
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task ClearPayTypeCache(Guid tenantId)
    {
        var key = CacheConst.Cache_PayType; //系统配置key
        await _simpleCacheService.HashDel<List<PayTypeEntity>>(key, tenantId.ToString());
    }

    /// <summary>
    /// 设置 驾校的支付类型 的缓存
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task<List<PayTypeEntity>> GetPayTypeCache(Guid tenantId)
    {
        var key = CacheConst.Cache_PayType; //系统配置key
        var payTypes = await _simpleCacheService.HashGetOne<List<PayTypeEntity>>(key, tenantId.ToString());
        if (payTypes == null)
        {
            payTypes = await Context.Queryable<PayTypeEntity>().Where(m => m.TenantId == tenantId).ToListAsync();
            await _simpleCacheService.HashAdd(key, tenantId.ToString(), payTypes);
        }

        return payTypes;
    }

    #endregion 缓存
}