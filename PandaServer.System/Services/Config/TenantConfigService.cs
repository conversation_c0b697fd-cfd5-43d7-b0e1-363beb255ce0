namespace PandaServer.System.Services.Config;

/// <summary>
///     <inheritdoc cref="ITenantConfigService" />
/// </summary>
public class TenantConfigService : CustomDbRepository<TenantConfigEntity>, ITenantConfigService, ITransient
{
    private readonly ISimpleCacheService _simpleCacheService;

    public TenantConfigService(ISimpleCacheService simpleCacheService)
    {
        _simpleCacheService = simpleCacheService;
    }

    #region 更新

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(TenantConfigInPut inPut)
    {
        var data = await Context.Queryable<TenantConfigEntity>()
            .Where(m => m.TenantId == UserManager.TenantId && m.ConfigKey == inPut.ConfigKey && m.IsDelete == false)
            .FirstAsync();

        if (data == null)
            throw Oops.Bah("当前数据为空，刷新页面重试");

        data.Delete();

        if (!Update(data))
            throw Oops.Bah("更新数据失败");

        return true;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = GetSingle(m => m.Id == id);

        if (data == null || data.IsDelete)
            throw Oops.Bah("当前数据为空，刷新页面重试");

        data.Delete();

        if (!Update(data))
            throw Oops.Bah("更新数据失败");

        return true;
    }

    /// <inheritdoc />
    public async Task<bool> Update(TenantConfigInPut inPut)
    {
        var data = GetSingle(m =>
            m.TenantId == UserManager.TenantId && m.ConfigKey == inPut.ConfigKey && m.IsDelete == false);

        if (data == null)
        {
            data = new TenantConfigEntity();
            data.Create();
            data.TenantId = UserManager.TenantId;
            data.ConfigKey = inPut.ConfigKey;
            data.ConfigValue = inPut.ConfigValue;
            data.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;
            data.SortCode = inPut.SortCode;

            await _simpleCacheService.HashDel<List<TenantConfigEntity>>(CacheConst.Cache_TenantConfig,
                UserManager.TenantId.ToString());
            return await InsertAsync(data);
        }

        data.Modify();
        data.ConfigValue = inPut.ConfigValue;
        data.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;
        data.SortCode = inPut.SortCode;

        await _simpleCacheService.HashDel<List<TenantConfigEntity>>(CacheConst.Cache_TenantConfig,
            UserManager.TenantId.ToString());

        return Update(data);
    }

    /// <inheritdoc />
    public async Task<bool> Update(Guid tenantId, string key, string value)
    {
        await Context.Deleteable<TenantConfigEntity>()
            .Where(m => m.TenantId == tenantId && m.ConfigKey == key)
            .ExecuteCommandAsync();

        var data = new TenantConfigEntity();
        data.Create();
        data.TenantId = tenantId;
        data.ConfigKey = key;
        data.ConfigValue = value;

        await _simpleCacheService.HashDel<List<TenantConfigEntity>>(CacheConst.Cache_TenantConfig, tenantId.ToString());
        int result = await Context.Insertable(data).ExecuteCommandAsync();

        if (result > 0)
        {
            await _simpleCacheService.HashAdd(CacheConst.Cache_TenantConfig, tenantId.ToString(), data);
        }

        return result > 0;
    }



    /// <inheritdoc />
    public async Task<bool> Update(Guid tenantId, Dictionary<string, string> values)
    {
        await Context.Deleteable<TenantConfigEntity>()
            .Where(m => m.TenantId == tenantId && values.Keys.Contains(m.ConfigKey))
            .ExecuteCommandAsync();

        var datas = new List<TenantConfigEntity>();

        // 2. 遍历 values
        foreach (var config in values)
        {
            var data = new TenantConfigEntity();
            data.TenantId = tenantId;
            data.Create();
            data.ConfigKey = config.Key;
            data.ConfigValue = config.Value;

            datas.Add(data);
        }

        await _simpleCacheService.HashDel<List<TenantConfigEntity>>(CacheConst.Cache_TenantConfig, tenantId.ToString());
        return await Context.Insertable(datas).ExecuteCommandAsync() > 0;
    }

    #endregion 更新

    #region 查询

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<TenantConfigOutPut>> Page(TenantConfigPageInPut inPut)
    {
        var query = Context.Queryable<TenantConfigEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false &&
                        m.ConfigKey.StartsWith("THIRDCONFIG_"))
            .WhereIF(!string.IsNullOrEmpty(inPut.SearchKey), m => m.ConfigKey.Contains(inPut.SearchKey.Trim()))
            .OrderBy(m => m.CreateTime)
            .Select((m, n) => new TenantConfigOutPut
            {
                Id = m.Id,
                ConfigKey = m.ConfigKey,
                ConfigValue = m.ConfigValue,
                SortCode = m.SortCode,
                Remark = m.Remark,
                CreateUserName = n.RealName,
                CreateTime = m.CreateTime
            });


        var pageInfo = await query.ToPagedListAsync(inPut.Current, inPut.Size); //分页

        pageInfo.Records.ForEach(m =>
        {
            m.ConfigKeyText = DevConfigConst.GetDescription(typeof(DevConfigConst), m.ConfigKey);
        });



        return pageInfo;
    }

    /// <inheritdoc />
    public async Task<List<TenantConfigEntity>> GetListByTenantId(Guid tenantId)
    {
        var configs = await _simpleCacheService.HashGetOne<List<TenantConfigEntity>>(CacheConst.Cache_TenantConfig, tenantId.ToString());
        if (configs == null)
        {
            configs = Context.Queryable<TenantConfigEntity>().Where(m => m.TenantId == tenantId && m.IsDelete == false).ToList();

            await _simpleCacheService.HashAdd(CacheConst.Cache_TenantConfig, tenantId.ToString(), configs);

            return configs;
        }

        return configs;
    }

    /// <inheritdoc />
    public string GetValueByConfigsAndKey(List<TenantConfigEntity> configs, string key)
    {
        var data = configs.Find(m => m.ConfigKey == key);

        if (data == null)
            return null;
        return data.ConfigValue;
    }

    /// <inheritdoc />
    public async Task<string> GetValueByTenantIdAndKey(DevConfigConst key, Guid tenantId)
    {
        return await GetValueByTenantIdAndKey(key.ToString(), tenantId);
    }

    /// <inheritdoc />
    public async Task<string> GetValueByTenantIdAndKey(string key, Guid tenantId)
    {
        var configs = await _simpleCacheService.HashGetOne<List<TenantConfigEntity>>(CacheConst.Cache_TenantConfig, tenantId.ToString());
        if (configs == null)
        {
            configs = Context.Queryable<TenantConfigEntity>().Where(m => m.TenantId == tenantId && m.IsDelete == false)
                .ToList();

            await _simpleCacheService.HashAdd(CacheConst.Cache_TenantConfig, tenantId.ToString(), configs);

            var result = configs.Find(m => m.ConfigKey == key);

            if (result == null)
            {
                return "";
            }
            return result.ConfigValue;
        }

        var config = configs.Where(m => m.ConfigKey == key).FirstOrDefault();

        if (config == null)
            return "";
        return config.ConfigValue;
    }

    /// <inheritdoc />
    public async Task<string> GetValue(DevConfigConst key)
    {
        return await GetValue(key.ToString());
    }

    /// <inheritdoc />
    public async Task<string> GetValue(string key)
    {
        return await GetValueByTenantIdAndKey(key, UserManager.TenantId);
    }

    /// <inheritdoc />
    public async Task<List<TenantConfigEntity>> GetListByKey(string key)
    {
        var result = await _simpleCacheService.GetList<TenantConfigEntity>(key);
        return result.ToList();
    }


    #endregion 查询
}