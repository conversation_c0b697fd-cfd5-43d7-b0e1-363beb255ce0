using PandaServer.System.Services.SystemManage.Dtos;

namespace PandaServer.System.Services.Base.Dtos;

public class CarInPut : CarEntity
{
    /// <summary>
    /// </summary>
    public string Image0 { get; set; }

    /// <summary>
    /// </summary>
    public string Image1 { get; set; }

    /// <summary>
    /// </summary>
    public string Image7 { get; set; }

    /// <summary>
    /// </summary>
    public string Image71 { get; set; }

    /// <summary>
    /// </summary>
    public string Image72 { get; set; }

    /// <summary>
    /// </summary>
    public string Image8 { get; set; }

    /// <summary>
    /// </summary>
    public string Image9 { get; set; }

    /// <summary>
    /// </summary>
    public string Image10 { get; set; }

    /// <summary>
    /// </summary>
    public string Image11 { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Guid UseUserId { get; set; }
}

public class CarPageInPut : BasePageInput
{
    /// <summary>
    ///     车辆号牌
    /// </summary>
    public string CarNumber { get; set; }


    /// <summary>
    /// </summary>
    public Guid ManagerTenantId { get; set; }


    /// <summary>
    /// </summary>
    public Guid TenantId { get; set; }


    /// <summary>
    /// </summary>
    public int CityId { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public List<Guid> JxDeptIds { get; set; } = new List<Guid>();

    /// <summary>
    /// 
    /// </summary>
    public List<Guid> JxFieldIds { get; set; } = new List<Guid>();

    /// <summary>
    /// 
    /// </summary>
    public Guid CategoryId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Guid CategoryParentId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Guid FieldId { get; set; }

    /// <summary>
    ///     场地名称
    /// </summary>
    public string FieldName { get; set; }

    /// <summary>
    ///     驾校场地名称
    /// </summary>
    public string JxFieldName { get; set; }

    /// <summary>
    ///     驾校部门名称
    /// </summary>
    public string JxDeptName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<CarStatusEnum> Statuss { get; set; } = new List<CarStatusEnum>();


    /// <summary>
    /// 
    /// </summary>
    public List<Guid> Ids { get; set; } = new List<Guid>();


    /// <summary>
    /// 
    /// </summary>
    public List<Guid> TemplateIds { get; set; } = new List<Guid>();
}

public class CarOutPut : CarEntity
{
    /// <summary>
    /// 
    /// </summary>
    public int RowIndex { get; set; }

    /// <summary>
    ///     创建人的名字
    /// </summary>
    public string CreateUserName { get; set; }

    /// <summary>
    ///     使用的 员工的名字
    /// </summary>
    public Guid UseUserId { get; set; }

    /// <summary>
    ///     使用的 员工的名字
    /// </summary>
    public string UseRealName { get; set; }

    /// <summary>
    ///     使用的 员工的电话
    /// </summary>
    public string UsePhone { get; set; }

    /// <summary>
    ///     使用的 
    /// </summary>
    public Guid UseJxDeptId { get; set; }

    /// <summary>
    ///     使用的 
    /// </summary>
    public string UseJxDeptName { get; set; }

    /// <summary>
    ///     使用的 
    /// </summary>
    public Guid UseJxFieldId { get; set; }

    /// <summary>
    ///     使用的 
    /// </summary>
    public string UseJxFieldName { get; set; }



    /// <summary>
    /// </summary>
    public string TenantName { get; set; }


    /// <summary>
    ///     车辆状态
    /// </summary>
    public string StatusText { get; set; }


    /// <summary>
    ///     计时设备状态
    /// </summary>
    public string StudyDeviceStatusText { get; set; }

    /// <summary>
    /// </summary>
    public new DateTime? RegisterDate { get; set; }

    /// <summary>
    /// </summary>
    public new DateTime? ValidDate { get; set; }

    /// <summary>
    /// </summary>
    public new DateTime? TestingDate { get; set; }

    /// <summary>
    /// </summary>
    public new DateTime? CompulsoryInsuranceValidDate { get; set; }

    /// <summary>
    /// </summary>
    public new DateTime? InsuranceValidDate { get; set; }

    /// <summary>
    /// </summary>
    public new DateTime? SecurityTestingDate { get; set; }

    /// <summary>
    /// </summary>
    public new DateTime? TransferOutDate { get; set; }

    /// <summary>
    /// </summary>
    public new CarStatusEnum? Status { get; set; }

    /// <summary>
    /// </summary>
    public new StudyDeviceStatusEnum? StudyDeviceStatus { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public List<UserCategoryUserListOutPut> CategoryDatas { get; set; }

    /// <summary>
    ///     场地名称
    /// </summary>
    public string FieldName { get; set; }

    /// <summary>
    ///     驾校场地名称
    /// </summary>
    public string JxFieldName { get; set; }

    /// <summary>
    ///     驾校部门名称
    /// </summary>
    public string JxDeptName { get; set; }


    /// <summary>
    ///     模板名称
    /// </summary>
    public string TemplateName { get; set; }

    /// <summary>
    ///     模板ID
    /// </summary>
    public Guid TemplateId { get; set; }
}