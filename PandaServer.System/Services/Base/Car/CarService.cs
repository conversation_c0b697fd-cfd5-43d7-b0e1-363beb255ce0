using System.Data;
using PandaServer.System.Entity.Car;
using PandaServer.System.Services.Base.Dtos;
using PandaServer.System.Services.SystemManage.Dtos;

namespace PandaServer.System.Services.Base.Car;

/// <summary>
///     <inheritdoc cref="ICarService" />
/// </summary>
public class CarService : CustomDbRepository<CarEntity>, ICarService, ITransient
{
    private readonly ISimpleCacheService _simpleCacheService;

    public CarService(ISimpleCacheService simpleCacheService)
    {
        _simpleCacheService = simpleCacheService;
    }

    #region 删除

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id && m.IsDelete == false);

        if (data == null)
            throw Oops.Bah("车辆信息为空，刷新页面重试!");

        data.Delete();

        if (!await UpdateAsync(data))
            throw Oops.Bah("删除车辆信息失败");

        // 清除缓存
        await ClearCarCache(data.TenantId);

        return true;
    }

    #endregion 删除

    #region 查询


    /// <inheritdoc />
    public async Task<List<CarEntity>> GetListByTenantId(Guid tenantId)
    {
        return await GetCarCache(tenantId);
    }

    /// <inheritdoc />
    public async Task<CarOutPut> GetById(Guid id)
    {
        var car = await GetSingleAsync(m => m.Id == id && m.IsDelete == false);

        var result = car.Adapt<CarOutPut>();

        if (result.RegisterDate <= Convert.ToDateTime("2000-01-01"))
            result.RegisterDate = null;

        if (result.ValidDate <= Convert.ToDateTime("2000-01-01"))
            result.ValidDate = null;

        if (result.TestingDate <= Convert.ToDateTime("2000-01-01"))
            result.TestingDate = null;

        if (result.CompulsoryInsuranceValidDate <= Convert.ToDateTime("2000-01-01"))
            result.CompulsoryInsuranceValidDate = null;

        if (result.InsuranceValidDate <= Convert.ToDateTime("2000-01-01"))
            result.InsuranceValidDate = null;

        if (result.SecurityTestingDate <= Convert.ToDateTime("2000-01-01"))
            result.SecurityTestingDate = null;

        if (result.TransferOutDate <= Convert.ToDateTime("2000-01-01"))
            result.TransferOutDate = null;

        if (Convert.ToInt32(result.StudyDeviceStatus) == 0)
            result.StudyDeviceStatus = null;

        var carUser = await Context.Queryable<CarUserEntity>().Where(m => m.CarId == result.Id && m.IsDelete == false).FirstAsync();

        if (carUser != null)
        {
            var user = await Context.Queryable<UserEntity>().Where(m => m.Id == carUser.UserId).FirstAsync();
            if (user != null)
            {
                result.UseUserId = user.Id;
                result.UseRealName = user.RealName;
                result.UsePhone = user.Phone;

                var jxUser = await Context.Queryable<JxUserEntity>().Where(m => m.Id == user.Id).FirstAsync();
                if (jxUser != null)
                {
                    result.UseJxDeptId = jxUser.JxDeptId;
                    result.UseJxFieldId = jxUser.JxFieldId;
                }
            }
        }

        return result;
    }


    /// <inheritdoc />
    public async Task<string> GetCarNumberById(Guid id)
    {
        return await Context.Queryable<CarEntity>()
            .Where(m => m.Id == id)
            .Select(m => m.CarNumber)
            .SingleAsync();
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<CarOutPut>> Page(CarPageInPut input)
    {
        string CategoryParentSysIds = "";

        if (input.CategoryParentId != Guid.Empty)
        {
            CategoryParentSysIds = await Context.Queryable<UserCategoryEntity>().Where(m => m.Id == input.CategoryParentId).Select(m => m.SysIds).SingleAsync();
        }

        var pageInfo = Context.Queryable<CarEntity>()

            .LeftJoin<UserEntity>((car, user) => car.CreateUserId == user.Id)
            .LeftJoin<CarUserEntity>((car, user, carUser) => carUser.CarId == car.Id && carUser.IsDelete == false)

            .InnerJoinIF(input.CategoryId != Guid.Empty,
                Context.Queryable<UserCategoryUserEntity>()
                .LeftJoin<UserCategoryEntity>((categoryUser, category) => categoryUser.CategoryId == category.Id)
                .Where((categoryUser, category) => category.IsDelete == false && categoryUser.IsDelete == false && categoryUser.CategoryId == input.CategoryId)
                .GroupBy(categoryUser => categoryUser.UserId)
                .Select(categoryUser => new { categoryUser.UserId }), (car, user, carUser, categoryUser) => carUser.UserId == categoryUser.UserId)

            .InnerJoinIF(input.CategoryParentId != Guid.Empty,
                Context.Queryable<UserCategoryUserEntity>()
                .LeftJoin<UserCategoryEntity>((categoryUser, category) => categoryUser.CategoryId == category.Id)
                .Where((categoryUser, category) => category.IsDelete == false && categoryUser.IsDelete == false && category.SysIds.StartsWith(CategoryParentSysIds))
                .GroupBy(categoryUser => categoryUser.UserId)
                .Select(categoryUser => new { categoryUser.UserId }), (car, user, carUser, categoryUser, parentCategoryUser) => carUser.UserId == parentCategoryUser.UserId)

            .LeftJoin<UserEntity>((car, user, carUser, categoryUser, parentCategoryUser, useUser) => useUser.Id == carUser.UserId)
            .LeftJoin<JxUserEntity>((car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser) => jxUser.Id == useUser.Id)
            .LeftJoin<JxDeptEntity>((car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept) => jxUser.JxDeptId == jxDept.Id)
            .LeftJoin<JxFieldEntity>((car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept, jxField) => jxUser.JxFieldId == jxField.Id)
            .LeftJoin<FieldEntity>((car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept, jxField, field) => car.FieldId == field.Id)

            .LeftJoin<JxScheduleTemplateTargetEntity>((car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept, jxField, field, jxOrderCarTarget) => jxOrderCarTarget.TargetId == car.Id)
            .LeftJoin<JxScheduleTemplateEntity>((car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept, jxField, field, jxOrderCarTarget, jxScheduleTemplate) => jxOrderCarTarget.TemplateId == jxScheduleTemplate.Id)

            .Where(car => car.TenantId == UserManager.TenantId && car.IsDelete == false)
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey), car => car.CarNumber.Contains(input.SearchKey) || car.CertificateNumber.Contains(input.SearchKey) || car.NickName.Contains(input.SearchKey) || car.Remark.Contains(input.SearchKey))
            .WhereIF(!string.IsNullOrEmpty(input.CarNumber), car => car.CarNumber.Contains(input.CarNumber))

            .WhereIF(input.JxDeptIds.Count > 0, (car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept, jxField) => input.JxDeptIds.Contains(jxUser.JxDeptId))
            .WhereIF(input.JxFieldIds.Count > 0, (car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept, jxField) => input.JxFieldIds.Contains(jxUser.JxFieldId))
            .WhereIF(input.FieldId != Guid.Empty, (car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept, jxField, field) => car.FieldId == input.FieldId)

            .WhereIF(input.Statuss.Count > 0, car => input.Statuss.Contains((CarStatusEnum)car.Status))

            .WhereIF(input.Ids.Count > 0, car => input.Ids.Contains(car.Id))

            .WhereIF(input.TemplateIds.Count > 0, (car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept, jxField, field, jxOrderCarTarget, jxScheduleTemplate) => input.TemplateIds.Contains(jxScheduleTemplate.Id))

            .OrderBy(car => car.CreateTime, OrderByType.Desc)
            .Select((car, user, carUser, categoryUser, parentCategoryUser, useUser, jxUser, jxDept, jxField, field, jxOrderCarTarget, jxScheduleTemplate) => new CarOutPut
            {
                Id = car.Id,
                CarNumber = car.CarNumber,
                NickName = car.NickName,
                CarType = car.CarType,
                CreateTime = car.CreateTime,
                CreateUserName = user.RealName,
                Vin = car.Vin,
                EngineNumber = car.EngineNumber,
                RegisterDate = car.RegisterDate,
                ValidDate = car.ValidDate,
                InsuranceValidDate = car.InsuranceValidDate,
                CompulsoryInsuranceValidDate = car.CompulsoryInsuranceValidDate,
                Status = car.Status,
                StudyDeviceStatus = car.StudyDeviceStatus,
                JtNum = car.JtNum,
                UseUserId = useUser.Id,
                UseRealName = useUser.RealName,
                UsePhone = useUser.Phone,
                UseJxDeptName = jxDept.Name,
                UseJxFieldName = jxField.Name,
                FieldName = field.NickName,
                JxFieldName = jxField.Name,
                JxDeptName = jxDept.Name,
                CertificateNumber = car.CertificateNumber,
                TransferOutDate = car.TransferOutDate,
                TemplateName = jxScheduleTemplate.Name,
                TemplateId = jxScheduleTemplate.Id
            });

        var totalNumber = 0;
        var data = pageInfo.ToOffsetPage(input.Current < 1 ? 1 : input.Current, input.Size == 0 ? 99999 : input.Size, ref totalNumber);


        var userIds = data.Select(m => m.UseUserId).ToList();

        var userCategoryData = await Context.Queryable<UserCategoryUserEntity>()
        .LeftJoin<UserCategoryEntity>((categoryUser, category) => categoryUser.CategoryId == category.Id)
        .WhereIF(userIds.Count() < 100, (categoryUser, category) => userIds.Contains(categoryUser.UserId))
        .Where((categoryUser, category) => category.IsDelete == false && categoryUser.IsDelete == false && category.ParentId != Guid.Empty)
        .Select((categoryUser, category) => new UserCategoryUserListOutPut
        {
            UserId = categoryUser.UserId,
            CategoryId = category.CategoryId,
            CategoryName = category.Name,
        }).ToListAsync();


        data = data.ToList().Select<CarOutPut, CarOutPut>((u, i) =>
        {
            u.RowIndex = (input.Current - 1) * input.Size + (i + 1);
            u.StatusText = u.Status.GetDescription();
            u.StudyDeviceStatusText = ((StudyDeviceStatusEnum)Convert.ToInt32(u.StudyDeviceStatus)).GetDescription();

            u.CategoryDatas = userCategoryData.Where(m => m.UserId == u.UseUserId).ToList();
            return u;
        }).ToList();


        var result =
            data.ToSqlSugarPagedList(input.Current, input.Size, totalNumber);


        return result;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsByCarNumber(Guid id, Guid tenantId, string carNumber)
    {
        return await IsAnyAsync(m =>
            m.CarNumber == carNumber && m.Id != id && m.TenantId == tenantId && m.IsDelete == false);
    }


    /// <inheritdoc />
    public async Task<bool> ExistsById(Guid id)
    {
        return await IsAnyAsync(m => m.Id == id);
    }

    /// <inheritdoc />
    public async Task<CarOutPut> GetBySimCardNo(string simCardNo)
    {
        return await Context.Queryable<CarEntity>()
            .LeftJoin<TenantEntity>((m, n) => m.TenantId == n.Id)
            .Where((m, n) => m.SimCardNo == simCardNo)
            .Select((m, n) => new CarOutPut
            {
                Id = m.Id,
                CarNumber = m.CarNumber,
                TenantId = n.Id,
                TenantName = n.TenantName,
                CarType = m.CarType
            })
            .SingleAsync();
    }

    #endregion 查询

    #region 编辑

    /// <inheritdoc />
    public async Task<bool> Update(CarInPut input)
    {
        if (string.IsNullOrEmpty(input.CarNumber))
            throw Oops.Bah("请输入正确的车牌号码");

        if (await ExistsByCarNumber(input.Id, UserManager.TenantId, input.CarNumber))
            throw Oops.Bah("车牌号码重复，请检查重试");

        if (input.CarNumber.Length != 7 && input.CarNumber.Length != 8)
            throw Oops.Bah("车牌号码必须是 7 位 或 8 位");


        var data = await GetSingleAsync(m => m.Id == input.Id && m.IsDelete == false);

        if (data == null)
            throw Oops.Bah("车辆信息为空，刷新页面重试!");

        data.CarNumber = input.CarNumber;
        data.CarType = input.CarType;
        data.ValidDate = input.ValidDate;
        data.InsuranceValidDate = input.InsuranceValidDate;
        data.CompulsoryInsuranceValidDate = input.CompulsoryInsuranceValidDate;
        data.TestingDate = input.TestingDate;
        data.SecurityTestingDate = input.SecurityTestingDate;
        data.CarType = input.CarType;
        data.CarModelId = input.CarModelId;

        data.Brand = string.IsNullOrEmpty(input.Brand) ? "" : input.Brand;
        data.Model = string.IsNullOrEmpty(input.Model) ? "" : input.Model;
        data.SimCardNo = string.IsNullOrEmpty(input.SimCardNo) ? "" : input.SimCardNo;
        data.RegisterDate = input.RegisterDate;
        data.NickName = string.IsNullOrEmpty(input.NickName) ? "" : input.NickName;
        data.EngineNumber = string.IsNullOrEmpty(input.EngineNumber) ? "" : input.EngineNumber;
        data.Vin = string.IsNullOrEmpty(input.Vin) ? "" : input.Vin;
        data.Status = input.Status;
        data.StatusDetail = string.IsNullOrEmpty(data.StatusDetail) ? "" : data.StatusDetail;

        data.StudyDeviceStatus = input.StudyDeviceStatus;
        data.StudyDeviceStatusDetail =
            string.IsNullOrEmpty(data.StudyDeviceStatusDetail) ? "" : data.StudyDeviceStatusDetail;


        data.JxDeptId = input.JxDeptId;
        data.JxFieldId = input.JxFieldId;
        data.FieldId = input.FieldId;
        data.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;

        data.CertificateNumber = string.IsNullOrEmpty(input.CertificateNumber) ? "" : input.CertificateNumber;
        data.TransferOutDate = input.TransferOutDate;


        if (!await UpdateAsync(data))
            throw Oops.Bah("更新车辆信息失败");

        var carUser = await Context.Queryable<CarUserEntity>().Where(m => m.CarId == data.Id && m.IsDelete == false).FirstAsync();

        if (carUser == null)
        {
            if (input.UseUserId != Guid.Empty)
            {
                carUser = new CarUserEntity
                {
                    Id = Guid.NewGuid(),
                    TenantId = UserManager.TenantId,
                    CreateTime = DateTime.Now,
                    CreateUserId = UserManager.UserId,
                    CarId = data.Id,
                    UserId = input.UseUserId,
                };

                await Context.Insertable(carUser).ExecuteCommandAsync();
            }
        }
        else if (carUser.UserId != input.UseUserId)
        {
            carUser.Delete();
            await Context.Updateable(carUser).ExecuteCommandAsync();

            if (input.UseUserId != Guid.Empty)
            {
                carUser = new CarUserEntity
                {
                    Id = Guid.NewGuid(),
                    TenantId = UserManager.TenantId,
                    CreateTime = DateTime.Now,
                    CreateUserId = UserManager.UserId,
                    CarId = data.Id,
                    UserId = input.UseUserId,
                };

                await Context.Insertable(carUser).ExecuteCommandAsync();
            }
        }

        // 清除缓存
        await ClearCarCache(data.TenantId);

        return true;
    }


    /// <inheritdoc />
    public async Task<bool> UpdateJTNum(Guid carId, long jTNum)
    {
        var car = await GetSingleAsync(m => m.Id == carId);

        if (car == null)
            throw Oops.Bah("车辆信息为空，刷新页面重试");

        car.JtNum = jTNum;

        if (!await UpdateAsync(car))
            throw Oops.Bah("更新备案信息失败");

        return true;
    }

    #endregion 编辑

    #region 添加

    /// <inheritdoc />
    public async Task<CarEntity> Add(CarInPut input)
    {
        if (string.IsNullOrEmpty(input.CarNumber))
            throw Oops.Bah("请输入正确的车牌号码");

        if (await ExistsByCarNumber(input.Id, UserManager.TenantId, input.CarNumber))
            throw Oops.Bah("车牌号码重复，请检查重试");


        if (input.CarNumber.Length != 7 && input.CarNumber.Length != 8)
            throw Oops.Bah("车牌号码必须是 7 位 或 8 位");

        var car = input.Adapt<CarEntity>();
        car.Create();

        if (input.TenantId != Guid.Empty)
            car.TenantId = input.TenantId;
        else
            car.TenantId = UserManager.TenantId;
        car.EngineNumber = string.IsNullOrEmpty(input.EngineNumber) ? "" : input.EngineNumber;
        car.Vin = string.IsNullOrEmpty(input.Vin) ? "" : input.Vin;
        car.CarPhone = string.IsNullOrEmpty(car.CarPhone) ? "" : car.CarPhone;
        car.NickName = string.IsNullOrEmpty(car.NickName) ? "" : car.NickName;
        car.CarType = string.IsNullOrEmpty(car.CarType) ? "" : car.CarType;
        car.EngineNumber = string.IsNullOrEmpty(car.EngineNumber) ? "" : car.EngineNumber;
        car.Vin = string.IsNullOrEmpty(car.Vin) ? "" : car.Vin;
        car.Remark = string.IsNullOrEmpty(car.Remark) ? "" : car.Remark;
        car.Model = string.IsNullOrEmpty(car.Model) ? "" : car.Model;
        car.Brand = string.IsNullOrEmpty(car.Brand) ? "" : car.Brand;
        car.SimCardNo = string.IsNullOrEmpty(car.SimCardNo) ? "" : car.SimCardNo;

        car.StatusDetail = string.IsNullOrEmpty(car.StatusDetail) ? "" : car.StatusDetail;
        car.StudyDeviceStatusDetail =
            string.IsNullOrEmpty(car.StudyDeviceStatusDetail) ? "" : car.StudyDeviceStatusDetail;


        car.JxDeptId = input.JxDeptId;
        car.JxFieldId = input.JxFieldId;
        car.FieldId = input.FieldId;
        car.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;

        car.CertificateNumber = string.IsNullOrEmpty(input.CertificateNumber) ? "" : input.CertificateNumber;
        car.TransferOutDate = input.TransferOutDate;

        if (!await InsertAsync(car))
            throw Oops.Bah("添加车辆信息失败");

        if (input.UseUserId != Guid.Empty)
        {
            var carUser = new CarUserEntity
            {
                Id = Guid.NewGuid(),
                TenantId = UserManager.TenantId,
                CreateTime = DateTime.Now,
                CreateUserId = UserManager.UserId,
                CarId = car.Id,
                UserId = input.UseUserId,
            };

            await Context.Insertable(carUser).ExecuteCommandAsync();
        }

        // 清除缓存
        await ClearCarCache(car.TenantId);

        return car;
    }

    /// <inheritdoc />
    public async Task<bool> Add(CarEntity car)
    {
        car.CarPhone = string.IsNullOrEmpty(car.CarPhone) ? "" : car.CarPhone;
        car.NickName = string.IsNullOrEmpty(car.NickName) ? "" : car.NickName;
        car.CarType = string.IsNullOrEmpty(car.CarType) ? "" : car.CarType;
        car.EngineNumber = string.IsNullOrEmpty(car.EngineNumber) ? "" : car.EngineNumber;
        car.Vin = string.IsNullOrEmpty(car.Vin) ? "" : car.Vin;
        car.Remark = string.IsNullOrEmpty(car.Remark) ? "" : car.Remark;
        car.Model = string.IsNullOrEmpty(car.Model) ? "" : car.Model;
        car.Brand = string.IsNullOrEmpty(car.Brand) ? "" : car.Brand;
        car.SimCardNo = string.IsNullOrEmpty(car.SimCardNo) ? "" : car.SimCardNo;

        var result = await InsertAsync(car);

        if (result)
        {
            // 清除缓存
            await ClearCarCache(car.TenantId);
        }

        return result;
    }


    /// <inheritdoc />
    public async Task<bool> Add(DataTable carTable)
    {
        if (!carTable.Columns.Contains("车牌号码"))
            throw Oops.Bah("当前表内无 【车牌号码】 字段，请先添加以后再上传，如有此字段，请改成【车牌号码】");

        var cars = new List<CarEntity>();

        foreach (DataRow item in carTable.Rows)
        {
            var data = new CarEntity();
            data.Create();
            data.TenantId = UserManager.TenantId;

            data.CarNumber = item["车牌号码"].ParseToString();

            if (data.CarNumber.Length != 7 && data.CarNumber.Length != 8)
                throw Oops.Bah("车牌号码必须是 7 位 或 8 位，错误的车牌号码为[" + data.CarNumber + "]");

            if (!string.IsNullOrEmpty(data.CarNumber) && cars.Where(m => m.CarNumber == data.CarNumber).Count() == 0 &&
                !await ExistsByCarNumber(Guid.Empty, UserManager.TenantId, data.CarNumber))
            {
                if (carTable.Columns.Contains("车辆昵称"))
                    data.NickName = item["车辆昵称"].ParseToString();

                if (string.IsNullOrEmpty(data.NickName))
                    data.NickName = "";

                if (carTable.Columns.Contains("随车电话"))
                    data.CarPhone = item["随车电话"].ParseToString();

                if (string.IsNullOrEmpty(data.CarPhone))
                    data.CarPhone = "";

                if (carTable.Columns.Contains("车辆类型"))
                    data.CarType = item["车辆类型"].ParseToString();
                else if (carTable.Columns.Contains("车型"))
                    data.CarType = item["车辆类型"].ParseToString();
                else
                    throw Oops.Bah("必须要上传 车辆类型 或者 车型 字段");

                if (string.IsNullOrEmpty(data.CarType))
                    data.CarType = "";

                if (data.CarType == "手动挡")
                    data.CarType = "C1";
                if (data.CarType == "自动挡")
                    data.CarType = "C2";


                if (data.CarType != "C1" && data.CarType != "C2" && data.CarType != "C5")
                    throw Oops.Bah("车辆类型 请填写：C1、C2、B2 等类型，当前车型显示为[" + data.CarType + "]，该车牌为[" + data.CarNumber + "]");


                if (carTable.Columns.Contains("发动机号码"))
                    data.EngineNumber = item["发动机号码"].ParseToString();

                if (string.IsNullOrEmpty(data.EngineNumber))
                    data.EngineNumber = "";

                if (carTable.Columns.Contains("车架识别码"))
                    data.Vin = item["车架识别码"].ParseToString();

                if (string.IsNullOrEmpty(data.Vin))
                    data.Vin = "";

                if (carTable.Columns.Contains("型号"))
                    data.Model = item["型号"].ParseToString();

                if (string.IsNullOrEmpty(data.Model))
                    data.Model = "";

                if (carTable.Columns.Contains("品牌"))
                    data.Brand = item["品牌"].ParseToString();

                if (string.IsNullOrEmpty(data.Brand))
                    data.Brand = "";

                if (carTable.Columns.Contains("注册日期"))
                    data.RegisterDate = item["注册日期"].ParseToDateTime();

                if (carTable.Columns.Contains("车辆有效期"))
                    data.ValidDate = item["车辆有效期"].ParseToDateTime();

                if (carTable.Columns.Contains("保险有效期"))
                    data.InsuranceValidDate = item["保险有效期"].ParseToDateTime();

                if (carTable.Columns.Contains("车辆年检日期"))
                    data.TestingDate = item["车辆年检日期"].ParseToDateTime();

                if (carTable.Columns.Contains("交强险有效期"))
                    data.CompulsoryInsuranceValidDate = item["交强险有效期"].ParseToDateTime();

                if (carTable.Columns.Contains("安全技术检测日期"))
                    data.SecurityTestingDate = item["安全技术检测日期"].ParseToDateTime();

                data.NickName = string.IsNullOrEmpty(data.NickName) ? "" : data.NickName;
                data.CarType = string.IsNullOrEmpty(data.CarType) ? "" : data.CarType;
                data.Remark = string.IsNullOrEmpty(data.Remark) ? "" : data.Remark;
                data.SimCardNo = "";
                data.StatusDetail = "";
                data.Status = CarStatusEnum.Normal;
                data.StudyDeviceStatusDetail = "";

                cars.Add(data);
            }
        }

        if (!await InsertAsync(cars)) throw Oops.Bah("添加数据失败");

        // 清除缓存
        await ClearCarCache(UserManager.TenantId);

        return true;
    }

    #endregion 添加

    #region 缓存

    /// <summary>
    /// 清除车辆信息的缓存
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task ClearCarCache(Guid tenantId)
    {
        var key = CacheConst.Cache_Car;
        await _simpleCacheService.HashDel<List<CarEntity>>(key, tenantId.ToString());
    }

    /// <summary>
    /// 获取车辆信息的缓存
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task<List<CarEntity>> GetCarCache(Guid tenantId)
    {
        var key = CacheConst.Cache_Car;
        var cars = await _simpleCacheService.HashGetOne<List<CarEntity>>(key, tenantId.ToString());
        if (cars == null)
        {
            cars = await Context.Queryable<CarEntity>()
                .Where(m => m.TenantId == tenantId && m.IsDelete == false)
                .ToListAsync();

            await _simpleCacheService.HashAdd(key, tenantId.ToString(), cars);
        }

        return cars;
    }

    #endregion 缓存
}