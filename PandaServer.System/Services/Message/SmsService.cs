using Newtonsoft.Json;
using PandaServer.System.Services.Message.Dto;
using TencentCloud.Common;
using TencentCloud.Common.Profile;
using TencentCloud.Sms.V20210111;
using TencentCloud.Sms.V20210111.Models;

namespace PandaServer.System.Services.Message;

/// <summary>
///     <inheritdoc cref="ISmsService" />
/// </summary>
public class SmsService : ISmsService
{
    private readonly ITinreeService _tinreeService;


    public SmsService(ITinreeService tinreeService)
    {
        _tinreeService = tinreeService;
    }

    /// <inheritdoc />
    public async Task<SendResult> Send(string phone, string content, string userName, string passWord, string sign)
    {
        return await _tinreeService.Send(phone, content, userName, passWord, sign);
    }

    /// <inheritdoc />
    public async Task<SendResult> Send(string phone, string content, string sign)
    {
        // 使用原有的 Tinree 服务发送短信
        // 这里需要获取配置信息
        var userName = ""; // 从配置获取
        var passWord = ""; // 从配置获取
        return await _tinreeService.Send(phone, content, userName, passWord, sign);
    }

    /// <inheritdoc />
    public async Task<SendResult> SendVerifyCode(string phone, string verifyCode, string sign)
    {
        // 使用腾讯云发送验证码
        return await SendTencentSms(phone, verifyCode, sign);
    }

    /// <summary>
    /// 使用腾讯云发送验证码短信
    /// </summary>
    /// <param name="phone">手机号码</param>
    /// <param name="verifyCode">验证码</param>
    /// <param name="sign">签名</param>
    /// <returns></returns>
    private async Task<SendResult> SendTencentSms(string phone, string verifyCode, string sign)
    {

        try
        {
            // 创建认证对象
            var credential = new Credential
            {
                SecretId = "AKIDFTZcWHqCRcicJo7B6oNJ8j6FBtNkurb7",
                SecretKey = "a0HpGhz42vkR1Wr0zsYFwmT6WkneP5f6"
            };

            // 创建客户端配置
            var clientProfile = new ClientProfile();
            var httpProfile = new HttpProfile();
            httpProfile.Endpoint = "sms.tencentcloudapi.com";
            clientProfile.HttpProfile = httpProfile;

            // 创建短信客户端
            var client = new SmsClient(credential, "ap-guangzhou", clientProfile);

            // 创建发送短信请求
            var request = new SendSmsRequest();
            request.SmsSdkAppId = "1400033070";
            request.SignName = sign;
            request.TemplateId = "47393"; // 模板ID，需要在腾讯云控制台申请
            request.TemplateParamSet = new string[] { verifyCode, "10" }; // 验证码参数
            request.PhoneNumberSet = new string[] { phone };

            // 发送请求j
            var response = await client.SendSms(request);
            Console.WriteLine(response);
            Console.WriteLine(JsonConvert.SerializeObject(response));

            // 检查发送结果
            if (response.SendStatusSet != null && response.SendStatusSet.Length > 0)
            {
                var sendStatus = response.SendStatusSet[0];
                if (sendStatus.Code == "Ok")
                {
                    return new SendResult
                    {
                        Success = true,
                        Message = "验证码发送成功"
                    };
                }
                else
                {
                    return new SendResult
                    {
                        Success = false,
                        Message = $"验证码发送失败：{sendStatus.Code} - {sendStatus.Message}"
                    };
                }
            }
            else
            {
                return new SendResult
                {
                    Success = false,
                    Message = "验证码发送失败：未收到响应"
                };
            }
        }
        catch (Exception ex)
        {
            return new SendResult
            {
                Success = false,
                Message = $"验证码发送失败：{ex.Message}"
            };
        }
    }
}