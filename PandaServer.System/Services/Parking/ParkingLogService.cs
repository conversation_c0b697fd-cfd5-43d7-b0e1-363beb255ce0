using Newtonsoft.Json;
using PandaServer.System.Services.Oss;
using PandaServer.System.Services.Parking.Dto;
using PandaServer.System.Services.Parking.Dto.Json;
using PandaServer.System.Services.Parking.Gate;

namespace PandaServer.System.Services.Parking;

/// <summary>
///     <inheritdoc cref="IParkingLogService" />
/// </summary>
public class ParkingLogService : CustomDbRepository<ParkingLogEntity>, IParkingLogService
{
    private readonly IParkingCarService _parkingCarService;
    private readonly IParkingGateService _parkingGateService;
    private readonly IParkingPayService _parkingPayService;
    private readonly IParkingPlaceService _parkingPlaceService;
    private readonly IQCloudOssService _qCloudOssService;
    private readonly ISimpleCacheService _simpleCacheService;


    public ParkingLogService(IParkingPlaceService parkingPlaceService, IParkingGateService parkingGateService,
        IParkingCarService parkingCarService, IParkingPayService parkingPayService, IQCloudOssService qCloudOssService,
        ISimpleCacheService simpleCacheService)
    {
        _parkingPlaceService = parkingPlaceService;
        _parkingGateService = parkingGateService;
        _parkingCarService = parkingCarService;
        _parkingPayService = parkingPayService;
        _qCloudOssService = qCloudOssService;
        _simpleCacheService = simpleCacheService;
    }

    #region 查询

    /// <inheritdoc />
    public async Task<ParkingLogEntity> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id && m.IsDelete == false);
    }

    /// <inheritdoc />
    public async Task<bool> ExistsById(Guid id)
    {
        return await IsAnyAsync(m => m.Id == id);
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<ParkingLogOutPut>> Page(ParkingLogPageInPut input)
    {
        //分页的 开始 结束时间
        var startTime = Convert.ToDateTime("2000-01-01");
        var endTime = DateTime.Now;

        if (input.createTimes.Count > 0)
        {
            startTime = input.createTimes[0].AddDays(-1);
            endTime = input.createTimes[1].AddDays(1);
        }

        var query = Context.Queryable<ParkingLogEntity>().SplitTable(startTime, endTime)
            .LeftJoin<ParkingPlaceEntity>((m, n) => m.PlaceId == n.Id)
            .LeftJoin<ParkingGateEntity>((m, n, o) => m.GateId == o.Id)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .WhereIF(input.createTimes.Count == 2,
                (m, n) => SqlFunc.LessThan(n.CreateTime, input.createTimes[1].AddDays(1)))
            .WhereIF(input.createTimes.Count == 2,
                (m, n) => SqlFunc.GreaterThanOrEqual(n.CreateTime, input.createTimes[0]))
            .WhereIF(input.placeId != Guid.Empty, (m, n) => m.PlaceId == input.placeId)
            .WhereIF(input.gateId != Guid.Empty, (m, n) => m.GateId == input.gateId)
            .WhereIF(!string.IsNullOrEmpty(input.CarNumber),
                (m, n) => m.CarNumber.ToLower().Contains(input.CarNumber.ToLower().Trim()))
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n, o) => new ParkingLogOutPut
            {
                Id = m.Id,
                PlaceName = n.Name,
                GateName = o.Name,
                Result = m.Result,
                ImageUrl = m.ImageUrl,
                CarNumber = m.CarNumber,
                CreateTime = m.CreateTime,
                CanOpen = m.CanOpen
            });

        var pageInfo = await query.ToPagedListAsync(input.Current, input.Size); //分页

        return pageInfo;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsByCarNumberAndGateId(string carNumber, Guid gateId, int splitSenconds)
    {
        return await IsAnyAsync(m =>
            m.CarNumber == carNumber && m.GateId == gateId &&
            m.CreateTime > DateTime.Now.AddSeconds(0 - splitSenconds));
    }

    /// <inheritdoc />
    public async Task<bool> ExistsByCarNumberAndPlaceId(string carNumber, Guid placeId, int splitSenconds)
    {
        return await IsAnyAsync(m =>
            m.CarNumber == carNumber && m.PlaceId == placeId &&
            m.CreateTime > DateTime.Now.AddSeconds(0 - splitSenconds));
    }

    /// <inheritdoc />
    public async Task<ParkingLogEntity> GetLastInByCarNumberAndPlaceId(string carNumber, Guid placeId)
    {
        return await Context.Queryable<ParkingLogEntity>().SplitTable(m => m.Take(2))
            .LeftJoin<ParkingGateEntity>((m, n) => m.GateId == n.Id)
            .Where((m, n) =>
                n.Direction == 0 && m.CarNumber == carNumber && m.PlaceId == placeId && m.IsDelete == false)
            .OrderBy((m, n) => m.CreateTime, OrderByType.Desc)
            .Select((m, n) => m)
            .FirstAsync();
    }

    #endregion 查询

    #region 添加

    /// <inheritdoc />
    public async Task<ParkingLogEntity> AddIn(ParkingGateEntity gate, ParkingPlaceEntity place, string carNumber,
        string imageUrl)
    {
        return null;
    }

    /// <inheritdoc />
    public async Task<ParkingLogEntity> AddOut(ParkingGateEntity gate, ParkingPlaceEntity place, string carNumber,
        string imageUrl)
    {
        return null;
    }



    /// <inheritdoc />
    public async Task<bool> Add(ParkingLogEntity data)
    {
        return await InsertAsync(data);
    }

    /// <inheritdoc />
    public async Task<bool> Add(List<ParkingLogEntity> datas)
    {
        await DbContext.Db.Insertable(datas)
            .SplitTable().ExecuteCommandAsync();
        //分页能起到不错的性能提升
        // DbContext.Db.Utilities.PageEach(datas, 2000 ,pageList=> {
        //     var x = DbContext.Db.Storageable(pageList)
        //         .SplitUpdate(it => it.Any())//数据库存在更新 根据主键
        //         .SplitInsert(it => true )//其余插入
        //         .ToStorage();//将数据进行分组
        //     x.BulkCopy();
        //     x.BulkUpdate(); //5.0.4.6
        // });
        return true;
    }

    #endregion 添加

    #region 添加日志的主程序

    /// <summary>
    /// 验证相机输入数据并获取对应的闸机和停车场信息
    /// </summary>
    /// <param name="input">相机识别结果输入参数</param>
    /// <returns>返回闸机和停车场信息元组</returns>
    /// <exception cref="Exception">当设备序列号为空、设备未登记或无相关停车场配置时抛出异常</exception>
    private async Task<(ParkingGateEntity gate, ParkingPlaceEntity place)> ValidateAndGetBaseData(CameraPlateInPut input)
    {
        if (string.IsNullOrEmpty(input.alarmInfoPlate.serialno))
            throw Oops.Bah("设备序列号为空");

        var gate = await _parkingGateService.GetBySerialNo(input.alarmInfoPlate.serialno);
        if (gate == null)
            throw Oops.Bah("当前设备未登记");

        var place = await _parkingPlaceService.GetById(gate.PlaceId);
        if (place == null)
            throw Oops.Bah("无相关停车场配置记录");

        return (gate, place);
    }

    /// <summary>
    /// 验证并更新车牌号的缓存状态
    /// </summary>
    /// <param name="carNumber">车牌号</param>
    /// <returns>
    /// true: 可以继续处理
    /// false: 因为重复请求而需要忽略
    /// </returns>
    /// <remarks>
    /// 用于防止在短时间内（5秒内）重复处理同一车牌的请求
    /// </remarks>
    private async Task<bool> ValidateCacheAndUpdate(string carNumber)
    {
        var key = CacheConst.Cache_ParkingCarLog;
        var lastTime = await _simpleCacheService.HashGetOne<long?>(key, carNumber);

        if (lastTime != null && lastTime > (DateTime.Now - "1970-01-01".ParseToDateTime()).TotalSeconds.ParseToLong() - 5)
        {
            return false;
        }

        await _simpleCacheService.HashAdd(key, carNumber, (DateTime.Now - "1970-01-01".ParseToDateTime()).TotalSeconds.ParseToLong(), TimeSpan.FromSeconds(60));
        return true;
    }

    /// <summary>
    /// 获取车辆的白名单和黑名单状态
    /// </summary>
    /// <param name="carNumber">车牌号</param>
    /// <param name="tenantId">租户ID</param>
    /// <param name="placeId">停车场ID</param>
    /// <returns>返回车辆的白名单和黑名单状态元组</returns>
    /// <remarks>
    /// whiteList: 白名单且未过期且不在黑名单中
    /// blackList: 在黑名单中
    /// </remarks>
    private async Task<(bool whiteList, bool blackList)> GetCarListStatus(string carNumber, Guid tenantId, Guid placeId)
    {
        var parkingCar = await _parkingCarService.GetByCarNumberAndPlace(carNumber, tenantId, placeId);
        var whiteList = parkingCar != null && parkingCar.LastFreeTime > DateTime.Now && !parkingCar.BlackList;
        var blackList = parkingCar != null && parkingCar.BlackList;
        return (whiteList, blackList);
    }

    /// <summary>
    /// 创建停车记录日志实体
    /// </summary>
    /// <param name="carNumber">车牌号</param>
    /// <param name="gate">闸机信息</param>
    /// <param name="place">停车场信息</param>
    /// <param name="imageFile">车辆图片数据</param>
    /// <returns>创建的停车记录日志实体</returns>
    /// <remarks>
    /// 包含基本信息初始化和图片上传
    /// </remarks>
    private ParkingLogEntity CreateLogEntity(string carNumber, ParkingGateEntity gate, ParkingPlaceEntity place, string imageFile)
    {
        var log = new ParkingLogEntity();
        log.Create();
        log.TenantId = place.TenantId;
        log.CarNumber = carNumber;
        log.GateId = gate.Id;
        log.PlaceId = place.Id;
        log.ImageUrl = _qCloudOssService.TransferUploadBytes("Parking", imageFile.ParseToImage().ParseToBytes());
        return log;
    }

    /// <summary>
    /// 处理车辆入场逻辑
    /// </summary>
    /// <param name="gate">闸机信息</param>
    /// <param name="place">停车场信息</param>
    /// <param name="carNumber">车牌号</param>
    /// <param name="whiteList">是否为白名单</param>
    /// <param name="blackList">是否为黑名单</param>
    /// <returns>返回处理结果，包含提示信息、是否允许开闸和串口数据</returns>
    /// <remarks>
    /// 处理以下场景：
    /// 1. 仅限白名单且是白名单车辆
    /// 2. 仅限白名单但非白名单车辆
    /// 3. 白名单车辆
    /// 4. 临时车辆
    /// </remarks>
    private async Task<(string result, bool canOpen, List<object> serialData)> ProcessEntranceLogic(
        ParkingGateEntity gate, ParkingPlaceEntity place, string carNumber, bool whiteList, bool blackList)
    {
        var serialData = new List<object>();
        string result;
        bool canOpen;

        if (place.OnlyWhiteListIn && whiteList)
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.白名单车辆入场, carNumber, false, whiteList, blackList));
            result = "白名单车辆入场#1";
            canOpen = true;
        }
        else if (place.OnlyWhiteListIn && !whiteList)
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.非白名单车辆禁止入场, carNumber, false, whiteList, blackList));
            result = "非白名单车辆禁止入场#2";
            canOpen = false;
        }
        else if (whiteList)
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.白名单车辆入场, carNumber, false, whiteList, blackList));
            result = "白名单车辆入场#3";
            canOpen = true;
        }
        else
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.临时车辆入场, carNumber, false, whiteList, blackList));
            result = "临时车辆入场#4";
            canOpen = true;
        }

        return (result, canOpen, serialData);
    }

    /// <summary>
    /// 处理车辆出场逻辑
    /// </summary>
    /// <param name="gate">闸机信息</param>
    /// <param name="place">停车场信息</param>
    /// <param name="carNumber">车牌号</param>
    /// <param name="whiteList">是否为白名单</param>
    /// <param name="blackList">是否为黑名单</param>
    /// <returns>返回处理结果，包含提示信息、是否允许开闸和串口数据</returns>
    /// <remarks>
    /// 处理以下场景：
    /// 1. 白名单车辆出场
    /// 2. 仅限白名单但非白名单车辆
    /// 3. 无入场记录车辆
    /// 4. 需要支付的车辆
    /// </remarks>
    private async Task<(string result, bool canOpen, List<object> serialData)> ProcessExitLogic(
        ParkingGateEntity gate, ParkingPlaceEntity place, string carNumber, bool whiteList, bool blackList)
    {
        var serialData = new List<object>();

        if (whiteList)
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.白名单车辆出场, carNumber, false, whiteList, blackList));
            return ("白名单车辆出场#5", true, serialData);
        }

        if (place.OnlyWhiteListOut && !whiteList)
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.非白名单车辆禁止出场, carNumber, false, whiteList, blackList));
            return ("非白名单车辆禁止出场#6", false, serialData);
        }

        var lastData = await GetLastInByCarNumberAndPlaceId(carNumber, place.Id);
        if (lastData == null)
        {
            if (place.NoInCanNotOut)
            {
                serialData.AddRange(Common.GetData(gate, SceneEnum.出口没有入场记录禁止出场, carNumber, false, whiteList, blackList));
                return ("出口没有入场记录禁止出场#7", false, serialData);
            }

            serialData.AddRange(Common.GetData(gate, SceneEnum.临时车辆出场, carNumber, false, whiteList, blackList));
            return ("无入场记录的车辆出航#8", true, serialData);
        }

        return await ProcessPaymentLogic(gate, place, carNumber, lastData, whiteList, blackList);
    }

    /// <summary>
    /// 处理支付相关逻辑
    /// </summary>
    /// <param name="gate">闸机信息</param>
    /// <param name="place">停车场信息</param>
    /// <param name="carNumber">车牌号</param>
    /// <param name="lastData">最近一次入场记录</param>
    /// <param name="whiteList">是否为白名单</param>
    /// <param name="blackList">是否为黑名单</param>
    /// <returns>返回处理结果，包含提示信息、是否允许开闸和串口数据</returns>
    /// <remarks>
    /// 处理以下场景：
    /// 1. 免费停车（费用为0）
    /// 2. 已完成支付
    /// 3. 在免费时间内
    /// 4. 需要补缴费用
    /// </remarks>
    private async Task<(string result, bool canOpen, List<object> serialData)> ProcessPaymentLogic(
        ParkingGateEntity gate, ParkingPlaceEntity place, string carNumber, ParkingLogEntity lastData,
        bool whiteList, bool blackList)
    {
        var serialData = new List<object>();
        var pays = await _parkingPayService.GetListByCarNumberAndPlaceId(carNumber, place.Id, lastData.CreateTime);
        var parkingMinutes = (DateTime.Now - lastData.CreateTime).TotalMinutes.ParseToInt();
        var totalPayMoney = await _parkingPlaceService.GetPrice(place, parkingMinutes);

        if (totalPayMoney == 0)
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.临时车辆出场, carNumber, false, whiteList, blackList));
            return ($"无需交费直接开门，停车时长：{parkingMinutes}", true, serialData);
        }

        if (totalPayMoney <= pays.Sum(m => m.PayMoney))
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.临时车辆出场, carNumber, false, whiteList, blackList));
            return ($"已经交过费用 {pays.Sum(m => m.PayMoney):F2}，停车时长：{parkingMinutes} 分钟", true, serialData);
        }

        if (pays.Count > 0 && pays[0].PayTime > DateTime.Now.AddSeconds(0 - place.FreeMinutes))
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.临时车辆出场, carNumber, false, whiteList, blackList));
            return ($"已经交过费用 {pays.Sum(m => m.PayMoney):F2}，停车时长：{parkingMinutes} 分钟", true, serialData);
        }

        serialData.AddRange(Common.GetData(gate, SceneEnum.临时车辆出场, carNumber, false, whiteList, blackList));
        var remainingPayment = pays.Count > 0 ? totalPayMoney - pays.Sum(m => m.PayMoney) : totalPayMoney;
        return ($"请缴费 {remainingPayment:F2}，停车时长：{parkingMinutes} 分钟", false, serialData);
    }

    /// <summary>
    /// 处理相机识别结果并生成停车记录
    /// </summary>
    /// <param name="input">相机识别结果输入参数</param>
    /// <returns>生成的停车记录实体</returns>
    /// <remarks>
    /// 主要处理流程：
    /// 1. 验证基础数据
    /// 2. 防重复处理
    /// 3. 获取车辆状态
    /// 4. 根据不同场景（黑名单、无牌车、入场、出场）进行处理
    /// 5. 记录日志并返回结果
    /// </remarks>
    public async Task<ParkingLogEntity> Add(CameraPlateInPut input)
    {
        var (gate, place) = await ValidateAndGetBaseData(input);

        var carNumber = input.alarmInfoPlate.Result.plateResult.license;
        var imageFile = string.IsNullOrEmpty(input.alarmInfoPlate.Result.plateResult.imageFile)
            ? input.alarmInfoPlate.Result.plateResult.imageFragmentFile
            : input.alarmInfoPlate.Result.plateResult.imageFile;

        // if (!await ValidateCacheAndUpdate(carNumber))
        //     return null;

        var (whiteList, blackList) = await GetCarListStatus(carNumber, place.TenantId, place.Id);
        var log = CreateLogEntity(carNumber, gate, place, imageFile);
        var serialData = new List<object>();

        if (blackList)
        {
            serialData.AddRange(Common.GetData(gate,
                gate.Direction == 0 ? SceneEnum.黑名单车辆禁止入场 : SceneEnum.黑名单车辆禁止出场,
                carNumber, false, whiteList, blackList));
            log.Result = "黑名单车辆禁止入场#0";
            log.CanOpen = false;
        }
        else if (carNumber == "无牌车")
        {
            serialData.AddRange(Common.GetData(gate, SceneEnum.无牌车辆请扫码, carNumber, false, whiteList, blackList));
            log.Result = "无牌车辆不自动出场，需要手机扫码出场#10";
            log.CanOpen = false;
        }
        else
        {
            var (result, canOpen, data) = gate.Direction == 0
                ? await ProcessEntranceLogic(gate, place, carNumber, whiteList, blackList)
                : gate.Direction == 1
                    ? await ProcessExitLogic(gate, place, carNumber, whiteList, blackList)
                    : throw Oops.Bah("当前闸机参数错误");

            log.Result = result;
            log.CanOpen = canOpen;
            serialData.AddRange(data);
        }

        log.SerialData = JsonConvert.SerializeObject(serialData);
        log.CanOpen = false; // 测试用 先不开闸

        await InsertAsync(log);
        return log;
    }

    #endregion 添加日志的主程序
}