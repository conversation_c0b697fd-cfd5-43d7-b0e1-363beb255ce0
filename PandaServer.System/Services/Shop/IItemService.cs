using PandaServer.System.Services.Shop.Dtos;

namespace PandaServer.System.Services.Shop;

public interface IItemService
{
    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<ItemOutPut> GetById(Guid id);

    /// <summary>
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<ItemOutPut>> Page(ItemPageInPut inPut);

    /// <summary>
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<List<ItemOutPut>> GetList(ItemPageInPut inPut);

    /// <summary>
    ///     判断名字是否有重复
    /// </summary>
    /// <param name="id"></param>
    /// <param name="tenantId"></param>
    /// <param name="itemName"></param>
    /// <returns></returns>
    Task<bool> ExistsByName(Guid id, Guid tenantId, string itemName);

    /// <summary>
    /// 获取商品列表
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<List<ItemOutPut>> GetList(Guid tenantId);

    /// <summary>
    ///     更新
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<bool> Update(ItemInPut inPut);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<bool> UpdatePayAccountId(ItemInPut inPut);


    /// <summary>
    ///     更新删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> UpdateIsDelete(Guid id);

    /// <summary>
    ///     添加
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<ItemEntity> Add(ItemInPut inPut);

    /// <summary>
    /// 更新商品状态
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <param name="enabled">启用状态</param>
    /// <returns></returns>
    Task<bool> UpdateStatus(Guid id, bool enabled);


    /// <summary>
    /// 获取商品缓存
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<List<ItemOutPut>> GetItemCache(Guid tenantId);
}