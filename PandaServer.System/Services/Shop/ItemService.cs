using NewLife;
using PandaServer.System.Services.Shop.Dtos;
using PandaServer.System.Services.SystemSecurity;

namespace PandaServer.System.Services.Shop;

/// <summary>
///     <inheritdoc cref="IItemService" />
/// </summary>
public class ItemService : CustomDbRepository<ItemEntity>, IItemService, ITransient
{
    private readonly ISimpleCacheService _simpleCacheService;

    public ItemService(ISimpleCacheService simpleCacheService)
    {
        _simpleCacheService = simpleCacheService;
    }

    #region 添加

    /// <inheritdoc />
    public async Task<ItemEntity> Add(ItemInPut input)
    {
        var item = input.Adapt<ItemEntity>();
        item.CarTypes = input.CarTypeList.Join(",");

        if (await ExistsByName(Guid.Empty, UserManager.TenantId, item.ItemName))
            throw Oops.Bah("商品名称不能重复");

        item.Create();
        item.TenantId = UserManager.TenantId;
        item.StartTime = input.SaleTimes[0];
        item.EndTime = input.SaleTimes[1];

        item.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;
        item.PrintText = string.IsNullOrEmpty(input.PrintText) ? "" : input.PrintText;

        item.ExcludeCompanyName = string.IsNullOrEmpty(input.ExcludeCompanyName) ? "" : input.ExcludeCompanyName;
        item.IncludeCompanyName = string.IsNullOrEmpty(input.IncludeCompanyName) ? "" : input.IncludeCompanyName;

        item.CarTypes = NewLife.StringHelper.Join(input.CarTypeList, ",");
        item.IncludeTenantIds = NewLife.StringHelper.Join(input.IncludeTenantIdList, ",");

        item.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;
        item.PrintText = string.IsNullOrEmpty(input.PrintText) ? "" : input.PrintText;

        item.NeedAudit = input.NeedAudit;
        item.NeedTrainingComplete = input.NeedTrainingComplete;

        item.InvoiceSpbm = string.IsNullOrEmpty(input.InvoiceSpbm) ? "" : input.InvoiceSpbm;
        item.InvoiceId = input.InvoiceId;
        item.InvoiceSl = input.InvoiceSl;
        item.InvoiceSs = input.InvoiceSs;

        if (!await InsertAsync(item))
            throw Oops.Bah("添加数据失败，刷新页面重试");

        await ClearItemCache(UserManager.TenantId);
        return item;
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<ItemOutPut> GetById(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        var result = data.Adapt<ItemOutPut>();

        result.SaleTimes = new List<DateTime>();
        result.SaleTimes.Add(result.StartTime);
        result.SaleTimes.Add(result.EndTime);

        result.CarTypeList = result.CarTypes.ParseToStrings();
        result.IncludeTenantIdList = result.IncludeTenantIds.ParseToList<Guid>();

        result.RoleIdList = await Context.Queryable<ItemRoleEntity>().Where(m => m.ItemId == id).Select(m => m.RoleId).ToListAsync();

        return result;
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<ItemOutPut>> Page(ItemPageInPut input)
    {
        var pageInfo = Context.Queryable<ItemEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .LeftJoin<FieldEntity>((m, n, o) => o.Id == m.FieldId)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey), m => m.ItemName.Contains(input.SearchKey.Trim()))
            .WhereIF(input.FieldId != Guid.Empty, m => m.FieldId == input.FieldId)
            .WhereIF(input.IsUser, m => m.ForUserPrice > 0 || m.Price > 0)
            .WhereIF(input.IsAdmin, m => m.ForAdminPrice > 0 || m.ForUserPrice > 0 || m.Price > 0)
            .OrderBy(m => m.EndTime < DateTime.Now ? 0 : m.StartTime > DateTime.Now ? 1 : 2, OrderByType.Desc)
            .OrderBy(m => m.Price, OrderByType.Desc)
            .OrderBy(m => m.SortCode, OrderByType.Desc)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n, o) => new ItemOutPut
            {
                Id = m.Id,
                TenantId = m.TenantId,
                CreateUserId = m.CreateUserId,
                CreateTime = m.CreateTime,
                UpdateUserId = m.UpdateUserId,
                UpdateTime = m.UpdateTime,
                IsDelete = m.IsDelete,
                ItemName = m.ItemName,
                AccountId = m.AccountId,
                Price = m.Price,
                InComeMoney = m.InComeMoney,
                Coupon = m.Coupon,
                CouponPoint = m.CouponPoint,
                Remark = m.Remark,
                ForUserPrice = m.ForUserPrice,
                ForAdminPrice = m.ForAdminPrice,
                FieldId = m.FieldId,
                FirstItemId = m.FirstItemId,
                FirstDays = m.FirstDays,
                TimeLength = m.TimeLength,
                Circles = m.Circles,
                ValidDays = m.ValidDays,
                ExcludeCompanyName = m.ExcludeCompanyName,
                IncludeCompanyName = m.IncludeCompanyName,
                IncludeTenantIds = m.IncludeTenantIds,
                StartTime = m.StartTime,
                EndTime = m.EndTime,
                NeedPrint = m.NeedPrint,
                PrintMiniTicket = m.PrintMiniTicket,
                CarTypes = m.CarTypes,
                SortCode = m.SortCode,
                OrderExamNumber = m.OrderExamNumber,
                OnlyOnce = m.OnlyOnce,
                PrintText = m.PrintText,
                NeedPrintBuyOrderNumber = m.NeedPrintBuyOrderNumber,
                SaleCount = m.SaleCount,
                OnlyMobileBuy = m.OnlyMobileBuy,
                NeedAudit = m.NeedAudit,
                NeedTrainingComplete = m.NeedTrainingComplete,
                CouponValidDays = m.CouponValidDays,
                Status = m.StartTime < DateTime.Now && m.EndTime > DateTime.Now ? "Run" :
                    m.StartTime > DateTime.Now ? "NoStart" :
                    m.EndTime < DateTime.Now ? "End" : "Other",
                FieldNickName = o.NickName,
                OutTable = PayOutTableEnum.Item,
                RoleIdList = SqlFunc.Subqueryable<ItemRoleEntity>().Where(x => x.ItemId == m.Id).ToList(x => x.RoleId)
            });

        var totalNumber = 0;
        var data = pageInfo.ToOffsetPage(input.Current, input.Size == 0 ? 99999 : input.Size, ref totalNumber);

        data = data.ToList().Select<ItemOutPut, ItemOutPut>((u, i) =>
            {
                u.RowIndex = (input.Current - 1) * input.Size + (i + 1);

                return u;
            }).ToList();

        var result =
            data.ToSqlSugarPagedList(input.Current, input.Size, totalNumber);

        return result;
    }

    /// <inheritdoc />
    public async Task<List<ItemOutPut>> GetList(ItemPageInPut input)
    {
        Guid tenantId = input.TenantId;

        if (tenantId == Guid.Empty)
        {
            tenantId = UserManager.TenantId;
        }
        var data = await GetItemCache(tenantId);
        var result = data
            .Where(m => m.TenantId == tenantId && m.IsDelete == false)
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey),
                m => m.ItemName.Contains(input.SearchKey.Trim()))
            .WhereIF(input.FieldId != Guid.Empty, m => m.FieldId == input.FieldId)
            .WhereIF(input.IsUser, m => m.ForUserPrice > 0 || m.Price > 0)
            .WhereIF(input.IsAdmin, m => m.ForAdminPrice > 0 || m.ForUserPrice > 0 || m.Price > 0)
            .WhereIF(input.IsEnableds.Contains(true) && !input.IsEnableds.Contains(false), m => m.EndTime > DateTime.Now && m.StartTime < DateTime.Now)
            .WhereIF(!input.IsEnableds.Contains(true) && input.IsEnableds.Contains(false), m => m.EndTime < DateTime.Now || m.StartTime > DateTime.Now)
            .OrderByDescending(m => m.SortCode)
            .OrderByDescending(m => m.EndTime < DateTime.Now ? 0 : m.StartTime > DateTime.Now ? 1 : 2)
            .OrderByDescending(m => m.CreateTime)
            .Select(m => new ItemOutPut
            {
                Id = m.Id,
                CreateUserName = m.CreateUserName,
                AccountId = m.AccountId,
                CreateTime = m.CreateTime,
                ItemName = m.ItemName,
                StartTime = m.StartTime,
                EndTime = m.EndTime,
                FirstItemId = m.FirstItemId,
                FirstDays = m.FirstDays,
                Status = m.StartTime < DateTime.Now && m.EndTime > DateTime.Now ? "Run" :
                    m.StartTime > DateTime.Now ? "NoStart" :
                    m.EndTime < DateTime.Now ? "End" : "Other",
                ForAdminPrice = m.ForAdminPrice,
                ForUserPrice = m.ForUserPrice,
                IncludeCompanyName = m.IncludeCompanyName,
                IncludeTenantIds = m.IncludeTenantIds,
                ExcludeCompanyName = m.ExcludeCompanyName,
                CarTypes = m.CarTypes,
                Price = m.Price,
                Coupon = m.Coupon,
                CouponPoint = m.CouponPoint,
                FieldNickName = m.FieldNickName,
                OutTable = PayOutTableEnum.Item,
                RoleIdList = SqlFunc.Subqueryable<ItemRoleEntity>().Where(x => x.ItemId == m.Id).ToList(x => x.RoleId)
            })
            .ToList();

        return result;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsByName(Guid id, Guid tenantId, string itemName)
    {
        return await IsAnyAsync(m =>
            m.TenantId == tenantId && m.Id != id && m.ItemName == itemName && m.IsDelete == false);
    }

    /// <inheritdoc />
    public async Task<List<ItemOutPut>> GetList(Guid tenantId)
    {
        return await GetItemCache(tenantId);
    }

    #endregion 查询

    #region 编辑

    /// <inheritdoc />
    public async Task<bool> Update(ItemInPut input)
    {
        var data = await GetSingleAsync(m => m.Id == input.Id);

        if (data == null)
            throw Oops.Bah("数据为空，刷新页面重试");

        if (await ExistsByName(data.Id, data.TenantId, input.ItemName))
            throw Oops.Bah("商品名称不能重复");


        data.ItemName = input.ItemName;
        data.FieldId = input.FieldId;
        data.AccountId = input.AccountId;
        data.Price = input.Price;
        data.InComeMoney = input.InComeMoney;
        data.ForUserPrice = input.ForUserPrice;
        data.ForAdminPrice = input.ForAdminPrice;
        data.Coupon = input.Coupon;
        data.CouponPoint = input.CouponPoint;


        data.FirstItemId = input.FirstItemId;
        data.FirstDays = input.FirstDays;
        data.ValidDays = input.ValidDays;
        data.Circles = input.Circles;
        data.TimeLength = input.TimeLength;
        data.NeedPrint = input.NeedPrint;
        data.OrderExamNumber = input.OrderExamNumber;
        data.PrintMiniTicket = input.PrintMiniTicket;
        data.NeedPrintBuyOrderNumber = input.NeedPrintBuyOrderNumber;

        data.NeedAudit = input.NeedAudit;
        data.NeedTrainingComplete = input.NeedTrainingComplete;

        data.SortCode = input.SortCode;

        data.StartTime = input.SaleTimes[0];
        data.EndTime = input.SaleTimes[1];

        data.ExcludeCompanyName = string.IsNullOrEmpty(input.ExcludeCompanyName) ? "" : input.ExcludeCompanyName;
        data.IncludeCompanyName = string.IsNullOrEmpty(input.IncludeCompanyName) ? "" : input.IncludeCompanyName;

        data.CarTypes = NewLife.StringHelper.Join(input.CarTypeList, ",");
        data.IncludeTenantIds = NewLife.StringHelper.Join(input.IncludeTenantIdList, ",");

        data.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;
        data.PrintText = string.IsNullOrEmpty(input.PrintText) ? "" : input.PrintText;

        data.OnlyMobileBuy = input.OnlyMobileBuy;
        data.SaleCount = input.SaleCount;


        data.InvoiceSpbm = string.IsNullOrEmpty(input.InvoiceSpbm) ? "" : input.InvoiceSpbm;
        data.InvoiceId = input.InvoiceId;
        data.InvoiceSl = input.InvoiceSl;
        data.InvoiceSs = input.InvoiceSs;

        if (!await UpdateAsync(data))
            throw Oops.Bah("更新数据失败，刷新页面重试");

        // 更新 ItemRoleEntity 关联
        // 1. 获取当前所有关联的角色
        var existingRoles = await Context.Queryable<ItemRoleEntity>()
            .Where(m => m.ItemId == input.Id)
            .ToListAsync();

        // 2. 找出需要删除的角色
        var rolesToDelete = existingRoles
            .Where(x => !input.RoleIdList.Contains(x.RoleId))
            .ToList();

        // 3. 找出需要新增的角色
        var existingRoleIds = existingRoles.Select(x => x.RoleId);
        var rolesToAdd = input.RoleIdList
            .Where(roleId => !existingRoleIds.Contains(roleId))
            .Select(roleId => new ItemRoleEntity
            {
                Id = Guid.NewGuid(),
                ItemId = input.Id,
                RoleId = roleId,
                CreateTime = DateTime.Now
            })
            .ToList();

        // 4. 执行删除和添加操作
        if (rolesToDelete.Any())
        {
            await Context.Deleteable<ItemRoleEntity>()
                .Where(m => rolesToDelete.Select(x => x.Id).Contains(m.Id))
                .ExecuteCommandAsync();
        }

        if (rolesToAdd.Any())
        {
            await Context.Insertable(rolesToAdd).ExecuteCommandAsync();
        }

        await ClearItemCache(UserManager.TenantId);
        return true;
    }


    /// <inheritdoc />
    public async Task<bool> UpdatePayAccountId(ItemInPut input)
    {
        var data = await Context.Queryable<ItemEntity>().Where(m => input.Ids.Contains(m.Id)).ToListAsync();

        foreach (var item in data)
        {
            item.AccountId = input.AccountId;
        }

        if (data.Count > 0)
        {
            await Context.Updateable(data).ExecuteCommandAsync();
            await ClearItemCache(UserManager.TenantId);
        }

        return true;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data.IsDelete) throw Oops.Bah("该商品已经删除，刷新页面重试");

        data.Delete();

        if (!await UpdateAsync(data)) throw Oops.Bah("更新失败,刷新重试");

        await ClearItemCache(UserManager.TenantId);
        return true;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateStatus(Guid id, bool enabled)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data == null)
            throw Oops.Bah("数据为空，刷新页面重试");

        if (enabled)
        {
            // 启用时，确保结束时间大于当前时间
            data.EndTime = data.EndTime < DateTime.Now ? DateTime.Now.AddYears(1) : data.EndTime;
            data.StartTime = data.StartTime > DateTime.Now ? DateTime.Now : data.StartTime;
        }
        else
        {
            // 禁用时，将结束时间设置为当前时间
            data.EndTime = DateTime.Now;
        }

        if (!await UpdateAsync(data))
            throw Oops.Bah("更新状态失败，刷新页面重试");

        await ClearItemCache(UserManager.TenantId);
        return true;
    }

    #endregion 编辑

    #region 缓存

    /// <summary>
    /// 清除商品的缓存
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task ClearItemCache(Guid tenantId)
    {
        var key = CacheConst.Cache_Item; //系统配置key
        await _simpleCacheService.HashDel<List<ItemEntity>>(key, tenantId.ToString());
    }

    /// <summary>
    /// 获取商品的缓存
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task<List<ItemOutPut>> GetItemCache(Guid tenantId)
    {
        var key = CacheConst.Cache_Item; //系统配置key
        var items = await _simpleCacheService.HashGetOne<List<ItemOutPut>>(key, tenantId.ToString());
        if (items == null)
        {
            items = await Context.Queryable<ItemEntity>()
            .LeftJoin<FieldEntity>((m, n) => n.Id == m.FieldId)
            .Where(m => m.TenantId == tenantId)
            .Select((m, n) => new ItemOutPut
            {
                Id = m.Id,
                TenantId = m.TenantId,
                CreateUserId = m.CreateUserId,
                CreateTime = m.CreateTime,
                UpdateUserId = m.UpdateUserId,
                UpdateTime = m.UpdateTime,
                IsDelete = m.IsDelete,
                ItemName = m.ItemName,
                AccountId = m.AccountId,
                Price = m.Price,
                InComeMoney = m.InComeMoney,
                Coupon = m.Coupon,
                CouponPoint = m.CouponPoint,
                Remark = m.Remark,
                ForUserPrice = m.ForUserPrice,
                ForAdminPrice = m.ForAdminPrice,
                FieldId = m.FieldId,
                FirstItemId = m.FirstItemId,
                FirstDays = m.FirstDays,
                TimeLength = m.TimeLength,
                Circles = m.Circles,
                ValidDays = m.ValidDays,
                ExcludeCompanyName = m.ExcludeCompanyName,
                IncludeCompanyName = m.IncludeCompanyName,
                IncludeTenantIds = m.IncludeTenantIds,
                StartTime = m.StartTime,
                EndTime = m.EndTime,
                NeedPrint = m.NeedPrint,
                PrintMiniTicket = m.PrintMiniTicket,
                CarTypes = m.CarTypes,
                SortCode = m.SortCode,
                OrderExamNumber = m.OrderExamNumber,
                OnlyOnce = m.OnlyOnce,
                PrintText = m.PrintText,
                NeedPrintBuyOrderNumber = m.NeedPrintBuyOrderNumber,
                SaleCount = m.SaleCount,
                OnlyMobileBuy = m.OnlyMobileBuy,
                NeedAudit = m.NeedAudit,
                NeedTrainingComplete = m.NeedTrainingComplete,
                CouponValidDays = m.CouponValidDays,
                InvoiceSpbm = m.InvoiceSpbm,
                InvoiceId = m.InvoiceId,
                InvoiceSl = m.InvoiceSl,
                InvoiceSs = m.InvoiceSs,
                Status = m.StartTime < DateTime.Now && m.EndTime > DateTime.Now ? "Run" :
                    m.StartTime > DateTime.Now ? "NoStart" :
                    m.EndTime < DateTime.Now ? "End" : "Other",
                FieldNickName = n.NickName,
                OutTable = PayOutTableEnum.Item,
                RoleIdList = SqlFunc.Subqueryable<ItemRoleEntity>().Where(x => x.ItemId == m.Id).ToList(x => x.RoleId)
            })
            .ToListAsync();
            await _simpleCacheService.HashAdd(key, tenantId.ToString(), items);
        }

        return items;
    }

    #endregion 缓存
}