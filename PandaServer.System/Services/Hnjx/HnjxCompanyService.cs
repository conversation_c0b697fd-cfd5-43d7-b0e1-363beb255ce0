using PandaServer.System.Services.Hnjx.Dtos;
using PandaServer.System.Services.SystemManage;
using PandaServer.System.Services.SystemOrganize;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxCompanyService" />
/// </summary>
public class HnjxCompanyService : CustomDbRepository<HnjxCompanyEntity>, IHnjxCompanyService
{
    private readonly IHnjxCompnayCodeService _hnjxCompnayCodeService;
    private readonly IRoleService _roleService;
    private readonly ITenantService _tenantService;
    private readonly IUserLogOnService _userLogOnService;
    private readonly IUserRoleService _userRoleService;
    private readonly IUserService _userService;
    private readonly IUserInfoService _userInfoService;

    public HnjxCompanyService(IUserService userService, IUserLogOnService userLogOnService,
        IHnjxCompnayCodeService hnjxCompnayCodeService, ITenantService tenantService, IRoleService roleService,
        IUserRoleService userRoleService, IUserInfoService userInfoService)
    {
        _userService = userService;
        _userLogOnService = userLogOnService;
        _hnjxCompnayCodeService = hnjxCompnayCodeService;
        _tenantService = tenantService;
        _roleService = roleService;
        _userRoleService = userRoleService;
        _userInfoService = userInfoService;
    }

    #region 新增

    /// <inheritdoc />
    public async Task<string> Add(HnjxCompanyInPut inPut)
    {
        if (string.IsNullOrEmpty(inPut.Code))
            throw Oops.Bah("请填写注册码！");

        var codeData = await _hnjxCompnayCodeService.GetByCode(inPut.Code);
        if (codeData == null)
            throw Oops.Bah("请填写有效注册码！");

        if (codeData.CompanyId != Guid.Empty)
            throw Oops.Bah("当前注册码已经被使用！");

        if (codeData.CreateTime < DateTime.Now.AddHours(-24))
            throw Oops.Bah("当前注册码已经过期！");

        var data = inPut.Adapt<HnjxCompanyEntity>();

        data.Create();
        data.CreateTime = DateTime.Now;
        data.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;
        data.OutId = "";

        data.ShortName = string.IsNullOrEmpty(inPut.ShortName) ? "" : inPut.ShortName;

        if (await ExistsByFullName(Guid.Empty, inPut.TenantId, inPut.FullName))
            throw Oops.Bah("驾校全称重复，请检查全称!");

        if (await ExistsByShortName(Guid.Empty, inPut.TenantId, inPut.ShortName))
            throw Oops.Bah("驾校简称重复，请检查全称!");

        if (!await InsertAsync(data))
            throw Oops.Bah("添加公司信息出错，刷新页面重试");

        codeData.CompanyId = data.Id;
        await _hnjxCompnayCodeService.Edit(codeData);

        var tenant = await _tenantService.GetById(inPut.TenantId);

        var user = new UserEntity();
        user.Create();
        var companyMaxUser = await _userInfoService.GetCountByTenantId(inPut.TenantId);
        user.Account = tenant.AccountRule + companyMaxUser.ToString("000000");

        user.TenantId = inPut.TenantId;
        user.RealName = inPut.ContactPerson.Trim();
        user.Phone = inPut.ContactPersonPhone;
        user.IdCard = "";
        user.IsAdmin = false;
        user.Sex = 1;
        user.IsEnabled = true;
        user.IsAdmin = false;

        if (!await _userInfoService.Add(user))
            throw Oops.Bah("添加账户失败，请联系管理员");


        data.AdminUserId = user.Id;
        await UpdateAdminUserId(data.Id, user.Id);

        var role = await _roleService.GetByName("驾校", inPut.TenantId);

        if (role != null)
        {
            var userRole = new UserRoleEntity();
            userRole.Create();
            userRole.TenantId = inPut.TenantId;
            userRole.UserId = user.Id;

            userRole.RoleId = role.Id;

            await _userRoleService.Add(userRole);
        }


        var passWord = TextHelper.CreateRandomNo(8);

        var logon = new UserLogOnEntity();
        logon.Create();
        logon.UserId = user.Id;
        logon.UserSecretkey = Guid.NewGuid().ToString("N");
        logon.UserPassWord =
            Md5.md5(DESEncrypt.Encrypt(passWord.ToLower(), logon.UserSecretkey).ToLower(), 32).ToLower();

        logon.AllowStartTime = DateTime.Now;
        logon.AllowEndTime = DateTime.Now.AddYears(99);
        logon.LockStartDate = DateTime.Now;
        logon.LockEndDate = DateTime.Now;

        await _userLogOnService.Add(logon);

        return "您学校的登录账号为注册时填写的联系人手机号码，登录密码为：" + passWord + "，登录后请及时修改密码。如忘记密码，请返回登录页面点击重置密码即可。";
        ;
    }

    #endregion 新增


    #region 查询

    /// <inheritdoc />
    public async Task<HnjxCompanyEntity?> GetById(Guid companyId)
    {
        var data = await Context.Queryable<HnjxCompanyEntity>()
            .Where(m => m.IsDelete == false && m.Id == companyId)
            .SingleAsync();

        return data;
    }


    /// <inheritdoc />
    public async Task<string> GetShortNameById(Guid companyId)
    {
        return await GetSingleAsync(m => m.Id == companyId && m.IsDelete == false, m => m.ShortName);
    }



    /// <inheritdoc />
    public async Task<string> GetFullNameById(Guid companyId)
    {
        return await GetSingleAsync(m => m.Id == companyId && m.IsDelete == false, m => m.FullName);
    }

    /// <inheritdoc />
    public async Task<HnjxCompanyEntity?> GetByAdminUserId(Guid adminUserId)
    {
        var data = await Context.Queryable<HnjxCompanyEntity>()
            .Where(m => m.IsDelete == false && m.AdminUserId == adminUserId)
            .SingleAsync();

        return data;
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxCompanyOutPut>> Page(HnjxCompanyPageInPut inPut)
    {
        var query = Context.Queryable<HnjxCompanyEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .LeftJoin<UserEntity>((m, n, o) => m.AdminUserId == o.Id)
            .WhereIF(!string.IsNullOrEmpty(inPut.SearchKey),
                m => m.FullName.Contains(inPut.SearchKey.Trim()) || m.ShortName.Contains(inPut.SearchKey.Trim()))
            .WhereIF(!string.IsNullOrEmpty(inPut.Name),
                m => m.FullName.Contains(inPut.Name.Trim()) || m.ShortName.Contains(inPut.Name.Trim()))
            .WhereIF(!string.IsNullOrEmpty(inPut.LegalPerson), m => m.LegalPerson.Contains(inPut.LegalPerson.Trim()))
            .WhereIF(!string.IsNullOrEmpty(inPut.ContactPerson),
                m => m.ContactPerson.Contains(inPut.ContactPerson.Trim()))
            .WhereIF(!UserManager.IsTenantAdmin, m => m.AdminUserId == UserManager.UserId)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .OrderByDescending(m => m.CreateTime)
            .Select((m, n, o) => new HnjxCompanyOutPut
            {
                Id = m.Id,
                AdminAccount = o.Account,
                FullName = m.FullName,
                ShortName = m.ShortName,
                LegalPerson = m.LegalPerson,
                LegalPersonPhone = m.LegalPersonPhone,
                ContactPerson = m.ContactPerson,
                ContactPersonPhone = m.ContactPersonPhone,
                PostAddress = m.PostAddress,
                CreateUserName = n.RealName,
                CreateTime = m.CreateTime
            });


        var pageInfo = await query.ToPagedListAsync(inPut.Current, inPut.Size); //分页

        return pageInfo;
    }

    /// <summary>
    ///     判断是否  FullName  是否有重复
    /// </summary>
    /// <param name="id"></param>
    /// <param name="tenantId"></param>
    /// <param name="fullName"></param>
    /// <returns></returns>
    private async Task<bool> ExistsByFullName(Guid id, Guid tenantId, string fullName)
    {
        return await IsAnyAsync(m =>
            m.FullName == fullName && m.Id != id && m.TenantId == tenantId && m.IsDelete == false);
    }

    /// <summary>
    ///     判断是否  ShortName  是否有重复
    /// </summary>
    /// <param name="id"></param>
    /// <param name="tenantId"></param>
    /// <param name="shortName"></param>
    /// <returns></returns>
    private async Task<bool> ExistsByShortName(Guid id, Guid tenantId, string shortName)
    {
        return await IsAnyAsync(m =>
            m.ShortName == shortName && m.Id != id && m.TenantId == tenantId && m.IsDelete == false);
    }

    #endregion 查询

    #region 更新

    /// <inheritdoc />
    public async Task<bool> Edit(HnjxCompanyInPut inPut)
    {
        var data = await GetById(inPut.Id);

        if (data == null)
            throw Oops.Bah("当前数据不存在，请稍后重试");


        data.FullName = inPut.FullName;
        data.BusNum = inPut.BusNum.ParseToInt();
        data.TruckNum = inPut.TruckNum.ParseToInt();
        data.TractorNum = inPut.TractorNum.ParseToInt();
        data.CarNum = inPut.CarNum.ParseToInt();
        data.OtherNum = inPut.OtherNum.ParseToInt();
        data.CheckedCarNum = inPut.CheckedCarNum.ParseToInt();
        data.TheoryTrainersNum = inPut.TheoryTrainersNum.ParseToInt();
        data.OperationTrainersNum = inPut.OperationTrainersNum.ParseToInt();
        data.QualificationRecord = inPut.QualificationRecord;
        data.LegalPerson = inPut.LegalPerson;
        data.LegalPersonPhone = inPut.LegalPersonPhone;
        data.ContactPerson = inPut.ContactPerson;
        data.ContactPersonPhone = inPut.ContactPersonPhone;
        data.CityId = inPut.CityId;
        data.PostAddress = inPut.PostAddress;
        data.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;
        data.CoachPay = inPut.CoachPay;

        if (UserManager.IsTenantAdmin)
        {
            data.ShortName = inPut.ShortName;
            data.AdminUserId = inPut.AdminUserId.ParseToGuid();
        }

        data.Modify();

        if (!await UpdateAsync(data))
            throw Oops.Bah("更新数据失败，稍后重试");

        var user = await _userInfoService.GetById(data.AdminUserId);

        if (user != null)
        {
            user.RealName = data.ContactPerson;
            user.Phone = data.ContactPersonPhone;

            user.Modify();

            await _userInfoService.Update(user);
        }

        return true;
        ;
    }

    /// <inheritdoc />
    public async Task<bool> Edit(HnjxCompanyEntity data)
    {
        return await UpdateAsync(data);
    }


    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetById(id);

        if (data == null)
            throw Oops.Bah("未找到对象");

        data.Delete();
        return await UpdateAsync(data);
    }

    /// <summary>
    ///     单独更新  管理员
    /// </summary>
    /// <param name="companyId"></param>
    /// <param name="adminUserId"></param>
    /// <returns></returns>
    private async Task<bool> UpdateAdminUserId(Guid companyId, Guid adminUserId)
    {
        var result = await Context.Updateable<HnjxCompanyEntity>().SetColumns(m =>
            m.AdminUserId == adminUserId
        ).Where(m => m.Id == companyId).ExecuteCommandAsync();


        return result > 0;
    }

    #endregion 更新
}