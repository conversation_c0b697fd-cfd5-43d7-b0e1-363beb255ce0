using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxImageService" />
/// </summary>
public class HnjxImageService : CustomDbRepository<HnjxImageEntity>, IHnjxImageService
{
    #region 添加

    public async Task<bool> Add(HnjxImageInPut inPut)
    {
        var data = await GetSingleAsync(m => m.ImageId == inPut.ImageId && m.ApplyType == inPut.ApplyType);

        if (data != null) return false;

        data = new HnjxImageEntity();
        data.ApplyType = inPut.ApplyType;
        data.ImageId = inPut.ImageId;

        var result = await Context.Insertable(data).ExecuteCommandAsync();

        return result == 1;
    }

    #endregion 添加

    #region 删除

    public async Task<bool> Delete(HnjxImageInPut inPut)
    {
        var data = await GetSingleAsync(m => m.ImageId == inPut.ImageId && m.ApplyType == inPut.ApplyType);

        if (data == null) return false;

        var result = await Context.Deleteable<HnjxImageEntity>()
            .Where(m => m.ImageId == inPut.ImageId && m.ApplyType == inPut.ApplyType).ExecuteCommandAsync();

        return result == 1;
    }

    #endregion 删除

    #region 查询

    /// <inheritdoc />
    public async Task<List<HnjxImageOutPut>> GetListByApplyType(HnjxApplyTypeEnum applyType)
    {
        var data = await GetListAsync(m => m.ApplyType == applyType);

        var result = new List<HnjxImageOutPut>();

        foreach (var item in result)
        {
            var outPut = item.Adapt<HnjxImageOutPut>();
            outPut.ImageIdValue = item.GetDescriptionByEnum<HnjxCoachImageIdEnum>();
            result.Add(outPut);
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<List<HnjxImageOutPut>> GetList(HnjxApplyTypeEnum applyType)
    {
        var data = await GetListAsync(m => m.ApplyType == applyType);
        var result = new List<HnjxImageOutPut>();

        foreach (HnjxCoachImageIdEnum item in Enum.GetValues(typeof(HnjxCoachImageIdEnum)))
            if (data.Any(m => m.ImageId == item))
                result.Add(new HnjxImageOutPut
                {
                    ApplyType = applyType,
                    ImageId = item,
                    ImageIdValue = item.GetDescriptionByEnum<HnjxCoachImageIdEnum>(),
                    MustUpload = true
                });
            else
                result.Add(new HnjxImageOutPut
                {
                    ApplyType = applyType,
                    ImageId = item,
                    ImageIdValue = item.GetDescriptionByEnum<HnjxCoachImageIdEnum>(),
                    MustUpload = false
                });
        return result;
    }

    /// <inheritdoc />
    public async Task<HnjxImageEntity> GetByApplyTypeAndImageId(HnjxApplyTypeEnum applyType,
        HnjxCoachImageIdEnum imageId)
    {
        return await GetSingleAsync(m => m.ApplyType == applyType && m.ImageId == imageId);
    }

    #endregion 查询
}