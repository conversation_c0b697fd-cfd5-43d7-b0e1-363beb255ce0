using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

public interface IHnjxImageService : ITransient
{
    /// <summary>
    ///     通过申请类型 返回 照片类型的列表
    /// </summary>
    /// <param name="applyType"></param>
    /// <returns></returns>
    Task<List<HnjxImageOutPut>> GetListByApplyType(HnjxApplyTypeEnum applyType);

    /// <summary>
    ///     返回 全部的图片类型 并做好了 是否必须要上传的标记
    /// </summary>
    /// <param name="applyType"></param>
    /// <returns></returns>
    Task<List<HnjxImageOutPut>> GetList(HnjxApplyTypeEnum applyType);

    /// <summary>
    ///     通过申请类型  和 照片类型 返回 实体
    /// </summary>
    /// <param name="applyType"></param>
    /// <param name="imageId"></param>
    /// <returns></returns>
    Task<HnjxImageEntity> GetByApplyTypeAndImageId(HnjxApplyTypeEnum applyType,
        HnjxCoachImageIdEnum imageId);
}