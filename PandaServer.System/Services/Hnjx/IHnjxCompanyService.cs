using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     湖南驾协  下属驾校 的相关的
/// </summary>
public interface IHnjxCompanyService : ITransient
{
    /// <summary>
    ///     通过 Id 获取相关的驾校的  实体
    /// </summary>
    /// <param name="companyId"></param>
    /// <returns></returns>
    Task<HnjxCompanyEntity?> GetById(Guid companyId);


    /// <summary>
    ///     返回的 企业的 名称
    /// </summary>
    /// <param name="companyId"></param>
    /// <returns></returns>
    Task<string> GetShortNameById(Guid companyId);


    /// <summary>
    ///     返回的 企业的 全名
    /// </summary>
    /// <param name="companyId"></param>
    /// <returns></returns>
    Task<string> GetFullNameById(Guid companyId);

    /// <summary>
    ///     通过 驾校所属管理员的 用户编号 获取相关的驾校的  实体
    /// </summary>
    /// <param name="adminUserId"></param>
    Task<HnjxCompanyEntity?> GetByAdminUserId(Guid adminUserId);


    /// <summary>
    ///     分页
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxCompanyOutPut>> Page(HnjxCompanyPageInPut inPut);


    /// <summary>
    ///     更新
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<bool> Edit(HnjxCompanyInPut inPut);

    /// <summary>
    ///     直接更新实体
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task<bool> Edit(HnjxCompanyEntity data);

    /// <summary>
    ///     删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> UpdateIsDelete(Guid id);

    /// <summary>
    ///     新增
    /// </summary>
    /// <param name="inPu"></param>
    /// <returns></returns>
    Task<string> Add(HnjxCompanyInPut inPu);
}