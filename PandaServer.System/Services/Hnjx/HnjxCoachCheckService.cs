using PandaServer.System.Services.Wx;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxCoachCheckService" />
/// </summary>
public class HnjxCoachCheckService : CustomDbRepository<HnjxCoachCheckEntity>, IHnjxCoachCheckService
{
    private readonly IHnjxClassService _hnjxClassService;
    private readonly IHnjxCoachService _hnjxCoachService;
    private readonly IHnjxWxUserService _hnjxWxUserService;
    private readonly IWxConfigService _wxConfigService;
    private readonly IWxMessageService _wxMessageService;

    public HnjxCoachCheckService(IHnjxCoachService hnjxCoachService, IHnjxWxUserService hnjxWxUserService,
        IWxConfigService wxConfigService, IWxMessageService wxMessageService, IHnjxClassService hnjxClassService)
    {
        _hnjxCoachService = hnjxCoachService;
        _hnjxWxUserService = hnjxWxUserService;
        _wxConfigService = wxConfigService;
        _wxMessageService = wxMessageService;
        _hnjxClassService = hnjxClassService;
    }


    /// <inheritdoc />
    public async Task<bool> UpdateCheck(List<Guid> ids, HnjxCheckStatusEnum checkStatusEnum, string checkRemark)
    {
        if (!UserManager.IsTenantAdmin)
            throw Oops.Bah("您没有相关操作权限");

        // var config = await _wxConfigService.GetByTenantId(UserManager.TenantId);

        foreach (var id in ids)
        {
            var coach = await _hnjxCoachService.GetById(id);

            if (coach == null)
                throw Oops.Bah("教练信息无法查询，更新操作终止");


            var checkEntity = new HnjxCoachCheckEntity();

            checkEntity.Create();
            checkEntity.TenantId = UserManager.TenantId;
            checkEntity.CoachId = id;
            checkEntity.CheckTime = DateTime.Now;
            checkEntity.CheckUserId = UserManager.UserId;
            checkEntity.CheckStatus = checkStatusEnum;
            checkEntity.CheckRemark = string.IsNullOrEmpty(checkRemark) ? "" : checkRemark;


            if (!await InsertAsync(checkEntity))
                throw Oops.Bah("添加数据发生故障");


            coach.CheckRemark = checkEntity.CheckRemark;
            coach.CheckTime = checkEntity.CheckTime;
            coach.FileCheckTime = checkEntity.CheckTime;
            coach.CheckStatus = checkEntity.CheckStatus;
            coach.CheckUserId = UserManager.UserId;

            coach.Modify();

            await _hnjxCoachService.Edit(coach);

            // if (checkStatusEnum == HnjxCheckStatusEnum.Qualified && config != null)
            // {
            //     var users = await _hnjxWxUserService.GetListByUserId(coach.Id);

            //     foreach (var user in users)
            //         await _wxMessageService.SendMessage(user.OpenId,
            //             "Ii8us1DVzTzBdVXtvMeJvfIESXMB31m9Xca7tauJdD0", new
            //             {
            //                 thing1 = new
            //                 {
            //                     value = coach.xm + "-资料核验"
            //                 },
            //                 thing2 = new
            //                 {
            //                     value = "合格"
            //                 },
            //                 time5 = new
            //                 {
            //                     value = DateTime.Now.ToString("yyyy-MM-dd HH:mm")
            //                 },
            //                 thing3 = new
            //                 {
            //                     value = coach.ApplyType == HnjxApplyTypeEnum.Training ? "请耐心等待分班结果" : "请登录完成缴费"
            //                 }
            //             });
            // }
        }

        return true;
    }
}