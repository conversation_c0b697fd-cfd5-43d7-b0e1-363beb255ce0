using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

public interface IHnjxCoachWorkService : ITransient
{
    /// <summary>
    ///     返回实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HnjxCoachWorkEntity?> GetById(Guid id);


    /// <summary>
    ///     通过 教练的 Id 返回 实体列表
    /// </summary>
    /// <param name="coachId"></param>
    /// <returns></returns>
    Task<List<HnjxCoachWorkEntity>> GetListByCoachId(Guid coachId);

    /// <summary>
    ///     更新删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> UpdateIsDelete(Guid id);


    /// <summary>
    ///     编辑
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<bool> Edit(HnjxCoachWorkInPut inPut);


    /// <summary>
    ///     编辑
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<HnjxCoachWorkEntity> Add(HnjxCoachWorkInPut inPut);
}