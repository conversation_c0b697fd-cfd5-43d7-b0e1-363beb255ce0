using System.IO.Compression;
using PandaServer.System.Services.Hnjx.Dtos;
using PandaServer.System.Services.SystemSecurity;
using SixLabors.Fonts;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.Processing;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxCoachService" />
/// </summary>
public class HnjxCoachService : CustomDbRepository<HnjxCoachEntity>, IHnjxCoachService
{
    private readonly IHnjxCoachImageService _hnjxCoachImageService;
    private readonly IHnjxCoachQuestionService _hnjxCoachQuestionService;
    private readonly IHnjxCompanyService _hnjxCompanyService;
    private readonly IHnjxImageService _hnjxImageService;
    private readonly IHnjxMaxIdService _hnjxMaxIdService;

    private readonly IEasyLogService _easyLogService;

    public HnjxCoachService(IHnjxCoachQuestionService hnjxCoachQuestionService, IHnjxCompanyService hnjxCompanyService,
        IHnjxImageService hnjxImageService, IHnjxCoachImageService hnjxCoachImageService,
        IHnjxMaxIdService hnjxMaxIdService, IEasyLogService easyLogService)
    {
        _hnjxCoachQuestionService = hnjxCoachQuestionService;
        _hnjxCompanyService = hnjxCompanyService;
        _hnjxImageService = hnjxImageService;
        _hnjxCoachImageService = hnjxCoachImageService;
        _hnjxMaxIdService = hnjxMaxIdService;
        _easyLogService = easyLogService;
    }

    #region 添加

    /// <inheritdoc />
    public async Task<HnjxCoachEntity> Add(HnjxCoachInPut inPut)
    {
        if (!inPut.sfzmhm.IsIDCard18())
            throw Oops.Bah("身份证号码错误!");

        if (!inPut.yddh.IsMobileNumber())
            throw Oops.Bah("手机号码格式错误!");

        if (inPut.TeachType != "理论" && inPut.TeachCarTypes.Count == 0)
            throw Oops.Bah("非理论教练必须要选择准教车型!");

        if (await ExistsNoCompleteBySfzmhm(inPut.sfzmhm))
            throw Oops.Bah("身份证号码重复，无法重复录入，请联系驾协工作人员处理历史数据!");

        if (await ExistsBlackListBySfzmhm(inPut.sfzmhm))
            throw Oops.Bah("当前身份证有一个黑名单记录，无法进行任何操作，请联系驾协的工作人员!");


        var data = inPut.Adapt<HnjxCoachEntity>();
        data.Create();
        data.xm = data.xm.Trim();
        data.sfzmhm = data.sfzmhm.Trim();
        data.yddh = data.yddh.Trim();

        data.TenantId = UserManager.TenantId;
        data.CheckStatus = HnjxCheckStatusEnum.WaitSubmit;

        data.CarType = inPut.CarTypes.Count == 0 ? "" : string.Join(",", inPut.CarTypes);

        data.TeachType = string.IsNullOrEmpty(inPut.TeachType) ? "" : inPut.TeachType;
        data.OldCardNo = string.IsNullOrEmpty(inPut.OldCardNo) ? "" : inPut.OldCardNo;

        if (data.TeachType == "理论")
            data.TeachCarType = "";
        else
            data.TeachCarType = inPut.TeachCarTypes.Count == 0 ? "" : string.Join(",", inPut.TeachCarTypes);

        if (UserManager.IsTenantAdmin)
            data.CardNo = string.IsNullOrEmpty(inPut.CardNo) ? "" : inPut.CardNo;
        else
            data.CardNo = "";

        if (!UserManager.IsTenantAdmin)
        {
            var company = await _hnjxCompanyService.GetByAdminUserId(UserManager.UserId);

            if (company == null)
                throw Oops.Bah("当前账户未绑定任何驾校，没有添加教练的权限!");
            data.CompanyId = company.Id;
        }

        if (data.CompanyId == Guid.Empty)
            throw Oops.Bah("请选择所属的驾校!");

        if (data.ApplyType == HnjxApplyTypeEnum.Training && await ExistsTrainingBySfzmhm(data.CompanyId, data.sfzmhm))
            throw Oops.Bah("同一个身份证培训只能录一次，当前教练如果是增加准教类别，请将培训类型选择增教!");

        data.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;
        data.CheckRemark = "";
        data.OutId = "";

        data.TrackingNumber = "";
        data.TrackingTime = Convert.ToDateTime("2000-01-01");

        data.Education = string.IsNullOrEmpty(inPut.Education) ? "" : inPut.Education;
        data.TeachType = string.IsNullOrEmpty(inPut.TeachType) ? "" : inPut.TeachType;
        data.OldCardNo = string.IsNullOrEmpty(inPut.OldCardNo) ? "" : inPut.OldCardNo;


        if (await InsertAsync(data))
            return data;

        throw Oops.Bah("添加数据失败");
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<HnjxCoachOutPut?> GetById(Guid id)
    {
        if (id == Guid.Empty)
            return null;
        var data = await Context.Queryable<HnjxCoachEntity>().Where(m => m.Id == id && m.IsDelete == false).FirstAsync();


        var result = data.Adapt<HnjxCoachOutPut>();

        if (!string.IsNullOrEmpty(result.CarType))
        {
            result.CarTypes = result.CarType.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).ToList();
        }
        if (!string.IsNullOrEmpty(result.TeachCarType))
        {
            result.TeachCarTypes = result.TeachCarType.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).ToList();
        }
        return result;
    }


    /// <inheritdoc />
    public async Task<List<HnjxCoachEntity>> GetByIds(List<Guid> ids)
    {
        return await GetListAsync(m => ids.Contains(m.Id));
    }

    /// <inheritdoc />
    public async Task<HnjxCoachEntity?> GetByOpenId(string openId)
    {
        if (string.IsNullOrEmpty(openId))
            return null;

        return await Context.Queryable<HnjxCoachEntity>()
            .LeftJoin<WxUserEntity>((m, n) => m.Id == n.UserId)
            .Where((m, n) => n.OpenId == openId)
            .Select(m => m)
            .SingleAsync();
    }

    /// <inheritdoc />
    public async Task<string> GetSfzmhmById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id && m.IsDelete == false, m => m.sfzmhm);
    }

    /// <inheritdoc />
    public async Task<List<HnjxCoachEntity>> GetListBySfzmhmAndXm(string sfzmhm, string xm)
    {
        return await GetListAsync(m => m.sfzmhm == sfzmhm && m.xm == xm && m.IsDelete == false,
            m => m.CreateTime,
            OrderByType.Desc);
    }

    /// <inheritdoc />
    public async Task<List<string>> GetTeachCarTypeList()
    {
        return await Context.Queryable<HnjxCoachEntity>()
            .Where(m => m.IsDelete == false)
            .GroupBy(m => m.TeachCarType)
            .OrderBy(m => m.TeachCarType)
            .Select(m => m.TeachCarType)
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<string> GetCompanyFullNameById(Guid id)
    {
        return await Context.Queryable<HnjxCoachEntity>()
            .LeftJoin<HnjxCompanyEntity>((m, n) => m.CompanyId == n.Id)
            .Where(m => m.Id == id)
            .Select((m, n) => n.FullName)
            .SingleAsync();
    }


    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxCoachOutPut>> Page(HnjxCoachPageInPut inPut)
    {
        var keyWords = inPut.SearchKey.Split(" ".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);

        var pageInfo = Context.Queryable<HnjxCoachEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .LeftJoin<HnjxCompanyEntity>((m, n, o) => m.CompanyId == o.Id)
            .LeftJoin<HnjxClassEntity>((m, n, o, p) => m.ClassId == p.Id)
            .WhereIF(inPut.CompanyId != Guid.Empty, m => m.CompanyId == inPut.CompanyId)
            .WhereIF(!string.IsNullOrEmpty(inPut.CityId),
                (m, n, o) => o.CityId.Substring(0, 4) == inPut.CityId.Substring(0, 4))

            .WhereIF(inPut.CityIds.Count > 0, (m, n, o) => inPut.CityIds.Contains(o.CityId.Substring(0, 4) + "00"))

            .WhereIF(inPut.ClassId != Guid.Empty, m => m.ClassId == inPut.ClassId)
            .WhereIF(!string.IsNullOrEmpty(inPut.xm), m => m.xm.Contains(inPut.xm.Trim()))
            .WhereIF(!string.IsNullOrEmpty(inPut.sfzmhm), m => m.sfzmhm.Contains(inPut.sfzmhm.Trim()))
            .WhereIF(!string.IsNullOrEmpty(inPut.CardNo), m => m.CardNo.Contains(inPut.CardNo.Trim()))
            .WhereIF(!string.IsNullOrEmpty(inPut.OldCardNo), m => m.OldCardNo.Contains(inPut.OldCardNo.Trim()))
            .WhereIF(!string.IsNullOrEmpty(inPut.CarNoStatus) && inPut.CarNoStatus.ParseToInt() == 1,
                m => m.CardNo != "")
            .WhereIF(!string.IsNullOrEmpty(inPut.CarNoStatus) && inPut.CarNoStatus.ParseToInt() == 0,
                m => m.CardNo == "")
            .WhereIF(!string.IsNullOrEmpty(inPut.CompleteStatus) && inPut.CompleteStatus.ParseToInt() == 1,
                m => m.CompleteStatus == true)
            .WhereIF(!string.IsNullOrEmpty(inPut.CompleteStatus) && inPut.CompleteStatus.ParseToInt() == 0,
                m => m.CompleteStatus == false)
            .WhereIF(!string.IsNullOrWhiteSpace(inPut.SearchKey) && keyWords.Length == 1,
                m => m.xm.Contains(inPut.SearchKey.Trim()) || m.sfzmhm.Contains(inPut.SearchKey.Trim()) ||
                     m.yddh.Contains(inPut.SearchKey.Trim()) ||
                     (m.CardNo.Contains(inPut.SearchKey.Trim()) && m.CardNo != ""))
            .WhereIF(keyWords.Length > 1, m => keyWords.Contains(m.xm) || keyWords.Contains(m.sfzmhm))
            .WhereIF(!string.IsNullOrEmpty(inPut.ApplyType),
                m => Convert.ToInt32(m.ApplyType) == inPut.ApplyType.ParseToInt())
            .WhereIF(!string.IsNullOrEmpty(inPut.CheckStatus),
                m => Convert.ToInt32(m.CheckStatus) == inPut.CheckStatus.ParseToInt())
            .WhereIF(!string.IsNullOrEmpty(inPut.BlackList),
                m => m.BlackList == (Convert.ToInt32(inPut.BlackList) == 1))
            .WhereIF(inPut.SetClassStatus == "0", m => m.ClassId == Guid.Empty)
            .WhereIF(inPut.SetClassStatus == "1", m => m.ClassId != Guid.Empty)
            .WhereIF(inPut.CompleteStudys == "0", m => m.CompleteStudy == false)
            .WhereIF(inPut.CompleteStudys == "1", m => m.CompleteStudy == true)
            .WhereIF(inPut.CompleteExams == "0", m => m.CompleteExam == false)
            .WhereIF(inPut.CompleteExams == "1", m => m.CompleteExam == true)
            .WhereIF(!string.IsNullOrEmpty(inPut.OldData) && inPut.OldData.ParseToInt() == 0,
                m => m.CreateTime >= Convert.ToDateTime("2023-03-18"))
            .WhereIF(!string.IsNullOrEmpty(inPut.OldData) && inPut.OldData.ParseToInt() == 1,
                m => m.CreateTime < Convert.ToDateTime("2023-03-18"))
            .WhereIF(!string.IsNullOrEmpty(inPut.TrackingStatus) && inPut.TrackingStatus.ParseToInt() == 0,
                m => m.TrackingTime <= Convert.ToDateTime("2000-01-01"))
            .WhereIF(!string.IsNullOrEmpty(inPut.TrackingStatus) && inPut.TrackingStatus.ParseToInt() == 1,
                m => m.TrackingTime > Convert.ToDateTime("2000-01-01"))
            .WhereIF(!string.IsNullOrEmpty(inPut.PayStatus) && inPut.PayStatus.ParseToInt() == 0,
                m => m.PayTime <= Convert.ToDateTime("2000-01-01"))
            .WhereIF(!string.IsNullOrEmpty(inPut.PayStatus) && inPut.PayStatus.ParseToInt() == 1,
                m => m.PayTime > Convert.ToDateTime("2000-01-01"))
            .WhereIF(inPut.CreateTimes.Count > 0,
                m => SqlFunc.GreaterThanOrEqual(m.CreateTime, inPut.CreateTimes[0]))
            .WhereIF(inPut.CreateTimes.Count > 1,
                m => SqlFunc.LessThan(m.CreateTime, inPut.CreateTimes[1].AddDays(1)))
            .WhereIF(inPut.TrackingTimes.Count > 0,
                m => SqlFunc.GreaterThanOrEqual(m.TrackingTime, inPut.TrackingTimes[0]))
            .WhereIF(inPut.TrackingTimes.Count > 1,
                m => SqlFunc.LessThan(m.TrackingTime, inPut.TrackingTimes[1].AddDays(1)))
            .WhereIF(inPut.CheckTimes.Count > 0,
                m => SqlFunc.GreaterThanOrEqual(m.CheckTime, inPut.CheckTimes[0]))
            .WhereIF(inPut.CheckTimes.Count > 1,
                m => SqlFunc.LessThan(m.CheckTime, inPut.CheckTimes[1].AddDays(1)))
            .WhereIF(inPut.SubmitTimes.Count > 0,
                m => SqlFunc.GreaterThanOrEqual(m.SubmitTime, inPut.SubmitTimes[0]))
            .WhereIF(inPut.SubmitTimes.Count > 1,
                m => SqlFunc.LessThan(m.SubmitTime, inPut.SubmitTimes[1].AddDays(1)))
            .WhereIF(inPut.CreateCardTimes.Count > 0,
                m => SqlFunc.GreaterThanOrEqual(m.CreateCardTime, inPut.CreateCardTimes[0]))
            .WhereIF(inPut.CreateCardTimes.Count > 1,
                m => SqlFunc.LessThan(m.CreateCardTime, inPut.CreateCardTimes[1].AddDays(1)))
            .WhereIF(inPut.ApplyTypes.Count > 0,
                m => inPut.ApplyTypes.Contains(m.ApplyType))
            .WhereIF(!UserManager.IsTenantAdmin, (m, n, o) => o.AdminUserId == UserManager.UserId)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .OrderByIF(keyWords.Length > 1, m => SqlFunc.CharIndex(m.sfzmhm, string.Join("", keyWords)))
            .OrderByIF(keyWords.Length > 1, m => SqlFunc.CharIndex(m.xm, string.Join("", keyWords)))
            .OrderByIF(inPut.Size > 500, (m, n, o, p) => o.CityId)
            .OrderByDescending(m => m.CreateTime)
            .Select((m, n, o, p) => new HnjxCoachOutPut
            {
                Id = m.Id,
                xm = m.xm,
                xb = m.xb,
                sfzmhm = m.sfzmhm,
                yddh = m.yddh,
                CompanyShortName = o.ShortName == "" ? o.FullName : o.ShortName,
                CompanyContactPerson = o.ContactPerson,
                CompanyContactPersonPhone = o.ContactPersonPhone,
                CompanyPostAddress = o.PostAddress,
                CreateUserName = n.RealName,
                CheckTime = m.CheckTime,
                CheckRemark = m.CheckRemark,
                CreateTime = m.CreateTime,
                CheckStatus = m.CheckStatus,
                ApplyType = m.ApplyType,
                CardNo = m.CardNo,
                OldCardNo = m.OldCardNo,
                FileCheckTime = m.FileCheckTime,
                CreateCardTime = m.CreateCardTime,
                ClassName = p.Name,
                CompleteStudy = m.CompleteStudy,
                CompleteExam = m.CompleteExam,
                PayTime = m.PayTime,
                CarType = m.CarType,
                TeachCarType = m.TeachCarType,
                TeachType = m.TeachType,
                CityId = o.CityId,
                TrackingTime = m.TrackingTime
            });


        var totalNumber = 0;
        var data = pageInfo.ToOffsetPage(inPut.Current, inPut.Size == 0 ? 99999 : inPut.Size, ref totalNumber);

        data = data.ToList().Select<HnjxCoachOutPut, HnjxCoachOutPut>((u, i) =>
                {
                    u.RowIndex = (inPut.Current - 1) * inPut.Size + (i + 1);
                    u.CheckStatusLabel = ((HnjxCheckStatusEnum)u.CheckStatus.ParseToInt()).GetDescription();
                    u.ApplyTypeLabel = ((HnjxApplyTypeEnum)u.ApplyType.ParseToInt()).GetDescription();
                    return u;
                }).ToList();


        var result =
            data.ToSqlSugarPagedList(inPut.Current, inPut.Size, totalNumber);

        return result;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsNoCompleteBySfzmhm(string sfzmhm)
    {
        return await IsAnyAsync(m => m.sfzmhm == sfzmhm && m.IsDelete == false && m.CompleteStatus == false);
    }

    /// <inheritdoc />
    public async Task<bool> ExistsBlackListBySfzmhm(string sfzmhm)
    {
        return await IsAnyAsync(m => m.sfzmhm == sfzmhm && m.IsDelete == false && m.BlackList == true);
    }


    /// <inheritdoc />
    public async Task<bool> ExistsTrainingBySfzmhm(Guid companyId, string sfzmhm)
    {
        return await IsAnyAsync(m =>
            m.sfzmhm == sfzmhm && m.CompanyId == companyId && m.IsDelete == false &&
            Convert.ToInt32(m.ApplyType) == Convert.ToInt32(HnjxApplyTypeEnum.Training));
    }


    /// <inheritdoc />
    public async Task<bool> ExistsByCardNo(string cardNo)
    {
        return await IsAnyAsync(m => m.CardNo == cardNo && m.IsDelete == false);
    }


    /// <inheritdoc />
    public async Task<bool> ExistsByCompanyId(Guid companyId)
    {
        return await IsAnyAsync(m => m.CompanyId == companyId && m.IsDelete == false);
    }

    #endregion 查询

    #region 更新

    /// <inheritdoc />
    public async Task<bool> UpdatePayTimeById(Guid id, DateTime payTime)
    {
        var data = await GetById(id);
        data.PayTime = payTime;

        return await UpdateAsync(data);
    }


    /// <inheritdoc />
    public async Task<bool> UpdateCompleteStudyById(Guid id, bool completeStudy)
    {
        var data = await GetById(id);
        data.CompleteStudy = completeStudy;

        return await UpdateAsync(data);
    }

    /// <inheritdoc />
    public async Task<bool> UpdateCompleteExamById(Guid id, bool completeExam)
    {
        var data = await GetById(id);
        data.CompleteExam = completeExam;

        return await UpdateAsync(data);
    }

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data == null)
            throw Oops.Bah("未找到相关的数据，请刷新页面");

        data.Delete();

        return await UpdateAsync(data);
    }

    /// <inheritdoc />
    public async Task<bool> UpdateCardNo(List<Guid> ids, string cardNo, DateTime createCardTime)
    {
        var data = await GetListAsync(m => ids.Contains(m.Id));

        for (var i = 0; i < data.Count; i++)
        {
            data[i].CardNo = cardNo;
            data[i].CreateCardTime = createCardTime;
        }

        return await UpdateAsync(data);
    }

    /// <inheritdoc />
    public async Task<bool> UpdateCardNo(List<Guid> ids, string cardNo)
    {
        var data = await GetListAsync(m => ids.Contains(m.Id));

        for (var i = 0; i < data.Count; i++) data[i].CardNo = cardNo;

        return await UpdateAsync(data);
    }

    /// <inheritdoc />
    public async Task<bool> UpdateCreateCardTime(List<Guid> ids, DateTime createCardTime)
    {
        var data = await GetListAsync(m => ids.Contains(m.Id));

        for (var i = 0; i < data.Count; i++) data[i].CreateCardTime = createCardTime;

        return await UpdateAsync(data);
    }


    /// <inheritdoc />
    public async Task<bool> UpdateTrackingTime(List<Guid> ids, DateTime trackingTime)
    {
        var data = await GetListAsync(m => ids.Contains(m.Id));

        for (var i = 0; i < data.Count; i++) data[i].TrackingTime = trackingTime;

        return await UpdateAsync(data);
    }

    /// <inheritdoc />
    public async Task<bool> UpdateClassId(Guid id, Guid classId)
    {
        var data = await GetById(id);
        data.ClassId = classId;

        return await UpdateAsync(data);
    }


    /// <inheritdoc />
    public async Task<bool> Update(HnjxCoachInPut inPut)
    {
        if (!inPut.sfzmhm.IsIDCard18())
            throw Oops.Bah("身份证号码错误!");

        if (!inPut.yddh.IsMobileNumber())
            throw Oops.Bah("手机号码格式错误!");

        if (inPut.TeachType != "理论" && inPut.TeachCarTypes.Count == 0)
            throw Oops.Bah("非理论教练必须要选择准教车型!");

        var data = await Context.Queryable<HnjxCoachEntity>().Where(m => m.Id == inPut.Id).SingleAsync();

        if (data == null)
            throw Oops.Bah("当前数据为空，刷新重试");

        if (data.CreateUserId != UserManager.UserId && !UserManager.IsTenantAdmin)
            throw Oops.Bah("非数据创建人员或管理员无法修改该数据");

        if (!UserManager.IsTenantAdmin && data.CheckStatus != HnjxCheckStatusEnum.WaitSubmit &&
            data.CheckStatus != HnjxCheckStatusEnum.Unqualified)
            throw Oops.Bah("当前状态无法修改资料!");

        if (!UserManager.IsTenantAdmin && data.CompleteStatus)
            throw Oops.Bah("业务状态已经办结，无法修改任何资料!");

        data.xm = inPut.xm;
        data.xb = inPut.xb;

        if (data.sfzmhm != inPut.sfzmhm && await ExistsNoCompleteBySfzmhm(inPut.sfzmhm))
            throw Oops.Bah("身份证号码重复，无法重复录入，请联系驾协工作人员处理历史数据!");

        data.sfzmhm = inPut.sfzmhm;

        if (UserManager.IsTenantAdmin)
        {
            if (inPut.CompanyId != Guid.Empty) data.CompanyId = inPut.CompanyId;

            data.BlackList = inPut.BlackList.ParseToBool();
            data.CompleteStatus = inPut.CompleteStatus.ParseToBool();
            // data.CompleteStudy = inPut.CompleteStudy.ParseToBool();
        }

        data.ApplyType = (HnjxApplyTypeEnum)inPut.ApplyType.ParseToInt();
        data.yddh = inPut.yddh;
        data.clrq = inPut.clrq;
        data.Education = string.IsNullOrEmpty(inPut.Education) ? "" : inPut.Education;

        data.TeachType = string.IsNullOrEmpty(inPut.TeachType) ? "" : inPut.TeachType;
        data.OldCardNo = string.IsNullOrEmpty(inPut.OldCardNo) ? "" : inPut.OldCardNo;

        if (UserManager.IsTenantAdmin)
            data.CardNo = string.IsNullOrEmpty(inPut.CardNo) ? "" : inPut.CardNo;

        data.CarType = inPut.CarTypes.Count == 0 ? "" : string.Join(",", inPut.CarTypes);
        data.TeachCarType = inPut.TeachCarTypes.Count == 0 ? "" : string.Join(",", inPut.TeachCarTypes);


        if (UserManager.IsTenantAdmin)
        {
            if (data.ClassId != inPut.ClassId)
            {
                string oldClassName = "无班级";
                string newClassName = "无班级";

                if (data.ClassId != Guid.Empty)
                {
                    var oldClass = await Context.Queryable<HnjxClassEntity>().Where(m => m.Id == data.ClassId).FirstAsync();
                    if (oldClass != null)
                    {
                        oldClassName = oldClass.Name;
                    }
                }

                if (inPut.ClassId != Guid.Empty)
                {
                    var newClass = await Context.Queryable<HnjxClassEntity>().Where(m => m.Id == inPut.ClassId).FirstAsync();
                    if (newClass != null)
                    {
                        newClassName = newClass.Name;
                    }
                }

                string changeType = data.ClassId == Guid.Empty ? "分配" :
                                  inPut.ClassId == Guid.Empty ? "移除" : "变更";

                await _easyLogService.Add(data.Id, $"教练 {data.xm} 的班级{changeType}：从 {oldClassName} 变更为 {newClassName}", data.Id, data.TenantId);
            }
            data.ClassId = inPut.ClassId;
        }

        data.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;

        if (await UpdateAsync(data))
            return true;

        throw Oops.Bah("更新数据失败");
    }


    /// <inheritdoc />
    public async Task<bool> Edit(HnjxCoachEntity data)
    {
        return await UpdateAsync(data);
    }

    #endregion 更新

    #region 方法

    /// <inheritdoc />
    public async Task<Image> GetResultImage(Guid userId)
    {
        var question = await _hnjxCoachQuestionService.GetMaxScoreQuestionByUserId(userId);

        if (question.Score < 60)
            throw Oops.Bah("未找到合格记录，请重新答题");


        var fileInfo = new FileInfo(Assembly.GetEntryAssembly().Location);
        var image = Image.Load(fileInfo.DirectoryName + "/Images/hnjxjyz.jpg");


        var fontCollection = new FontCollection();
        var family = fontCollection.Add(fileInfo.DirectoryName + "/font/msyh.ttf");
        var font = family.CreateFont(65, FontStyle.Bold);

        var result = "优秀";

        if (question.Score < 90) result = "良好";
        if (question.Score < 70) result = "合格";

        var coach = await GetById(userId);
        image.Mutate(x =>
        {
            x.DrawText(coach.xm, font, Color.Black, new PointF(230, 2440));
            x.DrawText(question.CreateTime.ToString("yy"), font, Color.Black, new PointF(670, 2440));
            x.DrawText(question.CreateTime.ToString("MM"), font, Color.Black, new PointF(830, 2440));
            x.DrawText(question.CreateTime.ToString("dd"), font, Color.Black, new PointF(980, 2440));


            if (question.Score < 70)
                x.DrawText("√", font, Color.Black, new PointF(2210, 2620));
            else if (question.Score < 90)
                x.DrawText("√", font, Color.Black, new PointF(1960, 2620));
            else
                x.DrawText("√", font, Color.Black, new PointF(1710, 2620));

        });
        return image;
    }

    /// <inheritdoc />
    public async Task<bool> SubmitById(Guid id)
    {
        var coach = await Context.Queryable<HnjxCoachEntity>().Where(m => m.Id == id).FirstAsync();

        if (coach == null)
            throw Oops.Bah("未找到相关数据，刷新重试");



        if (!coach.sfzmhm.IsIDCard18())
            throw Oops.Bah("身份证号码错误!");

        if (!coach.yddh.IsMobileNumber())
            throw Oops.Bah("手机号码格式错误!");


        if (await ExistsBlackListBySfzmhm(coach.sfzmhm))
            throw Oops.Bah("当前身份证有一个黑名单记录，无法进行任何操作，请联系驾协的工作人员!");



        if (coach.CheckStatus != HnjxCheckStatusEnum.WaitSubmit && coach.CheckStatus != HnjxCheckStatusEnum.Unqualified)
            throw Oops.Bah("当前核验状态无法进行提交操作，如有疑问，请联系驾协的工作人员!");


        if (coach.TeachType.Contains("实操") &&
            (coach.sfzmhm.Substring(6, 4) + "-" + coach.sfzmhm.Substring(10, 2) + "-" + coach.sfzmhm.Substring(12, 2))
            .ParseToDateTime() < DateTime.Now.AddYears(-60).AddDays(90))
            throw Oops.Bah("实操教练必须要小于 60 岁!");

        if (coach.TeachType.Contains("实操") && coach.clrq > DateTime.Now.AddYears(5))
            throw Oops.Bah("实操教练驾驶证必须要满 5 年!");


        var images = await _hnjxImageService.GetListByApplyType(coach.ApplyType);
        var coachImages = await _hnjxCoachImageService.GetListById(coach.Id);


        foreach (var image in images)
            if (!coachImages.Any(m => m.ImageId == image.ImageId))
                throw Oops.Bah("请上传 " + image.ImageId.GetDescriptionByEnum<HnjxCoachImageIdEnum>());


        coach.CheckStatus = HnjxCheckStatusEnum.Wait;
        coach.SubmitTime = DateTime.Now;
        coach.CheckRemark = "";

        if (!await UpdateAsync(coach))
            throw Oops.Bah("提交数据时更新失败");

        return true;
    }


    /// <inheritdoc />
    public async Task<bool> SubmitBackById(Guid id)
    {
        var coach = await Context.Queryable<HnjxCoachEntity>().Where(m => m.Id == id).FirstAsync();

        if (coach == null)
            throw Oops.Bah("未找到相关数据，刷新重试");

        if (coach.CheckStatus != HnjxCheckStatusEnum.Wait)
            throw Oops.Bah("当前核验状态无法进行提交操作，如有疑问，请联系驾协的工作人员!");


        coach.CheckStatus = HnjxCheckStatusEnum.WaitSubmit;
        if (!await UpdateAsync(coach))
            throw Oops.Bah("提交数据时更新失败");

        return true;
    }


    /// <inheritdoc />
    public async Task<string> MakeCardNoById(Guid id)
    {
        var coach = await GetById(id);

        if (coach == null)
            throw Oops.Bah("未找到教练相关数据，刷新重试");

        var company = await _hnjxCompanyService.GetById(coach.CompanyId);

        if (company == null)
            throw Oops.Bah("未找到教练所属单位相关数据，刷新重试");

        var maxIdData = await _hnjxMaxIdService.GetByCityId(company.CityId);


        if (maxIdData == null)
            throw Oops.Bah(company.CityId + " 无法获取最大值的配置");

        var cardNo = maxIdData.PoliceCode + (maxIdData.MaxId + 1).ToString("00000");
        if (await ExistsByCardNo(cardNo))
            throw Oops.Bah("请重新设置最大值，当前最大值有重复!");

        coach.CardNo = cardNo;
        coach.CreateCardTime = DateTime.Now;

        coach.Modify();

        await UpdateAsync(coach);

        await _hnjxMaxIdService.UpdateAddByCityId(company.CityId);

        return cardNo;
    }


    /// <inheritdoc />
    public async Task<MemoryStream> MakeImageZip(List<Guid> ids)
    {
        var memoryStream = new MemoryStream();
        var successCount = 0;
        var totalCount = ids.Count;

        Console.WriteLine($"开始创建ZIP，输入ID数量: {ids.Count}");
        Console.WriteLine($"初始MemoryStream长度: {memoryStream.Length}");

        using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
        {
            Console.WriteLine("ZipArchive已创建");

            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30); // 设置超时时间

            for (var i = 0; i < ids.Count; i++)
            {
                try
                {
                    // 获取教练信息
                    var coach = await GetById(ids[i]);
                    if (coach == null)
                    {
                        Console.WriteLine($"跳过 - 未找到教练: {ids[i]}");
                        continue;
                    }

                    // 获取寸照信息
                    var image = await _hnjxCoachImageService.GetImage0ListById(ids[i]);
                    if (image == null)
                    {
                        Console.WriteLine($"跳过 - 教练{coach.xm}无寸照");
                        continue;
                    }

                    // 检查图片路径
                    if (string.IsNullOrEmpty(image.ImagePath))
                    {
                        Console.WriteLine($"跳过 - 教练{coach.xm}图片路径为空");
                        continue;
                    }

                    // 下载图片数据
                    var imageBytes = await httpClient.GetByteArrayAsync(image.ImagePath);

                    if (imageBytes == null || imageBytes.Length == 0)
                    {
                        Console.WriteLine($"跳过 - 教练{coach.xm}图片下载为空");
                        continue;
                    }

                    // 创建ZIP条目 - 清理文件名中的特殊字符
                    var cleanName = coach.xm.Replace("/", "_").Replace("\\", "_").Replace(":", "_");
                    var fileName = $"{(i + 1):000}_{cleanName}_{coach.sfzmhm}.jpg";

                    Console.WriteLine($"处理教练{coach.xm}，图片大小: {imageBytes.Length} bytes，文件名: {fileName}");

                    var zipEntry = archive.CreateEntry(fileName, CompressionLevel.Optimal);
                    Console.WriteLine($"ZIP条目已创建: {zipEntry.Name}");

                    // 写入图片数据到ZIP条目
                    using (var entryStream = zipEntry.Open())
                    {
                        await entryStream.WriteAsync(imageBytes, 0, imageBytes.Length);
                        await entryStream.FlushAsync(); // 确保数据写入
                        Console.WriteLine($"已写入 {imageBytes.Length} bytes 到ZIP条目");
                    } // 显式关闭entryStream

                    successCount++;
                    Console.WriteLine($"成功处理第 {successCount} 个文件");
                    Console.WriteLine($"当前MemoryStream长度: {memoryStream.Length}");
                }
                catch (Exception ex)
                {
                    var coach = await GetById(ids[i]);
                    Console.WriteLine($"处理失败 - 教练: {coach?.xm}, 错误: {ex.Message}");
                    Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                    // 记录错误日志，但继续处理其他图片
                    await _easyLogService.Add(ids[i], $"MakeImageZip 处理失败: 教练 {coach?.xm} - {ex.Message}", ids[i], coach?.TenantId ?? UserManager.TenantId);
                }
            }

            Console.WriteLine($"循环结束，准备dispose archive");
            Console.WriteLine($"Disposing archive前MemoryStream长度: {memoryStream.Length}");
        } // archive被dispose

        Console.WriteLine($"Archive已dispose，最终MemoryStream长度: {memoryStream.Length}");

        // 记录最终统计信息
        Console.WriteLine($"MakeImageZip 完成: 总数 {totalCount}, 成功 {successCount}, 失败 {totalCount - successCount}");

        memoryStream.Position = 0;
        Console.WriteLine($"返回MemoryStream，Position重置为0，Length: {memoryStream.Length}");
        return memoryStream;
    }

    /// <summary>
    /// 调试方法：检查单个教练的图片状态
    /// </summary>
    /// <param name="coachId">教练ID</param>
    /// <returns>图片状态信息</returns>
    public async Task<string> CheckCoachImageStatus(Guid coachId)
    {
        var result = new List<string>();

        try
        {
            // 检查教练信息
            var coach = await GetById(coachId);
            if (coach == null)
            {
                return $"错误：未找到教练信息 ID={coachId}";
            }

            result.Add($"教练信息：{coach.xm} - {coach.sfzmhm}");

            // 检查寸照信息
            var image = await _hnjxCoachImageService.GetImage0ListById(coachId);
            if (image == null)
            {
                result.Add("❌ 未找到寸照(image0)");

                // 检查是否有其他类型的图片
                var allImages = await _hnjxCoachImageService.GetListById(coachId);
                result.Add($"共有 {allImages.Count} 张图片：");
                foreach (var img in allImages)
                {
                    result.Add($"  - {img.ImageId}: {img.ImagePath}");
                }

                return string.Join("\n", result);
            }

            result.Add($"✅ 找到寸照信息");
            result.Add($"图片路径: {image.ImagePath}");

            // 检查图片路径
            if (string.IsNullOrEmpty(image.ImagePath))
            {
                result.Add("❌ 图片路径为空");
                return string.Join("\n", result);
            }

            // 尝试下载图片
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(10);

            result.Add("开始测试下载...");

            var imageBytes = await httpClient.GetByteArrayAsync(image.ImagePath);

            if (imageBytes == null || imageBytes.Length == 0)
            {
                result.Add("❌ 图片下载结果为空");
            }
            else
            {
                result.Add($"✅ 图片下载成功，大小: {imageBytes.Length} bytes");
            }
        }
        catch (HttpRequestException httpEx)
        {
            result.Add($"❌ HTTP下载错误: {httpEx.Message}");
        }
        catch (TaskCanceledException)
        {
            result.Add("❌ 下载超时");
        }
        catch (Exception ex)
        {
            result.Add($"❌ 其他错误: {ex.Message}");
        }

        return string.Join("\n", result);
    }

    #endregion 方法
}