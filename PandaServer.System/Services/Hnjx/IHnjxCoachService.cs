using PandaServer.System.Services.Hnjx.Dtos;
using SixLabors.ImageSharp;

namespace PandaServer.System.Services.Hnjx;

public interface IHnjxCoachService : ITransient
{
    /// <summary>
    ///     通过 id  返回 实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HnjxCoachOutPut?> GetById(Guid id);

    /// <summary>
    ///     通过 id 数组 获取 实体列表
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<HnjxCoachEntity>> GetByIds(List<Guid> ids);

    /// <summary>
    ///     通过 OpenId  获取 当前登录的用户实体
    /// </summary>
    /// <param name="openId"></param>
    /// <returns></returns>
    Task<HnjxCoachEntity?> GetByOpenId(string openId);

    /// <summary>
    ///     通过 id 返回 教练 身份证
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<string> GetSfzmhmById(Guid id);

    /// <summary>
    ///     通过 身份证 和 姓名 返回 相应的列表
    /// </summary>
    /// <param name="sfzmhm"></param>
    /// <param name="xm"></param>
    /// <returns></returns>
    Task<List<HnjxCoachEntity>> GetListBySfzmhmAndXm(string sfzmhm, string xm);


    /// <summary>
    ///     获取 TeachCarTyp  列表
    /// </summary>
    /// <returns></returns>
    Task<List<string>> GetTeachCarTypeList();

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<string> GetCompanyFullNameById(Guid id);

    /// <summary>
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxCoachOutPut>> Page(HnjxCoachPageInPut inPut);

    /// <summary>
    ///     检查是否有 未 办结 状态的 身份证
    /// </summary>
    /// <param name="sfzmhm"></param>
    /// <returns></returns>
    Task<bool> ExistsNoCompleteBySfzmhm(string sfzmhm);

    /// <summary>
    ///     检查是否有 黑名单 的 身份证
    /// </summary>
    /// <param name="sfzmhm"></param>
    /// <returns></returns>
    Task<bool> ExistsBlackListBySfzmhm(string sfzmhm);

    /// <summary>
    ///     当前公司 是否存在 培训记录
    /// </summary>
    /// <param name="companyId"></param>
    /// <param name="sfzmhm"></param>
    /// <returns></returns>
    Task<bool> ExistsTrainingBySfzmhm(Guid companyId, string sfzmhm);

    /// <summary>
    ///     检查 教练卡 是否 重复
    /// </summary>
    /// <param name="cardNo"></param>
    /// <returns></returns>
    Task<bool> ExistsByCardNo(string cardNo);

    /// <summary>
    ///     判断当前公司下面是否还有数据
    /// </summary>
    /// <param name="companyId"></param>
    /// <returns></returns>
    Task<bool> ExistsByCompanyId(Guid companyId);

    /// <summary>
    ///     更新 付款时间
    /// </summary>
    /// <param name="id"></param>
    /// <param name="payTime"></param>
    /// <returns></returns>
    Task<bool> UpdatePayTimeById(Guid id, DateTime payTime);

    /// <summary>
    ///     更新  完成学习状态
    /// </summary>
    /// <param name="id"></param>
    /// <param name="completeStudy"></param>
    /// <returns></returns>
    Task<bool> UpdateCompleteStudyById(Guid id, bool completeStudy);

    /// <summary>
    ///     更新  完成测验状态
    /// </summary>
    /// <param name="id"></param>
    /// <param name="completeExam"></param>
    /// <returns></returns>
    Task<bool> UpdateCompleteExamById(Guid id, bool completeExam);

    /// <summary>
    ///     更新删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> UpdateIsDelete(Guid id);

    /// <summary>
    ///     更新制卡信息
    /// </summary>
    /// <param name="ids"></param>
    /// <param name="cardNo"></param>
    /// <param name="createCardTime"></param>
    Task<bool> UpdateCardNo(List<Guid> ids, string cardNo, DateTime createCardTime);

    /// <summary>
    ///     更新卡号
    /// </summary>
    /// <param name="ids"></param>
    /// <param name="cardNo"></param>
    /// <returns></returns>
    Task<bool> UpdateCardNo(List<Guid> ids, string cardNo);

    /// <summary>
    ///     批量更新 制卡时间
    /// </summary>
    /// <param name="ids"></param>
    /// <param name="createCardTime"></param>
    /// <returns></returns>
    Task<bool> UpdateCreateCardTime(List<Guid> ids, DateTime createCardTime);

    /// <summary>
    ///     批量更新 快递时间
    /// </summary>
    /// <param name="ids"></param>
    /// <param name="trackingTime"></param>
    /// <returns></returns>
    Task<bool> UpdateTrackingTime(List<Guid> ids, DateTime trackingTime);

    /// <summary>
    ///     更新 班别
    /// </summary>
    /// <param name="id"></param>
    /// <param name="classId"></param>
    /// <returns></returns>
    Task<bool> UpdateClassId(Guid id, Guid classId);

    /// <summary>
    ///     更新
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<bool> Update(HnjxCoachInPut inPut);

    /// <summary>
    ///     直接更新实体
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task<bool> Edit(HnjxCoachEntity data);

    /// <summary>
    ///     添加
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<HnjxCoachEntity> Add(HnjxCoachInPut inPut);


    /// <summary>
    ///     制作 成绩的 展示 截图
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<Image> GetResultImage(Guid userId);

    /// <summary>
    ///     数据 提交
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> SubmitById(Guid id);


    /// <summary>
    ///     数据 提交 退回
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> SubmitBackById(Guid id);

    /// <summary>
    ///     通过 教练的Id 生成 卡号
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<string> MakeCardNoById(Guid id);

    /// <summary>
    ///     生成 教练的  寸照的  zip 文件流
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<MemoryStream> MakeImageZip(List<Guid> ids);

    /// <summary>
    /// 调试方法：检查单个教练的图片状态
    /// </summary>
    /// <param name="coachId">教练ID</param>
    /// <returns>图片状态信息</returns>
    Task<string> CheckCoachImageStatus(Guid coachId);
}