using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxClassService" />
/// </summary>
public class HnjxClassService : CustomDbRepository<HnjxClassEntity>, IHnjxClassService
{
    #region 添加

    /// <inheritdoc />
    public async Task<HnjxClassEntity> Add(HnjxClassInPut inPut)
    {
        if (string.IsNullOrEmpty(inPut.Name) || inPut.Name.Trim() == "")
            throw Oops.Bah("名称不能为空!");
        if (await ExistsByName(Guid.Empty, inPut.Name))
            throw Oops.Bah("名称重复，请重试");

        var data = inPut.Adapt<HnjxClassEntity>();
        data.Create();
        data.OutId = "";
        data.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;
        data.TenantId = UserManager.TenantId;
        data.StartTime = inPut.AllowTime[0].ParseToDateTime();
        data.EndTime = inPut.AllowTime[1].ParseToDateTime();

        if (await InsertAsync(data))
            return data;
        throw Oops.Bah("添加数据失败");
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<HnjxClassEntity?> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id && m.IsDelete == false);
    }

    /// <inheritdoc />
    public async Task<string> GetNameById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id && m.IsDelete == false, m => m.Name);
    }


    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxClassOutPut>> Page(HnjxClassPageInPut inPut)
    {
        var query = Context.Queryable<HnjxClassEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .WhereIF(!string.IsNullOrEmpty(inPut.SearchKey), m => m.Name.Contains(inPut.SearchKey.Trim()))
            .WhereIF(!string.IsNullOrEmpty(inPut.Name), m => m.Name.Contains(inPut.Name.Trim()))
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .WhereIF(inPut.OutClassId.ParseToGuid() != Guid.Empty, m => m.Id != inPut.OutClassId.ParseToGuid())
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n) => new HnjxClassOutPut
            {
                Id = m.Id,
                Name = m.Name,
                StartTime = m.StartTime,
                EndTime = m.EndTime,
                CreateUserName = n.RealName,
                CreateTime = m.CreateTime
            });


        var pageInfo = await query.ToPagedListAsync(inPut.Current, inPut.Size); //分页

        return pageInfo;
    }


    /// <inheritdoc />
    public async Task<bool> ExistsByName(Guid classId, string name)
    {
        return await IsAnyAsync(m => m.Id != classId && m.Name == name && m.TenantId == UserManager.TenantId);
    }

    #endregion 查询


    #region 更新

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data == null)
            throw Oops.Bah("未找到相关的数据，请刷新页面");

        data.Delete();

        return await UpdateAsync(data);
    }

    /// <inheritdoc />
    public async Task<bool> Edit(HnjxClassInPut inPut)
    {
        if (string.IsNullOrEmpty(inPut.Name) || inPut.Name.Trim() == "")
            throw Oops.Bah("名称不能为空!");

        var data = await GetById(inPut.Id);

        data.Name = inPut.Name;
        data.SortCode = inPut.SortCode;
        data.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;
        data.StartTime = inPut.AllowTime[0].ParseToDateTime();
        data.EndTime = inPut.AllowTime[1].ParseToDateTime();

        data.Modify();

        if (await ExistsByName(data.Id, data.Name))
            throw Oops.Bah("名称重复，请重试");
        return await UpdateAsync(data);
    }

    #endregion 更新
}