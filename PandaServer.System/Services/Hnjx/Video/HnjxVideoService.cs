using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxVideoService" />
/// </summary>
public class HnjxVideoService : CustomDbRepository<HnjxVideoEntity>, IHnjxVideoService
{
    #region 查询

    /// <inheritdoc />
    public async Task<List<HnjxVideoEntity>> GetList()
    {
        return await GetListAsync(m => 1 == 1);
    }

    /// <inheritdoc />
    public async Task<HnjxVideoOutPut> GetInfoByIdAndUserId(Guid id, Guid userId)
    {
        return await Context.Queryable<HnjxVideoEntity>()
            .LeftJoin(
                Context.Queryable<HnjxVideoPlayDetailEntity>().SplitTable(st => st).Where(m => m.UserId == userId),
                (m, n) => m.Id == n.VideoId)
            .Where(m => m.Id == id)
            .Select((m, n) => new HnjxVideoOutPut
            {
                CoverUrl = m.CoverUrl,
                Id = m.Id,
                TimeLength = m.TimeLength,
                PlayTime = n.PlayTime,
                VideoName = m.VideoName,
                VideoUrl2 = m.VideoUrl2
            }).SingleAsync();
    }

    /// <inheritdoc />
    public async Task<List<HnjxVideoOutPut>> GetListByUserId(Guid userId)
    {
        return await Context.Queryable<HnjxVideoEntity>()
            .LeftJoin(Context.Queryable<HnjxVideoPlayDetailEntity>().SplitTable(st => st),
                (m, n) => m.Id == n.VideoId && n.UserId == userId)
            .OrderBy(m => m.VideoName)
            .Select((m, n) => new HnjxVideoOutPut
            {
                VideoName = m.VideoName,
                Id = m.Id,
                CoverUrl = m.CoverUrl,
                TimeLength = m.TimeLength,
                PlayTime = n.PlayTime,
                NoPlay = false
            })
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxVideoOutPut>> Page(HnjxVideoPageInPut inPut)
    {
        var pageInfo = Context.Queryable<HnjxVideoEntity>()
            .LeftJoin(Context.Queryable<HnjxVideoPlayDetailEntity>().SplitTable(st => st),
                (m, n) => m.Id == n.VideoId && n.UserId == inPut.UserId)
            .OrderBy(m => m.VideoName)
            .Select((m, n) => new HnjxVideoOutPut
            {
                CoverUrl = m.CoverUrl,
                Id = m.Id,
                VideoName = m.VideoName,
                TimeLength = m.TimeLength,
                PlayTime = n.PlayTime,
                VideoUrl2 = m.VideoUrl2.Replace("http://", "https://")
            });



        var totalNumber = 0;
        var data = pageInfo.ToOffsetPage(inPut.Current, inPut.Size == 0 ? 99999 : inPut.Size, ref totalNumber);


        data = data.ToList().Select<HnjxVideoOutPut, HnjxVideoOutPut>((u, i) =>
        {
            u.RowIndex = (inPut.Current - 1) * inPut.Size + (i + 1);
            u.PlayTimeProgress = u.PlayTime + "  = " + (u.PlayTime / u.TimeLength * 100).ToString("F2") + " %";

            return u;
        }).ToList();

        var result =
            data.ToSqlSugarPagedList(inPut.Current, inPut.Size, totalNumber);

        return result;
    }


    /// <inheritdoc />
    public async Task<bool> IsPassByUserId(Guid userId)
    {
        return !await Context.Queryable<HnjxVideoEntity>()
            .LeftJoin(Context.Queryable<HnjxVideoPlayDetailEntity>().SplitTable(st => st),
                (m, n) => m.Id == n.VideoId && n.UserId == UserManager.UserId)
            .Where((m, n) => SqlFunc.EqualsNull(n.Id, null) || n.PlayTime < m.TimeLength * 0.95)
            .AnyAsync();
    }

    #endregion 查询
}