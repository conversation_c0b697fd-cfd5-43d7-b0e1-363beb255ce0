using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

public interface IHnjxVideoService : ITransient
{
    /// <summary>
    ///     返回 播放 的列表
    /// </summary>
    /// <returns></returns>
    Task<List<HnjxVideoEntity>> GetList();

    /// <summary>
    ///     返回 播放 的列表
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<List<HnjxVideoOutPut>> GetListByUserId(Guid userId);

    /// <summary>
    ///     通过 视频 Id 和 用户 Id  返回 相关的信息
    /// </summary>
    /// <param name="id"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<HnjxVideoOutPut> GetInfoByIdAndUserId(Guid id, Guid userId);

    /// <summary>
    ///     分页 查询
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxVideoOutPut>> Page(HnjxVideoPageInPut inPut);

    /// <summary>
    ///     已经 已经完成了 看视频
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<bool> IsPassByUserId(Guid userId);
}