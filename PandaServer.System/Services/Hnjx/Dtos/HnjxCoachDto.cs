namespace PandaServer.System.Services.Hnjx.Dtos;

public class HnjxCoachInPut : HnjxCoachEntity
{
    /// <summary>
    /// </summary>
    /// <value></value>
    public List<string> TeachCarTypes { get; set; } = new();


    /// <summary>
    /// </summary>
    /// <value></value>
    public List<string> CarTypes { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> Ids { get; set; } = new();
}

public class HnjxCoachPageInPut : BasePageInput
{
    /// <summary>
    /// </summary>
    /// <value></value>
    public List<HnjxApplyTypeEnum> ApplyTypes { get; set; } = new();

    /// <summary>
    /// </summary>
    /// <value></value>
    public string ApplyType { get; set; } = "";

    /// <summary>
    /// </summary>
    /// <value></value>
    public string xm { get; set; } = "";

    /// <summary>
    /// </summary>
    /// <value></value>
    public string sfzmhm { get; set; } = "";

    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid CompanyId { get; set; } = Guid.Empty;


    /// <summary>
    /// </summary>
    /// <value></value>
    public string CheckStatus { get; set; } = "";

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<DateTime> CheckTimes { get; set; } = new();


    /// <summary>
    /// </summary>
    /// <value></value>
    public List<DateTime> CreateCardTimes { get; set; } = new();


    /// <summary>
    /// </summary>
    /// <value></value>
    public List<DateTime> CreateTimes { get; set; } = new();

    /// <summary>
    /// </summary>
    /// <value></value>
    public string OldCardNo { get; set; } = "";

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CardNo { get; set; } = "";

    /// <summary>
    ///     制卡状态
    /// </summary>
    /// <value></value>
    public string CarNoStatus { get; set; } = "";


    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid ClassId { get; set; } = Guid.Empty;


    /// <summary>
    /// </summary>
    /// <value></value>
    public string CompleteStudys { get; set; } = "";


    /// <summary>
    /// </summary>
    /// <value></value>
    public string CompleteExams { get; set; } = "";


    /// <summary>
    /// </summary>
    /// <value></value>
    public string PayStatus { get; set; } = "";

    /// <summary>
    /// </summary>
    /// <value></value>
    public string SetClassStatus { get; set; } = "";

    /// <summary>
    /// </summary>
    /// <value></value>
    public string OldData { get; set; } = "";


    /// <summary>
    /// </summary>
    /// <value></value>
    public List<DateTime> SubmitTimes { get; set; } = new();

    /// <summary>
    /// </summary>
    /// <value></value>
    public string BlackList { get; set; } = "";

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CompleteStatus { get; set; } = "";

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CityId { get; set; } = "";

    /// <summary>
    /// 
    /// </summary>
    public List<string> CityIds { get; set; } = new List<string>();

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<DateTime> TrackingTimes { get; set; } = new();


    /// <summary>
    /// </summary>
    /// <value></value>
    public string TrackingStatus { get; set; } = "";
}

public class HnjxCoachOutPut : HnjxCoachEntity
{
    /// <summary>
    /// 
    /// </summary>
    public int RowIndex { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CompanyShortName { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CompanyContactPerson { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CompanyContactPersonPhone { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CompanyPostAddress { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CreateUserName { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string ClassName { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CityId { get; set; }


    /// <summary>
    /// </summary>
    public string CheckStatusLabel { get; set; }


    /// <summary>
    /// </summary>
    public string ApplyTypeLabel { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public List<HnjxApplyTypeEnum> ApplyTypes { get; set; } = new();

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<string> TeachCarTypes { get; set; } = new();


    /// <summary>
    /// </summary>
    /// <value></value>
    public List<string> CarTypes { get; set; } = new();


    // /// <summary>
    // /// </summary>
    // public string ApplyType { get; set; }

    // /// <summary>
    // /// </summary>
    // public string CheckStatus { get; set; }


    // /// <summary>
    // /// </summary>
    // public Guid ClassId { get; set; } = Guid.Empty;
}