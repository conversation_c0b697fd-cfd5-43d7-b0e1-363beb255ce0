namespace PandaServer.System.Services.Hnjx.Dtos;

public class HnjxCompanyInPut : HnjxCompanyEntity
{
    public string Code { get; set; }
}

public class HnjxCompanyPageInPut : BasePageInput
{
    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid Id { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string FullName { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string ShortName { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string QualificationRecord { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string BusNum { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string TractorNum { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string TruckNum { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CarNum { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string OtherNum { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CheckedCarNum { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string TheoryTrainersNum { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string OperationTrainersNum { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public string LegalPerson { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string LegalPersonPhone { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string ContactPerson { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string ContactPersonPhone { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public int ProvinceId { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public int CityId { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public int AreaId { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string PostAddress { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string Remark { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid? AdminUserId { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid TenantId { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public bool CoachPay { get; set; }


    /// <summary>
    ///     注册码
    /// </summary>
    /// <value></value>
    public string Code { get; set; }
}

public class HnjxCompanyOutPut : HnjxCompanyEntity
{
    /// <summary>
    /// </summary>
    /// <value></value>
    public string Name { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public int ProvinceId { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public int AreaId { get; set; }


    /// <summary>
    ///     注册码
    /// </summary>
    /// <value></value>
    public string Code { get; set; }

    /// <summary>
    /// </summary>
    public string CreateUserName { get; set; }


    /// <summary>
    /// </summary>
    public string AdminAccount { get; set; }
}