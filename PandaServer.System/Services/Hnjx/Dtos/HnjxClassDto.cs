namespace PandaServer.System.Services.Hnjx.Dtos;

public class HnjxClassInPut : HnjxClassEntity
{
    /// <summary>
    /// </summary>
    public List<DateTime> AllowTime { get; set; }
}

public class HnjxClassPageInPut : BasePageInput
{
    /// <summary>
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    public string ClassId { get; set; }

    /// <summary>
    ///     排除 这个 id
    /// </summary>
    public string OutClassId { get; set; }
}

public class HnjxClassOutPut : HnjxClassEntity
{
    /// <summary>
    /// </summary>
    public string CreateUserName { get; set; }


    /// <summary>
    /// </summary>
    public List<DateTime> AllowTime { get; set; }
}