namespace PandaServer.System.Services.Hnjx.Dtos;

/// <summary>
/// </summary>
public class HnjxImageInPut : HnjxImageEntity
{
    public List<HnjxCoachImageIdEnum> ImageIds { get; set; } = new();
}

/// <summary>
/// </summary>
public class HnjxImagePageInPut : BasePageInput
{
}

/// <summary>
/// </summary>
public class HnjxImageOutPut : HnjxImageEntity
{
    /// <summary>
    ///     图片类型的 文字描述
    /// </summary>
    public string ImageIdValue { get; set; }

    /// <summary>
    ///     必须上传 的表示
    /// </summary>
    public bool MustUpload { get; set; }
}