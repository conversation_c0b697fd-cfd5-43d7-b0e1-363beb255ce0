namespace PandaServer.System.Services.Hnjx.Dtos;

public class HnjxQuestionInPut : HnjxQuestionEntity
{
}

public class HnjxQuestionPageInPut : BasePageInput
{
    /// <summary>
    /// </summary>
    public string Question { get; set; }

    /// <summary>
    /// </summary>
    public string QuestionType { get; set; }
}

public class HnjxQuestionOutPut : HnjxQuestionEntity
{
    /// <summary>
    /// </summary>
    public string CreateUserName { get; set; }


    /// <summary>
    /// </summary>
    public string QuestionTypeDescription { get; set; }

    /// <summary>
    /// </summary>
    public string UserAnswer { get; set; }
}