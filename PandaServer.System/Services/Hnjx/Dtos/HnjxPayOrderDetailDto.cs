namespace PandaServer.System.Services.Hnjx.Dtos;

public class HnjxPayOrderDetailInPut : HnjxPayOrderDetailEntity
{
}

public class HnjxPayOrderDetailPageInPut : BasePageInput
{
    /// <summary>
    /// 教练ID
    /// </summary>
    public Guid CoachId { get; set; } = Guid.Empty;

    /// <summary>
    /// 订单ID
    /// </summary>
    public Guid OrderId { get; set; } = Guid.Empty;
}


public class HnjxPayOrderDetailOutPut : HnjxPayOrderDetailEntity
{
    /// <summary>
    /// </summary>
    public string xm { get; set; }

    /// <summary>
    /// </summary>
    public string CostTypeName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int RowIndex { get; set; }
}