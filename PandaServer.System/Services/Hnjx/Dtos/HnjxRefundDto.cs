namespace PandaServer.System.Services.Hnjx.Dtos;

/// <summary>
/// </summary>
public class HnjxRefundInPut : HnjxRefundEntity
{
}

/// <summary>
/// </summary>
public class HnjxRefundPageInPut : BasePageInput
{
    /// <summary>
    /// </summary>
    public List<DateTime> PayTimes { get; set; } = new();

    /// <summary>
    /// </summary>
    public Guid CoachId { get; set; }
}

/// <summary>
/// </summary>
public class HnjxRefundOutPut : HnjxRefundEntity
{
    /// <summary>
    /// </summary>
    public string CreateUserName { get; set; }
}