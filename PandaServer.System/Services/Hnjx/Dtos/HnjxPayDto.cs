namespace PandaServer.System.Services.Hnjx.Dtos;

/// <summary>
/// </summary>
public class HnjxPayInPut : HnjxPayEntity
{
}

/// <summary>
/// </summary>
public class HnjxPayPageInPut : BasePageInput
{
    /// <summary>
    /// </summary>
    public List<DateTime> PayTimes { get; set; } = new();


    /// <summary>
    /// </summary>
    public List<Guid> PayTypeIds { get; set; } = new();


    /// <summary>
    /// </summary>
    public List<Guid> CostTypeIds { get; set; } = new();


    /// <summary>
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string sfzmhm { get; set; }
}

/// <summary>
/// </summary>
public class HnjxPayOutPut : HnjxPayEntity
{
    /// <summary>
    /// 
    /// </summary>
    public int RowIndex { get; set; }

    /// <summary>
    /// </summary>
    public string xm { get; set; }

    /// <summary>
    /// </summary>
    public string sfzmhm { get; set; }

    /// <summary>
    /// </summary>
    public string PayTypeName { get; set; }

    /// <summary>
    /// </summary>
    public string CostTypeName { get; set; }

    /// <summary>
    /// </summary>
    public string CreateUserName { get; set; }
}