namespace PandaServer.System.Services.Hnjx.Dtos;

public class HnjxVideoInPut : HnjxVideoEntity
{
    /// <summary>
    ///     视频的 Id
    /// </summary>
    public Guid VideoId { get; set; }

    /// <summary>
    ///     当前播放的 时间
    /// </summary>
    public double CurrentTime { get; set; }
}

public class HnjxVideoPageInPut : BasePageInput
{
    public Guid UserId { get; set; }

    public Guid VideoId { get; set; }
}

public class HnjxVideoOutPut : HnjxVideoEntity
{
    /// <summary>
    ///     行号
    /// </summary>
    public int RowIndex { get; set; }

    /// <summary>
    ///     当前的播放时间
    /// </summary>
    public double PlayTime { get; set; }

    /// <summary>
    ///     不要显示 播放
    /// </summary>
    public bool NoPlay { get; set; }


    /// <summary>
    ///     播放进度
    /// </summary>
    public string PlayTimeProgress { get; set; }
}