using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxPayOrderDetailService" />
/// </summary>
public class HnjxPayOrderDetailService : CustomDbRepository<HnjxPayOrderDetailEntity>, IHnjxPayOrderDetailService, ITransient
{
    #region 添加

    /// <inheritdoc />
    public async Task<bool> Add(HnjxPayOrderDetailEntity orderDetail)
    {
        if (orderDetail.Id == Guid.Empty)
            orderDetail.Id = Guid.NewGuid();

        if (orderDetail.CreateTime < DateTime.Now.AddMinutes(-3))
            orderDetail.CreateTime = DateTime.Now;

        return await InsertAsync(orderDetail);
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<HnjxPayOrderDetailEntity?> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id);
    }

    /// <inheritdoc />
    public async Task<List<HnjxPayOrderDetailEntity>> GetListByOrderId(Guid orderId)
    {
        return await GetListAsync(m => m.OrderId == orderId);
    }

    /// <inheritdoc />
    public async Task<List<HnjxPayOrderDetailOutPut>> GetDetailListByOrderId(Guid orderId)
    {
        return await Context.Queryable<HnjxPayOrderDetailEntity>()
            .LeftJoin<HnjxCoachEntity>((m, n) => m.CoachId == n.Id)
            .LeftJoin<HnjxCostTypeEntity>((m, n, o) => m.CostTypeId == o.Id)
            .Where(m => m.OrderId == orderId)
            .Select((m, n, o) => new HnjxPayOrderDetailOutPut
            {
                Id = m.Id,
                xm = n.xm,
                CostTypeName = o.Name,
                PayMoney = m.PayMoney
            }).ToListAsync();
    }

    #endregion 查询

    #region 分页查询

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxPayOrderDetailOutPut>> Page(HnjxPayOrderDetailPageInPut input)
    {
        var query = Context.Queryable<HnjxPayOrderDetailEntity>()
            .LeftJoin<HnjxCoachEntity>((m, n) => m.CoachId == n.Id)
            .LeftJoin<HnjxCostTypeEntity>((m, n, o) => m.CostTypeId == o.Id)
            .Where(m => m.OrderId == input.OrderId)
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey), m => m.CoachId.ToString().Contains(input.SearchKey.Trim()))
            .WhereIF(input.CoachId != Guid.Empty, m => m.CoachId == input.CoachId)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n, o) => new HnjxPayOrderDetailOutPut
            {
                Id = m.Id,
                xm = n.xm,
                CostTypeName = o.Name,
                PayMoney = m.PayMoney,
                CreateTime = m.CreateTime,
                ShortId = m.ShortId
            });

        var data = await query.ToPagedListAsync(input.Current, input.Size);

        data.Records = data.Records.ToList().Select<HnjxPayOrderDetailOutPut, HnjxPayOrderDetailOutPut>((u, i) =>
        {
            u.RowIndex = (input.Current - 1) * input.Size + (i + 1);
            return u;
        });

        return data;
    }

    #endregion 分页查询
}