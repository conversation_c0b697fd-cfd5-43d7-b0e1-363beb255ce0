namespace PandaServer.System.Services.Hnjx;

public interface IHnjxPayOrderService
{
    /// <summary>
    ///     返回实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HnjxPayOrderEntity?> GetById(Guid id);

    /// <summary>
    ///     直接添加实体
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task<bool> Add(HnjxPayOrderEntity data);
}