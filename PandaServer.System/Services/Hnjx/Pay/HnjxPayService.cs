using PandaServer.System.Services.Hnjx.Dtos;
using PandaServer.System.Services.SystemSecurity;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxPayService" />
/// </summary>
public class HnjxPayService : CustomDbRepository<HnjxPayEntity>, IHnjxPayService
{
    private readonly IEasyLogService _easyLogService;
    private readonly IHnjxCoachService _hnjxCoachService;
    private readonly IHnjxPayOrderDetailService _hnjxPayOrderDetailService;

    public HnjxPayService(IHnjxPayOrderDetailService hnjxPayOrderDetailService, IHnjxCoachService hnjxCoachService,
        IEasyLogService easyLogService)
    {
        _hnjxPayOrderDetailService = hnjxPayOrderDetailService;
        _hnjxCoachService = hnjxCoachService;
        _easyLogService = easyLogService;
    }

    #region 更新

    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetById(id);

        if (data == null)
            throw Oops.Bah("当前数据为空");

        data.Delete();

        if (!await UpdateAsync(data))
            throw Oops.Bah("删除失败，稍后重试");

        await _hnjxCoachService.UpdatePayTimeById(data.CoachId, Convert.ToDateTime("2000-01-01"));
        return true;
    }

    #endregion 更新

    #region 添加

    /// <inheritdoc />
    public async Task<HnjxPayEntity> Add(HnjxPayInPut inPut)
    {
        if (inPut.CoachId == Guid.Empty)
            throw Oops.Bah("传入参数 Id  为空!");

        if (await ExistsByCoachId(inPut.CoachId))
            throw Oops.Bah("当前教练有缴费记录，无法重复缴费!");

        var hnjxPay = new HnjxPayEntity();
        hnjxPay.Create();
        hnjxPay.Id = Guid.NewGuid();
        hnjxPay.TenantId = UserManager.TenantId;
        hnjxPay.PayTypeId = inPut.PayTypeId;
        hnjxPay.CoachId = inPut.CoachId;
        hnjxPay.CostTypeId = inPut.CostTypeId;
        hnjxPay.PayMoney = inPut.PayMoney;
        hnjxPay.DiscountMoney = 0;
        hnjxPay.HnjxOrderId = Guid.Empty;

        hnjxPay.OrderId = Guid.Empty;
        hnjxPay.OrderDetailId = Guid.Empty;

        hnjxPay.SendCardTime = Convert.ToDateTime("1900-01-01");
        hnjxPay.SendCardTrackingNumber = "";
        hnjxPay.InvoiceName = "";
        hnjxPay.TaxNumber = "";
        hnjxPay.TaxFile = "";
        hnjxPay.Remark = "";
        hnjxPay.CardNumber = "";
        hnjxPay.PayTime = inPut.PayTime;

        hnjxPay.MerchantOrderSn = "";
        hnjxPay.OrderSn = "";
        hnjxPay.ChannelOrderSn = "";
        hnjxPay.InsOrderSn = "";

        hnjxPay.RefundPayId = Guid.Empty;

        if (!await InsertAsync(hnjxPay))
            throw Oops.Bah("添加记录失败");

        await _hnjxCoachService.UpdatePayTimeById(hnjxPay.CoachId, hnjxPay.PayTime);
        return hnjxPay;
    }

    /// <inheritdoc />
    public async Task<HnjxPayEntity?> Add(HnjxRefundEntity refund)
    {
        var hnjxPay = await GetById(refund.PayId);

        if (hnjxPay == null)
            throw Oops.Bah("缴费记录为空，刷新页面重试");

        var refunPay = new HnjxPayEntity();
        refunPay.Create();

        if (refund.RefundPayId != Guid.Empty)
            refunPay.Id = refund.RefundPayId;
        else
            refunPay.Id = Guid.NewGuid();
        refunPay.TenantId = UserManager.TenantId;
        refunPay.PayTypeId = hnjxPay.PayTypeId;
        refunPay.CoachId = hnjxPay.CoachId;
        refunPay.CostTypeId = hnjxPay.CostTypeId;
        refunPay.PayMoney = 0 - refund.RefundMoney;
        refunPay.DiscountMoney = 0;
        refunPay.HnjxOrderId = Guid.Empty;

        refunPay.OrderId = Guid.Empty;
        refunPay.OrderDetailId = Guid.Empty;

        refunPay.SendCardTime = Convert.ToDateTime("1900-01-01");
        refunPay.SendCardTrackingNumber = "";
        refunPay.InvoiceName = "";
        refunPay.TaxNumber = "";
        refunPay.TaxFile = "";
        refunPay.Remark = refund.Remark;
        refunPay.CardNumber = "";
        refunPay.PayTime = refund.FinishTime;

        refunPay.MerchantOrderSn = refund.MerchantOrderSn;
        refunPay.OrderSn = refund.OrderSn;
        refunPay.ChannelOrderSn = refund.ChannelOrderSn;
        refunPay.InsOrderSn = refund.InsOrderSn;

        hnjxPay.RefundPayId = refund.PayId;

        if (!await InsertAsync(refunPay))
            throw Oops.Bah("添加记录失败");

        return refunPay;
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<HnjxPayEntity?> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id && m.IsDelete == false);
    }

    /// <inheritdoc />
    public async Task<bool> ExistsById(Guid id)
    {
        return await IsAnyAsync(m => m.Id == id && m.IsDelete == false);
    }


    /// <inheritdoc />
    public async Task<bool> ExistsByCoachId(Guid coachId)
    {
        return await IsAnyAsync(m => m.CoachId == coachId && m.IsDelete == false);
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxPayOutPut>> Page(HnjxPayPageInPut inPut)
    {
        var pageInfo = Context.Queryable<HnjxPayEntity>()
            .LeftJoin<HnjxCostTypeEntity>((m, n) => m.CostTypeId == n.Id)
            .LeftJoin<PayTypeEntity>((m, n, o) => m.PayTypeId == o.Id)
            .LeftJoin<HnjxCoachEntity>((m, n, o, p) => m.CoachId == p.Id)
            .LeftJoin<HnjxCompanyEntity>((m, n, o, p, q) => p.CompanyId == q.Id)
            .LeftJoin<UserEntity>((m, n, o, p, q, r) => m.CreateUserId == r.Id)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .WhereIF(!string.IsNullOrEmpty(inPut.sfzmhm), (m, n, o, p) => p.sfzmhm.Contains(inPut.sfzmhm))
            .WhereIF(inPut.PayTimes.Count == 2,
                m => SqlFunc.LessThan(m.PayTime, inPut.PayTimes[1].AddDays(1)))
            .WhereIF(inPut.PayTimes.Count == 2,
                m => SqlFunc.GreaterThanOrEqual(m.PayTime, inPut.PayTimes[0]))
            .WhereIF(inPut.PayTypeIds.Count > 0,
                m => inPut.PayTypeIds.Contains(m.PayTypeId))
            .WhereIF(inPut.CostTypeIds.Count > 0,
                m => inPut.CostTypeIds.Contains(m.CostTypeId))
            .WhereIF(inPut.Id.ParseToGuid() != Guid.Empty, m => m.CoachId == inPut.Id.ParseToGuid())
            .WhereIF(!UserManager.IsTenantAdmin, (m, n, o, p, q) => q.AdminUserId == UserManager.UserId)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n, o, p, q, r) => new HnjxPayOutPut
            {
                Id = m.Id,
                xm = p.xm,
                sfzmhm = p.sfzmhm,
                CostTypeName = n.Name,
                PayTypeName = o.Name,
                PayTime = m.PayTime,
                PayMoney = m.PayMoney,
                CreateTime = m.CreateTime,
                CreateUserName = r.RealName,
                Remark = m.Remark,
                ChannelOrderSn = m.ChannelOrderSn,
                InsOrderSn = m.InsOrderSn,
                TaxFile = m.TaxFile,
                SendCardTrackingNumber = m.SendCardTrackingNumber,
                DiscountMoney = m.DiscountMoney
            });

        var totalNumber = 0;
        var data = pageInfo.ToOffsetPage(inPut.Current, inPut.Size == 0 ? 99999 : inPut.Size, ref totalNumber);

        data = data.ToList().Select<HnjxPayOutPut, HnjxPayOutPut>((u, i) =>
            {
                u.RowIndex = (inPut.Current - 1) * inPut.Size + (i + 1);

                return u;
            }).ToList();

        var result =
            data.ToSqlSugarPagedList(inPut.Current, inPut.Size, totalNumber);

        return result;
    }

    #endregion 查询

    #region 支付完成回调方法

    /// <inheritdoc />
    public async Task<bool> HnjxPayOrderDetailCompleteCallBack(OrderDetailEntity detail, DateTime payTime,
        Guid payTypeId, string merchantOrderSn = "", string orderSn = "", string channelOrderSn = "",
        string insOrderSn = "")
    {
        if (await ExistsById(detail.OutId))
            return false;


        var hnjxPayOrder = await _hnjxPayOrderDetailService.GetById(detail.OutId);

        if (hnjxPayOrder == null) return false;

        var hnjxPay = new HnjxPayEntity();
        hnjxPay.Create();
        hnjxPay.Id = detail.OutId;
        hnjxPay.TenantId = hnjxPayOrder.TenantId;
        hnjxPay.PayTypeId = payTypeId;
        hnjxPay.CoachId = hnjxPayOrder.CoachId;
        hnjxPay.CostTypeId = hnjxPayOrder.CostTypeId;
        hnjxPay.PayMoney = hnjxPayOrder.PayMoney;
        hnjxPay.DiscountMoney = 0;
        hnjxPay.HnjxOrderId = hnjxPayOrder.Id;

        hnjxPay.OrderId = detail.OrderId;
        hnjxPay.OrderDetailId = detail.Id;

        hnjxPay.SendCardTime = Convert.ToDateTime("1900-01-01");
        hnjxPay.SendCardTrackingNumber = "";
        hnjxPay.InvoiceName = hnjxPayOrder.InvoiceName;
        hnjxPay.TaxNumber = hnjxPayOrder.TaxNumber;
        hnjxPay.TaxFile = "";
        hnjxPay.Remark = "";
        hnjxPay.CardNumber = "";
        hnjxPay.PayTime = payTime;

        hnjxPay.MerchantOrderSn = merchantOrderSn;
        hnjxPay.OrderSn = orderSn;
        hnjxPay.ChannelOrderSn = channelOrderSn;
        hnjxPay.InsOrderSn = insOrderSn;

        if (!await InsertAsync(hnjxPay))
            return false;

        return await _hnjxCoachService.UpdatePayTimeById(hnjxPay.CoachId, payTime);
    }


    /// <inheritdoc />
    public async Task<bool> HnjxPayOrderCompleteCallBack(OrderDetailEntity detail, DateTime payTime, Guid payTypeId,
        string merchantOrderSn = "", string orderSn = "", string channelOrderSn = "", string insOrderSn = "")
    {
        var orderDetails = await _hnjxPayOrderDetailService.GetListByOrderId(detail.OutId);

        foreach (var orderDetail in orderDetails)
            if (!await ExistsById(orderDetail.Id))
            {
                var hnjxPay = new HnjxPayEntity();
                hnjxPay.Create();
                hnjxPay.Id = orderDetail.Id;
                hnjxPay.TenantId = orderDetail.TenantId;
                hnjxPay.PayTypeId = payTypeId;
                hnjxPay.CoachId = orderDetail.CoachId;
                hnjxPay.CostTypeId = orderDetail.CostTypeId;
                hnjxPay.PayMoney = orderDetail.PayMoney;
                hnjxPay.DiscountMoney = 0;
                hnjxPay.HnjxOrderId = orderDetail.Id;

                hnjxPay.OrderId = orderDetail.OrderId;
                hnjxPay.OrderDetailId = orderDetail.Id;

                hnjxPay.SendCardTime = Convert.ToDateTime("1900-01-01");
                hnjxPay.SendCardTrackingNumber = "";
                hnjxPay.InvoiceName = orderDetail.InvoiceName;
                hnjxPay.TaxNumber = orderDetail.TaxNumber;
                hnjxPay.TaxFile = "";
                hnjxPay.Remark = "";
                hnjxPay.CardNumber = "";
                hnjxPay.PayTime = payTime;

                hnjxPay.MerchantOrderSn = merchantOrderSn;
                hnjxPay.OrderSn = orderSn;
                hnjxPay.ChannelOrderSn = channelOrderSn;
                hnjxPay.InsOrderSn = insOrderSn;

                if (!await InsertAsync(hnjxPay))
                    return false;

                await _hnjxCoachService.UpdatePayTimeById(hnjxPay.CoachId, payTime);
            }

        return true;
    }

    #endregion 支付完成回调方法
}