using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxCostTypeService" />
/// </summary>
public class HnjxCostTypeService : CustomDbRepository<HnjxCostTypeEntity>, IHnjxCostTypeService
{
    #region 添加

    /// <inheritdoc />
    public async Task<HnjxCostTypeEntity> Add(HnjxCostTypeInPut inPut)
    {
        if (string.IsNullOrEmpty(inPut.Name) || inPut.Name.Trim() == "")
            throw Oops.Bah("名称不能为空!");
        if (await ExistsByName(Guid.Empty, inPut.Name))
            throw Oops.Bah("名称重复，请重试");

        var entity = inPut.Adapt<HnjxCostTypeEntity>();
        entity.Create();
        entity.TenantId = UserManager.TenantId;

        entity.Name = inPut.Name;
        entity.ApplyType = (HnjxApplyTypeEnum)inPut.ApplyType.ParseToInt();

        if (await InsertAsync(entity))
            return entity;
        throw Oops.Bah("添加失败");
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<HnjxCostTypeEntity?> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id);
    }


    /// <inheritdoc />
    public async Task<HnjxCostTypeEntity?> GetByApplyType(Guid tenantId, HnjxApplyTypeEnum applyType)
    {
        var data = await GetListAsync(m => m.TenantId == tenantId && m.ApplyType == applyType && m.IsDelete == false,
            m => m.CreateTime,
            OrderByType.Desc);

        if (data.Count > 0)
            return data[0];
        return null;
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxCostTypeOutPut>> Page(HnjxCostTypePageInPut inPut)
    {
        var query = Context.Queryable<HnjxCostTypeEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .WhereIF(!string.IsNullOrEmpty(inPut.SearchKey), m => m.Name.Contains(inPut.SearchKey.Trim()))
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .OrderByDescending(m => m.CreateTime)
            .Select((m, n) => new HnjxCostTypeOutPut
            {
                Id = m.Id,
                Name = m.Name,
                PayMoney = m.PayMoney,
                ApplyType = m.ApplyType,
                ApplyTypeLabel = "",
                CreateUserName = n.RealName,
                CreateTime = m.CreateTime
            });

        var pageInfo = await query.ToPagedListAsync(inPut.Current, inPut.Size); //分页

        pageInfo.Records.ToList().ForEach(u =>
        {
            u.ApplyTypeLabel = ((HnjxApplyTypeEnum)u.ApplyType.ParseToInt()).GetDescription();
        });
        return pageInfo;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsByName(Guid costTypeId, string name)
    {
        return await IsAnyAsync(m =>
            m.Id != costTypeId && m.Name == name && m.IsDelete == false && m.TenantId == UserManager.TenantId);
    }

    #endregion 查询

    #region 更新

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data == null)
            throw Oops.Bah("未找到相关的数据，请刷新页面");

        data.Delete();

        return await UpdateAsync(data);
    }


    /// <inheritdoc />
    public async Task<bool> Edit(HnjxCostTypeInPut inPut)
    {
        if (string.IsNullOrEmpty(inPut.Name) || inPut.Name.Trim() == "")
            throw Oops.Bah("名称不能为空!");

        var entity = await GetById(inPut.Id);

        entity.Name = inPut.Name;
        entity.ApplyType = (HnjxApplyTypeEnum)inPut.ApplyType.ParseToInt();

        entity.Modify();

        if (await ExistsByName(entity.Id, entity.Name))
            throw Oops.Bah("名称重复，请重试");
        return await UpdateAsync(entity);
    }

    #endregion 更新
}