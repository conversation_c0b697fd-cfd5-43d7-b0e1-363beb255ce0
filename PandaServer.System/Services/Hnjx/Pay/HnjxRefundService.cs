using PandaServer.System.Services.Hnjx.Dtos;
using Yitter.IdGenerator;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxRefundService" />
/// </summary>
public class HnjxRefundService : CustomDbRepository<HnjxRefundEntity>, IHnjxRefundService
{
    #region 更新

    /// <inheritdoc />
    public async Task<bool> Edit(HnjxRefundEntity data)
    {
        return await UpdateAsync(data);
    }

    #endregion 更新

    #region 添加

    /// <inheritdoc />
    public async Task<HnjxRefundEntity> Add(HnjxRefundInPut input, HnjxPayOrderDetailEntity orderDetail)
    {
        var refund = await Context.Queryable<HnjxRefundEntity>()
            .Where(m => m.PayId == input.PayId)
            .FirstAsync();

        if (refund != null)
            return refund;

        if (input.RefundMoney <= 0)
            throw Oops.Bah("退款金额错误");

        refund = new HnjxRefundEntity();
        refund.Create();
        refund.PayId = input.PayId;
        refund.TenantId = UserManager.TenantId;
        refund.RefundMoney = input.RefundMoney;
        refund.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;

        refund.OrderDetailId = orderDetail.Id;
        refund.RefundMoney = input.RefundMoney;

        refund.MerchantOrderSn = YitIdHelper.NextId().ToString();
        refund.OrderSn = "";
        refund.InsOrderSn = "";
        refund.ChannelOrderSn = "";

        refund.Result = "";


        if (!await InsertAsync(refund))
            throw Oops.Bah("添加退款订单失败");


        return refund;
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<HnjxRefundEntity> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id);
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxRefundOutPut>> Page(HnjxRefundPageInPut input)
    {
        var query = Context.Queryable<HnjxRefundEntity>()
            .LeftJoin<HnjxPayEntity>((m, n) => m.PayId == n.Id)
            .LeftJoin<UserEntity>((m, n, o) => m.CreateUserId == o.Id)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .WhereIF(input.PayTimes.Count == 2,
                (m, n) => SqlFunc.LessThan(n.PayTime, input.PayTimes[1].AddDays(1)))
            .WhereIF(input.PayTimes.Count == 2,
                (m, n) => SqlFunc.GreaterThanOrEqual(n.PayTime, input.PayTimes[0]))
            .WhereIF(input.CoachId != Guid.Empty, (m, n) => n.CoachId == input.CoachId)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n, o) => new HnjxRefundOutPut
            {
                Id = m.Id,
                RefundMoney = m.RefundMoney,
                Result = m.Result,
                MerchantOrderSn = m.MerchantOrderSn,
                OrderSn = m.OrderSn,
                ChannelOrderSn = m.ChannelOrderSn,
                InsOrderSn = m.InsOrderSn,
                Remark = m.Remark,
                CreateTime = m.CreateTime,
                CreateUserName = o.RealName,
                FinishTime = m.FinishTime
            });

        var pageInfo = await query.ToPagedListAsync(input.Current, input.Size); //分页

        return pageInfo;
    }

    #endregion 查询
}