using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

public interface IHnjxPayOrderDetailService
{
    /// <summary>
    ///     通过编号 获取 实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HnjxPayOrderDetailEntity?> GetById(Guid id);


    /// <summary>
    ///     通过 订单的编号  获取到 订单
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    Task<List<HnjxPayOrderDetailEntity>> GetListByOrderId(Guid orderId);

    /// <summary>
    ///     通过 订单的编号  获取到 订单的明细
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    Task<List<HnjxPayOrderDetailOutPut>> GetDetailListByOrderId(Guid orderId);

    /// <summary>
    ///     添加实体
    /// </summary>
    /// <param name="orderDetail"></param>
    /// <returns></returns>
    Task<bool> Add(HnjxPayOrderDetailEntity orderDetail);

    /// <summary>
    ///     分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxPayOrderDetailOutPut>> Page(HnjxPayOrderDetailPageInPut input);
}