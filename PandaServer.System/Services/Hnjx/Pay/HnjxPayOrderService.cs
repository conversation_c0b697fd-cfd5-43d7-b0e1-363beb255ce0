namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxPayOrderService" />
/// </summary>
public class HnjxPayOrderService : CustomDbRepository<HnjxPayOrderEntity>, IHnjxPayOrderService, ITransient
{
    #region 查询

    /// <inheritdoc />
    public async Task<HnjxPayOrderEntity?> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id);
    }

    #endregion 查询

    #region 添加

    /// <inheritdoc />
    public async Task<bool> Add(HnjxPayOrderEntity data)
    {
        return await InsertAsync(data);
    }

    #endregion 添加
}