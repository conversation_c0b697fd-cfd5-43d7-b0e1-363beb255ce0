using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

public interface IHnjxCostTypeService : ITransient
{
    /// <summary>
    ///     通过 Id  返回 实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HnjxCostTypeEntity?> GetById(Guid id);

    /// <summary>
    ///     通过 受理 类型 返回 实体
    /// </summary>
    /// <param name="tenantId"></param>
    /// <param name="applyType"></param>
    /// <returns></returns>
    Task<HnjxCostTypeEntity?> GetByApplyType(Guid tenantId, HnjxApplyTypeEnum applyType);


    /// <summary>
    ///     分页 查询
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxCostTypeOutPut>> Page(HnjxCostTypePageInPut inPut);


    /// <summary>
    ///     判断 名字 是否 重复
    /// </summary>
    /// <param name="classId"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    Task<bool> ExistsByName(Guid classId, string name);

    /// <summary>
    ///     更新删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> UpdateIsDelete(Guid id);

    /// <summary>
    ///     添加
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<HnjxCostTypeEntity> Add(HnjxCostTypeInPut inPut);


    /// <summary>
    ///     更新
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<bool> Edit(HnjxCostTypeInPut inPut);
}