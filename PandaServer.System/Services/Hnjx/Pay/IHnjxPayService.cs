using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

public interface IHnjxPayService : ITransient
{
    /// <summary>
    ///     根据 id  返回 对象
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HnjxPayEntity?> GetById(Guid id);

    /// <summary>
    ///     检查 id 是否 重复
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> ExistsById(Guid id);

    /// <summary>
    ///     检查  教练是否 有缴费记录
    /// </summary>
    /// <param name="coachId"></param>
    /// <returns></returns>
    Task<bool> ExistsByCoachId(Guid coachId);

    /// <summary>
    ///     分页查询
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxPayOutPut>> Page(HnjxPayPageInPut inPut);

    /// <summary>
    ///     根据 Id  假删除 并且更新 教练的缴费时间
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> UpdateIsDelete(Guid id);

    /// <summary>
    ///     添加实体
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<HnjxPayEntity> Add(HnjxPayInPut inPut);

    /// <summary>
    ///     通过退款 来添加实体
    /// </summary>
    /// <param name="refund"></param>
    /// <returns></returns>
    Task<HnjxPayEntity?> Add(HnjxRefundEntity refund);

    /// <summary>
    ///     订单的明细 下面支付完成以后 完成以后得 回调方法
    /// </summary>
    /// <param name="detail"></param>
    /// <param name="payTime"></param>
    /// <param name="payTypeId"></param>
    /// <param name="merchantOrderSn"></param>
    /// <param name="orderSn"></param>
    /// <param name="channelOrderSn"></param>
    /// <param name="insOrderSn"></param>
    /// <returns></returns>
    Task<bool> HnjxPayOrderDetailCompleteCallBack(OrderDetailEntity detail, DateTime payTime,
        Guid payTypeId, string merchantOrderSn = "", string orderSn = "", string channelOrderSn = "",
        string insOrderSn = "");


    /// <summary>
    ///     订单 支付完成以后 完成以后得 回调方法
    /// </summary>
    /// <param name="detail"></param>
    /// <param name="payTime"></param>
    /// <param name="payTypeId"></param>
    /// <param name="merchantOrderSn"></param>
    /// <param name="orderSn"></param>
    /// <param name="channelOrderSn"></param>
    /// <param name="insOrderSn"></param>
    /// <returns></returns>
    Task<bool> HnjxPayOrderCompleteCallBack(OrderDetailEntity detail, DateTime payTime, Guid payTypeId,
        string merchantOrderSn = "", string orderSn = "", string channelOrderSn = "", string insOrderSn = "");
}