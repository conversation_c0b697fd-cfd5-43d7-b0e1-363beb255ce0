using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     退款相关的 服务
/// </summary>
public interface IHnjxRefundService : ITransient
{
    /// <summary>
    ///     通过 Id  返回实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HnjxRefundEntity> GetById(Guid id);

    /// <summary>
    ///     分页查询
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxRefundOutPut>> Page(HnjxRefundPageInPut inPut);

    /// <summary>
    ///     更新
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task<bool> Edit(HnjxRefundEntity data);

    /// <summary>
    ///     添加
    /// </summary>
    /// <param name="inPut"></param>
    /// <param name="orderDetail"></param>
    /// <returns></returns>
    Task<HnjxRefundEntity> Add(HnjxRefundInPut inPut, HnjxPayOrderDetailEntity orderDetail);
}