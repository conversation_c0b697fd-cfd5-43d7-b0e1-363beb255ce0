using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxCoachWorkService" />
/// </summary>
public class HnjxCoachWorkService : CustomDbRepository<HnjxCoachWorkEntity>, IHnjxCoachWorkService
{
    private readonly IHnjxCoachService _hnjxCoachService;

    public HnjxCoachWorkService(IHnjxCoachService hnjxCoachService)
    {
        _hnjxCoachService = hnjxCoachService;
    }

    #region 新增

    /// <summary>
    ///     编辑
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    public async Task<HnjxCoachWorkEntity> Add(HnjxCoachWorkInPut inPut)
    {
        var coach = await _hnjxCoachService.GetById(inPut.CoachId);
        if (coach == null)
            throw Oops.Bah("教练信息为空，刷新页面重试");

        if (coach.CreateUserId != UserManager.UserId && UserManager.IsTenantAdmin)
            throw Oops.Bah("非数据创建人员或管理员无法编辑该数据");

        if (inPut.StartTime < Convert.ToDateTime("1980-01-01"))
            throw Oops.Bah("请选择正确的工作开始日期!");

        if (inPut.EndTime < Convert.ToDateTime("1980-01-01"))
            throw Oops.Bah("请选择正确的工作结束日期!");


        var data = inPut.Adapt<HnjxCoachWorkEntity>();
        data.Create();
        data.TenantId = UserManager.TenantId;
        data.OutId = "";

        if (string.IsNullOrEmpty(data.Company))
            throw Oops.Bah("请填写工作单位!");
        if (string.IsNullOrEmpty(data.CarType))
            throw Oops.Bah("请输入准驾车型!");


        if (!await InsertAsync(data))
            throw Oops.Bah("添加数据失败，稍后重试");

        return data;
    }

    #endregion 新增

    #region 查询

    /// <inheritdoc />
    public async Task<HnjxCoachWorkEntity?> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id && m.IsDelete == false);
    }


    /// <inheritdoc />
    public async Task<List<HnjxCoachWorkEntity>> GetListByCoachId(Guid coachId)
    {
        return await GetListAsync(m => m.CoachId == coachId && m.IsDelete == false, m => m.StartTime,
            OrderByType.Desc);
    }

    #endregion 查询

    #region 编辑

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data == null)
            throw Oops.Bah("未找到相关的数据，请刷新页面");

        data.Delete();

        return await UpdateAsync(data);
    }


    /// <inheritdoc />
    public async Task<bool> Edit(HnjxCoachWorkInPut inPut)
    {
        var coach = await _hnjxCoachService.GetById(inPut.CoachId);
        if (coach == null)
            throw Oops.Bah("教练信息为空，刷新页面重试");

        if (coach.CreateUserId != UserManager.UserId && UserManager.IsTenantAdmin)
            throw Oops.Bah("非数据创建人员或管理员无法编辑该数据");

        if (inPut.StartTime < Convert.ToDateTime("1980-01-01"))
            throw Oops.Bah("请选择正确的工作开始日期!");

        if (inPut.EndTime < Convert.ToDateTime("1980-01-01"))
            throw Oops.Bah("请选择正确的工作结束日期!");

        var data = await GetById(inPut.Id);
        data.StartTime = inPut.StartTime;
        data.EndTime = inPut.EndTime;
        data.Company = inPut.Company;
        data.CarType = inPut.CarType;

        if (string.IsNullOrEmpty(data.Company))
            throw Oops.Bah("请填写工作单位!");
        if (string.IsNullOrEmpty(data.CarType))
            throw Oops.Bah("请输入准驾车型!");

        if (!await UpdateAsync(data))
            throw Oops.Bah("更新数据失败，稍后重试");

        return true;
    }

    #endregion 编辑
}