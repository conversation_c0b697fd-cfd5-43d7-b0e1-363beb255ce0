using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
/// </summary>
public interface IHnjxClassService : ITransient
{
    /// <summary>
    ///     返回 实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HnjxClassEntity?> GetById(Guid id);


    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<string> GetNameById(Guid id);


    /// <summary>
    ///     翻页 查询
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxClassOutPut>> Page(HnjxClassPageInPut inPut);

    /// <summary>
    ///     判断 名字 是否 重复
    /// </summary>
    /// <param name="classId"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    Task<bool> ExistsByName(Guid classId, string name);

    /// <summary>
    ///     更新删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> UpdateIsDelete(Guid id);


    /// <summary>
    ///     更新
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<bool> Edit(HnjxClassInPut inPut);


    /// <summary>
    ///     添加
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<HnjxClassEntity> Add(HnjxClassInPut inPut);
}