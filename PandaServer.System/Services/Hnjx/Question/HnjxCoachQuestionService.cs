using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxCoachQuestionService" />
/// </summary>
public class HnjxCoachQuestionService : CustomDbRepository<HnjxCoachQuestionEntity>, IHnjxCoachQuestionService, ITransient
{
    private readonly IHnjxCoachQuestionDetailService _hnjxCoachQuestionDetailService;
    private readonly IHnjxQuestionService _hnjxQuestionService;

    public HnjxCoachQuestionService(IHnjxQuestionService hnjxQuestionService,
        IHnjxCoachQuestionDetailService hnjxCoachQuestionDetailService)
    {
        _hnjxQuestionService = hnjxQuestionService;
        _hnjxCoachQuestionDetailService = hnjxCoachQuestionDetailService;
    }


    #region 方法

    /// <inheritdoc />
    public async Task<decimal> SubmitAnswer(Guid coachQuestionId)
    {
        var score = await _hnjxCoachQuestionDetailService.GetScore(coachQuestionId);

        var data = await GetSingleAsync(m => m.Id == coachQuestionId);

        data.Score = score;

        if (!await UpdateAsync(data))
            throw Oops.Bah("保存成绩失败，稍后重试");

        return score;
    }

    #endregion 方法

    #region 查询

    /// <inheritdoc />
    public async Task<bool> IsPassByUserId(Guid userId)
    {
        return await Context.Queryable<HnjxCoachQuestionEntity>().SplitTable(st => st)
            .Where(m => m.UserId == UserManager.UserId && m.Score > 60)
            .AnyAsync();
    }

    /// <inheritdoc />
    public async Task<HnjxCoachQuestionOutPut> GetNewQuestion(int questionCount)
    {
        var data = await _hnjxQuestionService.GetRandomList(100);

        var indexData = new HnjxCoachQuestionEntity();
        indexData.Id = Guid.NewGuid();
        indexData.OpenId = HttpNewUtil.GetHeader("openId");
        indexData.UserId = UserManager.UserId;
        indexData.CreateTime = DateTime.Now;
        indexData.EndTime = Convert.ToDateTime("1900-01-01");
        indexData.Score = 0;

        if (!await InsertAsync(indexData))
            throw Oops.Bah("生成题目失败，稍后重试");


        var details = new List<HnjxCoachQuestionDetailEntity>();
        var outPuts = new List<HnjxCoachQuestionDetailOutPut>();

        foreach (var item in data)
        {
            var detail = new HnjxCoachQuestionDetailEntity
            {
                Id = Guid.NewGuid(),
                CoachQuestionId = indexData.Id,
                QuestionId = item.Id,
                Answer = "",
                AnswerTime = Convert.ToDateTime("1900-01-01"),
                CreateTime = DateTime.Now
            };

            details.Add(detail);
            var outPut = detail.Adapt<HnjxCoachQuestionDetailOutPut>();


            outPut.Question = item.Question;
            outPut.QuestionType = item.QuestionType;
            outPut.Answer = item.Answer;
            outPut.ImagePath = item.ImagePath;
            outPut.UserAnswer = "";

            outPuts.Add(outPut);
        }

        if (!await _hnjxCoachQuestionDetailService.Add(details))
            throw Oops.Bah("生成题目内容失败，稍后重试");

        var questionOutPut = new HnjxCoachQuestionOutPut();
        questionOutPut.CoachQuestionId = indexData.Id;
        questionOutPut.Questions = outPuts;

        return questionOutPut;
    }

    /// <inheritdoc />
    public async Task<HnjxCoachQuestionEntity> GetMaxScoreQuestionByUserId(Guid userId)
    {
        var data = await Context.Queryable<HnjxCoachQuestionEntity>().SplitTable(st => st)
            .Where(m => m.UserId == userId)
            .OrderBy(m => m.Score, OrderByType.Desc)
            .ToPageListAsync(1, 1);

        if (data.Count == 0)
            throw Oops.Bah("无相关记录");

        return data[0];
    }


    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxCoachQuestionOutPut>> Page(HnjxCoachQuestionPageInPut inPut)
    {
        var pageInfo = Context.Queryable<HnjxCoachQuestionEntity>().SplitTable(st => st)
            .WhereIF(inPut.Id != Guid.Empty, m => m.UserId == inPut.Id && m.Score > 0)
            .WhereIF(inPut.Id == Guid.Empty, m => m.UserId == UserManager.UserId && m.Score > 0)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select(m => new HnjxCoachQuestionOutPut
            {
                Id = m.Id,
                Score = m.Score,
                CreateTime = m.CreateTime
            });


        var totalNumber = 0;
        var data = pageInfo.ToOffsetPage(inPut.Current, inPut.Size == 0 ? 99999 : inPut.Size, ref totalNumber);


        data = data.ToList().Select<HnjxCoachQuestionOutPut, HnjxCoachQuestionOutPut>((u, i) =>
        {
            u.RowIndex = (inPut.Current - 1) * inPut.Size + (i + 1);

            return u;
        }).ToList();

        var result =
            data.ToSqlSugarPagedList(inPut.Current, inPut.Size, totalNumber);

        return result;
    }

    #endregion 查询
}