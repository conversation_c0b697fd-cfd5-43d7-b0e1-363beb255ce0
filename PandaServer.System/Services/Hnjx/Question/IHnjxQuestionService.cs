using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

public interface IHnjxQuestionService
{
    /// <summary>
    ///     返回实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HnjxQuestionEntity?> GetById(Guid id);

    /// <summary>
    ///     随机取
    /// </summary>
    /// <param name="count">Top 的条数</param>
    /// <returns></returns>
    Task<List<HnjxQuestionOutPut>> GetRandomList(int count);

    /// <summary>
    ///     翻页 查询
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxQuestionOutPut>> Page(HnjxQuestionPageInPut inPut);
}