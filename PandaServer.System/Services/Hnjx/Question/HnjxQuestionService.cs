using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

/// <summary>
///     <inheritdoc cref="IHnjxQuestionService" />
/// </summary>
public class HnjxQuestionService : CustomDbRepository<HnjxQuestionEntity>, IHnjxQuestionService, ITransient
{
    #region 查询

    /// <inheritdoc />
    public async Task<HnjxQuestionEntity?> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id);
    }


    /// <inheritdoc />
    public async Task<List<HnjxQuestionOutPut>> GetRandomList(int count)
    {
        return await Context.Queryable<HnjxQuestionEntity>()
            .Take(count)
            .OrderBy(st => SqlFunc.GetRandom())
            .Select(m => new HnjxQuestionOutPut
            {
                Id = m.Id,
                Question = m.Question,
                QuestionType = m.QuestionType,
                Answer = m.Answer,
                ImagePath = m.ImagePath,
                UserAnswer = ""
            })
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<HnjxQuestionOutPut>> Page(HnjxQuestionPageInPut inPut)
    {
        var query = Context.Queryable<HnjxQuestionEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .WhereIF(!string.IsNullOrEmpty(inPut.Question), m => m.Question.Contains(inPut.Question))
            .WhereIF(!string.IsNullOrEmpty(inPut.QuestionType),
                m => Convert.ToInt32(m.QuestionType) == inPut.QuestionType.ParseToInt())
            .OrderByDescending(m => m.CreateTime)
            .Select((m, n) => new HnjxQuestionOutPut
            {
                Id = m.Id,
                Question = m.Question,
                QuestionType = m.QuestionType,
                Answer = m.Answer,
                ImagePath = m.ImagePath,
                CreateUserName = n.RealName,
                CreateTime = m.CreateTime
            });

        var pageInfo = await query.ToPagedListAsync(inPut.Current, inPut.Size); //分页


        pageInfo.Records.ToList().ForEach(m => { m.QuestionTypeDescription = m.QuestionType.GetDescription(); });

        return pageInfo;
    }

    #endregion 查询
}