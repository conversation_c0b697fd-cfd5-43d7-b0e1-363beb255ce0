using PandaServer.System.Services.Hnjx.Dtos;

namespace PandaServer.System.Services.Hnjx;

public interface IHnjxCoachQuestionService
{
    /// <summary>
    ///     是否 通过
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<bool> IsPassByUserId(Guid userId);

    /// <summary>
    ///     生成 新的 题目
    /// </summary>
    /// <param name="questionCount"></param>
    /// <returns></returns>
    Task<HnjxCoachQuestionOutPut> GetNewQuestion(int questionCount);

    /// <summary>
    ///     获得 教练 最好的成绩 记录
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<HnjxCoachQuestionEntity> GetMaxScoreQuestionByUserId(Guid userId);

    /// <summary>
    ///     分页查询
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<HnjxCoachQuestionOutPut>> Page(HnjxCoachQuestionPageInPut inPut);

    /// <summary>
    ///     保存成绩
    /// </summary>
    /// <param name="coachQuestionId"></param>
    /// <returns></returns>
    Task<decimal> SubmitAnswer(Guid coachQuestionId);
}