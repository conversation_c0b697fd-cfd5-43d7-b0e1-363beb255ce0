using PandaServer.Core;
using PandaServer.SqlSugar;
using PandaServer.System.Entity;
using PandaServer.System.Services.Student.Dto.Contract;
using SqlSugar;

namespace PandaServer.System.Services.Student.Contract;

/// <summary>
/// 学生合同服务实现
/// </summary>
public class JxStudentContractService : CustomDbRepository<JxStudentContractEntity>, IJxStudentContractService, ITransient
{

    /// <summary>
    /// 根据学生ID获取合同信息
    /// </summary>
    /// <param name="studentId">学生ID</param>
    /// <returns>合同信息列表</returns>
    public async Task<JxStudentContractEntity> GetByStudentIdAsync(Guid studentId)
    {
        return await Context.Queryable<JxStudentContractEntity>()
            .Where(x => x.StudentId == studentId)
            .OrderByDescending(x => x.CreateTime)
            .FirstAsync();
    }

    /// <summary>
    /// 上传合同PDF文件
    /// </summary>
    /// <param name="input">合同上传输入参数</param>
    /// <returns>上传结果</returns>
    public async Task<JxStudentContractEntity> UploadContractFileAsync(JxStudentContractUploadInput input)
    {
        var entity = await Context.Queryable<JxStudentContractEntity>()
            .FirstAsync(x => x.Id == input.Id) ?? new JxStudentContractEntity();

        entity.StudentId = input.StudentId;
        entity.TemplateId = input.TemplateId;
        entity.OpenId = input.OpenId;
        entity.SignFile = input.PdfFile;

        if (entity.Id == Guid.Empty)
        {
            entity.Create();
            await InsertAsync(entity);
        }
        else
        {
            entity.Modify();
            await UpdateAsync(entity);
        }

        return entity;
    }

    /// <summary>
    /// 上传签名视频
    /// </summary>
    /// <param name="input">视频上传输入参数</param>
    /// <returns>上传结果</returns>
    public async Task<JxStudentContractEntity> UploadSignVideoAsync(JxStudentContractVideoInput input)
    {
        var entity = await Context.Queryable<JxStudentContractEntity>()
            .FirstAsync(x => x.Id == input.Id);

        if (entity == null)
            throw new Exception("合同不存在");

        entity.SignVideo = input.VideoFile;
        entity.Modify();
        await UpdateAsync(entity);

        return entity;
    }

    /// <summary>
    /// 上传签名笔迹
    /// </summary>
    /// <param name="input">笔迹上传输入参数</param>
    /// <returns>上传结果</returns>
    public async Task<JxStudentContractEntity> UploadSignAsync(JxStudentContractSignInput input)
    {
        var entity = await Context.Queryable<JxStudentContractEntity>()
            .FirstAsync(x => x.Id == input.Id);

        if (entity == null)
            throw new Exception("合同不存在");

        entity.Sign = input.SignFile;
        entity.Modify();
        await UpdateAsync(entity);

        return entity;
    }

    /// <summary>
    /// 提交合同签署信息
    /// </summary>
    /// <param name="input">签署信息提交参数</param>
    /// <returns>合同实体</returns>
    public async Task<JxStudentContractEntity> SubmitContractSignAsync(JxStudentContractSubmitInput input)
    {
        // 检查是否已经存在合同
        var existingContract = await Context.Queryable<JxStudentContractEntity>()
            .FirstAsync(x => x.StudentId == input.StudentId);

        if (existingContract != null)
            throw new Exception("合同已经签署，无法重复提交");




        // 创建新的合同记录
        var entity = new JxStudentContractEntity
        {
            StudentId = input.StudentId,
            TemplateId = input.TemplateId,
            OpenId = input.OpenId,
            Latitude = input.Latitude ?? "",
            Longitude = input.Longitude ?? "",
            SignFile = "",
            SignVideo = "",
            Sign = "",
            SignImages = ""
        };

        entity.Create();
        await InsertAsync(entity);

        return entity;
    }

    /// <summary>
    /// 上传签字信息
    /// </summary>
    /// <param name="input">签字上传输入参数</param>
    /// <returns>上传结果</returns>
    public async Task<JxStudentContractEntity> UploadSignatureAsync(JxStudentContractSignatureInput input)
    {
        var entity = await Context.Queryable<JxStudentContractEntity>()
            .FirstAsync(x => x.Id == input.Id);

        if (entity == null)
            throw new Exception("合同不存在");

        entity.Sign = input.SignFile;
        entity.Modify();
        await UpdateAsync(entity);

        return entity;
    }

    /// <summary>
    /// 上传拍照信息
    /// </summary>
    /// <param name="input">拍照上传输入参数</param>
    /// <returns>上传结果</returns>
    public async Task<JxStudentContractEntity> UploadPhotoAsync(JxStudentContractPhotoInput input)
    {
        var entity = await Context.Queryable<JxStudentContractEntity>()
            .FirstAsync(x => x.Id == input.Id);

        if (entity == null)
            throw new Exception("合同不存在");

        entity.SignImages = input.SignImages;
        entity.Modify();
        await UpdateAsync(entity);

        return entity;
    }
}