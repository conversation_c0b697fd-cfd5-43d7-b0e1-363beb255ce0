using PandaServer.System.Services.Student.Dto;
using PandaServer.System.Services.Student.Dto.Contract;
using Spire.Pdf;
using Document = Spire.Doc.Document;

namespace PandaServer.System.Services.Student.Contract;

public interface IJxContractTemplateService : ITransient
{
    /// <summary>
    ///     返回 实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<JxContractTemplateEntity> GetById(Guid id);


    /// <summary>
    ///     分页 查询
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<JxContractTemplateOutPut>> Page(JxContractTemplatePageInPut inPut);

    /// <summary>
    ///     判断 名字 是否 重复
    /// </summary>
    /// <param name="templateId"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    Task<bool> ExistsByName(Guid templateId, string name);

    /// <summary>
    ///     通过这几个参数  获取 合同模板
    /// </summary>
    /// <param name="student"></param>
    /// <returns></returns>
    Task<JxContractTemplateEntity?> GetContract(JxStudentOutPut student);

    /// <summary>
    ///     更新删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> UpdateIsDelete(Guid id);


    /// <summary>
    ///     更新
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<bool> Update(JxContractTemplatePageInPut inPut);

    /// <summary>
    ///     添加
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<JxContractTemplateEntity> Add(JxContractTemplatePageInPut inPut);


    /// <summary>
    ///     Pdf  替换 文本
    /// </summary>
    /// <param name="template"></param>
    /// <param name="signFilePath"></param>
    /// <param name="sealFilePath"></param>
    /// <param name="student"></param>
    /// <returns></returns>
    Task<PdfDocument> MakeContract(JxContractTemplateEntity template, string signFilePath, string sealFilePath, JxStudentOutPut student);

    /// <summary>
    ///     保存合同模板控件
    /// </summary>
    /// <param name="input">合同模板控件信息</param>
    /// <returns>保存结果</returns>
    Task<bool> SaveContractTemplateControls(JxContractTemplateControlsInPut input);

    /// <summary>
    ///     获取合同模板控件列表
    /// </summary>
    /// <param name="templateId">合同模板ID</param>
    /// <returns>合同模板控件列表</returns>
    Task<List<JxContractTemplateControlsEntity>> GetContractTemplateControlsList(Guid templateId);

    /// <summary>
    ///     获取合同模板控件列表
    /// </summary>
    /// <param name="templateId">合同模板ID</param>
    /// <param name="pageNo"></param>
    /// <returns>合同模板控件列表</returns>
    Task<List<JxContractTemplateControlsEntity>> GetContractTemplateControlsList(Guid templateId, int pageNo);

    /// <summary>
    /// 测试 PDF 文本写入功能
    /// </summary>
    /// <returns>测试结果</returns>
    Task<string> TestPdfTextWriting();
}