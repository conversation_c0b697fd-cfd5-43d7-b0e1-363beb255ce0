using System.Drawing;
using PandaServer.System.Services.Student.Pay;
using PandaServer.System.Services.Student.Dto.Contract;
using PandaServer.System.Services.Student.Jx;
using PandaServer.System.Services.Student.Dto;
using Spire.Pdf;
using Spire.Pdf.Graphics;
using System.Reflection;
using System.IO;
using System.Net.Http;
using PandaServer.Core.Utils.Pdf;

namespace PandaServer.System.Services.Student.Contract;

/// <summary>
///     <inheritdoc cref="IJxContractTemplateService" />
/// </summary>
public class JxContractTemplateService : CustomDbRepository<JxContractTemplateEntity>, IJxContractTemplateService
{
    private readonly IJxClassService _jxClassService;
    private readonly IJxCompanyService _jxCompanyService;
    private readonly IJxContractTemplateCarTypeService _jxContractTemplateCarTypeService;
    private readonly IJxContractTemplateJxClassService _jxContractTemplateJxClassService;
    private readonly IJxContractTemplateJxDeptService _jxContractTemplateJxDeptService;
    private readonly IJxContractTemplateJxFieldService _jxContractTemplateJxFieldService;
    private readonly IJxDeptService _jxDeptService;
    private readonly IJxFieldService _jxFieldService;

    public JxContractTemplateService(IJxContractTemplateCarTypeService jxContractTemplateCarTypeService,
        IJxContractTemplateJxClassService jxContractTemplateJxClassService,
        IJxContractTemplateJxDeptService jxContractTemplateJxDeptService,
        IJxContractTemplateJxFieldService jxContractTemplateJxFieldService, IJxClassService jxClassService,
        IJxDeptService jxDeptService, IJxFieldService jxFieldService, IJxCompanyService jxCompanyService)
    {
        _jxContractTemplateCarTypeService = jxContractTemplateCarTypeService;
        _jxContractTemplateJxClassService = jxContractTemplateJxClassService;
        _jxContractTemplateJxDeptService = jxContractTemplateJxDeptService;
        _jxContractTemplateJxFieldService = jxContractTemplateJxFieldService;

        _jxClassService = jxClassService;
        _jxDeptService = jxDeptService;
        _jxFieldService = jxFieldService;
        _jxCompanyService = jxCompanyService;
    }

    #region 添加

    /// <inheritdoc />
    public async Task<JxContractTemplateEntity> Add(JxContractTemplatePageInPut input)
    {
        if (string.IsNullOrEmpty(input.Name))
            throw Oops.Bah("名称不能为空!");

        if (await ExistsByName(Guid.Empty, input.Name))
            throw Oops.Bah("名称重复，请重试");

        var data = input.Adapt<JxContractTemplateEntity>();
        data.Create();
        data.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;
        data.TenantId = UserManager.TenantId;
        data.Name = input.Name;
        data.Priority = input.Priority;
        data.Url = input.Url == null ? "" : input.Url;

        if (!await InsertAsync(data))
            throw Oops.Bah("添加数据失败");


        await _jxContractTemplateCarTypeService.Update(input);
        await _jxContractTemplateJxClassService.Update(input);
        await _jxContractTemplateJxDeptService.Update(input);
        await _jxContractTemplateJxFieldService.Update(input);

        return data;
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<JxContractTemplateEntity> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id);
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<JxContractTemplateOutPut>> Page(JxContractTemplatePageInPut input)
    {
        var pageInfo = Context.Queryable<JxContractTemplateEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .WhereIF(!string.IsNullOrEmpty(input.Name),
                m => m.Name.Contains(input.Name.Trim()))
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n) => new JxContractTemplateOutPut
            {
                Id = m.Id,
                Name = m.Name,
                CreateUserName = n.RealName,
                CreateTime = m.CreateTime,
                CreateUserId = m.CreateUserId,
            });

        var totalNumber = 0;
        var data = pageInfo.ToOffsetPage(input.Current, input.Size == 0 ? 99999 : input.Size, ref totalNumber);

        data = data.ToList().Select<JxContractTemplateOutPut, JxContractTemplateOutPut>((u, i) =>
            {
                u.RowIndex = (input.Current - 1) * input.Size + (i + 1);

                return u;
            }).ToList();

        var result =
            data.ToSqlSugarPagedList(input.Current, input.Size, totalNumber);

        return result;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsByName(Guid templateId, string name)
    {
        return await IsAnyAsync(m =>
            m.Id != templateId && m.Name == name && m.IsDelete == false && m.TenantId == UserManager.TenantId);
    }


    /// <inheritdoc />
    public async Task<JxContractTemplateEntity?> GetContract(JxStudentOutPut student)
    {
        return await Context.Queryable<JxContractTemplateEntity>()
            .LeftJoin<JxContractTemplateCarTypeEntity>((m, n) => m.Id == n.TemplateId && n.IsDelete == false)
            .LeftJoin(Context.Queryable<JxContractTemplateCarTypeEntity>().Where(m => m.IsDelete == false)
                .GroupBy(m => m.TemplateId).Select(m => new
                {
                    m.TemplateId,
                    DataCount = SqlFunc.AggregateCount(1)
                }), (m, n, n1) => m.Id == n1.TemplateId)
            .LeftJoin<JxContractTemplateJxClassEntity>((m, n, n1, o) =>
                m.Id == o.TemplateId && o.IsDelete == false)
            .LeftJoin(Context.Queryable<JxContractTemplateJxClassEntity>().Where(m => m.IsDelete == false)
                .GroupBy(m => m.TemplateId).Select(m => new
                {
                    m.TemplateId,
                    DataCount = SqlFunc.AggregateCount(1)
                }), (m, n, n1, o, o1) => m.Id == o1.TemplateId)
            .LeftJoin<JxContractTemplateJxDeptEntity>((m, n, n1, o, o1, p) =>
                m.Id == p.TemplateId && p.IsDelete == false)
            .LeftJoin(Context.Queryable<JxContractTemplateJxDeptEntity>().Where(m => m.IsDelete == false)
                .GroupBy(m => m.TemplateId).Select(m => new
                {
                    m.TemplateId,
                    DataCount = SqlFunc.AggregateCount(1)
                }), (m, n, n1, o, o1, p, p1) => m.Id == p1.TemplateId)
            .LeftJoin<JxContractTemplateJxFieldEntity>((m, n, n1, o, o1, p, p1, q) =>
                m.Id == q.TemplateId && n.IsDelete == false)
            .LeftJoin(Context.Queryable<JxContractTemplateJxFieldEntity>().Where(m => m.IsDelete == false)
                .GroupBy(m => m.TemplateId).Select(m => new
                {
                    m.TemplateId,
                    DataCount = SqlFunc.AggregateCount(1)
                }), (m, n, n1, o, o1, p, p1, q, q1) => m.Id == q1.TemplateId)
            .Where((m, n, n1, o, o1, p, p1, q, q1) => m.IsDelete == false)
            .Where((m, n, n1, o, o1, p, p1, q, q1) => m.TenantId == student.TenantId)
            .Where((m, n, n1, o, o1, p, p1, q, q1) =>
                n.CarType == student.CarType || n1.DataCount == 0 || SqlFunc.EqualsNull(n1.TemplateId, null))
            .Where((m, n, n1, o, o1, p, p1, q, q1) => o.JxClassId == student.JxClassId || o1.DataCount == 0 ||
                                                      SqlFunc.EqualsNull(o1.TemplateId, null))
            .Where((m, n, n1, o, o1, p, p1, q, q1) => p.JxDeptId == student.JxDeptId || p1.DataCount == 0 ||
                                                      SqlFunc.EqualsNull(p1.TemplateId, null))
            .WhereIF(student.JxFieldId != Guid.Empty,
                (m, n, n1, o, o1, p, p1, q, q1) => q.JxFieldId == student.JxFieldId || q1.DataCount == 0 ||
                                                   SqlFunc.EqualsNull(q1.TemplateId, null))
            .WhereIF(student.JxFieldId == Guid.Empty,
                (m, n, n1, o, o1, p, p1, q, q1) => q1.DataCount == 0 || SqlFunc.EqualsNull(q1.TemplateId, null))
            .OrderBy(m => m.Priority, OrderByType.Desc)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .SingleAsync();
    }

    #endregion 查询

    #region 更新

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data == null)
            throw Oops.Bah("未找到相关的数据，请刷新页面");

        data.Delete();

        return await UpdateAsync(data);
    }


    /// <inheritdoc />
    public async Task<bool> Update(JxContractTemplatePageInPut input)
    {
        if (string.IsNullOrEmpty(input.Name))
            throw Oops.Bah("名称不能为空!");

        if (await ExistsByName(input.Id, input.Name))
            throw Oops.Bah("名称重复，请重试");

        var data = await GetById(input.Id);

        data.Name = input.Name;
        data.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;
        data.Name = input.Name;
        data.Priority = input.Priority;
        data.Url = input.Url == null ? "" : input.Url;

        data.Modify();

        if (!await UpdateAsync(data))
            throw Oops.Bah("更新失败，稍后重试");

        await _jxContractTemplateCarTypeService.Update(input);
        await _jxContractTemplateJxClassService.Update(input);
        await _jxContractTemplateJxDeptService.Update(input);
        await _jxContractTemplateJxFieldService.Update(input);

        return true;
    }

    #endregion 更新

    #region 方法

    /// <inheritdoc />
    public async Task<PdfDocument> MakeContract(JxContractTemplateEntity template, string signFilePath, string sealFilePath, JxStudentOutPut student)
    {
        var jxCompany = await _jxCompanyService.GetById(student.JxCompanyId);

        var doc = new PdfDocument();

        using (var client = new HttpClient())
        {
            var fileByte = await client.GetByteArrayAsync(template.Url);
            Stream stream = new MemoryStream(fileByte);
            doc.LoadFromStream(stream);
        }

        var controls = await GetContractTemplateControlsList(template.Id);

        // 使用 PDF 文本写入工具类
        var pdfWriter = new PdfTextWriter();

        // 检查字体文件是否存在
        if (!pdfWriter.IsFontFileExists())
        {
            throw Oops.Bah("字体文件不存在，请确保 font/simsun.ttf 文件存在于应用程序目录中");
        }

        foreach (var control in controls)
        {
            // 确保页码在有效范围内
            if (control.PageNo <= 0 || control.PageNo > doc.Pages.Count)
                continue;

            var page = doc.Pages[control.PageNo - 1]; // 页码从0开始

            // 根据字段名称获取对应的学生信息
            var fieldValue = await GetFieldValue(control.Field, student, jxCompany);

            if (!string.IsNullOrEmpty(fieldValue))
            {
                if (control.FontSize <= 0)
                {
                    control.FontSize = 15;
                }

                // 将比例坐标转换为实际坐标
                // control.X 和 control.Y 是页面比例值（0-1之间），需要乘以页面实际尺寸
                var pageWidth = page.Size.Width;
                var pageHeight = page.Size.Height;
                var actualX = control.X * pageWidth;
                var actualY = control.Y * pageHeight;

                // 使用工具类在指定位置绘制文本
                pdfWriter.DrawText(page, fieldValue, actualX, actualY, control.FontSize);
            }
        }

        return doc;
    }

    /// <summary>
    /// 根据字段名称获取对应的值
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <param name="student">学生信息</param>
    /// <param name="jxCompany">驾校公司信息</param>
    /// <returns>字段值</returns>
    private async Task<string> GetFieldValue(string fieldName, JxStudentOutPut student, JxCompanyEntity jxCompany)
    {
        return fieldName switch
        {
            // 报名日期相关
            "报名-年" => student.RegistrationDate.ToString("yyyy"),
            "报名-月" => student.RegistrationDate.ToString("MM"),
            "报名-日" => student.RegistrationDate.ToString("dd"),
            "报名日期" => student.RegistrationDate.ToString("yyyy-MM-dd"),

            // 签署日期相关（使用当前日期或注册时间）
            "签署-年" => DateTime.Now.ToString("yyyy"),
            "签署-月" => DateTime.Now.ToString("MM"),
            "签署-日" => DateTime.Now.ToString("dd"),
            "签署日期" => DateTime.Now.ToString("yyyy-MM-dd"),

            // 基本信息
            "姓名" or "xm" or "name" => student.xm,
            "证件号码" or "sfzmhm" or "idcard" => student.sfzmhm,
            "性别" or "xb" or "gender" => student.xb == XbEnum.Male ? "男" : "女",
            "手机号码" or "yddh" or "phone" => student.yddh,
            "驾校名称" or "jxmc" or "schoolname" => jxCompany?.Name ?? "",
            "登记地址" or "djzsxxdz" or "address" => student.djzsxxdz,
            "联系地址" or "lxzsxxdz" or "contactaddress" => student.lxzsxxdz,

            // 培训信息
            "车型" or "cartype" or "zjcx" => student.CarType,
            "报名学费" or "tuition" => student.Tuition.ToString("F2"),
            "已缴学费" or "tuitionpay" => student.TuitionPay.ToString("F2"),
            "报名班型" => student.JxClassName,
            "报名店面" => student.JxDeptName,
            "训练场地" => student.JxFieldName,
            "合同编号" => student.Id.ToString("N").Substring(0, 8).ToUpper(), // 生成8位合同编号

            // 相关人员
            "介绍人" => student.SaleUserName,
            "科一教练" => student.TeachOneUserName,
            "科二教练" => student.TeachTwoUserName,
            "科三教练" => student.TeachThreeUserName,

            // 兼容原有的英文字段名称
            "gddh" or "tel" => student.gddh,
            "csrq" or "birthdate" => student.csrq.ToString("yyyy-MM-dd"),
            "dzyx" or "email" => student.dzyx,
            "yzbm" or "postcode" => student.yzbm,
            "companyname" => jxCompany?.CompanyName ?? "",
            "companycode" => jxCompany?.CompanyCode ?? "",
            "transportcode" => jxCompany?.TransportCode ?? "",
            "companyaddress" => jxCompany?.Address ?? "",
            "xzjcx" => student.xzjcx,
            "dabh" or "archiveno" => student.dabh,
            "ywzl" => string.IsNullOrEmpty(student.xzjcx) ? "初次申领" : "增驾",
            "tuitionnopay" => student.TuitionNoPay.ToString("F2"),
            "tuitiondiscountmoney" => student.TuitionDiscountMoney.ToString("F2"),
            "shouldpaymoney" => student.ShouldPayMoney.ToString("F2"),
            "registertime" => student.RegisterTime.ToString("yyyy-MM-dd HH:mm:ss"),
            "paycompletetime" => student.PayCompleteTime.ToString("yyyy-MM-dd HH:mm:ss"),
            "sfzyxqs" => student.sfzyxqs.ToString("yyyy-MM-dd"),
            "sfzyxqz" => student.sfzyxqz.ToString("yyyy-MM-dd"),
            "sfzmmc" => student.sfzmmc,
            "remark" => student.Remark,
            "pinyin" => student.PinYin,
            "outid" => student.OutId,
            "jtnum" => student.JtNum,

            // 默认返回空字符串
            _ => ""
        };
    }

    /// <summary>
    /// 获取班型名称
    /// </summary>
    private async Task<string> GetJxClassName(Guid jxClassId)
    {
        if (jxClassId == Guid.Empty) return "";
        var jxClass = await _jxClassService.GetById(jxClassId);
        return jxClass?.Name ?? "";
    }

    /// <summary>
    /// 获取报名点名称
    /// </summary>
    private async Task<string> GetJxDeptName(Guid jxDeptId)
    {
        if (jxDeptId == Guid.Empty) return "";
        var jxDept = await _jxDeptService.GetById(jxDeptId);
        return jxDept?.Name ?? "";
    }

    /// <summary>
    /// 获取训练场名称
    /// </summary>
    private async Task<string> GetJxFieldName(Guid jxFieldId)
    {
        if (jxFieldId == Guid.Empty) return "";
        var jxField = await _jxFieldService.GetById(jxFieldId);
        return jxField?.Name ?? "";
    }

    /// <summary>
    /// 获取介绍人姓名
    /// </summary>
    private async Task<string> GetSaleUserName(Guid saleUserId)
    {
        if (saleUserId == Guid.Empty) return "";
        var user = await Context.Queryable<UserEntity>().Where(m => m.Id == saleUserId).FirstAsync();
        return user?.RealName ?? "";
    }

    /// <summary>
    /// 获取教练姓名
    /// </summary>
    private async Task<string> GetCoachName(Guid studentId, int subject)
    {
        // 这里需要根据实际的教练分配逻辑来实现
        // 暂时返回空字符串，需要根据具体的教练分配表来实现
        return "";
    }

    /// <summary>
    ///     保存合同模板控件
    /// </summary>
    /// <param name="input">合同模板控件信息</param>
    /// <returns>保存结果</returns>
    public async Task<bool> SaveContractTemplateControls(JxContractTemplateControlsInPut input)
    {
        if (input == null)
        {
            throw Oops.Bah("输入参数不能为空");
        }

        if (input.TemplateId == Guid.Empty)
        {
            throw Oops.Bah("模板ID不能为空");
        }

        var controls = await GetContractTemplateControlsList(input.TemplateId);

        foreach (var control in controls)
        {
            if (!input.Controls.Any(m => m.Id == control.Id))
            {
                await Context.Deleteable<JxContractTemplateControlsEntity>().Where(m => m.Id == control.Id).ExecuteCommandAsync();
            }
        }

        foreach (var control in input.Controls)
        {
            var data = await Context.Queryable<JxContractTemplateControlsEntity>().Where(m => m.Id == control.Id).FirstAsync();
            if (data == null)
            {
                data = new JxContractTemplateControlsEntity
                {
                    Id = control.Id,
                    TemplateId = input.TemplateId,
                    TenantId = input.TenantId,
                    Field = control.Field,
                    PageNo = control.PageNo,
                    X = control.X,
                    Y = control.Y,
                    FontSize = control.FontSize
                };

                await Context.Insertable(data).ExecuteCommandAsync();
            }
            else
            {
                data.Field = control.Field;
                data.PageNo = control.PageNo;
                data.X = control.X;
                data.Y = control.Y;
                data.FontSize = control.FontSize;

                await Context.Updateable(data).ExecuteCommandAsync();
            }
        }

        return true;
    }


    /// <inheritdoc />
    public async Task<List<JxContractTemplateControlsEntity>> GetContractTemplateControlsList(Guid templateId)
    {
        return await Context.Queryable<JxContractTemplateControlsEntity>().Where(m => m.TemplateId == templateId && m.TenantId == UserManager.TenantId).ToListAsync();
    }


    /// <inheritdoc />
    public async Task<List<JxContractTemplateControlsEntity>> GetContractTemplateControlsList(Guid templateId, int pageNo)
    {
        return await Context.Queryable<JxContractTemplateControlsEntity>().Where(m => m.TemplateId == templateId && m.PageNo == pageNo && m.TenantId == UserManager.TenantId).ToListAsync();
    }

    /// <summary>
    /// 测试 PDF 写字功能
    /// </summary>
    /// <returns>测试结果</returns>
    public async Task<string> TestPdfTextWriting()
    {
        try
        {
            // 创建一个简单的测试 PDF
            var doc = new PdfDocument();
            var page = doc.Pages.Add();

            using var pdfWriter = new PdfTextWriter();

            if (!pdfWriter.IsFontFileExists())
            {
                return "错误：字体文件不存在";
            }

            // 获取页面尺寸
            var pageWidth = page.Size.Width;
            var pageHeight = page.Size.Height;

            Console.WriteLine($"页面尺寸: {pageWidth} x {pageHeight}");

            // 测试固定坐标绘制文本
            pdfWriter.DrawText(page, "固定坐标测试 - 中文", 100, 100, 12);
            pdfWriter.DrawText(page, "Fixed Position Test - English", 100, 150, 12);
            pdfWriter.DrawText(page, "数字: 123456", 100, 200, 12);

            // 测试各种中文字符
            pdfWriter.DrawText(page, "姓名：张三", 100, 250, 14);
            pdfWriter.DrawText(page, "身份证号：110101199001011234", 100, 280, 12);
            pdfWriter.DrawText(page, "地址：北京市朝阳区某某街道", 100, 310, 12);
            pdfWriter.DrawText(page, "电话：13800138000", 100, 340, 12);
            pdfWriter.DrawText(page, "驾校：某某驾校", 100, 370, 12);

            // 测试比例坐标绘制文本
            // 左上角 (0.1, 0.1)
            var topLeftX = 0.1f * pageWidth;
            var topLeftY = 0.1f * pageHeight;
            pdfWriter.DrawText(page, "左上角比例坐标", topLeftX, topLeftY, 14);

            // 右上角 (0.8, 0.1)
            var topRightX = 0.8f * pageWidth;
            var topRightY = 0.1f * pageHeight;
            pdfWriter.DrawText(page, "右上角比例坐标", topRightX, topRightY, 14);

            // 左下角 (0.1, 0.8)
            var bottomLeftX = 0.1f * pageWidth;
            var bottomLeftY = 0.8f * pageHeight;
            pdfWriter.DrawText(page, "左下角比例坐标", bottomLeftX, bottomLeftY, 14);

            // 右下角 (0.8, 0.8)
            var bottomRightX = 0.8f * pageWidth;
            var bottomRightY = 0.8f * pageHeight;
            pdfWriter.DrawText(page, "右下角比例坐标", bottomRightX, bottomRightY, 14);

            // 中心位置 (0.5, 0.5)
            var centerX = 0.5f * pageWidth;
            var centerY = 0.5f * pageHeight;
            pdfWriter.DrawText(page, "中心位置比例坐标", centerX, centerY, 16);

            // 保存测试文件
            var testPath = Path.Combine(Path.GetTempPath(), $"test_pdf_{DateTime.Now:yyyyMMddHHmmss}.pdf");
            doc.SaveToFile(testPath);
            doc.Close();

            return $"测试成功！测试文件已保存到: {testPath}\n页面尺寸: {pageWidth:F2} x {pageHeight:F2} 点";
        }
        catch (Exception ex)
        {
            return $"测试失败：{ex.Message}\n详细错误：{ex.StackTrace}";
        }
    }

    #endregion 方法
}