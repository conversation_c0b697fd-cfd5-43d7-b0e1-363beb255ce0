using PandaServer.System.Services.Student.Pay;
using PandaServer.System.Services.Student.Dto.Contract;
using PandaServer.System.Services.Student.Jx;
using PandaServer.System.Services.Student.Dto;
using Spire.Pdf;
using Spire.Pdf.Graphics;
using System.Reflection;
using System.IO;
using System.Net.Http;
using PandaServer.Core.Utils.Pdf;
using SystemFile = System.IO.File;

namespace PandaServer.System.Services.Student.Contract;

/// <summary>
///     <inheritdoc cref="IJxContractTemplateService" />
/// </summary>
public class JxContractTemplateService : CustomDbRepository<JxContractTemplateEntity>, IJxContractTemplateService
{
    private readonly IJxClassService _jxClassService;
    private readonly IJxCompanyService _jxCompanyService;
    private readonly IJxContractTemplateCarTypeService _jxContractTemplateCarTypeService;
    private readonly IJxContractTemplateJxClassService _jxContractTemplateJxClassService;
    private readonly IJxContractTemplateJxDeptService _jxContractTemplateJxDeptService;
    private readonly IJxContractTemplateJxFieldService _jxContractTemplateJxFieldService;
    private readonly IJxDeptService _jxDeptService;
    private readonly IJxFieldService _jxFieldService;

    public JxContractTemplateService(IJxContractTemplateCarTypeService jxContractTemplateCarTypeService,
        IJxContractTemplateJxClassService jxContractTemplateJxClassService,
        IJxContractTemplateJxDeptService jxContractTemplateJxDeptService,
        IJxContractTemplateJxFieldService jxContractTemplateJxFieldService, IJxClassService jxClassService,
        IJxDeptService jxDeptService, IJxFieldService jxFieldService, IJxCompanyService jxCompanyService)
    {
        _jxContractTemplateCarTypeService = jxContractTemplateCarTypeService;
        _jxContractTemplateJxClassService = jxContractTemplateJxClassService;
        _jxContractTemplateJxDeptService = jxContractTemplateJxDeptService;
        _jxContractTemplateJxFieldService = jxContractTemplateJxFieldService;

        _jxClassService = jxClassService;
        _jxDeptService = jxDeptService;
        _jxFieldService = jxFieldService;
        _jxCompanyService = jxCompanyService;
    }

    #region 添加

    /// <inheritdoc />
    public async Task<JxContractTemplateEntity> Add(JxContractTemplatePageInPut input)
    {
        if (string.IsNullOrEmpty(input.Name))
            throw Oops.Bah("名称不能为空!");

        if (await ExistsByName(Guid.Empty, input.Name))
            throw Oops.Bah("名称重复，请重试");

        var data = input.Adapt<JxContractTemplateEntity>();
        data.Create();
        data.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;
        data.TenantId = UserManager.TenantId;
        data.Name = input.Name;
        data.Priority = input.Priority;
        data.Url = input.Url == null ? "" : input.Url;

        if (!await InsertAsync(data))
            throw Oops.Bah("添加数据失败");


        await _jxContractTemplateCarTypeService.Update(input);
        await _jxContractTemplateJxClassService.Update(input);
        await _jxContractTemplateJxDeptService.Update(input);
        await _jxContractTemplateJxFieldService.Update(input);

        return data;
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<JxContractTemplateEntity> GetById(Guid id)
    {
        return await GetSingleAsync(m => m.Id == id);
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<JxContractTemplateOutPut>> Page(JxContractTemplatePageInPut input)
    {
        var pageInfo = Context.Queryable<JxContractTemplateEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .Where(m => m.TenantId == UserManager.TenantId && m.IsDelete == false)
            .WhereIF(!string.IsNullOrEmpty(input.Name),
                m => m.Name.Contains(input.Name.Trim()))
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n) => new JxContractTemplateOutPut
            {
                Id = m.Id,
                Name = m.Name,
                CreateUserName = n.RealName,
                CreateTime = m.CreateTime,
                CreateUserId = m.CreateUserId,
            });

        var totalNumber = 0;
        var data = pageInfo.ToOffsetPage(input.Current, input.Size == 0 ? 99999 : input.Size, ref totalNumber);

        data = data.ToList().Select<JxContractTemplateOutPut, JxContractTemplateOutPut>((u, i) =>
            {
                u.RowIndex = (input.Current - 1) * input.Size + (i + 1);

                return u;
            }).ToList();

        var result =
            data.ToSqlSugarPagedList(input.Current, input.Size, totalNumber);

        return result;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsByName(Guid templateId, string name)
    {
        return await IsAnyAsync(m =>
            m.Id != templateId && m.Name == name && m.IsDelete == false && m.TenantId == UserManager.TenantId);
    }


    /// <inheritdoc />
    public async Task<JxContractTemplateEntity?> GetContract(JxStudentOutPut student)
    {
        return await Context.Queryable<JxContractTemplateEntity>()
            .LeftJoin<JxContractTemplateCarTypeEntity>((m, n) => m.Id == n.TemplateId && n.IsDelete == false)
            .LeftJoin(Context.Queryable<JxContractTemplateCarTypeEntity>().Where(m => m.IsDelete == false)
                .GroupBy(m => m.TemplateId).Select(m => new
                {
                    m.TemplateId,
                    DataCount = SqlFunc.AggregateCount(1)
                }), (m, n, n1) => m.Id == n1.TemplateId)
            .LeftJoin<JxContractTemplateJxClassEntity>((m, n, n1, o) =>
                m.Id == o.TemplateId && o.IsDelete == false)
            .LeftJoin(Context.Queryable<JxContractTemplateJxClassEntity>().Where(m => m.IsDelete == false)
                .GroupBy(m => m.TemplateId).Select(m => new
                {
                    m.TemplateId,
                    DataCount = SqlFunc.AggregateCount(1)
                }), (m, n, n1, o, o1) => m.Id == o1.TemplateId)
            .LeftJoin<JxContractTemplateJxDeptEntity>((m, n, n1, o, o1, p) =>
                m.Id == p.TemplateId && p.IsDelete == false)
            .LeftJoin(Context.Queryable<JxContractTemplateJxDeptEntity>().Where(m => m.IsDelete == false)
                .GroupBy(m => m.TemplateId).Select(m => new
                {
                    m.TemplateId,
                    DataCount = SqlFunc.AggregateCount(1)
                }), (m, n, n1, o, o1, p, p1) => m.Id == p1.TemplateId)
            .LeftJoin<JxContractTemplateJxFieldEntity>((m, n, n1, o, o1, p, p1, q) =>
                m.Id == q.TemplateId && n.IsDelete == false)
            .LeftJoin(Context.Queryable<JxContractTemplateJxFieldEntity>().Where(m => m.IsDelete == false)
                .GroupBy(m => m.TemplateId).Select(m => new
                {
                    m.TemplateId,
                    DataCount = SqlFunc.AggregateCount(1)
                }), (m, n, n1, o, o1, p, p1, q, q1) => m.Id == q1.TemplateId)
            .Where((m, n, n1, o, o1, p, p1, q, q1) => m.IsDelete == false)
            .Where((m, n, n1, o, o1, p, p1, q, q1) => m.TenantId == student.TenantId)
            .Where((m, n, n1, o, o1, p, p1, q, q1) =>
                n.CarType == student.CarType || n1.DataCount == 0 || SqlFunc.EqualsNull(n1.TemplateId, null))
            .Where((m, n, n1, o, o1, p, p1, q, q1) => o.JxClassId == student.JxClassId || o1.DataCount == 0 ||
                                                      SqlFunc.EqualsNull(o1.TemplateId, null))
            .Where((m, n, n1, o, o1, p, p1, q, q1) => p.JxDeptId == student.JxDeptId || p1.DataCount == 0 ||
                                                      SqlFunc.EqualsNull(p1.TemplateId, null))
            .WhereIF(student.JxFieldId != Guid.Empty,
                (m, n, n1, o, o1, p, p1, q, q1) => q.JxFieldId == student.JxFieldId || q1.DataCount == 0 ||
                                                   SqlFunc.EqualsNull(q1.TemplateId, null))
            .WhereIF(student.JxFieldId == Guid.Empty,
                (m, n, n1, o, o1, p, p1, q, q1) => q1.DataCount == 0 || SqlFunc.EqualsNull(q1.TemplateId, null))
            .OrderBy(m => m.Priority, OrderByType.Desc)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .SingleAsync();
    }

    #endregion 查询

    #region 更新

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data == null)
            throw Oops.Bah("未找到相关的数据，请刷新页面");

        data.Delete();

        return await UpdateAsync(data);
    }


    /// <inheritdoc />
    public async Task<bool> Update(JxContractTemplatePageInPut input)
    {
        if (string.IsNullOrEmpty(input.Name))
            throw Oops.Bah("名称不能为空!");

        if (await ExistsByName(input.Id, input.Name))
            throw Oops.Bah("名称重复，请重试");

        var data = await GetById(input.Id);

        data.Name = input.Name;
        data.Remark = string.IsNullOrEmpty(input.Remark) ? "" : input.Remark;
        data.Name = input.Name;
        data.Priority = input.Priority;
        data.Url = input.Url == null ? "" : input.Url;

        data.Modify();

        if (!await UpdateAsync(data))
            throw Oops.Bah("更新失败，稍后重试");

        await _jxContractTemplateCarTypeService.Update(input);
        await _jxContractTemplateJxClassService.Update(input);
        await _jxContractTemplateJxDeptService.Update(input);
        await _jxContractTemplateJxFieldService.Update(input);

        return true;
    }

    #endregion 更新

    #region 方法

    /// <inheritdoc />
    public async Task<PdfDocument> MakeContract(JxContractTemplateEntity template, string signFilePath, string sealFilePath, JxStudentOutPut student, bool autoAddRidingSeals = true)
    {
        var jxCompany = await _jxCompanyService.GetById(student.JxCompanyId);

        var doc = new PdfDocument();

        using (var client = new HttpClient())
        {
            var fileByte = await client.GetByteArrayAsync(template.Url);
            Stream stream = new MemoryStream(fileByte);
            doc.LoadFromStream(stream);
        }

        var controls = await GetContractTemplateControlsList(template.Id);

        // 使用 PDF 文本写入工具类
        var pdfWriter = new PdfTextWriter();

        // 检查字体文件是否存在
        if (!pdfWriter.IsFontFileExists())
        {
            throw Oops.Bah("字体文件不存在，请确保 font/simsun.ttf 文件存在于应用程序目录中");
        }
        Console.WriteLine($"开始处理控件，总数: {controls.Count}");
        Console.WriteLine($"签字文件路径: {signFilePath}");
        Console.WriteLine($"印章文件路径: {sealFilePath}");

        foreach (var control in controls)
        {
            Console.WriteLine($"处理控件: 字段={control.Field}, 页码={control.PageNo}, 坐标=({control.X}, {control.Y}), 字体大小={control.FontSize}");

            // 检查是否为特殊字段
            if (control.Field == "学员签字")
            {
                Console.WriteLine($"发现学员签字字段，signFilePath是否为空: {string.IsNullOrEmpty(signFilePath)}");
            }
            else if (control.Field == "驾校盖章")
            {
                Console.WriteLine($"发现驾校盖章字段，sealFilePath是否为空: {string.IsNullOrEmpty(sealFilePath)}");
            }
            else if (control.Field == "骑缝章")
            {
                Console.WriteLine($"发现骑缝章字段，sealFilePath是否为空: {string.IsNullOrEmpty(sealFilePath)}");
            }

            // 确保页码在有效范围内
            if (control.PageNo <= 0 || control.PageNo > doc.Pages.Count)
            {
                Console.WriteLine($"页码无效，跳过控件: {control.Field}");
                continue;
            }

            var page = doc.Pages[control.PageNo - 1]; // 页码从0开始

            // 将比例坐标转换为实际坐标
            var pageWidth = page.Size.Width;
            var pageHeight = page.Size.Height;
            var actualX = control.X * pageWidth;
            var actualY = control.Y * pageHeight;


            // 处理特殊字段：学员签字、驾校盖章和骑缝章
            if (control.Field == "学员签字" && !string.IsNullOrEmpty(signFilePath))
            {
                Console.WriteLine($"匹配到学员签字字段，开始绘制签字图片");
                await DrawImageOnPdf(page, signFilePath, actualX, actualY, control.FontSize, "signature");
            }
            else if (control.Field == "驾校盖章" && !string.IsNullOrEmpty(sealFilePath))
            {
                Console.WriteLine($"匹配到驾校盖章字段，开始绘制印章图片");
                await DrawImageOnPdf(page, sealFilePath, actualX, actualY, control.FontSize, "seal");
            }
            else if (control.Field == "骑缝章" && !string.IsNullOrEmpty(sealFilePath))
            {
                Console.WriteLine($"匹配到骑缝章字段，开始绘制骑缝章");
                await DrawImageOnPdf(page, sealFilePath, actualX, actualY, control.FontSize, "riding_seal");
            }
            else
            {
                Console.WriteLine($"处理普通文本字段: {control.Field}");
                // 处理普通文本字段
                var fieldValue = await GetFieldValue(control.Field, student, jxCompany);

                if (!string.IsNullOrEmpty(fieldValue))
                {
                    if (control.FontSize <= 0)
                    {
                        control.FontSize = 15;
                    }

                    // 使用工具类在指定位置绘制文本
                    pdfWriter.DrawText(page, fieldValue, actualX, actualY, control.FontSize);
                    Console.WriteLine($"绘制文本完成: {fieldValue}");
                }
                else
                {
                    Console.WriteLine($"字段值为空，跳过: {control.Field}");
                }
            }
        }

        // 添加骑缝章处理（如果有多页且需要骑缝章）
        if (autoAddRidingSeals && !string.IsNullOrEmpty(sealFilePath) && doc.Pages.Count > 1)
        {
            await AddRidingSeals(doc, sealFilePath);
        }

        return doc;
    }

    /// <summary>
    /// 加载图片字节数组
    /// </summary>
    /// <param name="imagePath">图片路径（本地路径或网址）</param>
    /// <returns>图片字节数组</returns>
    private async Task<byte[]> LoadImageBytes(string imagePath)
    {
        if (imagePath.StartsWith("http://") || imagePath.StartsWith("https://"))
        {
            // 从网址下载图片
            using var client = new HttpClient();
            return await client.GetByteArrayAsync(imagePath);
        }
        else
        {
            // 从本地文件读取图片
            if (!SystemFile.Exists(imagePath))
            {
                throw new FileNotFoundException($"图片文件不存在: {imagePath}");
            }
            return await SystemFile.ReadAllBytesAsync(imagePath);
        }
    }

    /// <summary>
    /// 在PDF页面上绘制骑缝章切片（基于参考代码的跨平台实现）
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="imageBytes">完整骑缝章图片字节数组</param>
    /// <param name="pageIndex">当前页索引（从0开始）</param>
    /// <param name="totalPages">总页数</param>
    /// <param name="sliceWidth">每片的宽度</param>
    /// <param name="sealHeight">印章高度</param>
    private void DrawRidingSealSlice(PdfPageBase page, byte[] imageBytes, int pageIndex, int totalPages, float sliceWidth, float sealHeight)
    {
        try
        {
            // 创建图片对象
            using var imageStream = new MemoryStream(imageBytes);
            var image = PdfImage.FromStream(imageStream);

            // 计算印章放置的位置（参考原代码：右边中间）
            float pageWidth = page.Size.Width;
            float pageHeight = page.Size.Height;

            // X坐标：页面右边缘向左偏移30个点（参考原代码）
            float x = pageWidth - 30f;

            // Y坐标：页面中间位置（参考原代码的计算方式）
            float y = pageHeight - sealHeight * 0.24f;

            // 缩放比例（参考原代码：0.24倍）
            float scaledWidth = sliceWidth * 0.24f;
            float scaledHeight = sealHeight * 0.24f;

            Console.WriteLine($"第{pageIndex + 1}页骑缝章切片位置: ({x}, {y}), 缩放尺寸: {scaledWidth}x{scaledHeight}");

            // 使用裁剪来模拟图片切分效果
            page.Canvas.Save();

            try
            {
                // 创建裁剪区域，只显示当前页对应的切片部分
                var clipPath = new PdfPath();
                clipPath.AddRectangle(x, y, scaledWidth, scaledHeight);
                page.Canvas.SetClip(clipPath);

                // 计算图片的偏移量，使得只显示当前页对应的部分
                // 每页向左偏移对应的距离，模拟切片效果
                float imageOffsetX = -pageIndex * scaledWidth;

                // 绘制完整图片，但由于裁剪只会显示当前切片
                float totalScaledWidth = sliceWidth * totalPages * 0.24f;
                page.Canvas.DrawImage(image, x + imageOffsetX, y, totalScaledWidth, scaledHeight);

                Console.WriteLine($"第{pageIndex + 1}页骑缝章切片绘制完成，偏移: {imageOffsetX}");
            }
            finally
            {
                page.Canvas.Restore();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"第{pageIndex + 1}页骑缝章切片绘制失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 添加骑缝章到多页文档（基于参考代码的跨平台实现）
    /// </summary>
    /// <param name="doc">PDF文档</param>
    /// <param name="sealFilePath">完整骑缝章图片路径</param>
    private async Task AddRidingSeals(PdfDocument doc, string sealFilePath)
    {
        try
        {
            Console.WriteLine($"开始添加骑缝章，总页数: {doc.Pages.Count}");

            if (doc.Pages.Count <= 1)
            {
                Console.WriteLine("单页文档无需骑缝章");
                return;
            }

            // 加载完整的骑缝章图片
            byte[] imageBytes = await LoadImageBytes(sealFilePath);

            Console.WriteLine($"骑缝章图片加载完成，大小: {imageBytes.Length} 字节");

            // 使用Spire.PDF创建图片对象来获取尺寸信息
            using var tempStream = new MemoryStream(imageBytes);
            var tempImage = PdfImage.FromStream(tempStream);

            // 由于无法直接获取PdfImage的尺寸，我们使用估算值
            // 或者可以通过绘制到临时画布来获取实际尺寸
            float estimatedWidth = 300f; // 估算的骑缝章宽度
            float estimatedHeight = 120f; // 估算的骑缝章高度

            Console.WriteLine($"估算骑缝章尺寸: {estimatedWidth}x{estimatedHeight}");

            // 计算每页应该分配的图片宽度
            float sliceWidth = estimatedWidth / doc.Pages.Count;

            Console.WriteLine($"每页骑缝章段宽度: {sliceWidth}");

            // 为每页添加骑缝章段
            for (int i = 0; i < doc.Pages.Count; i++)
            {
                var page = doc.Pages[i];

                Console.WriteLine($"在第{i + 1}页添加骑缝章段");

                // 使用裁剪方式模拟图片切分
                DrawRidingSealSlice(page, imageBytes, i, doc.Pages.Count, sliceWidth, estimatedHeight);
            }

            Console.WriteLine("骑缝章添加完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"添加骑缝章失败: {ex.Message}");
            // 如果骑缝章添加失败，不影响整个合同生成过程
        }
    }

    /// <summary>
    /// 绘制圆形印章
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="image">印章图片</param>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="diameter">印章直径</param>
    private void DrawCircularSeal(PdfPageBase page, PdfImage image, float x, float y, float diameter)
    {
        try
        {
            // 保存当前图形状态
            page.Canvas.Save();

            // 创建圆形裁剪路径
            var clipPath = new PdfPath();
            clipPath.AddEllipse(x, y, diameter, diameter);

            // 设置裁剪区域
            page.Canvas.SetClip(clipPath);

            // 在裁剪区域内绘制图片
            page.Canvas.DrawImage(image, x, y, diameter, diameter);

            // 恢复图形状态
            page.Canvas.Restore();

            Console.WriteLine($"圆形印章裁剪绘制完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"圆形印章绘制失败，使用普通方式: {ex.Message}");
            // 如果圆形裁剪失败，使用普通方式绘制
            page.Canvas.DrawImage(image, x, y, diameter, diameter);
        }
    }

    /// <summary>
    /// 绘制椭圆形骑缝章
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="image">印章图片</param>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="width">印章宽度</param>
    /// <param name="height">印章高度</param>
    private void DrawEllipticalSeal(PdfPageBase page, PdfImage image, float x, float y, float width, float height)
    {
        try
        {
            // 保存当前图形状态
            page.Canvas.Save();

            // 创建椭圆形裁剪路径
            var clipPath = new PdfPath();
            clipPath.AddEllipse(x, y, width, height);

            // 设置裁剪区域
            page.Canvas.SetClip(clipPath);

            // 在裁剪区域内绘制图片
            page.Canvas.DrawImage(image, x, y, width, height);

            // 恢复图形状态
            page.Canvas.Restore();

            Console.WriteLine($"椭圆形骑缝章裁剪绘制完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"椭圆形骑缝章绘制失败，使用普通方式: {ex.Message}");
            // 如果椭圆裁剪失败，使用普通方式绘制
            page.Canvas.DrawImage(image, x, y, width, height);
        }
    }

    /// <summary>
    /// 调整图片位置的辅助方法
    /// </summary>
    /// <param name="x">原始X坐标</param>
    /// <param name="y">原始Y坐标</param>
    /// <param name="pageWidth">页面宽度</param>
    /// <param name="pageHeight">页面高度</param>
    /// <param name="imageWidth">图片宽度</param>
    /// <param name="imageHeight">图片高度</param>
    /// <returns>调整后的坐标</returns>
    private (float x, float y) AdjustImagePosition(float x, float y, float pageWidth, float pageHeight, float imageWidth, float imageHeight)
    {
        // 可以根据需要选择不同的调整策略

        float adjustedX = x;
        float adjustedY = y;

        // 选择调整策略（可以根据实际情况修改）
        int strategy = 1; // 1=直接使用, 2=左下角坐标系, 3=居中对齐

        switch (strategy)
        {
            case 1:
                // 策略1: 直接使用原始坐标（适用于左上角坐标系）
                adjustedX = x;
                adjustedY = y;
                Console.WriteLine("使用策略1: 直接使用原始坐标");
                break;

            case 2:
                // 策略2: 从左下角计算（适用于左下角坐标系）
                adjustedX = x;
                adjustedY = pageHeight - y - imageHeight;
                Console.WriteLine("使用策略2: 左下角坐标系转换");
                break;

            case 3:
                // 策略3: 居中对齐
                adjustedX = x - imageWidth / 2;
                adjustedY = y - imageHeight / 2;
                Console.WriteLine("使用策略3: 居中对齐");
                break;
        }

        // 确保图片不超出页面边界
        adjustedX = Math.Max(0, Math.Min(adjustedX, pageWidth - imageWidth));
        adjustedY = Math.Max(0, Math.Min(adjustedY, pageHeight - imageHeight));

        Console.WriteLine($"位置调整: 原始({x}, {y}) -> 调整后({adjustedX}, {adjustedY})");
        Console.WriteLine($"页面尺寸: {pageWidth}x{pageHeight}, 图片尺寸: {imageWidth}x{imageHeight}");

        return (adjustedX, adjustedY);
    }

    /// <summary>
    /// 在PDF页面上绘制图片
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="imagePath">图片路径（本地路径或网址）</param>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="size">图片大小（基于字体大小计算）</param>
    /// <param name="imageType">图片类型：signature=签字, seal=印章, riding_seal=骑缝章</param>
    private async Task DrawImageOnPdf(PdfPageBase page, string imagePath, float x, float y, float size, string imageType = "signature")
    {
        Console.WriteLine($"开始绘制图片: {imagePath}, 坐标: ({x}, {y}), 大小: {size}");

        try
        {
            byte[] imageBytes;

            // 判断是否为网址
            if (imagePath.StartsWith("http://") || imagePath.StartsWith("https://"))
            {
                Console.WriteLine("从网址下载图片...");
                // 从网址下载图片
                using var client = new HttpClient();
                imageBytes = await client.GetByteArrayAsync(imagePath);
                Console.WriteLine($"下载完成，图片大小: {imageBytes.Length} 字节");
            }
            else
            {
                Console.WriteLine("从本地文件读取图片...");
                // 从本地文件读取图片
                if (!SystemFile.Exists(imagePath))
                {
                    Console.WriteLine($"文件不存在: {imagePath}");
                    return; // 文件不存在，跳过
                }
                imageBytes = await SystemFile.ReadAllBytesAsync(imagePath);
                Console.WriteLine($"读取完成，图片大小: {imageBytes.Length} 字节");
            }

            // 创建图片对象
            using var imageStream = new MemoryStream(imageBytes);
            var image = PdfImage.FromStream(imageStream);
            Console.WriteLine("图片对象创建成功");

            // 计算图片尺寸
            float imageWidth, imageHeight;

            switch (imageType.ToLower())
            {
                case "seal": // 驾校盖章
                    {
                        // 印章设置为圆形，按照标准公章尺寸
                        // 标准公章规格：
                        // - 直径: 40mm
                        // - 300 DPI: 472px
                        // - 72 DPI (PDF标准): 472 * 72 / 300 ≈ 113px

                        // 计算标准公章尺寸 (40mm)
                        float mmToPdfPoints = 2.834f; // 1mm = 2.834 PDF points (72 DPI)
                        float sealDiameterMm = 40f; // 标准公章直径40mm
                        float standardSealSize = sealDiameterMm * mmToPdfPoints; // ≈ 113.36 points

                        imageWidth = standardSealSize;
                        imageHeight = standardSealSize;
                        Console.WriteLine($"驾校盖章尺寸: 直径={standardSealSize:F1}pt ({sealDiameterMm}mm标准公章)");
                        break;
                    }
                case "riding_seal": // 骑缝章
                    {
                        // 骑缝章通常比普通印章小一些，椭圆形
                        float mmToPdfPoints = 2.834f;
                        float ridingSealWidth = 25f * mmToPdfPoints; // 宽度25mm
                        float ridingSealHeight = 35f * mmToPdfPoints; // 高度35mm

                        imageWidth = ridingSealWidth;
                        imageHeight = ridingSealHeight;
                        Console.WriteLine($"骑缝章尺寸: 宽度={ridingSealWidth:F1}pt, 高度={ridingSealHeight:F1}pt (25x35mm)");
                        break;
                    }
                default: // signature 签字
                    {
                        // 签字图片保持矩形比例，尺寸调大一倍
                        imageWidth = Math.Max(size * 6, 120); // 最小宽度120 (原来60的两倍)
                        imageHeight = Math.Max(size * 4, 80); // 最小高度80 (原来40的两倍)
                        Console.WriteLine($"签字图片尺寸: 宽度={imageWidth}, 高度={imageHeight} (调大一倍)");
                        break;
                    }
            }

            // 调整图片位置
            var (finalX, finalY) = AdjustImagePosition(x, y, page.Size.Width, page.Size.Height, imageWidth, imageHeight);

            // 根据图片类型选择绘制方式
            switch (imageType.ToLower())
            {
                case "seal": // 驾校盖章 - 圆形
                    DrawCircularSeal(page, image, finalX, finalY, imageWidth);
                    Console.WriteLine($"圆形驾校盖章绘制完成，位置: ({finalX}, {finalY}), 直径: {imageWidth}");
                    break;

                case "riding_seal": // 骑缝章 - 椭圆形
                    DrawEllipticalSeal(page, image, finalX, finalY, imageWidth, imageHeight);
                    Console.WriteLine($"椭圆形骑缝章绘制完成，位置: ({finalX}, {finalY}), 尺寸: {imageWidth}x{imageHeight}");
                    break;

                default: // signature 签字 - 矩形
                    page.Canvas.DrawImage(image, finalX, finalY, imageWidth, imageHeight);
                    Console.WriteLine($"签字图片绘制完成，位置: ({finalX}, {finalY}), 尺寸: {imageWidth}x{imageHeight}");
                    break;
            }

            // 可选：绘制边界框来标记图片位置，便于调试
            // var pen = new PdfPen(PdfBrushes.Red, 1);
            // switch (imageType.ToLower())
            // {
            //     case "seal":
            //         page.Canvas.DrawEllipse(pen, finalX, finalY, imageWidth, imageWidth);
            //         break;
            //     case "riding_seal":
            //         page.Canvas.DrawEllipse(pen, finalX, finalY, imageWidth, imageHeight);
            //         break;
            //     default:
            //         page.Canvas.DrawRectangle(pen, finalX, finalY, imageWidth, imageHeight);
            //         break;
            // }
        }
        catch (Exception ex)
        {
            // 图片加载失败时，可以选择记录日志或绘制占位文本
            Console.WriteLine($"绘制图片失败: {ex.Message}");
            Console.WriteLine($"异常详情: {ex}");
        }
    }

    /// <summary>
    /// 根据字段名称获取对应的值
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <param name="student">学生信息</param>
    /// <param name="jxCompany">驾校公司信息</param>
    /// <returns>字段值</returns>
    private async Task<string> GetFieldValue(string fieldName, JxStudentOutPut student, JxCompanyEntity jxCompany)
    {
        return fieldName switch
        {
            // 报名日期相关
            "报名-年" => student.RegistrationDate.ToString("yyyy"),
            "报名-月" => student.RegistrationDate.ToString("MM"),
            "报名-日" => student.RegistrationDate.ToString("dd"),
            "报名日期" => student.RegistrationDate.ToString("yyyy-MM-dd"),

            // 签署日期相关（使用当前日期或注册时间）
            "签署-年" => DateTime.Now.ToString("yyyy"),
            "签署-月" => DateTime.Now.ToString("MM"),
            "签署-日" => DateTime.Now.ToString("dd"),
            "签署日期" => DateTime.Now.ToString("yyyy-MM-dd"),

            // 基本信息
            "姓名" or "xm" or "name" => student.xm,
            "证件号码" or "sfzmhm" or "idcard" => student.sfzmhm,
            "性别" or "xb" or "gender" => student.xb == XbEnum.Male ? "男" : "女",
            "手机号码" or "yddh" or "phone" => student.yddh,
            "驾校名称" or "jxmc" or "schoolname" => jxCompany?.Name ?? "",
            "登记地址" or "djzsxxdz" or "address" => student.djzsxxdz,
            "联系地址" or "lxzsxxdz" or "contactaddress" => student.lxzsxxdz,

            // 培训信息
            "车型" or "cartype" or "zjcx" => student.CarType,
            "报名学费" or "tuition" => student.Tuition.ToString("F2"),
            "已缴学费" or "tuitionpay" => student.TuitionPay.ToString("F2"),
            "报名班型" => student.JxClassName,
            "报名店面" => student.JxDeptName,
            "训练场地" => student.JxFieldName,
            "合同编号" => student.Id.ToString("N").Substring(0, 8).ToUpper(), // 生成8位合同编号

            // 相关人员
            "介绍人" => student.SaleUserName,
            "科一教练" => student.TeachOneUserName,
            "科二教练" => student.TeachTwoUserName,
            "科三教练" => student.TeachThreeUserName,

            // 兼容原有的英文字段名称
            "gddh" or "tel" => student.gddh,
            "csrq" or "birthdate" => student.csrq.ToString("yyyy-MM-dd"),
            "dzyx" or "email" => student.dzyx,
            "yzbm" or "postcode" => student.yzbm,
            "companyname" => jxCompany?.CompanyName ?? "",
            "companycode" => jxCompany?.CompanyCode ?? "",
            "transportcode" => jxCompany?.TransportCode ?? "",
            "companyaddress" => jxCompany?.Address ?? "",
            "xzjcx" => student.xzjcx,
            "dabh" or "archiveno" => student.dabh,
            "ywzl" => string.IsNullOrEmpty(student.xzjcx) ? "初次申领" : "增驾",
            "tuitionnopay" => student.TuitionNoPay.ToString("F2"),
            "tuitiondiscountmoney" => student.TuitionDiscountMoney.ToString("F2"),
            "shouldpaymoney" => student.ShouldPayMoney.ToString("F2"),
            "registertime" => student.RegisterTime.ToString("yyyy-MM-dd HH:mm:ss"),
            "paycompletetime" => student.PayCompleteTime.ToString("yyyy-MM-dd HH:mm:ss"),
            "sfzyxqs" => student.sfzyxqs.ToString("yyyy-MM-dd"),
            "sfzyxqz" => student.sfzyxqz.ToString("yyyy-MM-dd"),
            "sfzmmc" => student.sfzmmc,
            "remark" => student.Remark,
            "pinyin" => student.PinYin,
            "outid" => student.OutId,
            "jtnum" => student.JtNum,

            // 默认返回空字符串
            _ => ""
        };
    }

    /// <summary>
    /// 获取班型名称
    /// </summary>
    private async Task<string> GetJxClassName(Guid jxClassId)
    {
        if (jxClassId == Guid.Empty) return "";
        var jxClass = await _jxClassService.GetById(jxClassId);
        return jxClass?.Name ?? "";
    }

    /// <summary>
    /// 获取报名点名称
    /// </summary>
    private async Task<string> GetJxDeptName(Guid jxDeptId)
    {
        if (jxDeptId == Guid.Empty) return "";
        var jxDept = await _jxDeptService.GetById(jxDeptId);
        return jxDept?.Name ?? "";
    }

    /// <summary>
    /// 获取训练场名称
    /// </summary>
    private async Task<string> GetJxFieldName(Guid jxFieldId)
    {
        if (jxFieldId == Guid.Empty) return "";
        var jxField = await _jxFieldService.GetById(jxFieldId);
        return jxField?.Name ?? "";
    }

    /// <summary>
    /// 获取介绍人姓名
    /// </summary>
    private async Task<string> GetSaleUserName(Guid saleUserId)
    {
        if (saleUserId == Guid.Empty) return "";
        var user = await Context.Queryable<UserEntity>().Where(m => m.Id == saleUserId).FirstAsync();
        return user?.RealName ?? "";
    }

    /// <summary>
    /// 获取教练姓名
    /// </summary>
    private async Task<string> GetCoachName(Guid studentId, int subject)
    {
        // 这里需要根据实际的教练分配逻辑来实现
        // 暂时返回空字符串，需要根据具体的教练分配表来实现
        return "";
    }

    /// <summary>
    ///     保存合同模板控件
    /// </summary>
    /// <param name="input">合同模板控件信息</param>
    /// <returns>保存结果</returns>
    public async Task<bool> SaveContractTemplateControls(JxContractTemplateControlsInPut input)
    {
        if (input == null)
        {
            throw Oops.Bah("输入参数不能为空");
        }

        if (input.TemplateId == Guid.Empty)
        {
            throw Oops.Bah("模板ID不能为空");
        }

        var controls = await GetContractTemplateControlsList(input.TemplateId);

        foreach (var control in controls)
        {
            if (!input.Controls.Any(m => m.Id == control.Id))
            {
                await Context.Deleteable<JxContractTemplateControlsEntity>().Where(m => m.Id == control.Id).ExecuteCommandAsync();
            }
        }

        foreach (var control in input.Controls)
        {
            var data = await Context.Queryable<JxContractTemplateControlsEntity>().Where(m => m.Id == control.Id).FirstAsync();
            if (data == null)
            {
                data = new JxContractTemplateControlsEntity
                {
                    Id = control.Id,
                    TemplateId = input.TemplateId,
                    TenantId = input.TenantId,
                    Field = control.Field,
                    PageNo = control.PageNo,
                    X = control.X,
                    Y = control.Y,
                    FontSize = control.FontSize
                };

                await Context.Insertable(data).ExecuteCommandAsync();
            }
            else
            {
                data.Field = control.Field;
                data.PageNo = control.PageNo;
                data.X = control.X;
                data.Y = control.Y;
                data.FontSize = control.FontSize;

                await Context.Updateable(data).ExecuteCommandAsync();
            }
        }

        return true;
    }


    /// <inheritdoc />
    public async Task<List<JxContractTemplateControlsEntity>> GetContractTemplateControlsList(Guid templateId)
    {
        return await Context.Queryable<JxContractTemplateControlsEntity>().Where(m => m.TemplateId == templateId && m.TenantId == UserManager.TenantId).ToListAsync();
    }


    /// <inheritdoc />
    public async Task<List<JxContractTemplateControlsEntity>> GetContractTemplateControlsList(Guid templateId, int pageNo)
    {
        return await Context.Queryable<JxContractTemplateControlsEntity>().Where(m => m.TemplateId == templateId && m.PageNo == pageNo && m.TenantId == UserManager.TenantId).ToListAsync();
    }

    /// <summary>
    /// 测试 PDF 写字功能
    /// </summary>
    /// <returns>测试结果</returns>
    public async Task<string> TestPdfTextWriting()
    {
        try
        {
            // 创建一个简单的测试 PDF
            var doc = new PdfDocument();
            var page = doc.Pages.Add();

            using var pdfWriter = new PdfTextWriter();

            if (!pdfWriter.IsFontFileExists())
            {
                return "错误：字体文件不存在";
            }

            // 获取页面尺寸
            var pageWidth = page.Size.Width;
            var pageHeight = page.Size.Height;

            Console.WriteLine($"页面尺寸: {pageWidth} x {pageHeight}");

            // 测试固定坐标绘制文本
            pdfWriter.DrawText(page, "固定坐标测试 - 中文", 100, 100, 12);
            pdfWriter.DrawText(page, "Fixed Position Test - English", 100, 150, 12);
            pdfWriter.DrawText(page, "数字: 123456", 100, 200, 12);

            // 测试各种中文字符
            pdfWriter.DrawText(page, "姓名：张三", 100, 250, 14);
            pdfWriter.DrawText(page, "身份证号：110101199001011234", 100, 280, 12);
            pdfWriter.DrawText(page, "地址：北京市朝阳区某某街道", 100, 310, 12);
            pdfWriter.DrawText(page, "电话：13800138000", 100, 340, 12);
            pdfWriter.DrawText(page, "驾校：某某驾校", 100, 370, 12);

            // 测试比例坐标绘制文本
            // 左上角 (0.1, 0.1)
            var topLeftX = 0.1f * pageWidth;
            var topLeftY = 0.1f * pageHeight;
            pdfWriter.DrawText(page, "左上角比例坐标", topLeftX, topLeftY, 14);

            // 右上角 (0.8, 0.1)
            var topRightX = 0.8f * pageWidth;
            var topRightY = 0.1f * pageHeight;
            pdfWriter.DrawText(page, "右上角比例坐标", topRightX, topRightY, 14);

            // 左下角 (0.1, 0.8)
            var bottomLeftX = 0.1f * pageWidth;
            var bottomLeftY = 0.8f * pageHeight;
            pdfWriter.DrawText(page, "左下角比例坐标", bottomLeftX, bottomLeftY, 14);

            // 右下角 (0.8, 0.8)
            var bottomRightX = 0.8f * pageWidth;
            var bottomRightY = 0.8f * pageHeight;
            pdfWriter.DrawText(page, "右下角比例坐标", bottomRightX, bottomRightY, 14);

            // 中心位置 (0.5, 0.5)
            var centerX = 0.5f * pageWidth;
            var centerY = 0.5f * pageHeight;
            pdfWriter.DrawText(page, "中心位置比例坐标", centerX, centerY, 16);

            // 保存测试文件
            var testPath = Path.Combine(Path.GetTempPath(), $"test_pdf_{DateTime.Now:yyyyMMddHHmmss}.pdf");
            doc.SaveToFile(testPath);
            doc.Close();

            return $"测试成功！测试文件已保存到: {testPath}\n页面尺寸: {pageWidth:F2} x {pageHeight:F2} 点";
        }
        catch (Exception ex)
        {
            return $"测试失败：{ex.Message}\n详细错误：{ex.StackTrace}";
        }
    }

    #endregion 方法
}