using System.Collections;
using Newtonsoft.Json;
using PandaServer.System.Entity.Student.Pay;
using PandaServer.System.Entity.Student.Student;
using PandaServer.System.Services.Config;
using PandaServer.System.Services.Student.Dto;
using PandaServer.System.Services.SystemManage;
using PandaServer.System.Services.SystemSecurity;

namespace PandaServer.System.Services.Student;

/// <summary>
/// 驾校学员服务
/// </summary>
public class JxStudentService : CustomDbRepository<JxStudentEntity>, IJxStudentService, ITransient
{
    private readonly IEasyLogService _easyLogService;
    private readonly IJxMyStudentColumnConfigService _jxMyStudentColumnConfigService;
    private readonly ITenantConfigService _tenantConfigService;

    private readonly IUserRoleService _userRoleService;

    /// <summary>
    /// 驾校学员服务构造函数
    /// </summary>
    /// <param name="easyLogService">日志服务</param>
    /// <param name="tenantConfigService">租户配置服务</param>
    /// <param name="jxMyStudentColumnConfigService">学员列表配置服务</param>
    /// <param name="userRoleService">用户角色服务</param>
    public JxStudentService(IEasyLogService easyLogService, ITenantConfigService tenantConfigService, IJxMyStudentColumnConfigService jxMyStudentColumnConfigService, IUserRoleService userRoleService)
    {
        _easyLogService = easyLogService;
        _tenantConfigService = tenantConfigService;
        _jxMyStudentColumnConfigService = jxMyStudentColumnConfigService;
        _userRoleService = userRoleService;
    }

    #region 分页查询

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<JxStudentOutPut>> Page(JxStudentPageInPut input)
    {
        if (string.IsNullOrEmpty(input.SearchKey))
            input.SearchKey = "";
        else
            input.SearchKey = input.SearchKey.Trim().Replace("*", "_");

        if (!string.IsNullOrEmpty(input.SearchKey))
            input.SearchKey = input.SearchKey.Trim().Replace("*", "_").Replace("'", "");
        if (!string.IsNullOrEmpty(input.xm))
            input.xm = input.xm.Trim().Replace("*", "_").Replace(" ", "");
        if (!string.IsNullOrEmpty(input.sfzmhm))
            input.sfzmhm = input.sfzmhm.Trim().Replace("*", "_");

        var keyWords = input.SearchKey.Split(" ".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
        if (keyWords.Length <= 1)
        {
            keyWords = input.SearchKey.Replace("\t", "\r\n").Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
        }


        var searchDays = 0;
        var noLockDeptAndField = false;

        var jxUser = await Context.Queryable<JxUserEntity>().Where(m => m.Id == UserManager.UserId).SingleAsync();
        if (jxUser != null)
        {
            searchDays = jxUser.SearchDays > 9999 ? 9999 : jxUser.SearchDays;
            noLockDeptAndField = jxUser.NoLockDeptAndField;
        }


        if (!string.IsNullOrEmpty(input.NoPayMin) && !input.NoPayMin.IsDecimal())
        {
            input.NoPayMin = "";
        }
        if (!string.IsNullOrEmpty(input.NoPayMax) && !input.NoPayMax.IsDecimal())
        {
            input.NoPayMax = "";
        }
        if (!string.IsNullOrEmpty(input.TuitionNoPayMin) && !input.TuitionNoPayMin.IsDecimal())
        {
            input.TuitionNoPayMin = "";
        }
        if (!string.IsNullOrEmpty(input.TuitionNoPayMax) && !input.TuitionNoPayMax.IsDecimal())
        {
            input.TuitionNoPayMax = "";
        }

        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();

        if (!Context.DbMaintenance.IsAnyTable(studentTable, false))
        {
            studentTable = "student_student_" + Guid.Empty.ToString().Replace("-", "_").ToLower();
        }
        // 创建索引
        // JxStudentEntity.CreateIndexIfNotExists(DbContext.Db, studentTable);
        var payTable = "student_pay_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        if (!Context.DbMaintenance.IsAnyTable(payTable, false))
        {
            payTable = "student_pay_" + Guid.Empty.ToString().Replace("-", "_").ToLower();
        }
        string columnTable = "student_MyStudentColumnData_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        if (!Context.DbMaintenance.IsAnyTable(columnTable, false))
        {
            columnTable = "student_MyStudentColumnData_" + Guid.Empty.ToString().Replace("-", "_").ToLower();
        }
        var shouldPayTable = "student_ShouldPay_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();

        if (!Context.DbMaintenance.IsAnyTable(shouldPayTable, false))
        {
            shouldPayTable = "student_ShouldPay_" + Guid.Empty.ToString().Replace("-", "_").ToLower();
        }

        var columns = await _jxMyStudentColumnConfigService.GetListByTenantId(UserManager.TenantId); var emptyColumns = new List<JxMyStudentColumnOutPut>();

        string columnWhereSql = "";


        if (input.MyColumnData.Count > 0)
        {
            string columnDataTable = "student_MyStudentColumnData_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();

            columnWhereSql = "";
            foreach (DictionaryEntry entry in input.MyColumnData)
            {
                if (entry.Value != null && !string.IsNullOrEmpty(entry.Value.ParseToString()))
                {
                    columnWhereSql += $" AND (F_ColumnId = '{entry.Key}' AND F_ColumnData LIKE '%{entry.Value}%')";
                }
            }

            if (!string.IsNullOrEmpty(columnWhereSql))
            {
                columnWhereSql = $" m.F_Id IN (SELECT F_StudentId FROM {columnDataTable} WHERE F_DeleteMark = 0 " + columnWhereSql;
                columnWhereSql += ")";
            }
        }

        var order_sfzmhm = "CASE";
        var order_xm = "CASE";

        for (int i = 0; i < keyWords.Length; i++)
        {
            order_sfzmhm += $" WHEN F_sfzmhm = '{keyWords[i]}' THEN {i + 1}";
            if (keyWords[i].Length != 18)
            {
                order_xm += $" WHEN F_xm = '{keyWords[i]}' THEN {i + 1}";
            }
        }

        order_sfzmhm += " ELSE 999 END";
        if (order_xm == "CASE")
        {
            order_xm = "";
        }
        else
        {
            order_xm += " ELSE 999 END";
        }

        string openId = HttpNewUtil.GetHeader("openid");

        // if (!string.IsNullOrEmpty(openId) && input.MyStudentType != "MyAll" && input.MyStudentType != "MySale" && input.MyStudentType != "MyTeach")
        // {
        //     input.MyStudentType = "MyAll";
        // }


        var studentImageTable = "student_Image_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();


        var query = Context.Queryable<JxStudentEntity>().AS(studentTable)

            .LeftJoin<JxUserEntity>((m, n) => m.SaleUserId == n.Id && n.TenantId == UserManager.TenantId)

            .LeftJoin(Context.Queryable<UserCategoryUserEntity>()
                .WhereIF(input.SaleCategoryIds.Count == 0, x => x.Id == Guid.NewGuid())
                .Where(x => x.IsDelete == false && x.TenantId == UserManager.TenantId && input.SaleCategoryIds.Contains(x.CategoryId))
                .GroupBy(x => x.UserId)
                .Select(x => new { x.UserId }),
                (m, n, x) => m.SaleUserId == x.UserId)

            .LeftJoin(Context.Queryable<JxPayEntity>().AS(payTable)
                .WhereIF(input.CostTypeIds.Count == 0, x => x.Id == Guid.NewGuid())
                .Where(x => x.IsDelete == false && input.CostTypeIds.Contains(x.CostTypeId))
                .GroupBy(x => x.StudentId)
                .Select(x => new { x.StudentId }),
                (m, n, x, y) => m.Id == y.StudentId)

            .LeftJoin<JxStudentSourceEntity>((m, n, x, y, z) => m.SourceId == z.Id)

            .LeftJoinIF(input.NoPhotos.Count > 0 || input.HavePhotos.Count > 0,
                Context.Queryable<JxStudentImageEntity>().AS(studentImageTable)
                    .Where(p => p.IsDelete == false)
                    .GroupBy(p => p.StudentId)
                    .Having(
                        (input.NoPhotos.Count > 0
                            ? $"SUM(CASE WHEN F_ImageId IN ({string.Join(",", input.NoPhotos)}) THEN 1 ELSE 0 END) = 0"
                            : "1=1")
                        + (input.HavePhotos.Count > 0
                            ? $" AND COUNT(DISTINCT CASE WHEN F_ImageId IN ({string.Join(",", input.HavePhotos)}) THEN ImageId ELSE NULL END) = {input.HavePhotos.Count}"
                            : "")
                    )
                    .Select(p => new { p.StudentId }),
                (m, n, x, y, z, img) => m.Id == img.StudentId)


            .WhereIF(input.NoPhotos.Count > 0 || input.HavePhotos.Count > 0, (m, n, x, y, z, img) => !SqlFunc.EqualsNull(img.StudentId, null))


            .Where(m => m.IsDelete == false && m.TenantId == UserManager.TenantId)
            .WhereIF(input.SaleCategoryIds.Count > 0, (m, n, x, y) => !SqlFunc.EqualsNull(x.UserId, null))
            .WhereIF(input.CostTypeIds.Count > 0, (m, n, x, y) => !SqlFunc.EqualsNull(y.StudentId, null))

            .WhereIF(!string.IsNullOrEmpty(input.RegisterSchoolName), m => m.RegisterSchoolName.Contains(input.RegisterSchoolName))
            .WhereIF(!string.IsNullOrEmpty(input.Remark), m => m.Remark.Contains(input.Remark))
            .WhereIF(input.Ids.Count > 0, m => input.Ids.Contains(m.Id))
            .WhereIF(input.SaleUserId != Guid.Empty, m => m.SaleUserId == input.SaleUserId)
            .WhereIF(input.SaleUserIds.Count > 0, m => input.SaleUserIds.Contains(m.SaleUserId))
            .WhereIF(input.SaleUserId2s.Count > 0, m => input.SaleUserId2s.Contains(m.SaleUserId2))
            .WhereIF(input.SaleUserId3s.Count > 0, m => input.SaleUserId3s.Contains(m.SaleUserId3))
            .WhereIF(input.TeachOneUserIds.Count > 0, m => input.TeachOneUserIds.Contains(m.TeachOneUserId))
            .WhereIF(input.TeachTwoUserIds.Count > 0, m => input.TeachTwoUserIds.Contains(m.TeachTwoUserId))
            .WhereIF(input.TeachThreeUserIds.Count > 0, m => input.TeachThreeUserIds.Contains(m.TeachThreeUserId))

            .WhereIF(input.TeachUserIds.Count > 0, m => input.TeachUserIds.Contains(m.TeachOneUserId) || input.TeachUserIds.Contains(m.TeachTwoUserId) || input.TeachUserIds.Contains(m.TeachThreeUserId))
            .WhereIF(input.SaleOrTeachUserIds.Count > 0, m => input.SaleOrTeachUserIds.Contains(m.SaleUserId) || input.SaleOrTeachUserIds.Contains(m.SaleUserId2) || input.SaleOrTeachUserIds.Contains(m.SaleUserId3) || input.SaleOrTeachUserIds.Contains(m.TeachOneUserId) || input.SaleOrTeachUserIds.Contains(m.TeachTwoUserId) || input.SaleOrTeachUserIds.Contains(m.TeachThreeUserId))

            .WhereIF(input.MyStudentType == "MyAll", m => (m.SaleUserId == UserManager.UserId || m.SaleUserId2 == UserManager.UserId || m.SaleUserId3 == UserManager.UserId || m.TeachOneUserId == UserManager.UserId || m.TeachTwoUserId == UserManager.UserId || m.TeachThreeUserId == UserManager.UserId) && (m.Status == JxStudentStatusEnum.WaitStudy || m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate))
            .WhereIF(input.MyStudentType == "MySale", m => (m.SaleUserId == UserManager.UserId || m.SaleUserId2 == UserManager.UserId || m.SaleUserId3 == UserManager.UserId) && (m.Status == JxStudentStatusEnum.WaitStudy || m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate))
            .WhereIF(input.MyStudentType == "MyTeach", m => (m.TeachOneUserId == UserManager.UserId || m.TeachTwoUserId == UserManager.UserId || m.TeachThreeUserId == UserManager.UserId) && (m.Status == JxStudentStatusEnum.WaitStudy || m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate))

            .WhereIF(input.SaleJxDeptId != Guid.Empty, m => m.SaleJxDeptId == input.SaleJxDeptId)
            .WhereIF(input.CurrentSaleJxDeptId != Guid.Empty, (m, n) => n.JxDeptId == input.CurrentSaleJxDeptId)
            .WhereIF(input.JxDeptIds.Count > 0, m => input.JxDeptIds.Contains(m.JxDeptId))
            .WhereIF(input.SaleJxDeptIds.Count > 0, (m, n) => input.SaleJxDeptIds.Contains(m.SaleJxDeptId))
            .WhereIF(input.CurrentSaleJxDeptIds.Count > 0, (m, n) => input.CurrentSaleJxDeptIds.Contains(n.JxDeptId))
            .WhereIF(input.SaleJxFieldIds.Count > 0, (m, n) => input.SaleJxFieldIds.Contains(n.JxFieldId))
            .WhereIF(input.JxClassIds.Count > 0, m => input.JxClassIds.Contains(m.JxClassId))
            .WhereIF(input.JxFieldIds.Count > 0, m => input.JxFieldIds.Contains(m.JxFieldId))
            .WhereIF(input.CarTypes.Count > 0, m => input.CarTypes.Contains(m.CarType))
            .WhereIF(input.KeMu1ResultIds.Count > 0, m => input.KeMu1ResultIds.Contains((int)m.KeMu1))
            .WhereIF(input.KeMu1ExamDates.Count > 0, m => m.KeMu1Date >= input.KeMu1ExamDates[0])
            .WhereIF(input.KeMu1ExamDates.Count > 1, m => m.KeMu1Date < input.KeMu1ExamDates[1].AddDays(1))
            .WhereIF(input.KeMu2ResultIds.Count > 0, m => input.KeMu2ResultIds.Contains((int)m.KeMu2))
            .WhereIF(input.KeMu2ExamDates.Count > 0, m => m.KeMu2Date >= input.KeMu2ExamDates[0])
            .WhereIF(input.KeMu2ExamDates.Count > 1, m => m.KeMu2Date < input.KeMu2ExamDates[1].AddDays(1))
            .WhereIF(input.KeMu3ResultIds.Count > 0, m => input.KeMu3ResultIds.Contains((int)m.KeMu3))
            .WhereIF(input.KeMu3ExamDates.Count > 0, m => m.KeMu3Date >= input.KeMu3ExamDates[0])
            .WhereIF(input.KeMu3ExamDates.Count > 1, m => m.KeMu3Date < input.KeMu3ExamDates[1].AddDays(1))
            .WhereIF(input.KeMu4ResultIds.Count > 0, m => input.KeMu4ResultIds.Contains((int)m.KeMu4))
            .WhereIF(input.KeMu4ExamDates.Count > 0, m => m.KeMu4Date >= input.KeMu4ExamDates[0])
            .WhereIF(input.KeMu4ExamDates.Count > 1, m => m.KeMu4Date < input.KeMu4ExamDates[1].AddDays(1))
            .WhereIF(input.SourceIds.Count > 0, m => input.SourceIds.Contains(m.SourceId))



            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchKey) && keyWords.Length <= 1, m => m.xm.Contains(input.SearchKey.Trim()) || m.sfzmhm.Contains(input.SearchKey.Trim())
            || m.yddh.Contains(input.SearchKey.Trim())
            || m.PinYin.ToLower().Contains(input.SearchKey.ToLower().Trim()))

            .WhereIF(input.sfzmmcs.Count > 0, m => input.sfzmmcs.Contains(m.sfzmmc))
            .WhereIF(keyWords.Length > 1, m => keyWords.Contains(m.xm) || keyWords.Contains(m.sfzmhm))
            .WhereIF(input.Statuss.Count > 0, m => input.Statuss.Contains(m.Status))
            .WhereIF(!string.IsNullOrWhiteSpace(input.xm), m => m.xm.Contains(input.xm.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.sfzmhm), m => m.sfzmhm.Contains(input.sfzmhm.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.sfzmhm_Start), m => m.sfzmhm.StartsWith(input.sfzmhm_Start.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.sfzmhm_End), m => m.sfzmhm.EndsWith(input.sfzmhm_End.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.yddh), m => m.yddh.Contains(input.yddh.Trim()))


            .WhereIF(input.CreateTimes.Count > 0, m => m.CreateTime >= input.CreateTimes[0])
            .WhereIF(input.CreateTimes.Count > 1, m => m.CreateTime < input.CreateTimes[1].AddDays(1))


            .WhereIF(input.Ages.Count > 0, m => SqlFunc.DateDiff(DateType.Year, m.csrq, DateTime.Now) >= input.Ages[0] && SqlFunc.DateDiff(DateType.Year, m.csrq, DateTime.Now) <= input.Ages[1])

            .WhereIF(input.NoPays.Count > 0, m => m.NoPay >= input.NoPays[0] && m.NoPay <= input.NoPays[1])
            .WhereIF(input.TuitionNoPays.Count > 0, m => m.TuitionNoPay >= input.TuitionNoPays[0] && m.TuitionNoPay <= input.TuitionNoPays[1])
            .WhereIF(input.TuitionPays.Count > 0, m => m.TuitionPay >= input.TuitionPays[0] && m.TuitionPay <= input.TuitionPays[1])

            .WhereIF(input.KeMu2Times.Count > 0, m => m.KeMu2Times >= input.KeMu2Times[0] && m.KeMu2Times <= input.KeMu2Times[1])
            .WhereIF(input.KeMu3Times.Count > 0, m => m.KeMu3Times >= input.KeMu3Times[0] && m.KeMu3Times <= input.KeMu3Times[1])

            //1:在培
            .WhereIF(input.SearchType == "1", m => m.Status == JxStudentStatusEnum.OnStudy)

            //2:毕业
            .WhereIF(input.SearchType == "2", m => m.Status == JxStudentStatusEnum.Graduate)

            //3:退学
            .WhereIF(input.SearchType == "3", m => m.Status == JxStudentStatusEnum.Quit)

            //-1:未注册
            .WhereIF(input.SearchType == "-1", m => m.Status == JxStudentStatusEnum.OnStudy && m.RegisterTime <= Convert.ToDateTime("2000-01-01"))

            //101:科一
            .WhereIF(input.SearchType == "101", m => m.Status == JxStudentStatusEnum.OnStudy && m.KeMu1 != JxExamResultEnum.Pass && m.RegisterTime > Convert.ToDateTime("2000-01-01"))

            //102:科二
            .WhereIF(input.SearchType == "102", m => m.Status == JxStudentStatusEnum.OnStudy && m.KeMu1 == JxExamResultEnum.Pass && m.KeMu2 != JxExamResultEnum.Pass)

            //103:科三
            .WhereIF(input.SearchType == "103", m => m.Status == JxStudentStatusEnum.OnStudy && m.KeMu2 == JxExamResultEnum.Pass && m.KeMu3 != JxExamResultEnum.Pass)

            //104:科四
            .WhereIF(input.SearchType == "104", m => (m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate) && m.KeMu3 == JxExamResultEnum.Pass && m.KeMu4 != JxExamResultEnum.Pass)

            //203:三个月内过期
            .WhereIF(input.SearchType == "203", m => m.Status == JxStudentStatusEnum.OnStudy && m.KeMu1 == JxExamResultEnum.Pass && m.KeMu4 != JxExamResultEnum.Pass && m.KeMu1Date < DateTime.Now.AddYears(-3).AddMonths(3) && m.KeMu1Date > DateTime.Now.AddYears(-3))

            //206:六个月内过期
            .WhereIF(input.SearchType == "206", m => m.Status == JxStudentStatusEnum.OnStudy && m.KeMu1 == JxExamResultEnum.Pass && m.KeMu4 != JxExamResultEnum.Pass && m.KeMu1Date < DateTime.Now.AddYears(-3).AddMonths(6) && m.KeMu1Date > DateTime.Now.AddYears(-3).AddMonths(-3))

            //200:已经过期
            .WhereIF(input.SearchType == "200", m => m.Status == JxStudentStatusEnum.OnStudy && m.KeMu1 == JxExamResultEnum.Pass && m.KeMu4 != JxExamResultEnum.Pass && m.KeMu1Date < DateTime.Now.AddYears(-3))

            .WhereIF(string.IsNullOrEmpty(openId) && !UserManager.IsTenantAdmin && input.SearchKey.Length >= 4 && keyWords.Length <= 1,
                m => m.sfzmhm.Contains(input.SearchKey) || m.xm == input.SearchKey || m.yddh.Contains(input.SearchKey) || m.PinYin.ToLower().Contains(input.SearchKey.ToLower().Trim()))

            .WhereIF(string.IsNullOrEmpty(openId) && !UserManager.IsTenantAdmin && input.SearchKey.Length >= 2 && input.SearchKey.Length < 4 && keyWords.Length <= 1,
                m => m.xm == input.SearchKey || m.PinYin.ToLower().Contains(input.SearchKey.ToLower().Trim()))

            .WhereIF(string.IsNullOrEmpty(openId) && !UserManager.IsTenantAdmin && input.SearchKey.Length < 2 && keyWords.Length <= 1 && input.MyStudentType != "MyAll" && input.MyStudentType != "MySale" && input.MyStudentType != "MyTeach",
                m => m.CreateTime > DateTime.Now.AddDays(0 - searchDays))

            .WhereIF(string.IsNullOrEmpty(openId) &&
                !UserManager.IsTenantAdmin && input.SearchKey.Length < 2 && keyWords.Length <= 1 && input.MyStudentType != "MyAll" && input.MyStudentType != "MySale" && input.MyStudentType != "MyTeach" && !noLockDeptAndField,
                (m, n) =>
                SqlFunc.Subqueryable<JxUserJxDeptEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxDeptId == m.JxDeptId).Any()
                || SqlFunc.Subqueryable<JxUserJxFieldEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxFieldId == m.JxFieldId).Any()
                || SqlFunc.Subqueryable<JxUserJxDeptEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxDeptId == m.SaleJxDeptId).Any()
                || SqlFunc.Subqueryable<JxUserJxDeptEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxDeptId == n.JxDeptId).Any()
                || SqlFunc.Subqueryable<JxUserJxFieldEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxFieldId == n.JxFieldId).Any()
                || (!string.IsNullOrEmpty(input.MacAddress) && SqlFunc.Subqueryable<JxDeptComputerEntity>().Where(m1 => m1.MacAddress == input.MacAddress && m1.JxDeptId == m.JxDeptId).Any())
                || (m.SaleUserId == UserManager.UserId || m.SaleUserId2 == UserManager.UserId || m.SaleUserId3 == UserManager.UserId))


            .WhereIF(!string.IsNullOrEmpty(openId)
            && !UserManager.IsTenantAdmin && input.MyStudentType != "MyAll" && input.MyStudentType != "MySale" && input.MyStudentType != "MyTeach",
             (m, n) => (
                (
                SqlFunc.Subqueryable<JxUserJxDeptEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxDeptId == m.JxDeptId).Any()
                || SqlFunc.Subqueryable<JxUserJxFieldEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxFieldId == m.JxFieldId).Any()
                || SqlFunc.Subqueryable<JxUserJxDeptEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxDeptId == m.SaleJxDeptId).Any()
                || SqlFunc.Subqueryable<JxUserJxDeptEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxDeptId == n.JxDeptId).Any()
                || SqlFunc.Subqueryable<JxUserJxFieldEntity>().Where(m1 => m1.UserId == UserManager.UserId && m1.JxFieldId == n.JxFieldId).Any()
             ) && m.CreateTime > DateTime.Now.AddDays(0 - searchDays))
                || (m.SaleUserId == UserManager.UserId || m.SaleUserId2 == UserManager.UserId || m.SaleUserId3 == UserManager.UserId || m.TeachOneUserId == UserManager.UserId || m.TeachTwoUserId == UserManager.UserId || m.TeachThreeUserId == UserManager.UserId))

            .WhereIF(input.TimeType == "CreateTime" && input.Times.Count > 0, m => m.CreateTime >= input.Times[0])
            .WhereIF(input.TimeType == "CreateTime" && input.Times.Count > 1, m => m.CreateTime < input.Times[1].AddDays(1))
            .WhereIF(input.TimeType == "RegistrationDate" && input.Times.Count > 0, m => m.RegistrationDate >= input.Times[0])
            .WhereIF(input.TimeType == "RegistrationDate" && input.Times.Count > 1, m => m.RegistrationDate < input.Times[1].AddDays(1))
            .WhereIF(input.TimeType == "PayTime" && input.Times.Count > 0, m => m.PayTime >= input.Times[0])
            .WhereIF(input.TimeType == "PayTime" && input.Times.Count > 1, m => m.PayTime < input.Times[1].AddDays(1))
            .WhereIF(input.TimeType == "RegisterTime" && input.Times.Count > 0, m => m.RegisterTime >= input.Times[0])
            .WhereIF(input.TimeType == "RegisterTime" && input.Times.Count > 1, m => m.RegisterTime < input.Times[1].AddDays(1))
            .WhereIF(input.TimeType == "DropOutTime" && input.Times.Count > 0, m => m.DropOutTime >= input.Times[0])
            .WhereIF(input.TimeType == "DropOutTime" && input.Times.Count > 1, m => m.DropOutTime < input.Times[1].AddDays(1))
            .WhereIF(input.TimeType == "PayCompleteTime" && input.Times.Count > 0, m => m.PayCompleteTime >= input.Times[0])
            .WhereIF(input.TimeType == "PayCompleteTime" && input.Times.Count > 1, m => m.PayCompleteTime < input.Times[1].AddDays(1))
            .WhereIF(input.TimeType == "csrq" && input.Times.Count > 0, m => m.csrq >= input.Times[0])
            .WhereIF(input.TimeType == "csrq" && input.Times.Count > 1, m => m.csrq < input.Times[1].AddDays(1))
            .WhereIF(input.TimeType2 == "CreateTime" && input.Times2.Count > 0, m => m.CreateTime >= input.Times2[0])
            .WhereIF(input.TimeType2 == "CreateTime" && input.Times2.Count > 1, m => m.CreateTime < input.Times2[1].AddDays(1))
            .WhereIF(input.TimeType2 == "RegistrationDate" && input.Times2.Count > 0, m => m.RegistrationDate >= input.Times2[0])
            .WhereIF(input.TimeType2 == "RegistrationDate" && input.Times2.Count > 1, m => m.RegistrationDate < input.Times2[1].AddDays(1))
            .WhereIF(input.TimeType2 == "PayTime" && input.Times2.Count > 0, m => m.PayTime >= input.Times2[0])
            .WhereIF(input.TimeType2 == "PayTime" && input.Times2.Count > 1, m => m.PayTime < input.Times2[1].AddDays(1))
            .WhereIF(input.TimeType2 == "RegisterTime" && input.Times2.Count > 0, m => m.RegisterTime >= input.Times2[0])
            .WhereIF(input.TimeType2 == "RegisterTime" && input.Times2.Count > 1, m => m.RegisterTime < input.Times2[1].AddDays(1))
            .WhereIF(input.TimeType2 == "DropOutTime" && input.Times2.Count > 0, m => m.DropOutTime >= input.Times2[0])
            .WhereIF(input.TimeType2 == "DropOutTime" && input.Times2.Count > 1, m => m.DropOutTime < input.Times2[1].AddDays(1))
            .WhereIF(input.TimeType2 == "PayCompleteTime" && input.Times2.Count > 0, m => m.PayCompleteTime >= input.Times2[0])
            .WhereIF(input.TimeType2 == "PayCompleteTime" && input.Times2.Count > 1, m => m.PayCompleteTime < input.Times2[1].AddDays(1))
            .WhereIF(input.TimeType2 == "csrq" && input.Times2.Count > 0, m => m.csrq >= input.Times2[0])
            .WhereIF(input.TimeType2 == "csrq" && input.Times2.Count > 1, m => m.csrq < input.Times2[1].AddDays(1))


            .WhereIF(input.JxStudentImformationStatusIds.Count > 0,
                m => input.JxStudentImformationStatusIds.Contains(m.JxStudentImformationStatusId))
            .WhereIF(input.DocStatusIds.Count > 0, m => input.DocStatusIds.Contains(m.DocStatusId))
            .WhereIF(input.RegisterId != Guid.Empty, m => SqlFunc.Subqueryable<JxStudentRegisterDetailEntity>()
            .Where(x => m.Id == x.StudentId && x.RegisterId == input.RegisterId && x.IsDelete == false).Any())

            .WhereIF(!string.IsNullOrEmpty(columnWhereSql), columnWhereSql)

            .OrderByIF(keyWords.Length > 1, order_sfzmhm)
            .OrderByIF(keyWords.Length > 1 && !string.IsNullOrEmpty(order_xm), order_xm)

            .OrderByIF(keyWords.Length <= 1 && !string.IsNullOrEmpty(input.SortField), $"{input.SortField} {input.SortOrder}")
            .OrderByIF(keyWords.Length <= 1 && string.IsNullOrEmpty(input.SortField), m => m.SysId, OrderByType.Desc);


        var pageInfo = query
            .Select((m, n, x, y, z) => new JxStudentOutPut
            {
                SysId = m.SysId,
                Id = m.Id,
                xm = m.xm,
                xb = m.xb,
                csrq = m.csrq,
                Age = SqlFunc.DateDiff(DateType.Year, m.csrq, DateTime.Now),
                sfzmmc = m.sfzmmc,
                sfzmhm = m.sfzmhm,
                Ywzt = m.Ywzt,
                yddh = m.yddh,
                CarType = m.CarType,
                JxDeptName = SqlFunc.Subqueryable<JxDeptEntity>().Where(x => x.Id == m.JxDeptId)
                    .Select(x => x.Name),
                SaleJxDeptName = SqlFunc.Subqueryable<JxDeptEntity>().Where(x => x.Id == m.SaleJxDeptId)
                    .Select(x => x.Name),
                CurrentSaleJxDeptName = SqlFunc.Subqueryable<JxDeptEntity>().Where(x => x.Id == n.JxDeptId)
                    .Select(x => x.Name),
                JxFieldName = SqlFunc.Subqueryable<JxFieldEntity>().Where(x => x.Id == m.JxFieldId)
                    .Select(x => x.Name),
                JxClassName = SqlFunc.Subqueryable<JxClassEntity>().Where(x => x.Id == m.JxClassId)
                    .Select(x => x.Name),
                Status = m.Status,
                SaleUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.SaleUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                SaleUserName2 = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.SaleUserId2)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                SaleUserName3 = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.SaleUserId3)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),

                TeachOneUserId = m.TeachOneUserId,
                TeachTwoUserId = m.TeachTwoUserId,
                TeachThreeUserId = m.TeachThreeUserId,

                TenantId = m.TenantId,
                CreateTime = m.CreateTime,
                DropOutTime = m.DropOutTime,
                CreateUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.CreateUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                RegistrationDate = m.RegistrationDate,
                RegisterTime = m.RegisterTime,
                RegisterErrorReason = m.RegisterErrorReason,
                RegisterSchoolName = m.RegisterSchoolName,
                Image2 = m.Image2,
                Image4 = m.Image4,
                Image6 = m.Image6,
                Image7 = m.Image7,
                KeMu1 = m.KeMu1,
                KeMu1Times = m.KeMu1Times,
                KeMu1Date = m.KeMu1Date,
                KeMu2 = m.KeMu2,
                KeMu2Times = m.KeMu2Times,
                KeMu2Date = m.KeMu2Date,
                KeMu3 = m.KeMu3,
                KeMu3Times = m.KeMu3Times,
                KeMu3Date = m.KeMu3Date,
                KeMu4 = m.KeMu4,
                KeMu4Times = m.KeMu4Times,
                KeMu4Date = m.KeMu4Date,
                DocStatus = SqlFunc.Subqueryable<JxStudentDocStatusEntity>().Where(x => x.Id == m.DocStatusId)
                    .Select(x => x.DocStatus),
                TeachOneUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.TeachOneUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                TeachTwoUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.TeachTwoUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                TeachThreeUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.TeachThreeUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),

                TeachTwoCarNumber = SqlFunc.Subqueryable<CarEntity>().Where(x => x.Id == m.TeachTwoCar)
                    .Select(x => x.CarNumber),
                TeachThreeCarNumber = SqlFunc.Subqueryable<CarEntity>().Where(x => x.Id == m.TeachThreeCar)
                    .Select(x => x.CarNumber),

                KeMu1StudyCompleteDate = m.KeMu1StudyCompleteDate,
                KeMu2StudyCompleteDate = m.KeMu2StudyCompleteDate,
                KeMu2StudyTimeLength = m.KeMu2StudyTimeLength,
                KeMu3StudyCompleteDate = m.KeMu3StudyCompleteDate,
                KeMu3StudyTimeLength = m.KeMu3StudyTimeLength,

                PayTime = m.PayTime,
                PayCompleteTime = m.PayCompleteTime,
                Tuition = m.Tuition,
                TuitionNoPay = m.TuitionNoPay,
                TuitionPay = m.TuitionPay,
                TuitionDiscountMoney = m.TuitionDiscountMoney,
                ShouldPayMoney = m.ShouldPayMoney,
                NoPay = m.NoPay,
                PayMoney = m.ShouldPayMoney - m.NoPay,
                JxInCome = m.JxInCome,
                Remark = m.Remark,
                xzjcx = m.xzjcx,
                dabh = m.dabh,
                djzsxxdz = m.djzsxxdz,
                lxzsxxdz = m.lxzsxxdz,
                yjdz = m.yjdz,
                yzbm = m.yzbm,
                PushMoney = m.PushMoney,
                PinYin = m.PinYin.ToUpper(),
                MyStudentColumnDataText = "",
                JxStudentImformationStatusText = SqlFunc.Subqueryable<JxStudentImformationStatusEntity>()
                .Where(x => x.IsDelete == false && x.Id == m.JxStudentImformationStatusId)
                .Select(x => x.Name),
                PayDetails = SqlFunc.Subqueryable<JxPayEntity>().AS(payTable)
                .Where(x => x.StudentId == m.Id && x.IsDelete == false)
                .GroupBy(x => x.CostTypeId)
                .SelectStringJoin(x => $"{(SqlFunc.Subqueryable<CostTypeEntity>().Where(y => y.Id == x.CostTypeId).Select(y => y.Name))}:{SqlFunc.AggregateSum(x.PayMoney).ToString("F2")}", ","),

                NoPayDetails = SqlFunc.Subqueryable<JxShouldPayEntity>().AS(shouldPayTable)
                    .LeftJoin<CostTypeEntity>((x, y) => x.CostTypeId == y.Id)
                    .Where(x => x.StudentId == m.Id && x.IsDelete == false && x.NoPay > 0)
                    .OrderBy(x => x.CreateTime)
                    .SelectStringJoin((x, y) => y.Name + ":" + x.NoPay.ToString("F2"), ";"),

                SourceName = z.Name,
            });

        if (columns.Count > 0)
        {
            pageInfo = query
            .Select((m, n, x, y, z) => new JxStudentOutPut
            {
                SysId = m.SysId,
                Id = m.Id,
                xm = m.xm,
                xb = m.xb,
                csrq = m.csrq,
                Age = SqlFunc.DateDiff(DateType.Year, m.csrq, DateTime.Now),
                sfzmmc = m.sfzmmc,
                sfzmhm = m.sfzmhm,
                Ywzt = m.Ywzt,
                yddh = m.yddh,
                CarType = m.CarType,
                JxDeptName = SqlFunc.Subqueryable<JxDeptEntity>().Where(x => x.Id == m.JxDeptId)
                    .Select(x => x.Name),
                SaleJxDeptName = SqlFunc.Subqueryable<JxDeptEntity>().Where(x => x.Id == m.SaleJxDeptId)
                    .Select(x => x.Name),
                CurrentSaleJxDeptName = SqlFunc.Subqueryable<JxDeptEntity>().Where(x => x.Id == n.JxDeptId)
                    .Select(x => x.Name),
                JxFieldName = SqlFunc.Subqueryable<JxFieldEntity>().Where(x => x.Id == m.JxFieldId)
                    .Select(x => x.Name),
                JxClassName = SqlFunc.Subqueryable<JxClassEntity>().Where(x => x.Id == m.JxClassId)
                    .Select(x => x.Name),
                Status = m.Status,
                SaleUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.SaleUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                SaleUserName2 = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.SaleUserId2)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                SaleUserName3 = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.SaleUserId3)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),

                TeachOneUserId = m.TeachOneUserId,
                TeachTwoUserId = m.TeachTwoUserId,
                TeachThreeUserId = m.TeachThreeUserId,

                TenantId = m.TenantId,
                CreateTime = m.CreateTime,
                DropOutTime = m.DropOutTime,
                CreateUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.CreateUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                RegistrationDate = m.RegistrationDate,
                RegisterTime = m.RegisterTime,
                RegisterErrorReason = m.RegisterErrorReason,
                RegisterSchoolName = m.RegisterSchoolName,
                Image2 = m.Image2,
                Image4 = m.Image4,
                Image6 = m.Image6,
                Image7 = m.Image7,
                KeMu1 = m.KeMu1,
                KeMu1Times = m.KeMu1Times,
                KeMu1Date = m.KeMu1Date,
                KeMu2 = m.KeMu2,
                KeMu2Times = m.KeMu2Times,
                KeMu2Date = m.KeMu2Date,
                KeMu3 = m.KeMu3,
                KeMu3Times = m.KeMu3Times,
                KeMu3Date = m.KeMu3Date,
                KeMu4 = m.KeMu4,
                KeMu4Times = m.KeMu4Times,
                KeMu4Date = m.KeMu4Date,
                DocStatus = SqlFunc.Subqueryable<JxStudentDocStatusEntity>().Where(x => x.Id == m.DocStatusId)
                    .Select(x => x.DocStatus),
                TeachOneUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.TeachOneUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                TeachTwoUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.TeachTwoUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),
                TeachThreeUserName = SqlFunc.Subqueryable<UserEntity>().Where(x => x.Id == m.TeachThreeUserId)
                    .Select(x => x.RealName + (x.IsEnabled ? "" : "[禁用]")),


                TeachTwoCarNumber = SqlFunc.Subqueryable<CarEntity>().Where(x => x.Id == m.TeachTwoCar)
                    .Select(x => x.CarNumber),
                TeachThreeCarNumber = SqlFunc.Subqueryable<CarEntity>().Where(x => x.Id == m.TeachThreeCar)
                    .Select(x => x.CarNumber),

                KeMu1StudyCompleteDate = m.KeMu1StudyCompleteDate,
                KeMu2StudyCompleteDate = m.KeMu2StudyCompleteDate,
                KeMu2StudyTimeLength = m.KeMu2StudyTimeLength,
                KeMu3StudyCompleteDate = m.KeMu3StudyCompleteDate,
                KeMu3StudyTimeLength = m.KeMu3StudyTimeLength,

                PayTime = m.PayTime,
                PayCompleteTime = m.PayCompleteTime,
                Tuition = m.Tuition,
                TuitionNoPay = m.TuitionNoPay,
                TuitionPay = m.TuitionPay,
                TuitionDiscountMoney = m.TuitionDiscountMoney,
                ShouldPayMoney = m.ShouldPayMoney,
                NoPay = m.NoPay,
                PayMoney = m.ShouldPayMoney - m.NoPay,
                JxInCome = m.JxInCome,
                Remark = m.Remark,
                xzjcx = m.xzjcx,
                dabh = m.dabh,
                djzsxxdz = m.djzsxxdz,
                lxzsxxdz = m.lxzsxxdz,
                yjdz = m.yjdz,
                yzbm = m.yzbm,
                PushMoney = m.PushMoney,
                PinYin = m.PinYin.ToUpper(),
                SourceName = z.Name,
                MyStudentColumnDataText = SqlFunc.Subqueryable<JxMyStudentColumnEntity>().AS(columnTable)
                .Where(z => z.StudentId == m.Id && z.IsDelete == false)
                .SelectStringJoin(z => $"{z.ColumnId}:{z.ColumnData}", ","),
                JxStudentImformationStatusText = SqlFunc.Subqueryable<JxStudentImformationStatusEntity>()
                .Where(x => x.IsDelete == false && x.Id == m.JxStudentImformationStatusId)
                .Select(x => x.Name),
                PayDetails = SqlFunc.Subqueryable<JxPayEntity>().AS(payTable)
                .Where(x => x.StudentId == m.Id && x.IsDelete == false)
                .GroupBy(x => x.CostTypeId)
                .SelectStringJoin(x => $"{(SqlFunc.Subqueryable<CostTypeEntity>().Where(y => y.Id == x.CostTypeId).Select(y => y.Name))}:{SqlFunc.AggregateSum(x.PayMoney)}", ","),
                NoPayDetails = SqlFunc.Subqueryable<JxShouldPayEntity>().AS(shouldPayTable)
                    .LeftJoin<CostTypeEntity>((x, y) => x.CostTypeId == y.Id)
                    .Where(x => x.StudentId == m.Id && x.IsDelete == false && x.NoPay > 0)
                    .OrderBy(x => x.CreateTime)
                    .SelectStringJoin((x, y) => y.Name + ":" + x.NoPay.ToString("F2"), ";"),
            });
        }

        var totalNumber = 0;
        var data = pageInfo.ToOffsetPage(input.Current, input.Size == 0 ? 99999 : input.Size, ref totalNumber);


        var columnConfig = await Context.Queryable<JxMyStudentColumnConfigEntity>().Where(x => x.IsDelete == false && x.TenantId == UserManager.TenantId).ToListAsync();

        data = data.ToList().Select<JxStudentOutPut, JxStudentOutPut>((u, i) =>
            {
                u.RowIndex = (input.Current - 1) * input.Size + (i + 1);
                u.StatusText = u.Status.GetDescription();
                u.sfzmhm = u.TenantId == UserManager.TenantId
                ? u.sfzmhm
                : TextHelper.SetSensitiveIdCardNo(u.sfzmhm);
                u.xm = u.TenantId == UserManager.TenantId ? u.xm : TextHelper.SetSensitiveName(u.xm);
                u.xbText = u.xb.GetDescription();
                u.KeMu1Text = ((JxExamResultEnum)u.KeMu1.ParseToInt()).GetDescription();
                u.KeMu2Text = ((JxExamResultEnum)u.KeMu2.ParseToInt()).GetDescription();
                u.KeMu3Text = ((JxExamResultEnum)u.KeMu3.ParseToInt()).GetDescription();
                u.KeMu4Text = ((JxExamResultEnum)u.KeMu4.ParseToInt()).GetDescription();
                u.KeMu1DateText = u.KeMu1Date < Convert.ToDateTime("2000-01-01")
                ? ""
                : u.KeMu1Date.ToString("yyyy-MM-dd");
                u.KeMu2DateText = u.KeMu2Date < Convert.ToDateTime("2000-01-01")
                ? ""
                : u.KeMu2Date.ToString("yyyy-MM-dd");
                u.KeMu3DateText = u.KeMu3Date < Convert.ToDateTime("2000-01-01")
                ? ""
                : u.KeMu3Date.ToString("yyyy-MM-dd");
                u.KeMu4DateText = u.KeMu4Date < Convert.ToDateTime("2000-01-01")
                ? ""
                : u.KeMu4Date.ToString("yyyy-MM-dd");

                u.MyStudentColumnDatas = new List<JxMyStudentColumnOutPut>();

                foreach (var column in columnConfig)
                {
                    if (u.MyStudentColumnDataText == null)
                        u.MyStudentColumnDataText = "";
                    string[] columnDatas = u.MyStudentColumnDataText.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries);

                    // 修复索引溢出异常：确保只处理包含冒号的元素，且确保分割后的数组长度足够
                    var columnData = columnDatas
                        .Where(m => m.Contains(":")) // 确保包含分隔符
                        .Select(m =>
                        {
                            var parts = m.Split(":".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                            // 确保数组有足够的元素再访问索引1
                            return new
                            {
                                Key = parts.Length > 0 ? parts[0].ParseToGuid() : Guid.Empty,
                                Value = parts.Length > 1 ? parts[1] : ""
                            };
                        })
                        .Where(m => m.Key == column.Id.ParseToGuid())
                        .Select(m => m.Value)
                        .FirstOrDefault(); // 修改这里，使用FirstOrDefault代替SingleOrDefault

                    u.MyStudentColumnDatas.Add(new JxMyStudentColumnOutPut
                    {
                        ColumnData = columnData == null ? "" : columnData,
                        ColumnId = column.Id,
                        ColumnName = column.ColumnName
                    });
                }


                return u;
            }).ToList();

        var result =
            data.ToSqlSugarPagedList(input.Current, input.Size, totalNumber);

        return result;

        // return data;
    }

    #endregion 分页查询

    #region 查询

    /// <inheritdoc />
    public async Task<JxStudentEntity?> GetById(Guid id)
    {
        if (id == Guid.Empty)
            throw Oops.Bah("传入 Id 为空");

        return await GetSingleAsync(m => m.Id == id, UserManager.TenantId);
    }

    /// <inheritdoc />
    public async Task<JxStudentEntity?> GetById(Guid id, Guid tenantId)
    {
        if (id == Guid.Empty)
            throw Oops.Bah("传入 Id 为空");

        var studentTable = "student_student_" + tenantId.ToString().Replace("-", "_").ToLower();
        if (!Context.DbMaintenance.IsAnyTable(studentTable, false))
        {
            studentTable = "student_student_" + Guid.Empty.ToString().Replace("-", "_").ToLower();
        }

        return await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).FirstAsync();
    }

    /// <inheritdoc />
    public async Task<JxStudentEntity?> GetByOutId(string outId, Guid tenantId)
    {
        if (string.IsNullOrEmpty(outId))
            throw Oops.Bah("传入 outId 为空");
        return await GetSingleAsync(m => m.OutId == outId, tenantId);
    }

    /// <inheritdoc />
    public async Task<List<JxStudentEntity>> GetByIds(List<Guid> ids, Guid tenantId)
    {
        return await GetListAsync(m => ids.Contains(m.Id));
    }

    /// <inheritdoc />
    public async Task<JxStudentEntity?> GetBySfzmhm(string sfzmhm, Guid tenantId)
    {
        var studentTable = "student_student_" + tenantId.ToString().Replace("-", "_").ToLower();
        return await Context.Queryable<JxStudentEntity>().AS(studentTable)
            .Where(m =>
                m.IsDelete == false && m.sfzmhm == sfzmhm &&
                (m.Status == JxStudentStatusEnum.Graduate || m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.WaitStudy) &&
                m.TenantId == tenantId)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .FirstAsync();
    }

    /// <inheritdoc />
    public async Task<JxStudentEntity?> GetBySfzmhm(string sfzmhm, string carType, Guid tenantId)
    {
        return await GetFirstAsync(m =>
            m.IsDelete == false && m.sfzmhm == sfzmhm &&
            m.CarType.Replace("C2", "C1") == carType.Replace("C2", "C1") &&
            (m.Status == JxStudentStatusEnum.Graduate || m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.WaitStudy) &&
            m.TenantId == tenantId, m => m.CreateTime, OrderByType.Desc);
    }

    /// <inheritdoc />
    public async Task<List<JxStudentOutPut>> GetListByInfo(string sfzmhm, string xm)
    {
        var result = await Context.Queryable<JxStudentEntity>().SplitTable(st => st)
         .LeftJoin<TenantEntity>((m, n) => m.TenantId == n.Id)
         .Where(m => m.IsDelete == false && (m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.WaitStudy || m.Status == JxStudentStatusEnum.Graduate))
         .Where(m => m.xm == xm && m.sfzmhm == sfzmhm)
         .Where((m, n) => n.IsDelete == false && n.ServiceEndTime > DateTime.Now)
         .OrderByDescending(m => m.CreateTime)
         .Select((m, n) => new JxStudentOutPut
         {
             Id = m.Id,
             xm = m.xm,
             TenantId = m.TenantId,
             sfzmmc = m.sfzmmc,
             sfzmhm = m.sfzmhm,
             CarType = m.CarType,
             CreateTime = m.CreateTime,
             RegistrationDate = m.RegistrationDate,
             TenantName = n.TenantName
         })
         .ToListAsync();
        return result;
    }

    /// <inheritdoc />
    public async Task<List<JxStudentEntity>> GetListByInfo(string sfzmhm, string xm, string carType, Guid tenantId)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();

        if (xm.Contains("*") || sfzmhm.Contains("*"))
        {
            xm = xm.Replace("*", "_");
            sfzmhm = sfzmhm.Replace("*", "_");

            var result = await Context.Queryable<JxStudentEntity>().AS(studentTable)
                .Where(m => m.IsDelete == false && m.xm.Contains(xm)
                                                && (m.sfzmmc == "A" || m.sfzmmc == "Q"
                                                    ? m.sfzmhm.Contains(sfzmhm)
                                                    : (m.sfzmmc + m.sfzmhm).Contains(sfzmhm))
                                                && m.CarType.Replace("C2", "C1").Replace("E", "D") == carType.Replace("C2", "C1").Replace("E", "D") &&
                                                (m.Status == JxStudentStatusEnum.OnStudy ||
                                                 m.Status == JxStudentStatusEnum.WaitStudy ||
                                                 m.Status == JxStudentStatusEnum.Graduate) && m.TenantId == tenantId)
                .OrderBy(m => m.CreateTime)
                .ToListAsync();

            return result;
        }
        else
        {
            var result = await Context.Queryable<JxStudentEntity>().AS(studentTable)
                .Where(m => m.IsDelete == false && m.xm == xm
                                                && m.sfzmhm == sfzmhm
                                                && m.CarType.Replace("C2", "C1").Replace("E", "D") == carType.Replace("C2", "C1").Replace("E", "D") &&
                                                (m.Status == JxStudentStatusEnum.OnStudy ||
                                                 m.Status == JxStudentStatusEnum.WaitStudy ||
                                                 m.Status == JxStudentStatusEnum.Graduate) && m.TenantId == tenantId)
                .OrderBy(m => m.CreateTime)
                .ToListAsync();

            return result;
        }
    }

    /// <inheritdoc />
    public async Task<JxStudentEntity?> GetByOpenId(string openId)
    {
        return await Context.Queryable<JxStudentEntity>().SplitTable(st => st)
            .LeftJoin<WxUserEntity>((m, n) => m.Id == n.UserId)
            .Where((m, n) => n.OpenId == openId)
            .Select((m, n) => m)
            .SingleAsync();
    }

    /// <inheritdoc />
    public async Task<bool> ExistsById(Guid id)
    {
        return await IsAnyAsync(m => m.Id == id && m.IsDelete == false);
    }

    /// <inheritdoc />
    public async Task<bool> ExistBySfzmhm(string sfzmhm, string carType, Guid tenantId)
    {
        return await IsAnyAsync(m =>
            m.IsDelete == false && m.sfzmhm == sfzmhm &&
            m.CarType.Replace("C2", "C1") == carType.Replace("C2", "C1") &&
            (m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.WaitStudy) &&
            m.TenantId == tenantId, tenantId);
    }

    /// <inheritdoc />
    public async Task<bool> ExistBySfzmhm(string sfzmhm, List<Guid> tenantIds)
    {
        foreach (var tenantId in tenantIds)
        {
            if (tenantId != Guid.Empty)
            {
                var studentTable = "student_student_" + tenantId.ToString().Replace("-", "_").ToLower();

                if (await Context.Queryable<JxStudentEntity>()
                .AS(studentTable)
                .Where(m => m.sfzmhm == sfzmhm && m.IsDelete == false && (m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate)).CountAsync() > 0)
                {
                    return true;
                }
            }
        }
        return false;
    }


    /// <inheritdoc />
    public async Task<bool> ExistByOutId(string outId, Guid tenantId)
    {
        return await IsAnyAsync(m =>
            m.OutId == outId && m.IsDelete == false && m.TenantId == tenantId, tenantId);
    }

    /// <inheritdoc />
    public async Task<dynamic> GetSalePrice(Guid id, Guid tenantId)
    {
        var studentTable = "student_student_" + tenantId.ToString().Replace("-", "_").ToLower();

        return await Context.Queryable<JxStudentEntity>().AS(studentTable)
            .LeftJoin<JxStudentSaleEntity>((m, n) => m.SaleId == n.Id)
            .Where(m => m.Id == id)
            .Select((m, n) => SqlFunc.EqualsNull(n.Id, null) ? 0 : n.DiscountMoney)
            .SingleAsync();
    }

    /// <inheritdoc />
    public async Task<List<JxStudentOutPut>> GetMiniList(string sfzmhm, string xm,
        JxStudentStatusEnum[] statusEnums, Guid tenantId, string carType)
    {
        if (!string.IsNullOrEmpty(sfzmhm) && sfzmhm.Length > 0 && sfzmhm.Length != 18)
            sfzmhm = sfzmhm.Substring(1);

        if (tenantId == Guid.Empty && string.IsNullOrEmpty(sfzmhm))
            throw Oops.Bah("数据范围过大，请传入公司编号或者证件号码");


        if (tenantId == Guid.Empty)
            return await Context.Queryable<JxStudentEntity>().SplitTable(st => st)
                .Where(m => m.IsDelete == false)
                .WhereIF(!string.IsNullOrEmpty(carType), m => m.CarType.Substring(0, 1) == carType.Substring(0, 1))
                .WhereIF(!string.IsNullOrEmpty(sfzmhm), m => m.sfzmhm.Contains(sfzmhm.Replace("*", "_")))
                .WhereIF(!string.IsNullOrEmpty(xm) && xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
                .WhereIF(!string.IsNullOrEmpty(xm) && !xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
                .WhereIF(statusEnums.Length > 0, m => statusEnums.Contains(m.Status))
                .WhereIF(statusEnums.Length == 0,
                    m => m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate)
                .OrderBy(m => m.CreateTime, OrderByType.Desc)
                .Select(m => new JxStudentOutPut
                {
                    Id = m.Id,
                    xm = m.xm,
                    TenantId = m.TenantId,
                    sfzmmc = m.sfzmmc,
                    sfzmhm = m.sfzmhm,
                    CarType = m.CarType,
                    CreateTime = m.CreateTime,
                    RegistrationDate = m.RegistrationDate
                })
                .ToListAsync();
        return await Context.Queryable<JxStudentEntity>().SplitTable(st =>
                st.ContainsTableNamesIfNullDefaultFirst(tenantId.ToString().Replace("-", "_").ToLower()))
            .Where(m => m.IsDelete == false)
            .WhereIF(!string.IsNullOrEmpty(carType), m => m.CarType.Substring(0, 1) == carType.Substring(0, 1))
            .WhereIF(!string.IsNullOrEmpty(sfzmhm), m => m.sfzmhm.Contains(sfzmhm.Replace("*", "_")))
            .WhereIF(!string.IsNullOrEmpty(xm) && xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
            .WhereIF(!string.IsNullOrEmpty(xm) && !xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
            .WhereIF(tenantId != Guid.Empty, m => m.TenantId == tenantId)
            .WhereIF(statusEnums.Length > 0, m => statusEnums.Contains(m.Status))
            .WhereIF(statusEnums.Length == 0,
                m => m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select(m => new JxStudentOutPut
            {
                Id = m.Id,
                xm = m.xm,
                TenantId = m.TenantId,
                sfzmmc = m.sfzmmc,
                sfzmhm = m.sfzmhm,
                CarType = m.CarType,
                CreateTime = m.CreateTime,
                RegistrationDate = m.RegistrationDate
            })
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<List<JxStudentOutPut>> GetMiniList(string sfzmhm, string xm, JxStudentStatusEnum[] statusEnums,
        string jxmc, string carType)
    {
        if (!string.IsNullOrEmpty(sfzmhm) && sfzmhm.Length > 0 && sfzmhm.Length != 18)
            sfzmhm = sfzmhm.Substring(1);

        if (string.IsNullOrEmpty(sfzmhm))
            throw Oops.Bah("数据范围过大，请传入公司编号或者证件号码");


        if (jxmc.Contains("----"))
            return await Context.Queryable<JxStudentEntity>().SplitTable(st => st)
                .Where(m => m.IsDelete == false)
                .WhereIF(!string.IsNullOrEmpty(carType), m => m.CarType.Substring(0, 1) == carType.Substring(0, 1))
                .WhereIF(!string.IsNullOrEmpty(sfzmhm), m => m.sfzmhm.Contains(sfzmhm.Replace("*", "_")))
                .WhereIF(!string.IsNullOrEmpty(xm) && xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
                .WhereIF(!string.IsNullOrEmpty(xm) && !xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
                .WhereIF(statusEnums.Length > 0, m => statusEnums.Contains(m.Status))
                .WhereIF(statusEnums.Length == 0,
                    m => m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate)
                .OrderBy(m => m.CreateTime, OrderByType.Desc)
                .Select(m => new JxStudentOutPut
                {
                    Id = m.Id,
                    xm = m.xm,
                    TenantId = m.TenantId,
                    sfzmmc = m.sfzmmc,
                    sfzmhm = m.sfzmhm,
                    CarType = m.CarType,
                    CreateTime = m.CreateTime,
                    RegistrationDate = m.RegistrationDate
                })
                .ToListAsync();

        var configs = await _tenantConfigService.GetListByKey("Config_JX_UPDATE_BY_JXMCS");

        var list = configs.Where(m => m.ConfigValue.Split(";".ToCharArray()).Contains(jxmc)).Select(m =>
                "student_student_" + m.TenantId.ToString().ToLower().Replace("-", "_")).ToList();

        if (list.Count > 0)
            return await Context.Queryable<JxStudentEntity>()
                .SplitTable(st => st.Where(m => list.Contains(m.TableName)))
                .Where(m => m.IsDelete == false)
                .WhereIF(!string.IsNullOrEmpty(carType), m => m.CarType.Substring(0, 1) == carType.Substring(0, 1))
                .WhereIF(!string.IsNullOrEmpty(sfzmhm), m => m.sfzmhm.Contains(sfzmhm.Replace("*", "_")))
                .WhereIF(!string.IsNullOrEmpty(xm) && xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
                .WhereIF(!string.IsNullOrEmpty(xm) && !xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
                .WhereIF(statusEnums.Length > 0, m => statusEnums.Contains(m.Status))
                .WhereIF(statusEnums.Length == 0,
                    m => m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate)
                .OrderBy(m => m.CreateTime, OrderByType.Desc)
                .Select(m => new JxStudentOutPut
                {
                    Id = m.Id,
                    xm = m.xm,
                    TenantId = m.TenantId,
                    sfzmmc = m.sfzmmc,
                    sfzmhm = m.sfzmhm,
                    CarType = m.CarType,
                    CreateTime = m.CreateTime,
                    RegistrationDate = m.RegistrationDate
                })
                .ToListAsync();
        return await Context.Queryable<JxStudentEntity>().SplitTable(st => st.Take(1))
            .Where(m => m.IsDelete == false)
            .WhereIF(!string.IsNullOrEmpty(carType), m => m.CarType.Substring(0, 1) == carType.Substring(0, 1))
            .WhereIF(!string.IsNullOrEmpty(sfzmhm), m => m.sfzmhm.Contains(sfzmhm.Replace("*", "_")))
            .WhereIF(!string.IsNullOrEmpty(xm) && xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
            .WhereIF(!string.IsNullOrEmpty(xm) && !xm.Contains("*"), m => m.xm.Contains(xm.Replace("*", "_")))
            .WhereIF(statusEnums.Length > 0, m => statusEnums.Contains(m.Status))
            .WhereIF(statusEnums.Length == 0,
                m => m.Status == JxStudentStatusEnum.OnStudy || m.Status == JxStudentStatusEnum.Graduate)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select(m => new JxStudentOutPut
            {
                Id = m.Id,
                xm = m.xm,
                TenantId = m.TenantId,
                sfzmmc = m.sfzmmc,
                sfzmhm = m.sfzmhm,
                CarType = m.CarType,
                CreateTime = m.CreateTime,
                RegistrationDate = m.RegistrationDate
            })
            .ToListAsync();
    }



    #endregion 查询

    #region 更新缓存

    /// <inheritdoc />
    public async Task<bool> UpdateMoney(Guid studentId, Guid tenantId)
    {
        if (studentId == Guid.Empty)
        {
            return false;
        }
        var studentTable = "student_student_" + tenantId.ToString().Replace("-", "_").ToLower();
        var shouldPayTable = "student_ShouldPay_" + tenantId.ToString().Replace("-", "_").ToLower();
        var payTable = "student_pay_" + tenantId.ToString().Replace("-", "_").ToLower();
        var pushMoneyTable = "student_pushMoney_" + tenantId.ToString().Replace("-", "_").ToLower();


        var strSQL = @" WITH CTE AS(
            SELECT
                *,
                ROW_NUMBER() OVER(PARTITION BY F_StudentId ORDER BY F_CreatorTime) AS rn
            FROM " + shouldPayTable + @" WHERE F_StudentId = '" + studentId + @"' AND F_DeleteMark = 0
            )

            UPDATE CTE SET F_Tuition = CASE WHEN rn = 1 THEN 1 ELSE 0 END ;";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = @"UPDATE T SET F_NoPay = F_PayMoney -
			ISNULL((SELECT SUM(F_PayMoney) FROM " + payTable +
                 @" WHERE F_ShouldPayId = T.F_Id AND F_DeleteMark = 0),0)
            FROM " + shouldPayTable + @" AS T  WHERE F_StudentId = '" + studentId + "';";

        // Console.WriteLine("strSQL:" + strSQL);
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = @"UPDATE T SET F_NoPay = 0
            FROM " + shouldPayTable + @" AS T WHERE F_NoPay < 0 AND F_StudentId = '" + studentId + "';";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = @"UPDATE " + studentTable + @"
                SET
                    F_NoPay = ISNULL((SELECT SUM(F_NoPay)
                    FROM " + shouldPayTable + @"
                    WHERE F_DeleteMark = 0 AND F_StudentId = '" + studentId + @"'), 0)
                FROM " + studentTable + @" WHERE F_Id = '" + studentId + @"'; ";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = @"UPDATE T SET F_JxInCome = F_PayMoney FROM " + payTable + @" AS T WHERE T.F_StudentId = '" + studentId + $@"';

            UPDATE T SET F_JxInCome = 0 FROM " + payTable + @" AS T WHERE F_PayTypeId IN (SELECT F_Id FROM pay_type WHERE F_Name = '优惠') AND T.F_StudentId = '" + studentId + $@"';

            IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{pushMoneyTable}')
                BEGIN
                    UPDATE T SET F_JxInCome = F_PayMoney - ISNULL((SELECT SUM(F_PushMoney) FROM {pushMoneyTable} WHERE F_PayId = T.F_Id AND F_DeleteMark = 0), 0)
                    FROM {payTable} AS T WHERE T.F_StudentId = '" + studentId + $@"';

                    UPDATE T SET F_PushMoney = ISNULL((SELECT SUM(F_PushMoney) FROM {pushMoneyTable} WHERE F_PayId = T.F_Id AND F_DeleteMark = 0), 0)
                    FROM {payTable} AS T WHERE T.F_StudentId = '" + studentId + $@"';

                    UPDATE T SET F_PushMoney = ISNULL((SELECT SUM(F_PushMoney) FROM {pushMoneyTable} WHERE F_StudentId = T.F_Id AND F_DeleteMark = 0), 0)
                    FROM {studentTable} AS T WHERE T.F_Id = '" + studentId + $@"';
                END ";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = @"UPDATE " + studentTable + @"
                SET
                    F_Tuition = ISNULL((SELECT SUM(F_PayMoney)
                    FROM " + shouldPayTable + @"
                    WHERE F_Tuition = 1 AND F_DeleteMark = 0 AND F_PayMoney > 0 AND F_StudentId = '" + studentId +
                 @"'), 0)
                WHERE F_Id = '" + studentId + @"'; ";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = @"UPDATE " + studentTable + @"
                SET
                    F_TuitionPay = ISNULL((SELECT SUM(F_PayMoney) - SUM(F_NoPay)
                    FROM " + shouldPayTable + @"
                    WHERE F_Tuition = 1 AND F_DeleteMark = 0 AND F_PayMoney > 0
                    AND F_StudentId = '" + studentId + @"'), 0)
                WHERE F_Id = '" + studentId + @"'; ";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = @"UPDATE " + studentTable + @"
                SET
                    F_TuitionNoPay = ISNULL((SELECT SUM(F_NoPay)
                    FROM " + shouldPayTable + @"
                    WHERE F_Tuition = 1 AND F_DeleteMark = 0 AND F_NoPay > 0
                    AND F_StudentId = '" + studentId + @"'), 0)
                WHERE F_Id = '" + studentId + @"'; ";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = @"UPDATE " + studentTable + @"
                SET
                    F_PayTime = ISNULL((SELECT MIN(p.F_PayTime) AS MinPayTime
                    FROM " + shouldPayTable + @" AS s
                    LEFT JOIN " + payTable + @" AS p ON p.F_ShouldPayId = s.F_Id
                    WHERE s.F_Tuition = 1 AND s.F_DeleteMark = 0 AND p.F_DeleteMark = 0
                    AND s.F_StudentId = '" + studentId + @"'), '1900-01-01')
                FROM " + studentTable + @" AS T
                WHERE F_Id = '" + studentId + @"'; ";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE {studentTable} SET F_Ywzt = CONVERT(VARCHAR(10), F_RegistrationDate, 20) + ' 报名成功' WHERE F_Status IN(1, 2) AND F_Ywzt LIKE '%报名' AND F_Id = '{studentId}'";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);
        return true;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateSystemData(Guid tenantId)
    {
        var tenantTableSuffix = tenantId.ToString().Replace("-", "_").ToLower();


        string strSQL = $@"UPDATE student_student_{tenantTableSuffix}
SET F_PinYin = dbo.GetPinyinFirstLetter(F_XM)
WHERE F_PinYin = '' ";

        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = $@"
            UPDATE ss SET ss.F_Status = 1
            FROM student_student_{tenantTableSuffix} ss
            INNER JOIN student_ShouldPay_{tenantTableSuffix} sp ON ss.F_Id = sp.F_StudentId
            LEFT JOIN student_pay_{tenantTableSuffix} p ON sp.F_Id = p.F_ShouldPayId
            WHERE ss.F_Status = 0 AND ss.F_TuitionNoPay = 0 AND sp.F_Tuition = 1 AND (p.F_ShouldPayId IS NOT NULL) AND p.F_PayMoney > 500;";


        strSQL += $@"
            ; WITH CTE AS(SELECT *, ROW_NUMBER() OVER(PARTITION BY F_StudentId ORDER BY F_CreatorTime) AS rn FROM student_ShouldPay_{tenantTableSuffix}
            WHERE F_DeleteMark = 0)
                UPDATE CTE SET F_Tuition = CASE WHEN rn = 1 THEN 1 ELSE 0 END;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"
        UPDATE T SET F_NoPay = F_PayMoney - ISNULL((SELECT SUM(F_PayMoney) FROM student_pay_{tenantTableSuffix}
            WHERE F_ShouldPayId = T.F_Id AND F_DeleteMark = 0), 0)
                FROM student_ShouldPay_{tenantTableSuffix}
        AS T;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"
        UPDATE T SET F_DiscountMoney = ISNULL((SELECT SUM(F_PayMoney) FROM student_pay_{tenantTableSuffix}
            WHERE F_ShouldPayId = T.F_Id AND F_DeleteMark = 0 AND F_PayTypeId IN(SELECT F_Id FROM pay_type WHERE F_Name = '优惠')), 0)
                FROM student_ShouldPay_{tenantTableSuffix}
        AS T;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"
        UPDATE T SET F_JxInCome = F_PayMoney FROM student_pay_{tenantTableSuffix} AS T;

        UPDATE T SET F_JxInCome = 0 FROM student_pay_{tenantTableSuffix} AS T WHERE F_PayTypeId IN (SELECT F_Id FROM pay_type WHERE F_Name = '优惠');

        IF EXISTS(SELECT* FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'student_pushMoney_{tenantTableSuffix}')
            BEGIN
                UPDATE T SET F_JxInCome = F_PayMoney - ISNULL((SELECT SUM(F_PushMoney) FROM student_pushMoney_{tenantTableSuffix}
                WHERE F_PayId = T.F_Id AND F_DeleteMark = 0), 0)
                FROM student_pay_{tenantTableSuffix} AS T;

                UPDATE T SET F_PushMoney = ISNULL((SELECT SUM(F_PushMoney) FROM student_pushMoney_{tenantTableSuffix}
                WHERE F_PayId = T.F_Id AND F_DeleteMark = 0), 0)
                FROM student_pay_{tenantTableSuffix} AS T;

                UPDATE T SET F_PushMoney = ISNULL((SELECT SUM(F_PushMoney) FROM student_pushMoney_{tenantTableSuffix}
                WHERE F_StudentId = T.F_Id AND F_DeleteMark = 0), 0)
                FROM student_student_{tenantTableSuffix} AS T;

            END";

        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE T SET F_NoPay = 0 FROM student_ShouldPay_{tenantTableSuffix}
        AS T WHERE F_NoPay< 0;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE T SET F_NoPay = ISNULL(p.F_NoPay, 0) FROM student_student_{tenantTableSuffix}
        AS T
                LEFT JOIN(SELECT F_StudentId, SUM(F_NoPay) AS F_NoPay FROM student_ShouldPay_{tenantTableSuffix}
        WHERE F_DeleteMark = 0 GROUP BY F_StudentId) AS p ON T.F_Id = p.F_StudentId;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE T SET F_JxInCome = ISNULL(p.F_JxInCome, 0) FROM student_student_{tenantTableSuffix}
        AS T
                LEFT JOIN(SELECT F_StudentId, SUM(F_JxInCome) AS F_JxInCome FROM student_pay_{tenantTableSuffix}
        WHERE F_DeleteMark = 0 GROUP BY F_StudentId) AS p ON T.F_Id = p.F_StudentId;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE T SET F_Tuition = ISNULL(p.F_PayMoney, 0), F_TuitionDiscountMoney = ISNULL(p.F_DiscountMoney, 0) FROM student_student_{tenantTableSuffix}
        AS T
                LEFT JOIN(SELECT F_StudentId, SUM(F_PayMoney) AS F_PayMoney, SUM(F_DiscountMoney) AS F_DiscountMoney FROM student_ShouldPay_{tenantTableSuffix}
        WHERE F_Tuition = 1 AND F_DeleteMark = 0 AND F_PayMoney > 0 GROUP BY F_StudentId) AS p ON T.F_Id = p.F_StudentId;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE T SET F_TuitionPay = ISNULL(p.F_PayMoney - ISNULL(p.F_NoPay, 0) - ISNULL(p.F_DiscountMoney, 0), 0) FROM student_student_{tenantTableSuffix}
        AS T
                LEFT JOIN(SELECT F_StudentId, SUM(F_PayMoney) AS F_PayMoney, SUM(F_NoPay) AS F_NoPay, SUM(F_DiscountMoney) AS F_DiscountMoney FROM student_ShouldPay_{tenantTableSuffix}
        WHERE F_Tuition = 1 AND F_DeleteMark = 0 AND F_PayMoney > 0 GROUP BY F_StudentId) AS p ON T.F_Id = p.F_StudentId;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE T SET F_TuitionNoPay = ISNULL(p.F_NoPay, 0) FROM student_student_{tenantTableSuffix}
        AS T
                LEFT JOIN(SELECT F_StudentId, SUM(F_NoPay) AS F_NoPay FROM student_ShouldPay_{tenantTableSuffix}
        WHERE F_Tuition = 1 AND F_DeleteMark = 0 AND F_NoPay > 0 GROUP BY F_StudentId) AS p ON T.F_Id = p.F_StudentId;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE T SET F_ShouldPayMoney = ISNULL(p.F_PayMoney, 0) FROM student_student_{tenantTableSuffix}
        AS T
                LEFT JOIN(SELECT F_StudentId, SUM(F_PayMoney) AS F_PayMoney FROM student_ShouldPay_{tenantTableSuffix}
        WHERE F_DeleteMark = 0 AND F_PayMoney > 0 GROUP BY F_StudentId) AS p ON T.F_Id = p.F_StudentId;";

        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE T SET F_NoPay = ISNULL(p.F_NoPay, 0) FROM student_student_{tenantTableSuffix}
        AS T
                LEFT JOIN(SELECT F_StudentId, SUM(F_NoPay) AS F_NoPay FROM student_ShouldPay_{tenantTableSuffix}
        WHERE F_DeleteMark = 0 AND F_NoPay > 0 GROUP BY F_StudentId) AS p ON T.F_Id = p.F_StudentId;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);




        strSQL = $@"UPDATE T SET F_PayTime = ISNULL(p.MinPayTime, '1900-01-01') FROM student_student_{tenantTableSuffix}
        AS T
                LEFT JOIN(SELECT s.F_StudentId, MIN(p.F_PayTime) AS MinPayTime FROM student_shouldPay_{tenantTableSuffix}
        AS s
                LEFT JOIN student_pay_{tenantTableSuffix}
        AS p ON p.F_ShouldPayId = s.F_Id WHERE s.F_Tuition = 1 AND s.F_DeleteMark = 0 AND p.F_DeleteMark = 0 GROUP BY s.F_StudentId) AS p ON T.F_Id = p.F_StudentId;";


        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"WITH CTE AS(SELECT s.F_StudentId AS StudentId, p.F_PayTime, ROW_NUMBER() OVER(PARTITION BY s.F_Id ORDER BY p.F_PayTime DESC) AS rn FROM student_shouldPay_{tenantTableSuffix}
        s
        LEFT JOIN student_pay_{tenantTableSuffix}
        p ON p.F_ShouldPayId = s.F_Id WHERE s.F_NoPay = 0 AND s.F_Tuition = 1 AND s.F_DeleteMark = 0 AND p.F_DeleteMark = 0)
                UPDATE s SET s.F_PayCompleteTime = ISNULL(cte.F_PayTime, '1900-01-01') FROM student_student_{tenantTableSuffix}
        s
        LEFT JOIN CTE cte ON s.F_Id = cte.StudentId AND cte.rn = 1; ";

        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        strSQL = $@"UPDATE[student_student_{tenantTableSuffix}] SET F_Ywzt = CONVERT(VARCHAR(10), F_RegistrationDate, 20) + ' 报名成功' WHERE F_Status IN(1, 2) ";

        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = $"UPDATE[student_student_{tenantTableSuffix}] SET F_Ywzt = CONVERT(VARCHAR(10), F_RegisterTime, 20) + ' 注册完成' WHERE F_Status IN(1, 2) AND F_RegisterTime > '2000-01-01' ";

        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = $@"
-- 更新最新记录的 F_KeMu1 和 F_KeMu1Date 字段
WITH LatestExam AS (
    SELECT *,
           ROW_NUMBER() OVER (PARTITION BY F_StudentId ORDER BY F_ksrq DESC) AS rn
    FROM student_exam_{tenantTableSuffix} WHERE F_ResultId <> 6 AND F_KeMuId = 1
)
UPDATE ss
SET ss.F_KeMu1 = se.F_ResultId,
    ss.F_KeMu1Date = se.F_ksrq,
    ss.F_KeMu1Times = se.F_Times
FROM LatestExam se
JOIN student_student_{tenantTableSuffix} ss ON se.F_StudentId = ss.F_Id
WHERE se.rn = 1;";
        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = $@"
--更新最新记录的 F_KeMu2 和 F_KeMu2Date 字段
WITH LatestExam AS(
    SELECT *,
           ROW_NUMBER() OVER(PARTITION BY F_StudentId ORDER BY F_ksrq DESC) AS rn
    FROM student_exam_{tenantTableSuffix}
        WHERE F_ResultId<> 6 AND F_KeMuId = 2
)
UPDATE ss
SET ss.F_KeMu2 = se.F_ResultId,
    ss.F_KeMu2Date = se.F_ksrq,
    ss.F_KeMu2Times = se.F_Times
FROM LatestExam se
JOIN student_student_{tenantTableSuffix}
        ss ON se.F_StudentId = ss.F_Id
WHERE se.rn = 1;";
        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = $@"
--更新最新记录的 F_KeMu3 和 F_KeMu3Date 字段
WITH LatestExam AS(
    SELECT *,
           ROW_NUMBER() OVER(PARTITION BY F_StudentId ORDER BY F_ksrq DESC) AS rn
    FROM student_exam_{tenantTableSuffix}
        WHERE F_ResultId<> 6 AND F_KeMuId = 3
)
UPDATE ss
SET ss.F_KeMu3 = se.F_ResultId,
    ss.F_KeMu3Date = se.F_ksrq,
    ss.F_KeMu3Times = se.F_Times
FROM LatestExam se
JOIN student_student_{tenantTableSuffix}
        ss ON se.F_StudentId = ss.F_Id
WHERE se.rn = 1;";
        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = $@"
--更新最新记录的 F_KeMu4 和 F_KeMu4Date 字段
WITH LatestExam AS(
    SELECT *,
           ROW_NUMBER() OVER(PARTITION BY F_StudentId ORDER BY F_ksrq DESC) AS rn
    FROM student_exam_{tenantTableSuffix}
        WHERE F_ResultId <> 6 AND F_KeMuId = 4
)
UPDATE ss
SET ss.F_KeMu4 = se.F_ResultId,
    ss.F_KeMu4Date = se.F_ksrq,
    ss.F_KeMu4Times = se.F_Times
FROM LatestExam se
JOIN student_student_{tenantTableSuffix}
        ss ON se.F_StudentId = ss.F_Id
WHERE se.rn = 1;";
        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);


        //         strSQL = @"
        // UPDATE[student_student_{tenantTableSuffix}] SET F__Ywzt = CONVERT(VARCHAR(10), F_KeMu1Date, 20) + ' 科目一 ' +
        //                                                                            CASE F_KeMu1
        //         WHEN 0 THEN '未考'
        //         WHEN 2 THEN '合格'
        //         WHEN 3 THEN '不合格'
        //         WHEN 4 THEN '缺考'
        //         WHEN 5 THEN '仪器故障'
        //         WHEN 6 THEN '取消'
        //         WHEN 7 THEN '待考'
        //         ELSE '未知状态'
        //     END
        //                                                              WHERE F_Status IN(1,2) AND F_KeMu1Date > '2000-01-01'


        // UPDATE[student_student_{tenantTableSuffix}] SET F__Ywzt = CONVERT(VARCHAR(10), F_KeMu2Date, 20) + ' 科目二 ' +
        //                                                                            CASE F_KeMu2
        //         WHEN 0 THEN '未考'
        //         WHEN 2 THEN '合格'
        //         WHEN 3 THEN '不合格'
        //         WHEN 4 THEN '缺考'
        //         WHEN 5 THEN '仪器故障'
        //         WHEN 6 THEN '取消'
        //         WHEN 7 THEN '待考'
        //         ELSE '未知状态'
        //     END
        //                                                              WHERE F_Status IN(1,2) AND F_KeMu2Date > '2000-01-01'


        // UPDATE[student_student_{tenantTableSuffix}] SET F__Ywzt = CONVERT(VARCHAR(10), F_KeMu3Date, 20) + ' 科目三 ' +
        //                                                                            CASE F_KeMu3
        //         WHEN 0 THEN '未考'
        //         WHEN 2 THEN '合格'
        //         WHEN 3 THEN '不合格'
        //         WHEN 4 THEN '缺考'
        //         WHEN 5 THEN '仪器故障'
        //         WHEN 6 THEN '取消'
        //         WHEN 7 THEN '待考'
        //         ELSE '未知状态'
        //     END
        //                                                              WHERE F_Status IN(1,2) AND F_KeMu3Date > '2000-01-01' AND F_KeMu3Date > F_KeMu2Date


        // UPDATE[student_student_{tenantTableSuffix}] SET F__Ywzt = CONVERT(VARCHAR(10), F_KeMu4Date, 20) + ' 科目四 ' +
        //                                                                            CASE F_KeMu4
        //         WHEN 0 THEN '未考'
        //         WHEN 2 THEN '合格'
        //         WHEN 3 THEN '不合格'
        //         WHEN 4 THEN '缺考'
        //         WHEN 5 THEN '仪器故障'
        //         WHEN 6 THEN '取消'
        //         WHEN 7 THEN '待考'
        //         ELSE '未知状态'
        //     END
        //                                                              WHERE F_Status IN(1,2) AND F_KeMu4Date > '2000-01-01'


        // UPDATE[student_student_{tenantTableSuffix}] SET F_Ywzt = F__Ywzt WHERE F__Ywzt IS NOT NULL";

        //         DbContext.Db.Ado.CommandTimeOut = 99999999;
        //         await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        strSQL = $@"
        IF EXISTS(SELECT * FROM sys_tenantConfig WHERE F_TenantId = '" + tenantId + $@"' AND F_ConfigKey = 'Config_JX_YWZT_SHOW_LAST_EXAM' AND F_ConfigValue = 'True')
            BEGIN
                WITH RankedExams AS (
                    SELECT *,
                        ROW_NUMBER() OVER (PARTITION BY F_StudentId ORDER BY F_ksrq DESC) AS rn,
                        COUNT(*) OVER (PARTITION BY F_StudentId) AS total_count
                    FROM student_exam_{tenantTableSuffix} WHERE F_ksrq > '2000-01-01' AND F_KeMuId IN (1,2,3,4) AND F_DeleteMark = 0
                )
                UPDATE ss
                SET ss.F_Ywzt = CONVERT(VARCHAR(10), F_ksrq, 20) +
                    CASE F_KeMuId
                        WHEN 1 THEN ' 科目一 '
                        WHEN 2 THEN ' 科目二 '
                        WHEN 3 THEN ' 科目三 '
                        WHEN 4 THEN ' 科目四 '
                    END +
                    CASE F_ResultId
                        WHEN 0 THEN '未考'
                        WHEN 2 THEN '合格'
                        WHEN 3 THEN '不合格'
                        WHEN 4 THEN '缺考'
                        WHEN 5 THEN '仪器故障'
                        WHEN 6 THEN '取消'
                        WHEN 7 THEN '待考'
                        ELSE '未知状态'
                    END
                FROM student_student_{tenantTableSuffix} ss
                JOIN RankedExams se ON se.F_StudentId = ss.F_Id
                WHERE (se.rn = 2 AND se.total_count >= 2) OR (se.rn = 1 AND se.total_count = 1);
            END
        ELSE
            BEGIN
                WITH RankedExams AS (
                    SELECT *,
                        ROW_NUMBER() OVER (PARTITION BY F_StudentId ORDER BY F_ksrq DESC) AS rn,
                        COUNT(*) OVER (PARTITION BY F_StudentId) AS total_count
                    FROM student_exam_{tenantTableSuffix} WHERE F_ksrq > '2000-01-01' AND F_KeMuId IN (1,2,3,4) AND F_DeleteMark = 0
                )
                UPDATE ss
                SET ss.F_Ywzt = CONVERT(VARCHAR(10), F_ksrq, 20) +
                    CASE F_KeMuId
                        WHEN 1 THEN ' 科目一 '
                        WHEN 2 THEN ' 科目二 '
                        WHEN 3 THEN ' 科目三 '
                        WHEN 4 THEN ' 科目四 '
                    END +
                    CASE F_ResultId
                        WHEN 0 THEN '未考'
                        WHEN 2 THEN '合格'
                        WHEN 3 THEN '不合格'
                        WHEN 4 THEN '缺考'
                        WHEN 5 THEN '仪器故障'
                        WHEN 6 THEN '取消'
                        WHEN 7 THEN '待考'
                        ELSE '未知状态'
                    END
                FROM student_student_{tenantTableSuffix} ss
                JOIN RankedExams se ON se.F_StudentId = ss.F_Id
                WHERE se.rn = 1;
            END
        ";

        DbContext.Db.Ado.CommandTimeOut = 99999999;
        await DbContext.Db.Ado.ExecuteCommandAsync(strSQL);

        return true;
    }


    /// <inheritdoc />
    public async Task<bool> UpdateNoPay(JxStudentEntity student, decimal noPay)
    {
        var studentTable = "student_student_" + student.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.NoPay == noPay)
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateTuitionNoPay(JxStudentEntity student, decimal tuitionNoPay)
    {
        var studentTable = "student_student_" + student.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.TuitionNoPay == tuitionNoPay)
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();

        return result > 0;
    }


    /// <inheritdoc />
    public async Task<bool> UpdateKeMu1Data(JxStudentEntity student, JxExamResultEnum examResult, DateTime examDate)
    {
        var studentTable = "student_student_" + student.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.KeMu1 == examResult && m.KeMu1Date == examDate)
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateKeMu2Data(JxStudentEntity student, JxExamResultEnum examResult, DateTime examDate)
    {
        var studentTable = "student_student_" + student.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.KeMu2 == examResult && m.KeMu2Date == examDate)
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateKeMu3Data(JxStudentEntity student, JxExamResultEnum examResult, DateTime examDate)
    {
        var studentTable = "student_student_" + student.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.KeMu3 == examResult && m.KeMu3Date == examDate)
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateKeMu4Data(JxStudentEntity student, JxExamResultEnum examResult, DateTime examDate)
    {
        var studentTable = "student_student_" + student.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.KeMu4 == examResult && m.KeMu4Date == examDate)
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateImage2ById(Guid id, bool image2)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.Image2 == image2)
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateImage4ById(Guid id, bool image4)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.Image4 == image4)
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateImage6ById(Guid id, bool image6)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.Image6 == image6)
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateImage7ById(Guid id, bool image7)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.Image7 == image7)
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    #endregion 更新缓存

    #region 更新

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetById(id);

        if (data == null)
            throw Oops.Bah("当前没有找到相关的学员数据");

        data.Delete();

        await _easyLogService.Add(data.Id, "删除学员信息", data.Id, data.TenantId);

        await UpdateAsync(data);

        var shouldPayTable = "student_ShouldPay_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var payTable = "student_pay_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();

        await Context.Updateable<JxShouldPayEntity>().AS(shouldPayTable)
            .SetColumns(m => new JxShouldPayEntity
            {
                IsDelete = true,
                DeleteTime = DateTime.Now,
                DeleteUserId = UserManager.UserId
            })
            .Where(m => m.StudentId == data.Id && m.IsDelete == false)
            .ExecuteCommandAsync();

        await Context.Updateable<JxPayEntity>().AS(payTable)
            .SetColumns(m => new JxPayEntity
            {
                IsDelete = true,
                DeleteTime = DateTime.Now,
                DeleteUserId = UserManager.UserId
            })
            .Where(m => m.StudentId == data.Id && m.IsDelete == false)
            .ExecuteCommandAsync();

        var examTable = "student_exam_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();

        if (!DbContext.Db.DbMaintenance.IsAnyTable(examTable))
        {
            examTable = "student_exam_" + Guid.Empty.ToString().Replace("-", "_").ToLower();
        }
        await Context.Updateable<JxStudentExamEntity>().AS(examTable)
        .SetColumns(m => new JxStudentExamEntity
        {
            IsDelete = true,
            DeleteTime = DateTime.Now,
            DeleteUserId = UserManager.UserId
        })
        .Where(m => m.StudentId == data.Id && m.IsDelete == false)
        .ExecuteCommandAsync();

        await _easyLogService.Add(data.Id, $"删除 [{data.xm}  {data.sfzmhm}] 学员数据全部数据完成", data.Id, data.TenantId);

        return true;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateYxq(Guid id, DateTime sfzyxqs, DateTime sfzyxqz)
    {
        var result = await Context.Updateable<JxStudentEntity>()
            .AS("student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower())
            .SetColumns(m => new JxStudentEntity
            {
                sfzyxqs = sfzyxqs,
                sfzyxqz = sfzyxqz
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateOrderCarSubject(Guid id, int orderCarSubject)
    {
        await _easyLogService.Add(id, string.Format("更新约车科目 为 {0} ", orderCarSubject), id, UserManager.TenantId);

        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                OrderCarSubject = orderCarSubject
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateRegisterTime(Guid id, DateTime registerTime, string registerSchoolName)
    {
        await _easyLogService.Add(id,
            string.Format("更新注册时间 为 {0} 注册驾校为 {1}", registerTime.ToString("yyyy-MM-dd"), registerSchoolName), id, UserManager.TenantId);

        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                RegisterTime = registerTime,
                RegisterSchoolName = string.IsNullOrEmpty(registerSchoolName) ? "" : registerSchoolName
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateSaleUser(Guid id, Guid saleUserId)
    {
        if (!UserManager.IsTenantAdmin && !await _userRoleService.Exists(PermissionConst.STUDENT_MODIFY_USERSALE1, UserManager.UserId))
        {
            throw Oops.Bah("当前用户没有权限修改推荐人");
        }
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();

        if (student.SaleUserId == saleUserId) throw Oops.Bah($"{student.xm} 推荐人未修改");
        await _easyLogService.Add(id,
            string.Format("推荐人由 {0} 更新为 {1}",
                await Context.Queryable<UserEntity>().Where(m => m.Id == student.SaleUserId).Select(m => m.RealName)
                    .SingleAsync(),
                await Context.Queryable<UserEntity>().Where(m => m.Id == saleUserId).Select(m => m.RealName)
                    .SingleAsync()), id, UserManager.TenantId);

        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                SaleUserId = saleUserId
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateSaleUser2(Guid id, Guid saleUserId2)
    {
        if (!UserManager.IsTenantAdmin && !await _userRoleService.Exists(PermissionConst.STUDENT_MODIFY_USERSALE2, UserManager.UserId))
        {
            throw Oops.Bah("当前用户没有权限修改责任人");
        }
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();

        await _easyLogService.Add(id,
            string.Format("责任人由 {0} 更新为 {1}",
                await Context.Queryable<UserEntity>().Where(m => m.Id == student.SaleUserId2).Select(m => m.RealName)
                    .SingleAsync(),
                await Context.Queryable<UserEntity>().Where(m => m.Id == saleUserId2).Select(m => m.RealName)
                    .SingleAsync()), id, UserManager.TenantId);

        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                SaleUserId2 = saleUserId2
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateSaleUser3(Guid id, Guid saleUserId3)
    {
        if (!UserManager.IsTenantAdmin && !await _userRoleService.Exists(PermissionConst.STUDENT_MODIFY_USERSALE3, UserManager.UserId))
        {
            throw Oops.Bah("当前用户没有权限修改协单人");
        }
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();

        await _easyLogService.Add(id,
            string.Format("协单人由 {0} 更新为 {1}",
                await Context.Queryable<UserEntity>().Where(m => m.Id == student.SaleUserId3).Select(m => m.RealName)
                    .SingleAsync(),
                await Context.Queryable<UserEntity>().Where(m => m.Id == saleUserId3).Select(m => m.RealName)
                    .SingleAsync()), id, UserManager.TenantId);

        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                SaleUserId3 = saleUserId3
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateJxClassId(Guid id, Guid jxClassId)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();

        if (student.JxClassId == jxClassId) throw Oops.Bah($"{student.xm} 班别未修改");
        await _easyLogService.Add(id,
            string.Format("班别由 {0} 更新为 {1}",
                await Context.Queryable<JxClassEntity>().Where(m => m.Id == student.JxClassId).Select(m => m.Name)
                    .SingleAsync(),
                await Context.Queryable<JxClassEntity>().Where(m => m.Id == jxClassId).Select(m => m.Name)
                    .SingleAsync()), id, UserManager.TenantId);

        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                JxClassId = jxClassId
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateJxDeptId(Guid id, Guid jxDeptId)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();

        if (student.JxDeptId == jxDeptId) throw Oops.Bah($"{student.xm} 报名点未修改");
        await _easyLogService.Add(id,
            string.Format("报名点由 {0} 更新为 {1}",
                await Context.Queryable<JxDeptEntity>().Where(m => m.Id == student.JxDeptId).Select(m => m.Name)
                    .SingleAsync(),
                await Context.Queryable<JxDeptEntity>().Where(m => m.Id == jxDeptId).Select(m => m.Name)
                    .SingleAsync()), id, UserManager.TenantId);

        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                JxDeptId = jxDeptId
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateJxFieldId(Guid id, Guid jxFieldId)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();

        if (student.JxFieldId == jxFieldId) throw Oops.Bah($"{student.xm} 训练场未修改");
        await _easyLogService.Add(id,
            string.Format("训练场由 {0} 更新为 {1}",
                await Context.Queryable<JxFieldEntity>().Where(m => m.Id == student.JxFieldId).Select(m => m.Name)
                    .SingleAsync(),
                await Context.Queryable<JxFieldEntity>().Where(m => m.Id == jxFieldId).Select(m => m.Name)
                    .SingleAsync()), id, UserManager.TenantId);

        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                JxFieldId = jxFieldId
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }


    /// <inheritdoc />
    public async Task<bool> UpdateJxStudentImformationStatusId(Guid id, Guid jxStudentImformationStatusId)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();

        if (student.JxStudentImformationStatusId == jxStudentImformationStatusId) throw Oops.Bah($"{student.xm} 资料状态未修改");

        await _easyLogService.Add(id,
            string.Format("资料状态由 {0} 更新为 {1}",
                await Context.Queryable<JxStudentImformationStatusEntity>().Where(m => m.Id == student.JxStudentImformationStatusId).Select(m => m.Name)
                    .SingleAsync(),
                await Context.Queryable<JxStudentImformationStatusEntity>().Where(m => m.Id == jxStudentImformationStatusId).Select(m => m.Name)
                    .SingleAsync()), id, UserManager.TenantId);

        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                JxStudentImformationStatusId = jxStudentImformationStatusId
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateSourceId(Guid id, Guid sourceId)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();

        if (student.SourceId == sourceId) throw Oops.Bah($"{student.xm} 来源未修改");

        // 获取旧来源名称
        string oldSourceName = "";
        if (student.SourceId != Guid.Empty)
        {
            var oldSource = await Context.Queryable<JxStudentSourceEntity>().Where(m => m.Id == student.SourceId).FirstAsync();
            if (oldSource != null)
            {
                oldSourceName = oldSource.Name;
            }
        }

        // 获取新来源名称
        string newSourceName = "";
        if (sourceId != Guid.Empty)
        {
            var newSource = await Context.Queryable<JxStudentSourceEntity>().Where(m => m.Id == sourceId).FirstAsync();
            if (newSource != null)
            {
                newSourceName = newSource.Name;
            }
        }

        // 记录日志
        await _easyLogService.Add(id,
            string.Format("来源由 {0} 更新为 {1}",
                string.IsNullOrEmpty(oldSourceName) ? "无" : oldSourceName,
                string.IsNullOrEmpty(newSourceName) ? "无" : newSourceName),
            id, UserManager.TenantId);

        // 更新数据
        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity
            {
                SourceId = sourceId
            })
            .Where(m => m.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }



    /// <summary>
    /// 更新学员信息
    /// </summary>
    /// <param name="student"></param>
    /// <returns></returns>
    public async Task<bool> Update(JxStudentEntity student)
    {
        await Context.Updateable(student).SplitTable().ExecuteCommandAsync();
        return true;
    }


    /// <inheritdoc />
    public async Task<bool> UpdateStatus(JxStudentEntity student, JxStudentStatusEnum statusEnum)
    {
        if (student.Status == statusEnum)
            throw Oops.Bah($"{student.xm} 状态未修改");

        var studentTable = "student_student_" + student.TenantId.ToString().Replace("-", "_").ToLower();

        await _easyLogService.Add(student.Id, string.Format("状态由 {0} 更新为 {1}", student.Status.GetDescription(), statusEnum.GetDescription()), student.Id, student.TenantId);

        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.Status == statusEnum)
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <inheritdoc />
    public async Task<bool> UpdateStatus(Guid id, JxStudentStatusEnum statusEnum)
    {
        if (!UserManager.IsTenantAdmin)
            throw Oops.Bah("无权限操作");


        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();

        if (student.Status == statusEnum)
            throw Oops.Bah($"{student.xm} 状态未修改");

        await _easyLogService.Add(id, string.Format("状态由 {0} 更新为 {1}", student.Status.GetDescription(), statusEnum.GetDescription()), id, UserManager.TenantId);

        if (statusEnum == JxStudentStatusEnum.Quit)
        {
            student.DropOutTime = DateTime.Now;

            await Context.Updateable(student).SplitTable().ExecuteCommandAsync();
            var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => new JxStudentEntity()
            {
                Status = statusEnum,
                DropOutTime = DateTime.Now
            })
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();
            return result > 0;
        }
        else
        {
            var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.Status == statusEnum)
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();
            return result > 0;
        }

    }

    /// <inheritdoc />
    public async Task<bool> UpdateRemark(Guid id, string remark)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var student = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.Id == id).SingleAsync();


        await _easyLogService.Add(id, string.Format("备注添加内容 {0}", remark), id, UserManager.TenantId);

        var result = await Context.Updateable<JxStudentEntity>()
            .AS(studentTable)
            .SetColumns(m => m.Remark == m.Remark + remark)
            .Where(m => m.Id == student.Id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    #endregion 更新

    #region 方法

    /// <inheritdoc />
    public async Task<int> RestoreData(string sfzmhm)
    {
        var studentTable = "student_student_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var payTable = "student_pay_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var shouldPayTable = "student_ShouldPay_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();
        var examTable = "student_exam_" + UserManager.TenantId.ToString().Replace("-", "_").ToLower();

        var data = await Context.Queryable<JxStudentEntity>().AS(studentTable).Where(m => m.sfzmhm == sfzmhm && m.IsDelete == true).ToListAsync();

        foreach (var item in data)
        {
            item.IsDelete = false;
            item.Remark = item.Remark + " 恢复数据";

            await Context.Updateable(item).SplitTable().ExecuteCommandAsync();

            var payData = await Context.Queryable<JxPayEntity>().AS(payTable).Where(m => m.StudentId == item.Id && m.IsDelete == true).ToListAsync();

            foreach (var payItem in payData)
            {
                payItem.IsDelete = false;
                payItem.Remark = payItem.Remark + " 恢复数据";

                await Context.Updateable(payItem).SplitTable().ExecuteCommandAsync();
            }

            var shouldPayData = await Context.Queryable<JxShouldPayEntity>().AS(shouldPayTable).Where(m => m.StudentId == item.Id && m.IsDelete == true).ToListAsync();

            foreach (var shouldPayItem in shouldPayData)
            {
                shouldPayItem.IsDelete = false;
                shouldPayItem.Remark = shouldPayItem.Remark + " 恢复数据";

                await Context.Updateable(shouldPayItem).SplitTable().ExecuteCommandAsync();
            }

            var examData = await Context.Queryable<JxStudentExamEntity>().AS(examTable).Where(m => m.StudentId == item.Id && m.IsDelete == true).ToListAsync();

            foreach (var examItem in examData)
            {
                examItem.IsDelete = false;
                examItem.Remark = examItem.Remark + " 恢复数据";

                await Context.Updateable(examItem).SplitTable().ExecuteCommandAsync();
            }
        }

        return data.Count;
    }

    #endregion 方法
}