using PandaServer.System.Services.Oss;
using PandaServer.System.Services.Student.Dto.Jx;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;

namespace PandaServer.System.Services.Student.Jx;



/// <summary>
///     <inheritdoc cref="IJxCompanyService" />
/// </summary>
public class JxCompanyService : CustomDbRepository<JxCompanyEntity>, IJxCompanyService, ITransient
{
    private readonly ISimpleCacheService _simpleCacheService;
    private readonly IQCloudOssService _qCloudOssService;

    public JxCompanyService(ISimpleCacheService simpleCacheService, IQCloudOssService qCloudOssService)
    {
        _simpleCacheService = simpleCacheService;
        _qCloudOssService = qCloudOssService;
    }
    #region 添加

    /// <inheritdoc />
    public async Task<JxCompanyEntity> Add(JxCompanyInPut inPut)
    {
        if (string.IsNullOrEmpty(inPut.Name) || inPut.Name.Trim() == "")
            throw Oops.Bah("名称不能为空!");
        if (await ExistsByName(Guid.Empty, inPut.Name))
            throw Oops.Bah("名称重复，请重试");

        var jxCompany = inPut.Adapt<JxCompanyEntity>();
        jxCompany.Create();
        jxCompany.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;
        jxCompany.CityCode = string.IsNullOrEmpty(inPut.CityCode) ? "" : inPut.CityCode;

        jxCompany.CompanyCode = string.IsNullOrEmpty(inPut.CompanyCode) ? "" : inPut.CompanyCode;
        jxCompany.LegalPerson = string.IsNullOrEmpty(inPut.LegalPerson) ? "" : inPut.LegalPerson;
        jxCompany.LegalPersonaIdCard = string.IsNullOrEmpty(inPut.LegalPersonaIdCard) ? "" : inPut.LegalPersonaIdCard;
        jxCompany.LegalPersonPhone = string.IsNullOrEmpty(inPut.LegalPersonPhone) ? "" : inPut.LegalPersonPhone;
        jxCompany.ChargePerson = string.IsNullOrEmpty(inPut.ChargePerson) ? "" : inPut.ChargePerson;
        jxCompany.ChargePersonaIdCard =
            string.IsNullOrEmpty(inPut.ChargePersonaIdCard) ? "" : inPut.ChargePersonaIdCard;
        jxCompany.ChargePersonPhone = string.IsNullOrEmpty(inPut.ChargePersonPhone) ? "" : inPut.ChargePersonPhone;
        jxCompany.ContactPerson = string.IsNullOrEmpty(inPut.ContactPerson) ? "" : inPut.ContactPerson;
        jxCompany.ContactPersonIdCard =
            string.IsNullOrEmpty(inPut.ContactPersonIdCard) ? "" : inPut.ContactPersonIdCard;
        jxCompany.ContactPersonPhone = string.IsNullOrEmpty(inPut.ContactPersonPhone) ? "" : inPut.ContactPersonPhone;
        jxCompany.TransportCode = string.IsNullOrEmpty(inPut.TransportCode) ? "" : inPut.TransportCode.Trim();

        jxCompany.CompanyPhone = string.IsNullOrEmpty(inPut.CompanyPhone) ? "" : inPut.CompanyPhone;
        jxCompany.SealFilePath = "";
        jxCompany.LegalPersonSignFilePath = "";

        jxCompany.Address = string.IsNullOrEmpty(inPut.Address) ? "" : inPut.Address;
        jxCompany.TrainingCarType = inPut.TrainingCarTypes.Count == 0 ? "" : string.Join(",", inPut.TrainingCarTypes);

        if (inPut.TenantId == Guid.Empty)
            jxCompany.TenantId = UserManager.TenantId;

        if (!await InsertAsync(jxCompany))
            throw Oops.Bah("添加数据失败");

        await ClearJxCompanyCache(UserManager.TenantId);

        return jxCompany;
    }

    #endregion 添加

    #region 查询

    /// <inheritdoc />
    public async Task<JxCompanyOutPut?> GetById(Guid id)
    {
        if (id == Guid.Empty)
            return null;

        var jxCompanies = await GetJxCompanyCache(UserManager.TenantId);

        var data = jxCompanies.Where(m => m.Id == id && m.IsDelete == false).FirstOrDefault();

        var result = data.Adapt<JxCompanyOutPut>();

        result.TrainingCarTypes =
            result.TrainingCarType.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).ToList();

        return result;
    }


    /// <inheritdoc />
    public async Task<List<JxCompanyOutPut>> GetListByTenatId(Guid tenantId)
    {
        var jxCompanies = await GetJxCompanyCache(tenantId);
        return jxCompanies.Adapt<List<JxCompanyOutPut>>();
    }

    /// <inheritdoc />
    public async Task<string> GetCompanyNameById(Guid id)
    {
        var jxCompanies = await GetJxCompanyCache(UserManager.TenantId);

        var data = jxCompanies.Where(m => m.Id == id && m.IsDelete == false).FirstOrDefault();

        return data.CompanyName;
    }

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<JxCompanyOutPut>> Page(JxCompanyPageInPut inPut)
    {
        var query = Context.Queryable<JxCompanyEntity>()
            .LeftJoin<UserEntity>((m, n) => m.CreateUserId == n.Id)
            .LeftJoin<TenantEntity>((m, n, o) => m.TenantId == o.Id)
            .Where(m => m.IsDelete == false)
            .WhereIF(inPut.ManagerTenantId == Guid.Empty, m => m.TenantId == UserManager.TenantId)
            .WhereIF(inPut.ManagerTenantId != Guid.Empty, (m, n, o) => o.ManageTenantId == UserManager.TenantId)
            .WhereIF(inPut.TenantId != Guid.Empty, m => m.TenantId == inPut.TenantId)
            .WhereIF(inPut.CityId > 0, (m, n, o) => o.CityId == inPut.CityId)
            .WhereIF(!string.IsNullOrEmpty(inPut.Name),
                m => m.Name.Contains(inPut.Name.Trim()))
            .OrderBy(m => m.SortCode, OrderByType.Desc)
            .OrderBy(m => m.CreateTime, OrderByType.Desc)
            .Select((m, n) => new JxCompanyOutPut
            {
                Id = m.Id,
                Name = m.Name,
                CompanyName = m.CompanyName,
                CompanyCode = m.CompanyCode,
                CityCode = m.CityCode,
                CreateUserName = n.RealName,
                CreateTime = m.CreateTime,
                SortCode = m.SortCode
            });


        var data = await query.ToPagedListAsync(inPut.Current, inPut.Size);

        data.Records = data.Records.ToList().Select<JxCompanyOutPut, JxCompanyOutPut>((u, i) =>
            {
                u.RowIndex = (inPut.Current - 1) * inPut.Size + (i + 1);

                return u;
            });
        return data;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsByName(Guid jxCompanyId, string name)
    {
        return await IsAnyAsync(m =>
            m.Id != jxCompanyId && m.Name == name && m.IsDelete == false && m.IsDelete == false &&
            m.TenantId == UserManager.TenantId);
    }

    /// <inheritdoc />
    public async Task<bool> ExistsByName(Guid jxCompanyId, string name, Guid tenantId)
    {
        return await IsAnyAsync(m =>
            m.Id != jxCompanyId && m.Name == name && m.IsDelete == false && m.IsDelete == false &&
            m.TenantId == tenantId);
    }


    /// <inheritdoc />
    public async Task<List<JxCompanyOutPut>> GetList(Guid tenantId)
    {
        var jxCompanies = await GetJxCompanyCache(tenantId);
        return jxCompanies.Adapt<List<JxCompanyOutPut>>();
    }

    #endregion 查询

    #region 更新

    /// <inheritdoc />
    public async Task<bool> UpdateIsDelete(Guid id)
    {
        var data = await GetSingleAsync(m => m.Id == id);

        if (data == null)
            throw Oops.Bah("未找到相关的数据，请刷新页面");

        data.Delete();

        if (!await UpdateAsync(data))
        {
            throw Oops.Bah("删除失败");
        }

        await ClearJxCompanyCache(UserManager.TenantId);
        return true;
    }


    /// <inheritdoc />
    public async Task<bool> Update(JxCompanyInPut inPut)
    {
        if (string.IsNullOrEmpty(inPut.Name) || inPut.Name.Trim() == "")
            throw Oops.Bah("名称不能为空!");

        var jxCompany = await GetById(inPut.Id);

        jxCompany.Name = inPut.Name;
        jxCompany.SortCode = inPut.SortCode;
        jxCompany.CompanyName = inPut.CompanyName;
        // jxCompany.CityCode = inPut.CityCode;
        jxCompany.Remark = string.IsNullOrEmpty(inPut.Remark) ? "" : inPut.Remark;

        jxCompany.CompanyCode = string.IsNullOrEmpty(inPut.CompanyCode) ? "" : inPut.CompanyCode;

        jxCompany.Modify();

        if (await ExistsByName(jxCompany.Id, jxCompany.Name))
            throw Oops.Bah("名称重复，请重试");


        if (!await UpdateAsync(jxCompany))
        {
            throw Oops.Bah("更新失败");
        }

        await ClearJxCompanyCache(UserManager.TenantId);
        return true;
    }

    /// <inheritdoc />
    public async Task<int> GetCountByTenantId(Guid tenantId)
    {
        return await CountAsync(m => m.TenantId == tenantId && m.IsDelete == false);
    }

    /// <inheritdoc />
    public async Task<bool> UploadSeal(Guid id, byte[] imageData)
    {
        var company = await GetById(id);
        if (company == null)
            throw Oops.Bah("未找到相关的公司数据");

        if (company.SealFilePath != null && !string.IsNullOrEmpty(company.SealFilePath.Trim()))
            throw Oops.Bah("公章图片已存在，请勿重复上传");

        var filePath = _qCloudOssService.UploadBytes(
            "/JxCompany/Seal/" + DateTime.Now.ToString("yyyy/MM/dd/") + Guid.NewGuid() + ".jpg", imageData);

        company.SealFilePath = filePath;
        if (await UpdateAsync(company))
        {
            await ClearJxCompanyCache(company.TenantId);
            return true;
        }
        else
        {
            throw Oops.Bah("上传公章图片失败");
        }
    }

    #endregion 更新

    #region 缓存

    /// <summary>
    /// 清除 驾校的区域 的缓存
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task ClearJxCompanyCache(Guid tenantId)
    {
        var key = CacheConst.Cache_JxCompany; //系统配置key
        await _simpleCacheService.HashDel<List<JxCompanyEntity>>(key, tenantId.ToString());
    }

    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task<List<JxCompanyEntity>> GetJxCompanyCache(Guid tenantId)
    {
        var key = CacheConst.Cache_JxCompany; //系统配置key
        var jxCompanies = await _simpleCacheService.HashGetOne<List<JxCompanyEntity>>(key, tenantId.ToString());
        if (jxCompanies == null)
        {
            jxCompanies = await Context.Queryable<JxCompanyEntity>().Where(m => m.TenantId == tenantId).ToListAsync();
            await _simpleCacheService.HashAdd(key, tenantId.ToString(), jxCompanies);
        }

        return jxCompanies;
    }

    #endregion 缓存
}