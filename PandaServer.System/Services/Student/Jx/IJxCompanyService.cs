using PandaServer.System.Services.Student.Dto.Jx;

namespace PandaServer.System.Services.Student.Jx;


public interface IJxCompanyService
{
    /// <summary>
    ///     返回 实体
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<JxCompanyOutPut?> GetById(Guid id);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<List<JxCompanyOutPut>> GetListByTenatId(Guid tenantId);

    /// <summary>
    ///     直接返回 驾校的名字
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<string> GetCompanyNameById(Guid id);

    /// <summary>
    ///     分页 查询
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<JxCompanyOutPut>> Page(JxCompanyPageInPut inPut);

    /// <summary>
    ///     判断 名字 是否 重复
    /// </summary>
    /// <param name="areaId"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    Task<bool> ExistsByName(Guid areaId, string name);

    /// <summary>
    ///     判断 名字 是否 重复
    /// </summary>
    /// <param name="areaId"></param>
    /// <param name="name"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<bool> ExistsByName(Guid areaId, string name, Guid tenantId);

    /// <summary>
    ///     获取 驾校列表
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<List<JxCompanyOutPut>> GetList(Guid tenantId);

    /// <summary>
    ///     当前公司下面有几个资质
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<int> GetCountByTenantId(Guid tenantId);

    /// <summary>
    ///     更新删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> UpdateIsDelete(Guid id);

    /// <summary>
    ///     添加
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<JxCompanyEntity> Add(JxCompanyInPut inPut);


    /// <summary>
    ///     更新
    /// </summary>
    /// <param name="inPut"></param>
    /// <returns></returns>
    Task<bool> Update(JxCompanyInPut inPut);

    /// <summary>
    ///     上传公章图片
    /// </summary>
    /// <param name="id">公司ID</param>
    /// <param name="imageData">图片数据</param>
    /// <returns></returns>
    Task<bool> UploadSeal(Guid id, byte[] imageData);
}