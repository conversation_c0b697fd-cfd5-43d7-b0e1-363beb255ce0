using System.ComponentModel.DataAnnotations;
using PandaServer.Core;

namespace PandaServer.System.Services.Student.Dto.Contract;

/// <summary>
/// 合同文件上传参数
/// </summary>
public class JxStudentContractUploadInput : BaseIdInput
{
    /// <summary>
    /// 学员Id
    /// </summary>
    [Required(ErrorMessage = "学员Id不能为空")]
    public Guid StudentId { get; set; }

    /// <summary>
    /// 模板编号
    /// </summary>
    [Required(ErrorMessage = "模板编号不能为空")]
    public Guid TemplateId { get; set; }

    /// <summary>
    /// 签署的微信OpenId
    /// </summary>
    [Required(ErrorMessage = "OpenId不能为空")]
    public string OpenId { get; set; }

    /// <summary>
    /// PDF文件路径
    /// </summary>
    [Required(ErrorMessage = "PDF文件不能为空")]
    public string PdfFile { get; set; }
}

/// <summary>
/// 签名视频上传参数
/// </summary>
public class JxStudentContractVideoInput : BaseIdInput
{
    /// <summary>
    /// 视频文件路径
    /// </summary>
    [Required(ErrorMessage = "视频文件不能为空")]
    public string VideoFile { get; set; }
}

/// <summary>
/// 签名笔迹上传参数
/// </summary>
public class JxStudentContractSignInput : BaseIdInput
{
    /// <summary>
    /// 笔迹文件路径
    /// </summary>
    [Required(ErrorMessage = "笔迹文件不能为空")]
    public string SignFile { get; set; }
}

/// <summary>
/// 提交合同签署信息参数
/// </summary>
public class JxStudentContractSubmitInput
{
    /// <summary>
    /// 学员Id
    /// </summary>
    [Required(ErrorMessage = "学员Id不能为空")]
    public Guid StudentId { get; set; }

    /// <summary>
    /// 模板编号
    /// </summary>
    [Required(ErrorMessage = "模板编号不能为空")]
    public Guid TemplateId { get; set; }

    /// <summary>
    /// 签署的微信OpenId
    /// </summary>
    [Required(ErrorMessage = "OpenId不能为空")]
    public string OpenId { get; set; }

    /// <summary>
    /// GPS定位信息
    /// </summary>
    public string LocationInfo { get; set; }


    /// <summary>
    /// 签字的 文件路径
    /// </summary>
    public string SignFilePath { get; set; }


    /// <summary>
    /// 人脸识别的 文件路径
    /// </summary>
    public string FaceFilePath { get; set; }
}

/// <summary>
/// 上传签字信息参数
/// </summary>
public class JxStudentContractSignatureInput : BaseIdInput
{
    /// <summary>
    /// 签字文件路径
    /// </summary>
    [Required(ErrorMessage = "签字文件不能为空")]
    public string SignFile { get; set; }
}

/// <summary>
/// 上传拍照信息参数
/// </summary>
public class JxStudentContractPhotoInput : BaseIdInput
{
    /// <summary>
    /// 拍照文件路径
    /// </summary>
    [Required(ErrorMessage = "拍照文件不能为空")]
    public string SignImages { get; set; }
}
