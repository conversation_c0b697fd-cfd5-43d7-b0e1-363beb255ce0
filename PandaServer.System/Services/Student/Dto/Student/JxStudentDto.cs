
using System.Collections;
using PandaServer.System.Entity.Student.Wages;
using PandaServer.System.Services.Student.Dto.Wages;
using PandaServer.System.Services.Student.Dto;
using PandaServer.System.Services.Student.Dto.Pay;
using PandaServer.System.Services.Student.Dto.Exam;

namespace PandaServer.System.Services.Student.Dto;

public class JxStudentInPut : JxStudentEntity
{
    /// <summary>
    ///     学费金额
    /// </summary>
    public decimal PayMoney { get; set; }


    /// <summary>
    ///     优惠金额
    /// </summary>
    public decimal DiscountMoney { get; set; }


    /// <summary>
    ///     介绍的人的 名字
    /// </summary>
    /// <value></value>
    public string SaleUserName { get; set; }


    /// <summary>
    ///     报名点的名称
    /// </summary>
    public string JxDeptName { get; set; }

    /// <summary>
    ///     训练场的名称
    /// </summary>
    public string JxFieldName { get; set; }

    /// <summary>
    ///     报名班型的名称
    /// </summary>
    public string JxClassName { get; set; }


    /// <summary>
    ///     状态名称
    /// </summary>
    public string StatusName { get; set; }


    /// <summary>
    ///     当前电脑的 Mac 地址
    /// </summary>
    public string MacAddress { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid UserId { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string TenantName { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid CreatorUserId { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CreateUserName { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public List<DateTime> CreateTimes { get; set; }


    /// <summary>
    ///     录入日期  (时间戳)
    /// </summary>
    public long CreateTimeStamp { get; set; }


    /// <summary>
    ///     科目一 的考试结果
    /// </summary>
    /// <value></value>
    public string KeMu1Text { get; set; }


    /// <summary>
    ///     科目二 的考试结果
    /// </summary>
    /// <value></value>
    public string KeMu2Text { get; set; }


    /// <summary>
    ///     科目三 的考试结果
    /// </summary>
    /// <value></value>
    public string KeMu3Text { get; set; }

    /// <summary>
    ///     科目四 的考试结果
    /// </summary>
    /// <value></value>
    public string KeMu4Text { get; set; }


    /// <summary>
    ///     长沙驾协   认证时间
    /// </summary>
    /// <value></value>
    public DateTime CertifiedTime { get; set; }


    /// <summary>
    ///     寸照 的图片地址
    /// </summary>
    /// <value></value>
    public string Image0Path { get; set; }


    /// <summary>
    ///     身份证电子照片 的图片地址
    /// </summary>
    /// <value></value>
    public string Image1Path { get; set; }


    /// <summary>
    ///     现场照片的 地址
    /// </summary>
    /// <value></value>
    public string Image2Path { get; set; }


    /// <summary>
    ///     数码照片 的图片地址（一般图片效果优于 寸照 对应的是 广东的数码回执的照片）
    /// </summary>
    /// <value></value>
    public string Image8Path { get; set; }


    /// <summary>
    ///     合同签署现场 地址
    /// </summary>
    /// <value></value>
    public string Image200Path { get; set; }


    /// <summary>
    ///     身份证正反面 地址
    /// </summary>
    /// <value></value>
    public string Image4Path { get; set; }


    /// <summary>
    ///     身份证正面 地址
    /// </summary>
    /// <value></value>
    public string Image40Path { get; set; }


    /// <summary>
    ///     身份证反面 地址
    /// </summary>
    /// <value></value>
    public string Image41Path { get; set; }

    /// <summary>
    ///     所有的应缴的 编号 列表
    /// </summary>
    /// <value></value>
    public List<Guid> ShoudPayIds { get; set; }

    /// <summary>
    ///     所有的缴费的 编号 列表
    /// </summary>
    /// <value></value>
    public List<Guid> PayIds { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid JxCompanyId { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<JxShouldPayInPut> JxShouldPays { get; set; }


    /// <summary>
    ///     查询 包含费用
    /// </summary>
    /// <value></value>
    public List<Guid> CostTypeIds { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string MarkText { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string MarkColor { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<string> JxMarkColors { get; set; }


    /// <summary>
    /// </summary>
    public List<int> djzsxzqhs { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<int> lxzsxzqhs { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<int> xzqhs { get; set; } = new();


    public List<JxShouldPayDetailInPut> JxShouldPayDetails { get; set; } = new();


    public List<JxMyStudentColumnOutPut> MyStudentColumnDatas { get; set; } = new();


    /// <summary>
    /// </summary>
    /// <returns></returns>
    public List<Guid> StudentIds { get; set; } = new();

    /// <summary>
    /// 是否导入学员图片
    /// </summary>
    public bool ImportPhoto { get; set; }

    /// <summary>
    /// 是否导入考试信息
    /// </summary>
    public bool ImportExamInfo { get; set; }

    /// <summary>
    /// 是否导入财务信息
    /// </summary>
    public bool ImportPayInfo { get; set; }

    /// <summary>
    ///     身份证证明号码 列表
    /// </summary>
    public string sfzmhms { get; set; }

    /// <summary>
    /// 身份证正面图片
    /// </summary>
    public string Image40 { get; set; }

    /// <summary>
    /// 身份证反面图片
    /// </summary>
    public string Image41 { get; set; }
}

public class JxStudentPageInPut : BasePageInput
{
    /// <summary>
    /// 搜索类别  1:在培，2:毕业，3:退学，-1:未注册，101:科一，102:科二，103:科三，104:科四，203:三个月内过期，206:六个月内过期，200:已经过期
    /// </summary>
    public string SearchType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string RegisterSchoolName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// </summary>
    public string TimeType { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public string CountType { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public List<string> CountTypes { get; set; } = new List<string>();


    /// <summary>
    /// 时间的统计
    /// </summary>
    public string TimePeriod { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string CountTypeValue { get; set; }

    /// <summary>
    /// </summary>
    public List<DateTime> Times { get; set; } = new();

    /// <summary>
    /// </summary>
    public string TimeType2 { get; set; }

    /// <summary>
    /// </summary>
    public List<DateTime> Times2 { get; set; } = new();

    /// <summary>
    ///     证件类型
    /// </summary>
    public List<string> sfzmmcs { get; set; } = new();

    /// <summary>
    ///     姓名
    /// </summary>
    public string xm { get; set; }

    /// <summary>
    ///     身份证证明号码
    /// </summary>
    public string sfzmhm { get; set; }

    /// <summary>
    ///     身份证证明号码 列表
    /// </summary>
    public string sfzmhms { get; set; }

    public string sfzmhm_Start { get; set; }

    public string sfzmhm_End { get; set; }

    public string yddh { get; set; }

    /// <summary>
    ///     当前的状态 培训状态
    /// </summary>
    public List<JxStudentStatusEnum> Statuss { get; set; } = new();


    /// <summary>
    ///     当前电脑的 Mac 地址
    /// </summary>
    public string MacAddress { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<DateTime> CreateTimes { get; set; } = new();

    /// <summary>
    ///     查询 包含费用
    /// </summary>
    /// <value></value>
    public List<Guid> CostTypeIds { get; set; } = new();

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<string> JxMarkColors { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> DocStatusIds { get; set; } = new();

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<string> CarTypes { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> Ids { get; set; } = new();

    /// <summary>
    /// 
    /// </summary>
    public Guid SaleUserId { get; set; } = Guid.Empty;

    /// <summary>
    /// </summary>
    public List<Guid> SaleUserIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> SaleUserId2s { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> SaleUserId3s { get; set; } = new();


    /// <summary>
    /// </summary>
    public List<Guid> TeachOneUserIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> TeachTwoUserIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> TeachThreeUserIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> TeachUserIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> SaleOrTeachUserIds { get; set; } = new();


    public Guid SaleJxDeptId { get; set; } = Guid.Empty;

    public Guid CurrentSaleJxDeptId { get; set; } = Guid.Empty;

    /// <summary>
    /// </summary>
    public List<Guid> JxDeptIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> SaleJxDeptIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> CurrentSaleJxDeptIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> SaleJxFieldIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> JxClassIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<Guid> JxFieldIds { get; set; } = new();


    /// <summary>
    /// </summary>
    public List<int> KeMu1ResultIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<int> KeMu2ResultIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<int> KeMu3ResultIds { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<int> KeMu4ResultIds { get; set; } = new();


    /// <summary>
    /// </summary>
    public List<DateTime> KeMu1ExamDates { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<DateTime> KeMu2ExamDates { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<DateTime> KeMu3ExamDates { get; set; } = new();

    /// <summary>
    /// </summary>
    public List<DateTime> KeMu4ExamDates { get; set; } = new();


    /// <summary>
    /// </summary>
    public Guid RegisterId { get; set; }

    /// <summary>
    ///     资料状态
    /// </summary>
    /// <returns></returns>
    public List<Guid> JxStudentImformationStatusIds { get; set; } = new();

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public Hashtable MyColumnData { get; set; } = new Hashtable();


    /// <summary>
    /// 查询我的学员的 类型 
    /// </summary>
    /// <value></value>
    public string MyStudentType { get; set; } = "";

    /// <summary>
    /// 欠费下限
    /// </summary>
    /// <value></value>
    public string NoPayMin { get; set; }

    /// <summary>
    /// 欠费上限
    /// </summary>
    /// <value></value>
    public string NoPayMax { get; set; }

    /// <summary>
    /// 欠学费下限
    /// </summary>
    /// <value></value>
    public string TuitionNoPayMin { get; set; }

    /// <summary>
    /// 欠学费上限
    /// </summary>
    /// <value></value>
    public string TuitionNoPayMax { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string TuitionPayMin { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string TuitionPayMax { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public List<Guid> CategoryIds { get; set; } = new List<Guid>();

    /// <summary>
    /// 用户分类
    /// </summary>
    public List<Guid> SaleCategoryIds { get; set; } = new List<Guid>();

    /// <summary>
    /// 来源
    /// </summary>
    public List<Guid> SourceIds { get; set; } = new List<Guid>();

    /// <summary>
    /// 年龄区间
    /// </summary>
    public List<decimal> Ages { get; set; } = new List<decimal>();

    /// <summary>
    /// 年龄区间
    /// </summary>
    public List<decimal> NoPays { get; set; } = new List<decimal>();

    /// <summary>
    /// 年龄区间
    /// </summary>
    public List<decimal> TuitionNoPays { get; set; } = new List<decimal>();

    /// <summary>
    /// 年龄区间
    /// </summary>
    public List<decimal> TuitionPays { get; set; } = new List<decimal>();

    /// <summary>
    /// 科二次数
    /// </summary>
    public List<int> KeMu2Times { get; set; } = new List<int>();

    /// <summary>
    /// 科三次数
    /// </summary>
    public List<int> KeMu3Times { get; set; } = new List<int>();


    /// <summary>
    /// 没有照片
    /// </summary>
    public List<int> NoPhotos { get; set; } = new List<int>();


    /// <summary>
    /// 有照片
    /// </summary>
    public List<int> HavePhotos { get; set; } = new List<int>();

}

public class JxStudentOutPut : JxStudentEntity
{
    // [SugarColumn(IsIgnore = true)]//需要加上
    public int RowIndex { get; set; } //行号 序号

    /// <summary>
    ///     证件类型 名称
    /// </summary>
    public string sfzmmcName { get; set; }


    /// <summary>
    /// </summary>
    public int[] xzqhs { get; set; }


    /// <summary>
    ///     学费金额
    /// </summary>
    public decimal PayMoney { get; set; }


    /// <summary>
    ///     优惠金额
    /// </summary>
    public decimal DiscountMoney { get; set; }


    /// <summary>
    ///     科目一教练 名字
    /// </summary>
    /// <value></value>
    public string TeachOneUserName { get; set; }

    /// <summary>
    ///     科目二教练 名字
    /// </summary>
    /// <value></value>
    public string TeachTwoUserName { get; set; }

    /// <summary>
    ///     科目三教练 名字
    /// </summary>
    /// <value></value>
    public string TeachThreeUserName { get; set; }

    /// <summary>
    ///     介绍的人的 名字
    /// </summary>
    /// <value></value>
    public string SaleUserName { get; set; }

    /// <summary>
    /// </summary>
    public string SaleUserName2 { get; set; }

    /// <summary>
    /// </summary>
    public string SaleUserName3 { get; set; }

    /// <summary>
    ///     欠费
    /// </summary>
    public decimal NoPay { get; set; }

    /// <summary>
    ///     报名点的名称
    /// </summary>
    public string JxDeptName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Guid SaleUserJxDeptId { get; set; }


    /// <summary>
    /// </summary>
    [ExporterHeader(DisplayName = "报名校区")]
    public string SaleJxDeptName { get; set; }

    /// <summary>
    /// </summary>
    [ExporterHeader(DisplayName = "当前销售门店")]
    public Guid CurrentSaleJxDeptId { get; set; }


    /// <summary>
    /// </summary>
    [ExporterHeader(DisplayName = "当前销售门店")]
    public string CurrentSaleJxDeptName { get; set; }


    /// <summary>
    ///     训练场的名称
    /// </summary>
    [ExporterHeader(DisplayName = "训练场")]
    public string JxFieldName { get; set; }

    /// <summary>
    ///     报名班型的名称
    /// </summary>
    [ExporterHeader(DisplayName = "班别")]
    public string JxClassName { get; set; }

    /// <summary>
    /// </summary>
    [ExporterHeader(DisplayName = "车型")]
    public string CarTypeName { get; set; }

    /// <summary>
    ///     当前的状态 培训状态
    /// </summary>
    public JxStudentStatusEnum Status { get; set; }

    /// <summary>
    ///     状态名称
    /// </summary>
    [ExporterHeader(DisplayName = "状态")]
    public string StatusText { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string xbText { get; set; }


    /// <summary>
    ///     当前电脑的 Mac 地址
    /// </summary>
    public string MacAddress { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid UserId { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string TenantName { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid CreatorUserId { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string CreateUserName { get; set; }

    /// <summary>
    ///     录入日期  也可以作为报名日期   当前的版本 报名日期 和 录入日期已经合并
    /// </summary>
    /// <value></value>
    public DateTime CreateTime { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public List<DateTime> CreateTimes { get; set; }


    /// <summary>
    ///     录入日期  (时间戳)
    /// </summary>
    public long CreateTimeStamp { get; set; }


    /// <summary>
    ///     科目一 的考试结果
    /// </summary>
    /// <value></value>
    public string KeMu1Text { get; set; }


    /// <summary>
    ///     科目一 的考试日期
    /// </summary>
    /// <value></value>
    public string KeMu1DateText { get; set; }


    /// <summary>
    ///     科目二 的考试结果
    /// </summary>
    /// <value></value>
    public string KeMu2Text { get; set; }


    /// <summary>
    ///     科目二 的考试日期
    /// </summary>
    /// <value></value>
    public string KeMu2DateText { get; set; }


    /// <summary>
    ///     科目三 的考试结果
    /// </summary>
    /// <value></value>
    public string KeMu3Text { get; set; }


    /// <summary>
    ///     科目三 的考试日期
    /// </summary>
    /// <value></value>
    public string KeMu3DateText { get; set; }


    /// <summary>
    ///     科目四 的考试结果
    /// </summary>
    /// <value></value>
    public string KeMu4Text { get; set; }


    /// <summary>
    ///     科目四 的考试日期
    /// </summary>
    /// <value></value>
    public string KeMu4DateText { get; set; }


    /// <summary>
    ///     长沙驾协   认证时间
    /// </summary>
    /// <value></value>
    public DateTime CertifiedTime { get; set; }


    /// <summary>
    ///     寸照 的图片地址
    /// </summary>
    /// <value></value>
    public string Image0Path { get; set; }


    /// <summary>
    ///     身份证电子照片 的图片地址
    /// </summary>
    /// <value></value>
    public string Image1Path { get; set; }


    /// <summary>
    ///     现场照片的 地址
    /// </summary>
    /// <value></value>
    public string Image2Path { get; set; }


    /// <summary>
    ///     数码照片 的图片地址（一般图片效果优于 寸照 对应的是 广东的数码回执的照片）
    /// </summary>
    /// <value></value>
    public string Image8Path { get; set; }


    /// <summary>
    ///     合同签署现场 地址
    /// </summary>
    /// <value></value>
    public string Image200Path { get; set; }


    /// <summary>
    ///     身份证正反面 地址
    /// </summary>
    /// <value></value>
    public string Image4Path { get; set; }


    /// <summary>
    ///     身份证正面 地址
    /// </summary>
    /// <value></value>
    public string Image40Path { get; set; }


    /// <summary>
    ///     身份证反面 地址
    /// </summary>
    /// <value></value>
    public string Image41Path { get; set; }

    /// <summary>
    ///     所有的应缴的 编号 列表
    /// </summary>
    /// <value></value>
    public List<Guid> ShoudPayIds { get; set; }

    /// <summary>
    ///     所有的缴费的 编号 列表
    /// </summary>
    /// <value></value>
    public List<Guid> PayIds { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public Guid JxCompanyId { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<JxShouldPayOutPut> JxShouldPays { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<JxPayOutPut> JxPays { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<JxStudentExamOutPut> JxExams { get; set; }


    /// <summary>
    ///     查询 包含费用
    /// </summary>
    /// <value></value>
    public List<Guid> CostTypeIds { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string MarkText { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public string MarkColor { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    public List<string> JxMarkColors { get; set; }


    /// <summary>
    /// </summary>
    public int[] djzsxzqhs { get; set; }

    /// <summary>
    /// </summary>
    public int[] lxzsxzqhs { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public new Guid SaleId { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    public new Guid SaleId2 { get; set; }


    /// <summary>
    ///     推荐人 的 用户 Id
    /// </summary>
    /// <value></value>
    public new Guid SaleUserId { get; set; }

    /// <summary>
    ///     第二 推荐人 的 用户 Id
    /// </summary>
    /// <value></value>
    public new Guid SaleUserId2 { get; set; }

    /// <summary>
    ///     第三 推荐人 的 用户 Id
    /// </summary>
    /// <value></value>
    public new Guid SaleUserId3 { get; set; }


    /// <summary>
    /// </summary>
    public new Guid JxFieldId { get; set; }


    /// <summary>
    /// </summary>
    public new Guid TeachOneUserId { get; set; }

    /// <summary>
    /// </summary>
    public new Guid TeachTwoUserId { get; set; }

    /// <summary>
    /// </summary>
    public new Guid TeachThreeUserId { get; set; }


    /// <summary>
    /// </summary>
    public string gjName { get; set; }

    /// <summary>
    /// </summary>
    public long csrqTimeStamp { get; set; }

    /// <summary>
    /// </summary>
    public long CreatdeTimeStamp { get; set; }

    /// <summary>
    /// </summary>
    public string ShareMarkColor { get; set; }

    /// <summary>
    /// </summary>
    public string ShareMarkText { get; set; }

    /// <summary>
    /// </summary>
    public string ShareMarkCreateUserName { get; set; }

    /// <summary>
    /// </summary>
    public DateTime ShareMarkCreateTime { get; set; }

    /// <summary>
    /// </summary>
    public string DocStatus { get; set; }


    /// <summary>
    /// </summary>
    public string SaleName { get; set; }


    /// <summary>
    /// </summary>
    public string SaleName2 { get; set; }


    /// <summary>
    /// </summary>
    public List<JxShouldPayDetailOutPut> JxShouldPayDetails { get; set; }


    /// <summary>
    ///     打印备注
    /// </summary>
    public string PrintRemark { get; set; }

    /// <summary>
    ///     待培 打印备注
    /// </summary>
    public string PrintWaitStudyRemark { get; set; }


    /// <summary>
    /// </summary>
    /// <value></value>
    [ExporterHeader(DisplayName = "合计")]
    public int DataCount { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    [ExporterHeader(DisplayName = "车型合计")]
    public string CarTypeCount { get; set; }

    /// <summary>
    /// </summary>
    /// <value></value>
    [ExporterHeader(DisplayName = "班别合计")]
    public string JxClassNameCount { get; set; }


    [ExporterHeader(DisplayName = "自定义字段")]
    public List<JxMyStudentColumnOutPut> MyStudentColumnDatas { get; set; }


    [ExporterHeader(DisplayName = "自定义字段  合并 Id:Name:Value 的格式")]
    public string MyStudentColumnDataText { get; set; }


    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string JxStudentImformationStatusText { get; set; }


    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string PayDetails { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public string NoPayDetails { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public int Age { get; set; }


    /// <summary>
    ///  统计用的 用户名称
    /// </summary>
    public string ComputerUserName { get; set; }

    /// <summary>
    /// 统计用的 用户 Id
    /// </summary>
    public Guid ComputerUserId { get; set; }

    /// <summary>
    /// 工资设计的 Id
    /// </summary>
    public WagesDesignOutPut Design { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Guid DesignId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int DesignSysId { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public JxClassEntity JxClass { get; set; }


    /// <summary>
    /// 计算工资的时候 显示 补考的次数
    /// </summary>
    public string MakeUpExam { get; set; }


    /// <summary>
    /// 计算工资的时候 显示 补考的结算
    /// </summary>
    public decimal? MakeUpSettlement { get; set; }


    /// <summary>
    /// 计算工资的时候 显示 其他类型的结算
    /// </summary>
    public decimal? OtherSettlement { get; set; }


    /// <summary>
    /// 计算工资的时候 显示 其他类型的结算 描述
    /// </summary>
    public string OtherSettlementDetails { get; set; }

    /// <summary>
    /// 计算工资的时候 显示 合格结算
    /// </summary>
    public decimal? QualifiedSettlement { get; set; }

    /// <summary>
    /// 计算工资的时候 显示 招生结算
    /// </summary>
    public decimal? RecruitmentSettlement { get; set; }

    /// <summary>
    /// 计算工资的时候 显示 年底结算
    /// </summary>
    public decimal? YearEndSettlement { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public JxWagesKeMuEnum WagesKeMuId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<WagesResultEntity> Wages { get; set; }

    /// <summary>
    /// 合计
    /// </summary>
    public decimal? Total { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string SourceName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string TeachTwoCarNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string TeachThreeCarNumber { get; set; }

}

public class JxStudentUpdateCoachInPut
{
    /// <summary>
    /// </summary>
    public List<Guid> Ids { get; set; }

    /// <summary>
    /// </summary>
    public int KeMuId { get; set; }

    /// <summary>
    /// </summary>
    public Guid UserId { get; set; }
}



public class JxStudentCountOutPut
{
    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public Guid SaleUserId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string SaleUserName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public int DataCount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public Guid JxDeptId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string JxDeptName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public Guid JxFieldId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string JxFieldName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public Guid SaleJxDeptId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string SaleJxDeptName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public Guid CurrentSaleJxDeptId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string CurrentSaleJxDeptName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public Guid SaleJxFieldId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string SaleJxFieldName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public Guid SourceId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string SourceName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public Guid CreateUserId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string CreateUserName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public Guid CategoryId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public string CategoryName { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public string CarType { get; set; }

    /// <summary>
    /// 时间段
    /// </summary>
    public string TimePeriod { get; set; }
}