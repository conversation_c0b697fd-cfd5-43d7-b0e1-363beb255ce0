<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PandaServer.Web.Core</name>
    </assembly>
    <members>
        <member name="T:PandaServer.Web.Core.Attributes.SkipValidationAttribute">
            <summary>
            标记不需要进行请求验证的接口
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.ArcSoftComponent">
            <summary>
                虹软的  相关组件
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.AuthComponent">
            <summary>
                认证相关组件
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.LoggingMonitorComponent">
            <summary>
                LoggingMonitor操作日志写入数据库插件
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.PluginSettingComponent">
            <summary>
                插件设置组件
                模拟 ConfigureService
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.PluginSettingsApplicationComponent">
            <summary>
                插件设置组件
                模拟 Configure
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.WebSettingsComponent">
            <summary>
                Web设置组件
                模拟 ConfigureService
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.WebSettingsApplicationComponent">
            <summary>
                Web设置组件
                模拟 Configure
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.Auth.LoginController">
            <summary>
                登录授权相关服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.Auth.QrCodeController.GetQrCode">
            <summary>
            获取登录二维码
            </summary>
            <returns>二维码信息</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.QrCodeStatusController">
            <summary>
            二维码登录相关服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.QrCodeStatusController.ScanConfirm(PandaServer.Web.Core.Controllers.Auth.QrCodeConfirmInput)">
            <summary>
            扫码确认（小程序调用）
            </summary>
            <param name="inPut">传入的参数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.QrCodeStatusController.CheckStatus(System.String)">
            <summary>
            检查二维码状态
            </summary>
            <param name="qrCodeId">二维码ID</param>
            <returns>二维码状态信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.QrCodeStatusController.ConfirmLogin(PandaServer.Web.Core.Controllers.Auth.QrCodeConfirmInput)">
            <summary>
            确认登录（移动端调用）
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.CreateUserCodeController">
            <summary>
                创建用户二维码
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.Dto._MenuOutPut`1">
            <summary>
                菜单输出参数
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Auth.Dto._MenuOutPut`1.key">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Auth.Dto._MenuOutPut`1.path">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Auth.Dto._MenuOutPut`1.name">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Auth.Dto._MenuOutPut`1.EnCode">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Auth.Dto._MenuOutPut`1.icon">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Auth.Dto._MenuOutPut`1.sort">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Auth.Dto._MenuOutPut`1.ParentId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Auth.Dto._MenuOutPut`1.children">
            <summary>
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.MenuController.GetMenu">
            <summary>
                获得 当前登录用户的菜单
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.MenuController.GetAllMenu">
            <summary>
                获得全部的菜单
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.MenuController.GetMyTenantMenu">
            <summary>
                获得公司的 全部菜单
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.MenuController.GetTenantMenuIds(PandaServer.System.Services.SystemManage.Dtos.MenuPageInPut)">
            <summary>
                获得指定租户的菜单 的 Id
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.MenuController.GetRoleMenuIds(PandaServer.System.Services.SystemManage.Dtos.MenuPageInPut)">
            <summary>
                获得指定权限的菜单 的 Id
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.Menu.RoleMenuController.SetRoleMenuIds(PandaServer.System.Services.SystemManage.Dtos.MenuIdsInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.MqttAuthController">
            <summary>
                Mqtt 相关的验证方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.MyController">
            <summary>
            当前登录的相关信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.MyMenuController">
            <summary>
            我的菜单
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.MyMenuController.GetMyMenu">
            <summary>
                获得 当前登录用户的菜单
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.MyMenuController.GetMyWxMenu">
            <summary>
                获得 当前登录用户的菜单
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.MyMenuController.GetMyReviewMenu">
            <summary>
                获得 当前登录用户的审核菜单
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.RoleController">
            <summary>
                权限相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.RoleController.GetRoleSelectList(PandaServer.System.Services.SystemManage.Dtos.RolePageInPut)">
            <summary>
                获得全部角色列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.RoleController.GetRoleList(PandaServer.System.Services.SystemManage.Dtos.RolePageInPut)">
            <summary>
                角色的查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.RoleController.GetRoleInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.RoleController.DeleteRoleInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.RoleController.SetRoleInfo(PandaServer.System.Services.SystemManage.Dtos.RoleInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.UserLocalStorageController">
            <summary>
                用户相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.WxUserChangeController">
            <summary>
                微信用户 切换
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.WxUserChangeController.GetTenantList">
            <summary>
            当前登录用户 的手机号码 管理啊的账号
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Auth.WxUserChangeController.ChangeUser(System.Guid)">
            <summary>
            切换用户
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Auth.WxUserController">
            <summary>
                微信 用户注册  和 审核的相关方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Sys.UserController">
            <summary>
                用户相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.AddressController">
            <summary>
                地理位置处理
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.AntConfigController">
            <summary>
                Antd 系统的 前面读取 相关的配置
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.AntConfigController.LayoutSettings">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.CarController">
            <summary>
            车辆管理控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.#ctor(PandaServer.System.Services.Base.Car.ICarService,PandaServer.System.Services.Base.Car.ICarImageService,PandaServer.System.Services.Parking.IParkingCarService,PandaServer.System.Services.ImportExport.IImportExportService,PandaServer.System.Services.SystemOrganize.ITenantService,PandaServer.System.Services.Wx.IWxConfigService,PandaServer.System.Services.Base.ICityService,PandaServer.System.Services.SystemManage.IExcelDesignService,PandaServer.System.Services.SystemManage.IUserCategoryService)">
            <summary>
            构造函数，注入所需服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.GetCarList(PandaServer.System.Services.Base.Dtos.CarPageInPut)">
            <summary>
            获取车辆列表
            </summary>
            <param name="input">查询参数</param>
            <returns>车辆列表数据</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.ExportCarList(PandaServer.System.Services.Base.Dtos.CarPageInPut)">
            <summary>
            导出车辆列表到Excel
            </summary>
            <param name="input">导出参数</param>
            <returns>Excel文件信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.GetCarSelectList(PandaServer.System.Services.Base.Dtos.CarPageInPut)">
            <summary>
            获取车辆下拉选择列表
            </summary>
            <param name="input">查询参数</param>
            <returns>车辆选择列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.GetCarStatusSelectList">
            <summary>
            获取车辆状态下拉列表
            </summary>
            <returns>车辆状态列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.GetStudyDeviceStatusSelectList">
            <summary>
            获取车辆计时设备状态下拉列表
            </summary>
            <returns>计时设备状态列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.GetWhiteExcel">
            <summary>
            获取空白的Excel模板
            </summary>
            <returns>Excel模板文件信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.UploadExcel(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            导入车辆Excel文件
            </summary>
            <param name="file">Excel文件</param>
            <returns>导入结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.SetLastFreeParkingTime(PandaServer.System.Services.Parking.Dto.ParkingCarInPut)">
            <summary>
            设置最后的停车时间
            </summary>
            <param name="input">停车信息</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.GetImageList(PandaServer.Core.BaseIdInput)">
            <summary>
            获取车辆图片列表
            </summary>
            <param name="input">查询参数</param>
            <returns>图片列表</returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException">参数错误异常</exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.UploadImageData(PandaServer.System.Services.Base.Dtos.CarImageInPut)">
            <summary>
            上传Base64格式的图片
            </summary>
            <param name="input">图片信息</param>
            <returns>上传结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.UploadImage(System.Guid,System.Int32,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传图片文件
            </summary>
            <param name="CarId">车辆ID</param>
            <param name="imageId">图片类型ID</param>
            <param name="file">图片文件</param>
            <returns>上传结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.DeleteImage(PandaServer.Core.BaseIdInput)">
            <summary>
            删除车辆图片
            </summary>
            <param name="input">图片ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.GetCarBarCode(PandaServer.Core.BaseIdInput)">
            <summary>
            获取车辆信息上传的二维码
            </summary>
            <param name="input">租户ID</param>
            <returns>二维码信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.BeiAn(PandaServer.Core.BaseIdInput)">
            <summary>
            提交车辆备案信息
            </summary>
            <param name="input">备案信息</param>
            <returns>备案结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarController.SetJTNum(PandaServer.System.Services.Base.Dtos.CarInPut)">
            <summary>
            手动修改车辆备案编号
            </summary>
            <param name="input">备案编号信息</param>
            <returns>修改结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.CarGroupController">
            <summary>
                车辆分组相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarGroupController.GetCarGroupList(PandaServer.System.Services.Base.Dtos.CarGroupPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarGroupController.GetCarGroupInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarGroupController.SetCarGroupInfo(PandaServer.System.Services.Base.Dtos.CarGroupInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarGroupController.DeleteGarGroupInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                删除分组信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.CarInfoController">
            <summary>
            车辆信息管理控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarInfoController.#ctor(PandaServer.System.Services.Base.Car.ICarService)">
            <summary>
            构造函数，注入所需服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarInfoController.GetCarInfo(System.Guid)">
            <summary>
            获取车辆详细信息
            </summary>
            <param name="id">车辆ID</param>
            <returns>车辆详细信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarInfoController.SetCarInfo(System.Guid,PandaServer.System.Services.Base.Dtos.CarInPut)">
            <summary>
            新增或修改车辆信息
            </summary>
            <param name="id">车辆ID</param>
            <param name="inPut">车辆信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CarInfoController.DeleteCarInfo(System.Guid)">
            <summary>
            删除车辆信息
            </summary>
            <param name="id">车辆ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.CityController">
            <summary>
                国内城市相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CityController.GetProvinceList">
            <summary>
                省份的列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CityController.GetCityList(PandaServer.Web.Core.Controllers.Base.Dto.CityInPut)">
            <summary>
                相关省份的城市列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CityController.GetAreaList(PandaServer.Web.Core.Controllers.Base.Dto.CityInPut)">
            <summary>
                相关城市下面区的列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CityController.GetAllCityData">
            <summary>
                全局的 省、市、区 全部数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.CityController.GetZip(PandaServer.Web.Core.Controllers.Base.Dto.CityInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.ConfigController">
            <summary>
                配置相关
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.CityInPut.Id">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.MustSaleId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.ShowSaleId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.MustMac">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.IdCardDown">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxAppId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.MpAppId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.CreateStatus">
            <summary>
                报名录入以后得  初始的状态
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.TuitionCostTypeId">
            <summary>
                默认的 学费的  费用类型
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.AddTuitionCostTypeId">
            <summary>
                默认的 学费补缴的  费用类型
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.CloseBusiness">
            <summary>
                关闭 业务信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.CreateModifyTuition">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.CreatedForbiddenModify">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.AfterPayForbiddenModify">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.AfterDayForbiddenModify">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.AccountFollowSaleUser">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.AccountFollowPayJxDept">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.ModifyAddShouldPayMoney">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.OrderCarMethod">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.FaceMaxDistance">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.PrinterTop">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.PrinterBottom">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.PrinterLeft">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.PrinterRight">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.PrinterNoPhone">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.PrinterPaper">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.PayIdRule">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.UpdateByJxmcs">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.MustSelectJxFiledId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.AssignCoachUpdateResultExam">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.DontUpdateExamCoachAfterExam">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.DebtForbiddenAssignCoach">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.YwztShowLastExam">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxNoShowRemark">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxNoShowPay">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxNoShowCost">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxNoShowJxField">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxNoShowTeachOne">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxNoShowTeachTwo">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxNoShowTeachThree">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxNoSendOrderExam">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.WxNoSendExamResult">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.NoLockJxDeptCreateStudent">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.UserPhoneNoMask">
            <summary>
            
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.SMSCompany">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.SMSAccount">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.SMSPWD">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.SMSSign">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.ConfigInfo.HideFinanceData">
            <summary>
            默认不显示财务数据
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.GetCarTypeListInPut.Exclude">
            <summary>
                排除的车型
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.GetCarTypeListInPut.ShowDetail">
            <summary>
                显示详情
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.NationOutPut.Id">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Base.Dto.NationOutPut.Name">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.ImageController">
            <summary>
                图片相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.ImageController.DecodeQrCode(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
                识别图片中的二维码内容
            </summary>
            <param name="file">图片文件</param>
            <returns>二维码内容</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.IpController">
            <summary>
                配置相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.IpController.IndexPost">
            <summary>
                返回当前设备的 ip 地址
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.IpController.DetailPost">
            <summary>
                返回当前设备的 详细 信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.NationController">
            <summary>
                用户相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.NationController.GetNationSelectList">
            <summary>
                返回国家的列表
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.SelectController">
            <summary>
                用户相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Base.StaffController">
            <summary>
                人员相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.GetStaffSelectList(PandaServer.System.Services.SystemOrganize.Dtos.StaffPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.GetStaffTypeSelect">
            <summary>
                获得  职员种类的  下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.SetStaffInfo(PandaServer.System.Services.SystemOrganize.Dtos.StaffInPut)">
            <summary>
                提交 保存 员工信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.GetStaffInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.GetImageList(PandaServer.Core.BaseIdInput)">
            <summary>
                获得图片的列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.UploadImageData(PandaServer.System.Services.SystemOrganize.Dtos.StaffImageInPut)">
            <summary>
                上传 Base 64 的图片
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.UploadImage(System.Guid,System.Int32,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
                上传 文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.DeleteImage(PandaServer.Core.BaseIdInput)">
            <summary>
                删除图片
            </summary>
            <param name="inPut"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.GetStaffBarCode(PandaServer.Core.BaseIdInput)">
            <summary>
                获取 员工信息上传的  二维码
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.DeleteStaffInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                删除车辆信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Base.StaffController.BeiAn(PandaServer.Core.BaseIdInput)">
            <summary>
                教练备案信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.ClassController">
            <summary>
                幼儿园 班级相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ClassController.GetClassSelectList(PandaServer.System.Services.Child.Dto.ChildClassPageInPut)">
            <summary>
                返回下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ClassController.GetClassList(PandaServer.System.Services.Child.Dto.ChildClassPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ClassController.GetClassInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ClassController.DeleteClassInfo(System.Guid)">
            <summary>
                删除 班级
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ClassController.SetClassInfo(PandaServer.System.Services.Child.Dto.ChildClassInPut)">
            <summary>
                提交 并 保存
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.CostTypeController">
            <summary>
                幼儿园 费用类型
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CostTypeController.GetCostTypeSelectList(PandaServer.System.Services.Child.Dto.ChildCostTypePageInPut)">
            <summary>
                返回下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CostTypeController.GetCostTypeList(PandaServer.System.Services.Child.Dto.ChildCostTypePageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CostTypeController.SetCostTypeInfo(PandaServer.System.Services.Child.Dto.ChildCostTypePageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CostTypeController.GetCostTypeOnlineList">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CostTypeController.GetCostTypeInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CostTypeController.DeleteCostTypeInfo(PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CostTypeController.GetCostTypePayInfo(PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo)">
            <summary>
                通过 费用类型 直接返回 相关的支付 信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.CouponController">
            <summary>
                幼儿园 优惠券
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.GetCouponSelectList(PandaServer.System.Services.Child.Dto.ChildCouponPageInPut)">
            <summary>
                返回下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.GetCouponList(PandaServer.System.Services.Child.Dto.ChildCouponPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.GetCouponInfo(PandaServer.System.Services.Child.Dto.ChildCouponPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.SetCouponInfo(PandaServer.System.Services.Child.Dto.ChildCouponPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.DeleteCouponInfo(System.Guid)">
            <summary>
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.SetCouponToStudent(PandaServer.System.Services.Child.Dto.ChildCouponPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.GetCouponListByStudent(PandaServer.System.Services.Child.Dto.ChildCouponPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.GetMyCouponList(PandaServer.System.Services.Child.Dto.ChildCouponPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.SetNewCouponToStudents(PandaServer.System.Services.Child.Dto.ChildCouponPageInPut)">
            <summary>
                生成新的优惠券 并且  添加
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.UpdateCouponOrder(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.CouponController.CancelCouponOrder(PandaServer.Core.BaseIdInput)">
            <summary>
                取消这个优惠的使用
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo.Name">
            <summary>
                班级的名称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo.PayMoney">
            <summary>
                费用的金额
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo.Remark">
            <summary>
                备注
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo.SortCode">
            <summary>
                排序
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo.IsWxItem">
            <summary>
                上架商品
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo.CreateUserName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildCostTypeInfo.CreateTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.Ids">
            <summary>
                提交订单的时候的 编号的数组
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.ShouldPayId">
            <summary>
                应缴的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.PayTypeId">
            <summary>
                应缴的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.CostTypeId">
            <summary>
                费用类型的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.PayWayId">
            <summary>
                付款方式的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.StudentId">
            <summary>
                学员的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.PayMoney">
            <summary>
                付款的金额
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.PayTime">
            <summary>
                付款的时间
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.PayTimes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.Remark">
            <summary>
                备注
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.MerchantOrderSn">
            <summary>
                商户订单号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.OrderSn">
            <summary>
                付呗等三方系统订单号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.ChannelOrderSn">
            <summary>
                通道订单号，微信订单号、支付宝订单号等
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.InsOrderSn">
            <summary>
                收单机构订单号(微信、支付宝、银行)
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.OrderId">
            <summary>
                OrderEntity 订单编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.OrderDetailId">
            <summary>
                OrderDetailEntity 订单编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.CreateUserName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.CreatorTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.CreatorTimes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.CostTypeName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.PayWayName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.xm">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.sfzmhm">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildPayInfo.SortCode">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.CostTypeId">
            <summary>
                费用类型的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.StudentId">
            <summary>
                学员的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.PayMoney">
            <summary>
                付款的金额
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.SortCode">
            <summary>
                费用的排序
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.TemplateDetailId">
            <summary>
                模板明细的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.Remark">
            <summary>
                备注
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.NoPay">
            <summary>
                未付款的金额
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.CostTypeName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.CreateUserName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.CreatorTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.xm">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.sfzmhm">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayInfo.NoPays">
            <summary>
                欠费状态
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.TemplateId">
            <summary>
                模板的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.CostTypeId">
            <summary>
                费用类型的编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.PayMoney">
            <summary>
                费用的金额
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.SortCode">
            <summary>
                费用的排序
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.Remark">
            <summary>
                备注
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.CreateUserName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.CreatorTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.CostTypeName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.ToTemplateId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.FromTemplateId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.FromRemark">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.ToRemark">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateDetailInfo.StudentIds">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateInfo.Name">
            <summary>
                模板名称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateInfo.Remark">
            <summary>
                备注
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateInfo.CreateUserName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto.ChildShouldPayTemplateInfo.CreatorTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.bmrq">
            <summary>
                报名日期
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.xm">
            <summary>
                姓名
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.NickName">
            <summary>
                昵称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.Status">
            <summary>
                学员状态
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.StatusName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.xb">
            <summary>
                性别
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.sfzmmc">
            <summary>
                学员证件类型
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.sfzmhm">
            <summary>
                学员证件号码
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.csrq">
            <summary>
                出生日期
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.yddh">
            <summary>
                联系的手机号码
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.dz">
            <summary>
                家庭地址
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.StudentNumber">
            <summary>
                学号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.Remark">
            <summary>
                备注
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.ClassId">
            <summary>
                班别编号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.ClassName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.ClassNickName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.zp">
            <summary>
                照片
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.ShouldPayMoney">
            <summary>
                应缴费用
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.AlreadyPayMoney">
            <summary>
                已缴费用
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.Introducer">
            <summary>
                介绍人员
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.PayMonth">
            <summary>
                是否支持月缴
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.PayMonths">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.bmrqs">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.CreateUserName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.CreatorTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.Age">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.StudentIds">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.Statuss">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Child.Dto._ChildStudentInfo.NoPays">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.PayController">
            <summary>
                幼儿园 缴费相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.PayController.GetPayListByShouldPay(PandaServer.System.Services.Child.Dto.ChildPayPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.PayController.SetPayOrder(PandaServer.System.Services.Child.Dto.ChildShouldPayPageInPut)">
            <summary>
                创建临时订单数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.PayController.UpdateMoney(PandaServer.System.Services.Child.Dto.ChildPayPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.PayController.Delete(PandaServer.Core.BaseIdsInput)">
            <summary>
                删除相关的项目
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.RefundController">
            <summary>
            退款控制器
            处理退款相关的请求
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.RefundController.Refund(PandaServer.System.Services.Child.Dto.ChildRefundInPut)">
            <summary>
                退费
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.RefundController.Page(PandaServer.System.Services.Child.Dto.ChildRefundPageInPut)">
            <summary>
                分页查询
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.ShouldPayController">
            <summary>
                幼儿园 挂账的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayController.GetShouldPayList(PandaServer.System.Services.Child.Dto.ChildShouldPayPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayController.GetShouldPayListByStudent(PandaServer.System.Services.Child.Dto.ChildShouldPayPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayController.GetMyShouldPayList">
            <summary>
                获取 我的全部账单
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayController.GetShouldPayInfo(PandaServer.System.Services.Child.Dto.ChildShouldPayInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayController.SetShouldPayInfo(PandaServer.System.Services.Child.Dto.ChildShouldPayInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayController.UpdateNoPay(PandaServer.System.Services.Child.Dto.ChildShouldPayInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayController.DeleteShouldPay(System.Guid)">
            <summary>
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.ShouldPayTemplateDetailController">
            <summary>
                幼儿园 挂账模板  明细
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayTemplateDetailController.GetShouldPayTemplateDetailList(PandaServer.System.Services.Child.Dto.ChildShouldPayTemplateDetailPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayTemplateDetailController.DeleteShouldPayTemplateDetailInfo(System.Guid)">
            <summary>
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayTemplateDetailController.SetShouldPayTemplateDetailInfo(PandaServer.System.Services.Child.Dto.ChildShouldPayTemplateDetailInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayTemplateDetailController.CopyShouldPayTemplateDetailListInfo(PandaServer.System.Services.Child.Dto.ChildShouldPayTemplateDetailInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.ShouldPayTemplateDetailController.SetShouldPayTemplateDetailListToStudent(PandaServer.System.Services.Child.Dto.ChildShouldPayTemplateDetailInPut)">
            <summary>
                通过 模板  对学员 添加挂账
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.StudentController">
            <summary>
                幼儿园 学员的服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.StudentController.GetStudentInfo(PandaServer.System.Services.Child.Dto.ChildStudentInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.StudentController.GetStudentList(PandaServer.System.Services.Child.Dto.ChildStudentPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.StudentController.SetStudentInfo(PandaServer.System.Services.Child.Dto.ChildStudentInPut)">
            <summary>
                提交 并 保存
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.StudentController.GetStatusSelectList">
            <summary>
                学员状态的 下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.StudentController.SetStatus(PandaServer.System.Services.Child.Dto.ChildStudentInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Child.StudentController.SetClassId(PandaServer.System.Services.Child.Dto.ChildStudentInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.WxConfigController">
            <summary>
                幼儿园 微信配置相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Child.WxLoginController">
            <summary>
                幼儿园 微信登录相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Childl.ShouldPayTemplateController">
            <summary>
                幼儿园 挂账模板
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Childl.ShouldPayTemplateController.GetShouldPayTemplateSelectList(PandaServer.System.Services.Child.Dto.ChildShouldPayTemplatePageInPut)">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Childl.ShouldPayTemplateController.GetShouldPayTemplateList(PandaServer.System.Services.Child.Dto.ChildShouldPayTemplatePageInPut)">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Childl.ShouldPayTemplateController.GetShouldPayTemplateInfo(PandaServer.System.Services.Child.Dto.ChildShouldPayTemplateInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Childl.ShouldPayTemplateController.SetShouldPayTemplateInfo(PandaServer.System.Services.Child.Dto.ChildShouldPayTemplateInPut)">
            <summary>
                提交 并 保存
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Childl.ShouldPayTemplateController.DeleteShouldPayTemplateList(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Config.ConfigController">
            <summary>
                系统全局配置 相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Config.ConfigController.GetConfigList(PandaServer.System.Services.Config.Dto.ConfigPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject1FirstExamRefund">
            <summary>
            科一初考退费
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject1FirstExamRefundCostTypeId">
            <summary>
            科一初考退费成本类型ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject1RetakeExamRefund">
            <summary>
            科一补考退费
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject1RetakeExamRefundCostTypeId">
            <summary>
            科一补考退费成本类型ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject2FirstExamRefund">
            <summary>
            科二初考退费
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject2FirstExamRefundCostTypeId">
            <summary>
            科二初考退费成本类型ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject2RetakeExamRefund">
            <summary>
            科二补考退费
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject2RetakeExamRefundCostTypeId">
            <summary>
            科二补考退费成本类型ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject3FirstExamRefund">
            <summary>
            科三初考退费
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject3FirstExamRefundCostTypeId">
            <summary>
            科三初考退费成本类型ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject3RetakeExamRefund">
            <summary>
            科三补考退费
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.Subject3RetakeExamRefundCostTypeId">
            <summary>
            科三补考退费成本类型ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.RefundMethod">
            <summary>
            退款方式
            BankTransfer - 通过银行转账方式退款
            WeChatRedPacket - 通过微信红包方式退款
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.ComputerAccountId">
            <summary>
            结算账户
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.FirstExamJxClassIds">
            <summary>
            初考可退班别ID列表，多个用逗号分隔
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.SecondExamJxClassIds">
            <summary>
            补考可退班别ID列表，多个用逗号分隔
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.MakeCardFee">
            <summary>
            制证费金额
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.MakeCardFeeCostTypeId">
            <summary>
            制证费挂账支出费用类型ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.BankScreenshotCount">
            <summary>
            银行转账截图数量
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.BankScreenPDFCount">
            <summary>
            银行转账PDF数量
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.BankScreenContent">
            <summary>
            银行转账 上传页面的 提示
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo.EnableOnlineApplication">
            <summary>
            是否允许学员线上申请退款
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Config.JxConfigController">
            <summary>
                系统全局配置 相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Config.JxRefundConfigController">
            <summary>
                驾校退款配置相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Config.JxRefundConfigController.GetRefundConfig(PandaServer.Core.BaseIdInput)">
            <summary>
            获取退款配置信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Config.JxRefundConfigController.SetRefundConfig(PandaServer.Web.Core.Controllers.Config.Dtos.RefundConfigInfo)">
            <summary>
            设置退款配置信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.DataBase.DataBaseController">
            <summary>
                系统全局数据库 的初始化
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.DataBase.DataBaseController.MakeDataBase">
            <summary>
                初始化数据库
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.DataBase.DataBaseController.CreateGlobalAdmin">
            <summary>
                初始化全局管理员
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.DataBase.DataBaseController.InitUserLogOn">
            <summary>
                初始化 登录信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.DataBase.DataBaseController.InitMenu">
            <summary>
                初始化 登录信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.DataBase.DataBaseController.InitCsjxData">
            <summary>
                初始化 长沙驾协 的全部驾校数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.DataBase.DBFirstController">
            <summary>
                系统全局数据库 的初始化
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.DataBase.DBFirstController.Make">
            <summary>
                DbFirst 生成 模型文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.DataBase.DBFirstController.MakeFiles(System.String)">
            <summary>
            </summary>
            <param name="DbName"></param>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ESign.ESignConfigController">
            <summary>
            驾校 电子签名配置相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ESign.ESignConfigController.GetConfig">
            <summary>
            获取e签宝配置信息
            </summary>
            <returns>配置信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ESign.ESignConfigController.SetConfig(System.Object)">
            <summary>
            设置e签宝配置
            </summary>
            <returns>是否设置成功</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ESign.ESignTemplateController">
            <summary>
                驾校 电子签名模板相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ESign.ESignTemplateController.GetTemplateComponents(System.Object)">
            <summary>
            查询合同模板中控件详情
            </summary>
            <returns>模板控件详情</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ESign.ESignTemplateController.GetTemplateList(System.Object)">
            <summary>
            查询合同模板列表
            </summary>
            <returns>合同模板列表</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Camera.CameraController">
            <summary>
            考场  关于  车辆 计圈摄像头 的相关服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.CashCouponController">
            <summary>
            页面 返利的相关页面
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.CarController">
            <summary>
            收银员车辆管理控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.CarController.#ctor(PandaServer.System.Services.Base.Car.ICarService)">
            <summary>
            构造函数，注入所需服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.CarController.GetList">
            <summary>
            获取车辆列表
            </summary>
            <returns>车辆列表数据</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.DoorController">
            <summary>
            考场  关于 门禁相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.DoorController.GetDoorConfig">
            <summary>
            获取闸机配置信息
            </summary>
            <returns>闸机配置信息</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.ExamController">
            <summary>
            考场  关于 考试名单
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.FieldController">
            <summary>
            考场  关于 场地相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.JxDeviceController">
            <summary>
            考场  关于 场地相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.PayController">
            <summary>
            考场  关于 付款相关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.PlanQueenController">
            <summary>
            考场  关于 场次预约的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.SaleController">
            <summary>
            考场  关于 商品信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.ShopController">
            <summary>
            考场  关于 商品信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.StudyController">
            <summary>
            收银员学习管理控制器
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.TouPiaoController">
            <summary>
            长沙点评
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.TouPiaoController.Login(System.Object)">
            <summary>
            登录接口
            </summary>
            <returns>登录结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.TouPiaoController.GetCompany">
            <summary>
            获取全部的考场信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Cashier.TouPiaoController.LoginAndCommentAdd(System.Object)">
            <summary>
            登录并提交点评
            </summary>
            <param name="input">包含所有点评参数的 dynamic 对象</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.CouponChargeController">
            <summary>
            考场服务-红包充值
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.CouponChargeController.GetChargeInfo(System.Guid)">
            <summary>
            查询红包充值信息
            </summary>
            <param name="id">考场ID</param>
            <returns>红包充值信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.CouponChargeController.Charge(System.Guid,PandaServer.System.Services.Pay.Dto.Coupon.CashCouponInPut)">
            <summary>
            红包充值
            </summary>
            <param name="id">考场ID</param>
            <param name="inPut">充值信息</param>
            <returns></returns>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Dto.PoliceKcInPut.PoliceYm">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Dto.PoliceKcInPut.ym">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Dto.PoliceKcInPut.fzjg">
            <summary>
            
            </summary> <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Dto.PoliceKcInPut.Subject">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.FieldController">
            <summary>
                考场相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.FieldController.GetFieldSelectList(PandaServer.System.Services.Student.Dto.ExamSite.FieldPageInPut)">
            <summary>
                获取全部考场的
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.FieldController.GetFieldList(PandaServer.System.Services.Student.Dto.ExamSite.FieldPageInPut)">
            <summary>
                考试场地的列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.FieldController.GetFieldInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                通过编号获取考试场地信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.FieldController.SetFieldInfo(PandaServer.System.Services.Student.Dto.ExamSite.FieldInPut)">
            <summary>
            
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PayController">
            <summary>
                考场收费相关的信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PlanController">
            <summary>
                考场排队计划
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PlanDetailOpenController">
            <summary>
                考场开关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PointCouponController">
            <summary>
                积分返现的记录
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PointCouponController.GetPointCouponList(PandaServer.System.Services.Pay.Dto.Coupon.PointCouponPageInPut)">
            <summary>
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PointCouponController.ExportPointCouponList(PandaServer.System.Services.Pay.Dto.Coupon.PointCouponPageInPut)">
            <summary>
                导出
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PoliceFieldController">
            <summary>
                登录授权相关服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PoliceFieldController.GetFzjgList">
            <summary>
                读取全部的发证机关
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PoliceFieldController.GetFzjgDetailList(PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Dto.PoliceKcInPut)">
            <summary>
                读取全部的发证机关的详情
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.PoliceFieldController.GetKcList(PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Dto.PoliceKcInPut)">
            <summary>
                获取全部的考场
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.RechargeApplicationController">
            <summary>
            
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.RechargeApplicationController.Create(PandaServer.System.Services.Pay.Dto.Coupon.RechargeApplicationInPut)">
            <summary>
            创建充值申请
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.RechargeApplicationController.Page(PandaServer.System.Services.Pay.Dto.Coupon.RechargeApplicationPageInPut)">
            <summary>
            分页查询充值申请列表
            </summary>
            <param name="input">查询参数</param>
            <returns>分页结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.RefundController">
            <summary>
                考场退款相关的信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.RefundController.SubmitRefund(System.Guid,PandaServer.System.Services.Pay.Dto.RefundInPut)">
            <summary>
            SaleE 的退款申请
            </summary>
            <param name="saleId">销售ID</param>
            <param name="input">退款申请信息</param>
            <returns>退款申请结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.RefundController.AuditRefund(System.Guid,PandaServer.System.Services.Pay.Dto.RefundInPut)">
            <summary>
            审核退费申请
            </summary>
            <param name="id">退款申请ID</param>
            <param name="input">审核信息</param>
            <returns>审核结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.RefundController.GetRefundList(PandaServer.System.Services.Pay.Dto.RefundPageInPut)">
            <summary>
            获取退费申请列表
            </summary>
            <param name="input">分页查询参数</param>
            <returns>退费申请列表分页数据</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.RefundController.GetRefundDetail(System.Guid)">
            <summary>
            获取退费申请详情
            </summary>
            <param name="id">申请ID</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.SaleInfoController">
            <summary>
                考场收费相关的信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.SaleInfoPrintController">
            <summary>
                考场 票据打印相关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.StudyController">
            <summary>
            
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.StudyQueenController">
            <summary>
            
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.CashCouponController">
            <summary>
            考场  关于  微信端 考试相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.CouponController">
            <summary>
            考场  关于  微信端 考试相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.ExamController">
            <summary>
            考场  关于  微信端 考试相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.FieldController">
            <summary>
            考场  关于  微信端 场地相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.FiledTicketController">
            <summary>
            考场  关于  微信端 场地相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.PayController">
            <summary>
            考场  关于  小程序 支付
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.PayResultController">
            <summary>
            考场  关于  小程序 支付
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.PlanController">
            <summary>
            考场  关于  微信端 场次预约计划
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.PlanQueenController">
            <summary>
            考场  关于  微信端 场次预约
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.PointCouponController">
            <summary>
            考场  关于  微信端 考试相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.RechargeApplicationController">
            <summary>
            考场 微信端 充值申请相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.RechargeApplicationController.Page(PandaServer.System.Services.Pay.Dto.Coupon.RechargeApplicationPageInPut)">
            <summary>
            分页获取充值申请列表
            </summary>
            <param name="input">查询参数</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.RechargeApplicationController.Approve(PandaServer.System.Services.Pay.Dto.Coupon.RechargeApplicationInPut)">
            <summary>
            审核通过充值申请
            </summary>
            <param name="input">充值申请实体</param>
            <returns>审核结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.RechargeApplicationController.Reject(PandaServer.System.Services.Pay.Dto.Coupon.RechargeApplicationInPut)">
            <summary>
            审核拒绝充值申请
            </summary>
            <param name="input">充值申请实体</param>
            <returns>审核结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.SaleController">
            <summary>
            考场  关于  微信端 商品购买相应的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.ShopController">
            <summary>
            考场  关于  微信端 商品购买相应的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.ExamSite.Wx.StudyController">
            <summary>
            
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CoachWorkInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CoachWorkInfo.CoachId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CoachWorkInfo.StartTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CoachWorkInfo.EndTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CoachWorkInfo.Company">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CoachWorkInfo.CarType">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.Name">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.FullName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.ShortName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.QualificationRecord">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.BusNum">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.TractorNum">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.TruckNum">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.CarNum">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.OtherNum">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.CheckedCarNum">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.TheoryTrainersNum">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.OperationTrainersNum">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.LegalPerson">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.LegalPersonPhone">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.ContactPerson">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.ContactPersonPhone">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.ProvinceId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.CityId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.AreaId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.PostAddress">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.Remark">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.AdminUserId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.TenantId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.CoachPay">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo.Code">
            <summary>
                注册码
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.GetQuestionListInPut.Question">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.GetQuestionListInPut.QuestionType">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxClassInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxClassInfo.ClassId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxClassInfo.Name">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxClassInfo.StartTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxClassInfo.EndTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxClassInfo.AllowTime">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxClassInfo.Remark">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.Ids">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.ApplyTypes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.ApplyType">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.ApplyTypeLabel">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.xm">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.sfzmhm">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.xb">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CarType">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CarTypes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.TeachType">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.TeachCarType">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.TeachCarTypes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.yddh">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.clrq">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.Education">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompanyId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.Remark">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.TenantId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CheckStatus">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CheckStatusLabel">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CheckRemark">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CheckTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CheckTimes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CheckUserId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CreatCardTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CreatCardTimes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompanyShortName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompanyContactPerson">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompanyContactPersonPhone">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CreateUserName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CreatorTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CreatorTimes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.OldCardNo">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CardNo">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CarNoStatus">
            <summary>
                制卡状态
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.FileCheckTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.ClassId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.ClassName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompleteStudy">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompleteStudys">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompleteExam">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompleteExams">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.PayTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.PayStatus">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.SetClassStatus">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.OldData">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.SubmitTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.SubmitTimes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.BlackList">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompleteStatus">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CityId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.CompanyPostAddress">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.TrackingTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.TrackingTimes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo.TrackingStatus">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCostTypeInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCostTypeInfo.Name">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCostTypeInfo.PayMoney">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCostTypeInfo.ApplyType">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCostTypeInfo.ApplyTypeLabel">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCostTypeInfo.CreateUserName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCostTypeInfo.CreatorTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxLogOnInPut.xm">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxLogOnInPut.sfzmhm">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxLogOnInPut.jxmc">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxMaxIdInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxMaxIdInfo.Name">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxMaxIdInfo.PoliceCode">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxMaxIdInfo.MaxId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.CostTypeId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.CostTypeIds">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.PayTypeId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.PayTypeIds">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.PayMoney">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.PayTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.Reamrk">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.CoachId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo.PayTimes">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayOrderInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayOrderInfo.CoachId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayOrderInfo.CostTypeId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayOrderInfo.PayMoney">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayOrderInfo.CreatorTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayOrderInfo.InvoiceName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayOrderInfo.TaxNumber">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPlayVideoInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPlayVideoInfo.VideoId">
            <summary>
                视频Id
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPlayVideoInfo.TimeLength">
            <summary>
                上传时间
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPlayVideoInfo.currentTime">
            <summary>
                当前播放的时间
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxQuestionInfo.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxQuestionInfo.Question">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxQuestionInfo.QuestionType">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxQuestionInfo.Answer">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxQuestionInfo.ImagePath">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxQuestionInfo.CreateUserName">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxQuestionInfo.CreatorTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Item.trainerExperienceId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Item.dLTypeRealName">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Item.endDate">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Item.companyName">
            <summary>
                出租车
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Item.dLTypeReal">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Item.startDate">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Item.trainerId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.applyType">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.trainerMobile">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.arrivalStatus">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.education">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.failStatusReason">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.teachType">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.remark">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.trainerSex">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.rows">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.cardNo">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.teachCarType">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.licenseDate">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.userId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.trainerName">
            <summary>
                肖国清
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.photoUrl">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.tenantName">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.ACTION">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.fileNo">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.fileStatus">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.tenantId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.trainerNo">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.dLType">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.oldTrainerNo">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.CoachEdit.Json.trainerId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.Question.Item.testLibItemId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.Question.Item.question">
            <summary>
                遇到《道路交通安全法》没有规定的情况，车辆、行人应在保障道路交通有序、安全、畅通的原则下通行。
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.Question.Item.answer">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.Question.Item.answerType">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.Question.Item.url">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.Question.Json.total">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.Json.GanCheRen.Question.Json.rows">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetAnswerInPut.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetAnswerInPut.CoachQuestionId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetAnswerInPut.QuestionId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetAnswerInPut.Answer">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetPassWordInPut.TenantId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetPassWordInPut.Phone">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetPassWordInPut.PassWord">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetPassWordInPut.PassWord2">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxClassController">
            <summary>
                湖南驾协 报班相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxClassController.GetClassList(PandaServer.System.Services.Hnjx.Dtos.HnjxClassPageInPut)">
            <summary>
                获取分班的列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxClassController.GetClassSelectList(PandaServer.System.Services.Hnjx.Dtos.HnjxClassPageInPut)">
            <summary>
                获取分班的列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxClassController.GetClassInfo(PandaServer.System.Services.Hnjx.Dtos.HnjxClassInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxClassController.DeleteClassInfo(System.Guid)">
            <summary>
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxClassController.SetClassInfo(PandaServer.System.Services.Hnjx.Dtos.HnjxClassInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachCheckController">
            <summary>
                湖南驾协 教练相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachCheckController.SetCoachCheckInfo(System.Guid,PandaServer.System.Entity.Hnjx.Dto.HnjxCoachCheckInPut)">
            <summary>
                设置教练员审核状态
            </summary>
            <param name="id"></param>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController">
            <summary>
                湖南驾协 教练相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.GetCoachList(PandaServer.System.Services.Hnjx.Dtos.HnjxCoachPageInPut)">
            <summary>
                返回教练的 列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.SetCoachInfo(PandaServer.System.Services.Hnjx.Dtos.HnjxCoachInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.SetCoachSubmit(PandaServer.System.Services.Hnjx.Dtos.HnjxCoachInPut)">
            <summary>
                提交教练的数据
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.SetCoachReSubmit(PandaServer.System.Services.Hnjx.Dtos.HnjxCoachInPut)">
            <summary>
                退回 提交的资料
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.GetCoachWorkList(PandaServer.System.Services.Hnjx.Dtos.HnjxCoachWorkInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.SetCoachCheckInfo(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.SetCoachWork(PandaServer.System.Services.Hnjx.Dtos.HnjxCoachWorkInPut)">
            <summary>
                新增或者修改工作经历的数据
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.DeleteCoachWork(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CoachWorkInfo)">
            <summary>
                删除工作经历的数据
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.GetImageList(PandaServer.Core.BaseIdInput)">
            <summary>
                获取 该教练的全部的图片
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.DeleteImage(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo)">
            <summary>
                删除图片
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.UploadImage(System.Guid,System.Int32,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
                上传图片
            </summary>
            <param name="Id"></param>
            <param name="imageId"></param>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.UploadImageData(PandaServer.System.Services.Hnjx.Dtos.HnjxCoachImageInPut)">
            <summary>
                上传 Base 64 的图片
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.SetCoachClassIds(PandaServer.System.Services.Hnjx.Dtos.HnjxCoachInPut)">
            <summary>
                分班
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.GetTeachCarType">
            <summary>
                准教车型 的下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.MakeCardNo(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo)">
            <summary>
                生成卡号
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.MakeCoachPhotoZip(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxCoachInfo)">
            <summary>
                导出教练员的 寸照 (直接输出 zipStream)
            </summary>
            <param name="inPut"></param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.ClearCardNo(PandaServer.Core.BaseIdInput)">
            <summary>
                清理教练卡号
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachController.ClearTrackingTime(PandaServer.Core.BaseIdInput)">
            <summary>
                清理教练卡号
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachImageController">
            <summary>
                湖南驾协 教练相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachImageController.GetImageList(System.Guid)">
            <summary>
                获取 该教练的全部的图片
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachInfoController">
            <summary>
                湖南驾协 教练相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachInfoController.GetCoachInfo(System.Guid)">
            <summary>
            
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachInfoController.SetCoachInfo(System.Guid,PandaServer.System.Services.Hnjx.Dtos.HnjxCoachInPut)">
            <summary>
                设置 教练信息
            </summary>
            <param name="id"></param>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachMaxIdController">
            <summary>
                湖南驾协 教练证号最大的编码
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachMaxIdController.GetMaxIdList">
            <summary>
                最大的 Id  列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCoachMaxIdController.SetMaxIdInfo(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxMaxIdInfo)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCompanyController">
            <summary>
                湖南驾协 公司相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCompanyController.GetCompanyList(PandaServer.System.Services.Hnjx.Dtos.HnjxCompanyPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCompanyController.GetCompanySelectList(PandaServer.System.Services.Hnjx.Dtos.HnjxCompanyPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCompanyController.GetCompanyInfo(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.CompanyInfo)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCompanyController.SetCompanyInfo(PandaServer.System.Services.Hnjx.Dtos.HnjxCompanyInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCompanyController.DeleteCompanyInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                删除 公司信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxCompanyController.MakeAddCompanyCode">
            <summary>
                生成 邀请码
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxController">
            <summary>
                湖南驾协的服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxController.#ctor(PandaServer.System.Services.Hnjx.IHnjxCoachService,PandaServer.System.Services.Hnjx.IHnjxWxUserService,PandaServer.System.Services.Hnjx.IHnjxCompanyService,PandaServer.System.Services.Hnjx.IHnjxVideoService,PandaServer.System.Services.Hnjx.IHnjxCoachQuestionService,PandaServer.System.Services.Hnjx.IHnjxClassService,PandaServer.System.Services.SystemOrganize.IUserLogOnService,PandaServer.System.Services.Auth.IAuthService,PandaServer.System.Services.SystemOrganize.IUserInfoService,PandaServer.System.Services.Wx.IWxUserService)">
            <summary>
            湖南驾协控制器构造函数
            </summary>
            <param name="hnjxCoachService">教练服务</param>
            <param name="hnjxWxUserService">微信用户服务</param>
            <param name="hnjxCompanyService">公司服务</param>
            <param name="hnjxVideoService">视频服务</param>
            <param name="hnjxCoachQuestionService">教练问题服务</param>
            <param name="hnjxClassService">班级服务</param>
            <param name="userLogOnService">用户登录服务</param>
            <param name="authService">认证服务</param>
            <param name="userInfoService">用户信息服务</param>
            <param name="wxUserService">微信用户服务</param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxController.LogOn(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxLogOnInPut)">
            <summary>
                教练的注册以及登录
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxController.CheckLogOn">
            <summary>
                检查当前登录的状态
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxController.GoPage">
            <summary>
                跳转到的页面
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxController.GetUserInfo">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxController.LogOut(PandaServer.System.Services.Auth.Dto.LoginOutIput)">
            <summary>
                登出系统
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxImageController">
            <summary>
                湖南驾协 照片配置相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxImageController.GetImageIdList">
            <summary>
                ///
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxImageController.GetConfigImageIdList(PandaServer.System.Services.Hnjx.Dtos.HnjxImageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxImageController.AddImageConfigInfo(PandaServer.System.Services.Hnjx.Dtos.HnjxImageInPut)">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxImageController.DeleteImageConfigInfo(PandaServer.System.Services.Hnjx.Dtos.HnjxImageInPut)">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxLogOnController">
            <summary>
                湖南驾协的服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxLogOnController.#ctor(PandaServer.System.Services.Auth.IAuthService,PandaServer.System.Services.Hnjx.IHnjxCoachService,PandaServer.System.Services.Hnjx.IHnjxWxUserService,PandaServer.System.Services.Wx.IWxUserService,PandaServer.System.Services.Hnjx.IHnjxCompanyService)">
            <summary>
            湖南驾协登录控制器构造函数
            </summary>
            <param name="authService">认证服务</param>
            <param name="hnjxCoachService">教练服务</param>
            <param name="hnjxWxUserService">微信用户服务</param>
            <param name="wxUserService">微信用户服务</param>
            <param name="hnjxCompanyService">驾校公司服务</param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxLogOnController.LogOn(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxLogOnInPut)">
            <summary>
                教练的注册以及登录
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxLogOnController.CheckLogOn">
            <summary>
                检查当前登录的状态
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxLogOnController.GoPage">
            <summary>
                跳转到的页面
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxLogOutController">
            <summary>
                湖南驾协的服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxMyPayController">
            <summary>
                湖南驾协 我的缴费
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxMyPayController.GetMyPayList(System.Guid)">
            <summary>
                获取付款的订单列表
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxMyStudyController">
            <summary>
                湖南驾协 我的学习
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxMyStudyController.GetMyVideoTimeList(System.Guid,PandaServer.System.Services.Hnjx.Dtos.HnjxVideoPageInPut)">
            <summary>
                我的视频学习情况
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxOrderController">
            <summary>
                湖南驾协 支付相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController">
            <summary>
                湖南驾协 支付相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.GetPayList(PandaServer.System.Services.Hnjx.Dtos.HnjxPayPageInPut)">
            <summary>
                缴费的查询页面
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.GetCostTypeList(PandaServer.System.Services.Hnjx.Dtos.HnjxCostTypePageInPut)">
            <summary>
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.GetCostTypeSelectList">
            <summary>
                返回下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.SetCostTypeInfo(PandaServer.System.Services.Hnjx.Dtos.HnjxCostTypeInPut)">
            <summary>
                更新，添加 相关的费用类型信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.GetCostTypeInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.GetPayInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                获取当前教练需要缴费的信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.SetPayOrder(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayOrderInfo)">
            <summary>
                创建临时订单数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.GetMyPayList(PandaServer.System.Services.Hnjx.Dtos.HnjxPayPageInPut)">
            <summary>
                获取付款的订单列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.Pay(PandaServer.System.Services.Hnjx.Dtos.HnjxPayInPut)">
            <summary>
                手工缴费
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.DeletePayInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                缴费记录 删除
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.PayBarCode(PandaServer.System.Services.Pay.Dto.PayInPut)">
            <summary>
                生成缴费的二维码
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.GetPayBillList(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayInfo)">
            <summary>
                账单的 明细
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxPayController.GetPayBillDetail(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxPayOrderInfo)">
            <summary>
                生成 批量缴费的 订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionServicee">
            <summary>
                湖南驾协 题目相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionServicee.GetQuestionList(PandaServer.System.Services.Hnjx.Dtos.HnjxQuestionPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionServicee.GetQuestionInfo(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.HnjxQuestionInfo)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionServicee.Get100QuestionIndex">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionServicee.SetAnswer(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetAnswerInPut)">
            <summary>
                保存 答成绩的进度
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionServicee.SubmitAnswer(PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.Dto.SetAnswerInPut)">
            <summary>
                最终的交卷
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionServicee.GetMyResultList(PandaServer.System.Services.Hnjx.Dtos.HnjxCoachQuestionPageInPut)">
            <summary>
                获得 我的成绩
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionServicee.GetMyResultImage">
            <summary>
                获取我的成绩证书
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionInfoController">
            <summary>
                湖南驾协 题目相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxQuestionInfoController.GetMyResultList(System.Guid)">
            <summary>
                获得 我的成绩
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxRefundController">
            <summary>
                湖南驾协 退款相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxRefundController.Refund(PandaServer.System.Services.Hnjx.Dtos.HnjxRefundInPut)">
            <summary>
                退费的提交
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxRefundController.GetMyRefundList(PandaServer.System.Services.Hnjx.Dtos.HnjxRefundPageInPut)">
            <summary>
                我的退费的数据
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxRefundController.SyncRefundInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                更新退款状态
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxStudyController">
            <summary>
                教练继续教育 视频相关方法
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxStudyController.GetVideoList">
            <summary>
                获取视频列表
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxStudyController.GetVideoDetail(PandaServer.System.Services.Hnjx.Dtos.HnjxVideoInPut)">
            <summary>
                获取 视频的 详情
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxStudyController.UploadPlayVideoTimeLength(PandaServer.System.Services.Hnjx.Dtos.HnjxVideoInPut)">
            <summary>
                上传播放时间
            </summary>
            <param name="inPut"></param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxStudyController.GetVideoTimeLength">
            <summary>
                获取视频学习的累计时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxStudyController.GetMyVideoTimeList(PandaServer.System.Services.Hnjx.Dtos.HnjxVideoPageInPut)">
            <summary>
                我的视频学习情况
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxStudyController.FillStudyData(PandaServer.Core.BaseIdInput)">
            <summary>
                填充模拟的学习记录数据
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxStudyController.ClearStudyData(PandaServer.Core.BaseIdInput)">
            <summary>
                清理学习记录
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.HnjxUserController.GetUserInfo">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.WxLoginController">
            <summary>
                湖南驾协的服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.WxLoginController.LogOn">
            <summary>
                登录系统 生成 accessToken
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.HNJiaXie.WxLoginController.AnonymousLogOn">
            <summary>
                匿名登录 生成 accessToken
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.JiaXiaoDevice.JxDeviceActivateController">
            <summary>
                驾校的学员门禁系统
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.JiaXiaoDevice.JxDeviceActivateController.GetApiConfig">
            <summary>
                通过设备 获取  人脸识别的 SDK Api
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.JiaXiaoDevice.JxStudentDoorController">
            <summary>
                驾校的学员门禁系统
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.JiaXiaoDevice.JxStudentDoorController.UploadImage(Microsoft.AspNetCore.Http.IFormFile,System.String)">
            <summary>
                直接上传图片 进行 人脸识别
            </summary>
            <param name="file"></param>
            <param name="deviceId"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.JiaXiaoDevice.JxStudentDoorController.GetBaiduToken">
            <summary>
                获取 百度的 Token
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.ClassCarTypeController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxAreaController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxAreaController.GetJxAreaList(PandaServer.System.Services.Student.Dto.Jx.JxAreaPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxAreaController.GetJxAreaSelectList(PandaServer.System.Services.Student.Dto.Jx.JxAreaPageInPut)">
            <summary>
                训练场地的下拉列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxAreaInfoController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxAreaInfoController.DeleteJxAreaInfo(System.Guid)">
            <summary>
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxAreaInfoController.GetJxAreaInfo(System.Guid)">
            <summary>
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxAreaInfoController.SetJxAreaInfo(System.Guid,PandaServer.System.Services.Student.Dto.Jx.JxAreaInPut)">
            <summary>
            </summary>
            <param name="id"></param>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxClassController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxClassInfoController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyController">
            <summary>
                驾校资质相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyController.GetJxCompanyList(PandaServer.System.Services.Student.Dto.Jx.JxCompanyPageInPut)">
            <summary>
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyController.GetJxCompanySelectList(PandaServer.System.Services.Student.Dto.Jx.JxCompanyPageInPut)">
            <summary>
                驾校资质的下拉
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyController.GetImageList(PandaServer.Core.BaseIdInput)">
            <summary>
                获得图片的列表
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyController.UploadImageData(PandaServer.System.Services.Student.Dto.Jx.JxCompanyImageInPut)">
            <summary>
                上传 Base 64 的图片
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyController.UploadImage(System.Guid,System.Int32,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
                上传 文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyController.DeleteImage(PandaServer.Core.BaseIdInput)">
            <summary>
                删除图片
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyController.UploadSeal(System.Guid,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
                上传印章图片
            </summary>
            <param name="id">驾校公司ID</param>
            <param name="file">印章图片文件</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyInfoController">
            <summary>
                驾校资质相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyInfoController.GetJxCompanyInfo(System.Guid)">
            <summary>
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxCompanyInfoController.SetJxCompanyInfo(System.Guid,PandaServer.System.Services.Student.Dto.Jx.JxCompanyInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptComputerController">
            <summary>
                驾校报名点的 电脑
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptComputerController.GetComputerList(PandaServer.System.Services.Student.Dto.Jx.JxDeptComputerPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptComputerController.SetJxDeptComputer(PandaServer.System.Services.Student.Dto.Jx.JxDeptComputerInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptComputerController.GetMyJxDeptId(PandaServer.System.Services.Student.Dto.Jx.JxDeptComputerInPut)">
            <summary>
                获得 我的这台电脑的所属报名点
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptComputerController.DeleteJxDeptComputer(System.Guid)">
            <summary>
                删除驾校报名点的  电脑配置
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController">
            <summary>
                驾校报名点服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.GetJxDeptList(PandaServer.System.Services.Student.Dto.Jx.JxDeptPageInPut)">
            <summary>
                当前公司的报名点
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.GetAllJxDeptList(PandaServer.System.Services.Student.Dto.Jx.JxDeptPageInPut)">
            <summary>
                获取全部的报名点 包括下级的
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.GetJxDeptSelectList(PandaServer.System.Services.Student.Dto.Jx.JxDeptPageInPut)">
            <summary>
                报名场地的下拉列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.GetGCJ02(PandaServer.System.Services.Student.Dto.Jx.JxDeptInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.GetWGS84(PandaServer.System.Services.Student.Dto.Jx.JxDeptInPut)">
            <summary>
                获取 转换了的 定位
            </summary>
            <param name="inPut"></param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.SetLocation(PandaServer.System.Services.Student.Dto.Jx.JxDeptInPut)">
            <summary>
                保存 定位信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.AuditPass(PandaServer.System.Services.Student.Dto.Jx.JxDeptInPut)">
            批量审核通过
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.AuditFail(PandaServer.System.Services.Student.Dto.Jx.JxDeptInPut)">
            <summary>
                批量审核拒绝
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.ChangeEnabledMark(PandaServer.System.Services.Student.Dto.Jx.JxDeptInPut)">
            <summary>
                修改 有效状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptController.GetWxSetLocation(PandaServer.System.Services.Student.Dto.Jx.JxDeptInPut)">
            <summary>
                获取设置定位的 二维码
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeptInfoController">
            <summary>
                驾校报名点服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceBaseController.GetJxDeviceTypeSelectList">
            <summary>
                返回 设备类型 的列表
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceController">
            <summary>
                驾校服务  人脸设备
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceController.GetJxDoorDirectionSelectList">
            <summary>
                闸机方向下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceController.GetJxDoorBrandModelSelectList">
            <summary>
                设备品牌型号
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceController.GetJxDeviceList(PandaServer.System.Services.Student.Dto.Jx.JxDevicePageInPut)">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceController.GetJxDeviceInfo(System.Guid)">
            <summary>
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceController.MakeSerialNumber">
            <summary>
            生成随机的 序列号
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceController.SetJxDeviceInfo(System.Guid,PandaServer.System.Services.Student.Dto.Jx.JxDeviceInPut)">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceController.DeleteJxDeviceInfo(System.Guid)">
            <summary>
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxDeviceController.GetSerialNumber(PandaServer.System.Services.Student.Dto.Jx.JxDeviceValidCodeInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController.GetJxFieldList(PandaServer.System.Services.Student.Dto.Jx.JxFieldPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController.GetAllJxFieldList(PandaServer.System.Services.Student.Dto.Jx.JxFieldPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController.GetJxFieldSelectList(PandaServer.System.Services.Student.Dto.Jx.JxFieldPageInPut)">
            <summary>
                训练场地的下拉列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController.GetWGS84(PandaServer.System.Services.Student.Dto.Jx.JxFieldInPut)">
            <summary>
                获取 转换了的 定位
            </summary>
            <param name="inPut"></param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController.SetLocation(PandaServer.System.Services.Student.Dto.Jx.JxFieldInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController.AuditPass(PandaServer.System.Services.Student.Dto.Jx.JxFieldInPut)">
            <summary>
                批量审核通过
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController.AuditFail(PandaServer.System.Services.Student.Dto.Jx.JxFieldInPut)">
            <summary>
                批量审核拒绝
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController.ChangeIsEnabled(PandaServer.System.Services.Student.Dto.Jx.JxFieldInPut)">
            <summary>
                修改 有效状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldController.GetWxSetLocation(PandaServer.System.Services.Student.Dto.Jx.JxFieldInPut)">
            <summary>
                获取设置定位的 二维码
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldInfoController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldInfoController.GetJxFieldInfo(PandaServer.System.Services.Student.Dto.Jx.JxFieldInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldInfoController.SetJxFieldInfo(PandaServer.System.Services.Student.Dto.Jx.JxFieldInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxFieldInfoController.DeleteJxFieldInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                删除 训练场
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxMarkController">
            <summary>
                驾校学员 标记 服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxMarkController.GetMyMarkSelectList">
            <summary>
                返回下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxMarkController.SetJxMarkInfo(PandaServer.System.Services.Student.Dto.Jx.JxMarkInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxMarkController.SetJxStudentMarkInfo(PandaServer.System.Services.Student.Dto.Jx.JxStudentMarkInPut)">
            <summary>
                更新学员的  标记
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxMarkController.GetMyShareMarkSelectList">
            <summary>
                返回下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxMarkController.SetJxStudentShareMarkInfo(PandaServer.System.Services.Student.Dto.Jx.JxStudentMarkInPut)">
            <summary>
                更新学员的  共享标记
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxUKeyController">
            <summary>
                驾校学员 数字证书 服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Base.JxUKeyController.GetJxUKeyList(PandaServer.System.Services.Student.Dto.Jx.JxUKeyPageInPut)">
            <summary>
                翻页查询
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.CountController">
            <summary>
             统计相关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.PoliceUpdateController">
            <summary>
             交警考试 更新相关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController">
            <summary>
                驾校学员的 考试 相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.#ctor(PandaServer.System.Services.ImportExport.ImportExportService,PandaServer.System.Services.Student.Exam.IJxStudentExamService,PandaServer.System.Services.Student.Exam.IJxUpdateExamLogService,PandaServer.System.Services.Student.IJxStudentService)">
            <summary>
            初始化考试结果控制器
            </summary>
            <param name="importExportService">导入导出服务</param>
            <param name="jxStudentExamService">学员考试服务</param>
            <param name="jxUpdateExamLogService">考试更新日志服务</param>
            <param name="jxStudentService">学员服务</param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetExamList(PandaServer.System.Services.Student.Dto.Exam.JxStudentExamPageInPut)">
            <summary>
            分页查询考试成绩列表
            </summary>
            <param name="input">查询条件输入参数</param>
            <returns>分页后的考试成绩数据</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetExamCount(PandaServer.System.Services.Student.Dto.Exam.JxStudentExamPageInPut)">
            <summary>
            统计考试成绩数据
            </summary>
            <param name="input">统计条件输入参数</param>
            <returns>考试成绩统计结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetMyExamList(PandaServer.System.Services.Student.Dto.Exam.JxStudentExamPageInPut)">
            <summary>
            获取指定学员的所有考试成绩记录
            </summary>
            <param name="input">学员ID输入参数</param>
            <returns>学员的考试成绩列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.ExportExamList(PandaServer.System.Services.Student.Dto.Exam.JxStudentExamPageInPut)">
            <summary>
            导出学员考试成绩数据到Excel
            </summary>
            <param name="input">导出条件输入参数</param>
            <returns>导出文件信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.ExportResultCount(PandaServer.System.Services.Student.Dto.Exam.JxStudentExamPageInPut)">
            <summary>
            导出考试成绩统计数据到Excel
            </summary>
            <param name="input">统计条件输入参数</param>
            <returns>导出文件信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetResultSelectList">
            <summary>
            获取考试结果枚举列表
            </summary>
            <returns>考试结果选项列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetKeMuSelectList(PandaServer.Core.BaseIdsInput)">
            <summary>
            获取考试科目枚举列表
            </summary>
            <param name="input">科目ID列表筛选参数</param>
            <returns>考试科目选项列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.DeleteResult(PandaServer.Core.BaseIdInput)">
            <summary>
            删除考试记录
            </summary>
            <param name="input">考试记录ID</param>
            <returns>删除操作结果</returns>
            <exception cref="T:PandaServer.Web.Core.Exception">无删除权限时抛出异常</exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.UploadExcel(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            从Excel文件导入学员考试成绩
            </summary>
            <param name="file">Excel文件</param>
            <returns>导入操作结果</returns>
            <exception cref="T:PandaServer.Web.Core.Exception">Excel格式不正确时抛出异常</exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetWhiteExcel">
            <summary>
            获取考试成绩导入模板
            </summary>
            <returns>Excel模板文件信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetSfzmhmById(PandaServer.Core.BaseIdInput)">
            <summary>
            根据考试记录ID获取学员身份证信息
            </summary>
            <param name="input">考试记录ID</param>
            <returns>学员身份证相关信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetSfzmhmByIds(PandaServer.Core.BaseIdsInput)">
            <summary>
            根据考试记录ID列表批量获取学员身份证信息
            </summary>
            <param name="input">考试记录ID列表</param>
            <returns>学员身份证信息列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetUpdateLogList(PandaServer.System.Services.Student.Dto.Exam.JxUpdateExamLogPageInPut)">
            <summary>
            获取考试记录更新日志列表
            </summary>
            <param name="input">查询条件输入参数</param>
            <returns>更新日志分页数据</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.GetResultInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            获取考试成绩详细信息
            </summary>
            <param name="input">考试记录ID</param>
            <returns>考试成绩详情</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultController.SetResultInfo(PandaServer.System.Services.Student.Dto.Exam.JxStudentExamInPut)">
            <summary>
            新增或更新考试成绩信息
            </summary>
            <param name="input">考试成绩信息</param>
            <returns>操作结果</returns>
            <exception cref="T:PandaServer.Web.Core.Exception">学员ID为空时抛出异常</exception>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultUpdateController">
            <summary>
                驾校学员的 考试 相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultUpdateController.UpdateTimes">
            <summary>
            更新所有学员的考试次数
            </summary>
            <returns>更新操作结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.ResultWxController">
            <summary>
                驾校学员的 考试 微信推送相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.StudentController">
            <summary>
                驾校学员的 考试 相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.UpdateController">
            <summary>
                驾校学员的 考试 相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.WxResultController">
            <summary>
                驾校学员的 考试 相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.WxResultController.#ctor(PandaServer.System.Services.Student.Exam.IJxStudentExamService)">
            <summary>
            初始化考试结果控制器
            </summary>
            <param name="jxStudentExamService">学员考试服务</param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Exam.WxResultController.GetMyExamList(PandaServer.System.Services.Student.Dto.Exam.JxStudentExamPageInPut)">
            <summary>
            获取指定学员的所有考试成绩记录
            </summary>
            <param name="input">学员ID输入参数</param>
            <returns>学员的考试成绩列表</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.FaceController">
            <summary>
                人脸的相关方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Image.IdCardImageController">
            <summary>
            身份证识别控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Image.IdCardImageController.GetIdCardInfo(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            识别并获取身份证信息
            </summary>
            <param name="file">身份证图片文件</param>
            <returns>身份证信息</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Image.StudentImageInfoController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Image.StudentImageInfoController.#ctor(PandaServer.System.Services.Student.IJxStudentImageService)">
            <summary>
            构造函数
            </summary>
            <param name="jxStudentImageService">学员图片服务</param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Image.StudentImageInfoController.GetStudentImages(System.Guid)">
            <summary>
            获取学员的所有图片
            </summary>
            <param name="id">学员ID</param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Image.StudentImageInfoController.GetAllStudentImages(PandaServer.System.Services.Student.Dto.JxStudentImagePageInPut)">
            <summary>
            获取学员的所有图片
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Image.StudentImageInfoController.GetStudentImage(System.Guid,System.String)">
            <summary>
            获取学员的全部图片
            </summary>
            <param name="id">学员ID</param>
            <param name="ImageId">图片类型ID</param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Image.StudentImageInfoController.GetStudentZp(System.String,System.String)">
            <summary>
            获取学员头像
            </summary>
            <param name="id">学员ID</param>
            <param name="TenantId">租户ID</param>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.ImportData.ImportController">
            <summary>
                口袋数据  导入程序
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.ImportData.OtherSystemController">
            <summary>
                口袋数据  导入程序
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.ImportData.OtherSystemController.ImportData(PandaServer.System.Services.Student.Dto.ImportData.OtherSystemLoginInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.JxField.IdCardController">
            <summary>
            身份证设备控制器
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Pay.CountController">
            <summary>
             统计相关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Pay.JxShouldPayFuBeiCallBackController">
            <summary>
                驾校学员支付 付呗回调
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Pay.JxPayController">
            <summary>
                驾校学员支付
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Pay.JxPayScanController">
            <summary>
                驾校学员扫码支付
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Pay.PayResultController">
            <summary>
                驾校费的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Pay.JxPayAccountController">
            <summary>
                缴费审核的服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Pay.JxPayAuditController">
            <summary>
                缴费审核的服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Pay.JxShouldPayInfoController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Pay.WxPayController">
            <summary>
                驾校批量缴费的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Police.ExamController">
            <summary>
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Police.ExamController.GetResultList(PandaServer.System.Services.Student.Dto.Exam.JxPoliceExamPageInPut)">
            <summary>
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Police.ExamController.GetWhiteExcel">
            <summary>
                预约考试信息导入模板
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Police.PoliceCacheController">
            <summary>
            警务数据缓存控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Police.PoliceCacheController.SetDomain(System.String)">
            <summary>
            设置当前用户的域名
            </summary>
            <param name="domain">域名</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Police.PoliceCacheController.GetDomain">
            <summary>
            获取当前用户的域名
            </summary> 
            <returns>域名</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Police.PoliceCacheController.SetPoliceJson(System.String)">
            <summary>
            保存当前用户的数字证书JSON数据
            </summary>
            <param name="policeDataJson">当前登录的相应的数据JSON数据</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Police.PoliceCacheController.GetPoliceJson">
            <summary>
            获取当前用户的数字证书JSON数据
            </summary>
            <returns>证书JSON数据</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.DiscountReviewController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.DiscountReviewController.Create(PandaServer.System.Services.Student.Dto.Audit.JxStudentDiscountReviewInPut)">
            <summary>
            创建学员优惠审核记录
            </summary>
            <param name="inPut">创建信息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.DiscountReviewController.Update(System.Guid,PandaServer.System.Services.Student.Dto.Audit.JxStudentDiscountReviewInPut)">
            <summary>
            更新学员优惠审核记录
            </summary>
            <param name="id">记录ID</param>
            <param name="input">更新信息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.DiscountReviewController.DeleteAsync(System.Guid)">
            <summary>
            删除学员优惠审核记录
            </summary>
            <param name="id">记录ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.DiscountReviewController.ApproveAsync(System.Guid)">
            <summary>
            审核通过学员优惠申请
            </summary>
            <param name="id">申请ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.DiscountReviewController.RejectAsync(System.Guid)">
            <summary>
            审核拒绝学员优惠申请
            </summary>
            <param name="id">申请ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.DiscountReviewController.GetAsync(System.Guid)">
            <summary>
            获取学员优惠审核记录
            </summary>
            <param name="id">记录ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.DiscountReviewController.GetListAsync(PandaServer.System.Services.Student.Dto.Audit.JxStudentDiscountReviewPageInPut)">
            <summary>
            获取学员优惠审核记录列表
            </summary>
            <param name="input">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.DiscountReviewController.GetWaitListByStudentIdAsync(System.Guid)">
            <summary>
            获取等待审核的学员优惠申请列表
            </summary>
            <param name="studentId">学员ID</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentModifyController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentModifyController.Create(PandaServer.System.Services.Student.Dto.Audit.JxStudentModifyAuditInPut)">
            <summary>
            创建学员信息修改审核记录
            </summary>
            <param name="inPut">创建信息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentModifyController.Update(System.Guid,PandaServer.System.Services.Student.Dto.Audit.JxStudentModifyAuditInPut)">
            <summary>
            更新学员信息修改审核记录
            </summary>
            <param name="id">记录ID</param>
            <param name="inPut">更新信息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentModifyController.DeleteAsync(System.Guid)">
            <summary>
            删除学员信息修改审核记录
            </summary>
            <param name="id">记录ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentModifyController.ApproveAsync(System.Guid)">
            <summary>
            审核通过学员信息修改申请
            </summary>
            <param name="id">申请ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentModifyController.RejectAsync(System.Guid)">
            <summary>
            审核拒绝学员信息修改申请
            </summary>
            <param name="id">申请ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentModifyController.GetAsync(System.Guid)">
            <summary>
            获取学员信息修改审核记录
            </summary>
            <param name="id">记录ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentModifyController.GetListAsync(PandaServer.System.Services.Student.Dto.Audit.JxStudentModifyAuditPageInPut)">
            <summary>
            获取学员信息修改审核记录列表
            </summary>
            <param name="inPut">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentModifyController.GetWaitListByStudentIdAsync(System.Guid)">
            <summary>
            获取等待审核的学员优惠申请列表
            </summary>
            <param name="studentId">学员ID</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentStatusChangeController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentStatusChangeController.GetList(PandaServer.System.Services.Student.Dto.Audit.JxStudentStatusChangeAuditPageInPut)">
            <summary>
            获取学员状态变更审核记录列表
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentStatusChangeInfoController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentStatusChangeInfoController.GetInfo(System.Guid)">
            <summary>
            获取学员状态变更审核记录详情
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.StudentStatusChangeInfoController.Audit(System.Guid,PandaServer.System.Services.Student.Dto.Audit.JxStudentStatusChangeAuditInPut)">
            <summary>
            审核学员状态变更
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.GetStudentByIDCard(PandaServer.Core.BaseIdInput)">
            <summary>
            通过身份证号获取学员信息
            </summary>
            <param name="input"></param>
            <returns>学员信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.Submit(PandaServer.System.Services.Student.Dto.Audit.WeChatRefundApplicationInPut)">
            <summary>
            提交退款审核
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.UploadScreenshot(System.Guid,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传退款申请截图
            </summary>
            <param name="id">退款申请ID</param>
            <param name="screenshot">截图图片</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.UploadBankCardScreenshot(System.Guid,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传退款申请银行卡截图
            </summary>
            <param name="id">退款申请ID</param>
            <param name="bankCardScreenshot">银行卡截图图片</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.Page(PandaServer.System.Services.Student.Dto.Audit.WeChatRefundApplicationPageInPut)">
            <summary>
            分页查询退款申请记录
            </summary>
            <param name="input">分页查询参数</param>
            <returns>分页查询结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.Export(PandaServer.System.Services.Student.Dto.Audit.WeChatRefundApplicationPageInPut)">
            <summary>
            导出退款申请记录
            </summary>
            <param name="input">查询参数</param>
            <returns>导出文件</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.GetRefundDetail(PandaServer.Core.BaseIdInput)">
            <summary>
            获取退款申请详情
            </summary>
            <param name="input"></param>
            <returns>退款申请详情</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.UpdateRefundAmount(PandaServer.System.Services.Student.Dto.Audit.WeChatRefundApplicationInPut)">
            <summary>
            更新退款金额
            </summary>
            <param name="input">退款申请ID</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.Approve(PandaServer.System.Services.Student.Dto.Audit.WeChatRefundApplicationInPut)">
            <summary>
            审核通过退款申请
            </summary>
            <param name="input">退款申请</param>
            <returns>审核结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditController.Reject(PandaServer.System.Services.Student.Dto.Audit.WeChatRefundApplicationInPut)">
            <summary>
            审核拒绝退款申请
            </summary>
            <param name="input">退款申请</param>
            <returns>审核结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditPayController.WechatPay(PandaServer.System.Services.Student.Dto.Audit.WeChatRefundApplicationInPut)">
            <summary>
            微信支付退款
            </summary>
            <param name="input">退款申请</param>
            <returns>支付结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditPayController.GetCostTypeId(PandaServer.Core.JxExamKeMuEnum,System.Int32)">
            <summary>
            获取 退款的费用类型
            </summary>
            <param name="keMuId"></param>
            <param name="times"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit.WxRefundAuditPayController.OfflinePay(PandaServer.System.Services.Student.Dto.Audit.WeChatRefundApplicationInPut)">
            <summary>
            线下支付退款
            </summary>
            <param name="input">退款申请</param>
            <returns>支付结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.ColurmnController">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Contract.WxContractController">
            <summary>
                驾校 合同 模板
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Contract.WxContractController.UploadSignature(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传签字信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Contract.WxContractController.UploadPhoto(PandaServer.System.Services.Student.Dto.Contract.JxStudentContractPhotoInput)">
            <summary>
            上传拍照信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Contract.WxContractController.SubmitContractSign(PandaServer.System.Services.Student.Dto.Contract.JxStudentContractSubmitInput)">
            <summary>
            提交合同签署信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.CountController">
            <summary>
             统计相关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.ImformationStatusController">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.OrderCar.JxOrderCarConfigController">
            <summary>
                驾校学员预约配置控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.OrderCar.JxOrderCarConfigController.#ctor(PandaServer.System.Services.Config.ITenantConfigService)">
            <summary>
                构造函数
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.OrderCar.JxOrderCarConfigController.GetOrderCarMethod">
            <summary>
                获取预约方式配置
            </summary>
            <returns>预约方式</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxOrderController">
            <summary>
                驾校预约控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxOrderController.#ctor(PandaServer.System.Services.Student.IJxOrderService)">
            <summary>
                构造函数
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxOrderController.Create(PandaServer.System.Services.Student.Dto.OrderCar.JxOrderInput)">
            <summary>
                创建预约
            </summary>
            <param name="input">输入参数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxOrderController.Cancel(PandaServer.System.Services.Student.Dto.OrderCar.CancelOrderInput)">
            <summary>
                取消预约
            </summary>
            <param name="input">输入参数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxOrderController.Complete(PandaServer.System.Services.Student.Dto.OrderCar.CompleteOrderInput)">
            <summary>
                完成预约
            </summary>
            <param name="input">输入参数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxOrderController.getOrderCarList(PandaServer.System.Services.Student.Dto.OrderCar.JxOrderPageInput)">
            <summary>
                获取预约车辆列表
            </summary>
            <param name="input">查询参数</param>
            <returns>预约车辆列表</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateController">
            <summary>
                驾校时间模板控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateController.#ctor(PandaServer.System.Services.Student.IJxScheduleTemplateService,PandaServer.System.Services.Config.ITenantConfigService)">
            <summary>
                构造函数
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateController.GetList(PandaServer.System.Services.Student.Dto.OrderCar.JxScheduleTemplatePageInput)">
            <summary>
                获取时间模板列表
            </summary>
            <param name="input">查询参数</param>
            <returns>时间模板列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateController.Create(PandaServer.System.Services.Student.Dto.OrderCar.JxScheduleTemplateInPut)">
            <summary>
                添加时间模板
            </summary>
            <param name="input">输入参数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateController.Update(PandaServer.System.Services.Student.Dto.OrderCar.JxScheduleTemplateInPut)">
            <summary>
                更新时间模板
            </summary>
            <param name="input">输入参数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateController.Delete(PandaServer.System.Services.Student.Dto.OrderCar.JxScheduleTemplateInPut)">
            <summary>
                删除时间模板
            </summary>
            <param name="input">输入参数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateController.GetList(System.Guid)">
            <summary>
                通过模板ID获取时间模板详情列表
            </summary>
            <param name="templateId">模板ID</param>
            <returns>时间模板详情列表</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateDetailController">
            <summary>
                驾校时间模板详情控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateDetailController.#ctor(PandaServer.System.Services.Student.IJxScheduleTemplateService)">
            <summary>
                构造函数
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateDetailController.Update(System.Guid,System.Collections.Generic.List{PandaServer.System.Services.Student.Dto.OrderCar.JxScheduleTemplateDetailInput})">
            <summary>
                更新时间模板详情
            </summary>
            <param name="templateId">模板ID</param>
            <param name="input">输入参数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateDetailController.Delete(System.Guid)">
            <summary>
                删除时间模板详情
            </summary>
            <param name="templateId">模板ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateTargetController">
            <summary>
                驾校时间模板目标控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateTargetController.#ctor(PandaServer.System.Services.Student.IJxScheduleTemplateService)">
            <summary>
                构造函数
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.JxScheduleTemplateTargetController.Update(System.Guid,System.Collections.Generic.List{PandaServer.System.Services.Student.Dto.OrderCar.JxScheduleTemplateTargetInput})">
            <summary>
                更新时间模板目标
            </summary>
            <param name="templateId">模板ID</param>
            <param name="input">输入参数</param>
            <returns>是否成功</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.SMSController">
            <summary>
            学员短信控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.SMSController.SendSMS(System.Guid,PandaServer.System.Services.Student.Dto.JxStudentSMSInPut)">
            <summary>
            发送短信
            </summary>
            <param name="studentId">学员ID</param>
            <param name="inPut">短信内容</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.SMSController.GetPage(System.Guid,PandaServer.System.Services.Student.Dto.JxStudentSMSPageInPut)">
            <summary>
            获取短信记录
            </summary>
            <param name="StudentId"></param>
            <param name="inPut">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.SMSController.GetPage(PandaServer.System.Services.Student.Dto.JxStudentSMSPageInPut)">
            <summary>
            获取短信记录
            </summary>
            <param name="inPut">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.SMSController.Export(PandaServer.System.Services.Student.Dto.JxStudentSMSPageInPut)">
            <summary>
            获取短信记录
            </summary>
            <param name="inPut">查询参数</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.SourceController">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentController">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentDocmentController">
            <summary>
                驾校 学员 相关文档的处理
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentDocmentController.ReplacePlaceholder(Spire.Pdf.PdfPageBase,System.String,System.String,System.Single,Spire.Pdf.Graphics.PdfTrueTypeFont,Spire.Pdf.Graphics.PdfStringFormat)">
            <summary>
            通用的占位符替换方法
            </summary>
            <param name="page">PDF页面</param>
            <param name="placeholder">占位符</param>
            <param name="value">要替换的值</param>
            <param name="offsetX">X轴偏移量</param>
            <param name="font">字体</param>
            <param name="centerFormat">居中格式</param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentDocmentController.ReplacePlaceholders(Spire.Pdf.PdfPageBase,System.Collections.Generic.Dictionary{System.String,System.String},System.Single,Spire.Pdf.Graphics.PdfTrueTypeFont,Spire.Pdf.Graphics.PdfStringFormat,System.Collections.Generic.Dictionary{System.String,System.Single})">
            <summary>
            批量替换占位符工具类
            </summary>
            <param name="page">PDF页面</param>
            <param name="placeholders">占位符字典 (占位符, 值)</param>
            <param name="defaultOffsetX">默认X轴偏移量</param>
            <param name="font">字体</param>
            <param name="centerFormat">居中格式</param>
            <param name="customOffsets">自定义偏移量字典 (占位符, 偏移量)</param>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentImportController">
            <summary>
                驾校 学员 相关文档的处理
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentImportController.ParseExcel(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            解析Excel文件并验证学员数据
            </summary>
            <param name="file">Excel文件</param>
            <returns>验证后的学员信息列表</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentImportOtherTenantController">
            <summary>
                驾校 学员 相关文档的处理
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentInfoController">
            <summary>
            学员信息控制器
            用于管理驾校学员的基本信息、详情查询、添加、修改和删除等操作
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentInfoController.#ctor(PandaServer.System.Services.Student.IJxStudentQueryService,PandaServer.System.Services.Student.IJxStudentInfoService,PandaServer.System.Services.Student.IJxStudentService,PandaServer.System.Services.SystemManage.IUserRoleService,PandaServer.System.Services.Config.ITenantConfigService,PandaServer.System.Services.Oss.IImageService,PandaServer.System.Services.SystemSecurity.IEasyLogService)">
            <summary>
            学员信息控制器构造函数
            </summary>
            <param name="jxStudentQueryService">学员查询服务，用于获取学员详细信息</param>
            <param name="jxStudentInfoService">学员信息服务，用于添加和修改学员基本信息</param>
            <param name="jxStudentService">学员服务，用于更新学员状态和费用信息</param>
            <param name="userRoleService">用户角色服务，用于权限验证</param>
            <param name="tenantConfigService">租户配置服务，用于获取租户特定的配置信息</param>
            <param name="imageService"></param>
            <param name="easyLogService"></param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentInfoController.GetStudentInfo(System.Guid,System.Guid)">
            <summary>
            获取学员详细信息
            </summary>
            <param name="id">学员ID，唯一标识符</param>
            <param name="tenantId"></param>
            <returns>学员详细信息，包括基本信息、费用信息、考试信息等</returns>
            <remarks>
            根据微信端访问时的配置，可能会隐藏部分敏感信息
            如备注、支付信息、教练信息等
            </remarks>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentInfoController.HaveRole">
            <summary>
            检查用户是否拥有操作学员信息的权限
            </summary>
            <returns>如果有权限返回true，否则抛出异常</returns>
            <exception cref="T:PandaServer.Web.Core.Exception">当用户没有权限时抛出异常</exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentInfoController.SetStudentInfo(System.Guid,PandaServer.System.Services.Student.Dto.JxStudentInPut)">
            <summary>
            添加或更新学员信息
            </summary>
            <param name="id">学员ID，如果为空则添加新学员，否则更新已有学员</param>
            <param name="inPut">学员信息输入对象</param>
            <returns>添加时返回新学员ID，更新时返回成功消息</returns>
            <exception cref="T:PandaServer.Web.Core.Exception">当用户没有权限时抛出异常</exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentInfoController.Delete(System.Guid)">
            <summary>
            删除学员信息
            </summary>
            <param name="id">要删除的学员ID</param>
            <returns>删除成功的消息</returns>
            <exception cref="T:PandaServer.Web.Core.Exception">当用户不是租户管理员时抛出异常</exception>
            <remarks>
            此方法只有租户管理员才能调用
            实际上是将学员标记为已删除状态，而非物理删除
            </remarks>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.GetRegisterList(PandaServer.System.Services.Student.Dto.JxStudentRegisterPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.GetRegisterInfo(PandaServer.System.Services.Student.Dto.JxStudentRegisterInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.SetRegisterInfo(PandaServer.System.Services.Student.Dto.JxStudentRegisterInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.GetStudentRegisterList(PandaServer.System.Services.Student.Dto.JxStudentRegisterDetailPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.AddStudentToRegister(PandaServer.System.Services.Student.Dto.JxStudentRegisterDetailInPut)">
            <summary>
                将学员 添加到名册
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.AddStudentsToRegister(PandaServer.System.Services.Student.Dto.JxStudentRegisterDetailInPut)">
            <summary>
                将学员列表 添加到名册
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.RemoveStudentFromRegister(PandaServer.System.Services.Student.Dto.JxStudentRegisterDetailInPut)">
            <summary>
                将学员 从名册里面移除
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.RemoveRegisterDetail(PandaServer.Core.BaseIdInput)">
            <summary>
                注册名单明细 删除
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.RemoveStudentsFromRegister(PandaServer.System.Services.Student.Dto.JxStudentRegisterDetailInPut)">
            <summary>
                将学员列表 从名册里面移除
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.GetRegisterDetailList(PandaServer.System.Services.Student.Dto.JxStudentRegisterDetailPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentRegisterController.ExportRegisterDetailList(PandaServer.System.Services.Student.Dto.JxStudentRegisterDetailPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentSlztController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentSlztController.UpdateStudentStatus(PandaServer.System.Entity.Student.Student.JxStudentSlztEntity)">
            <summary>
            更新学生身份状态
            </summary>
            <param name="entity">学生身份状态实体</param>
            <returns>更新结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentSMSConfigController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentSMSConfigController.GetSMSSceneSelect">
            <summary>
            获取场景枚举下拉列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentSMSConfigController.GetPage(PandaServer.System.Services.Student.Dto.JxStudentSMSConfigPageInPut)">
            <summary>
            获取分页数据
            </summary>
            <param name="inPut">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentSMSConfigController.Edit(System.Guid,PandaServer.System.Services.Student.Dto.JxStudentSMSConfigInPut)">
            <summary>
            修改或添加配置
            </summary>
            <param name="inPut">配置信息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentSMSConfigController.Delete(System.Guid)">
            <summary>
            删除配置
            </summary>
            <param name="id">配置ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentSMSConfigController.Get(System.Guid)">
            <summary>
            获取单个配置
            </summary>
            <param name="id">配置ID</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.StudentSMSController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.TenantRechargeController">
            <summary>
            租户充值控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.TenantRechargeController.Page(PandaServer.System.Services.Student.Dto.Tenant.TenantRechargePageInPut)">
            <summary>
            分页获取租户充值记录
            </summary>
            <param name="input">查询参数</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.TenantRechargeController.Add(PandaServer.System.Services.Student.Dto.Tenant.TenantRechargeInPut)">
            <summary>
            添加租户充值记录
            </summary>
            <param name="input">充值记录信息</param>
            <returns>添加结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.TenantRechargeController.BatchDeduct(PandaServer.System.Services.Student.Dto.Tenant.TenantRechargeInPut)">
            <summary>
            批量扣费
            </summary>
            <param name="input">批量扣费参数</param>
            <returns>扣费结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.WxLoginConrtroller">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Wages.OldDataController">
            <summary>
                驾校  工资  相应的 老数据的处理方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Wages.ResultController">
            <summary>
                驾校  工资  计算结果
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Jx.Wages.ResultUserController">
            <summary>
                驾校  工资  相关人员查询
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController">
            <summary>
            发票授权管理控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.Page(PandaServer.System.Services.Student.Dto.Invoice.JxInvoiceAuthPageInPut)">
            <summary>
            分页查询发票授权信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.GetDetail(System.Guid,System.Guid)">
            <summary>
            获取发票授权详细信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.Add(PandaServer.System.Services.Student.Dto.Invoice.JxInvoiceAuthInPut)">
            <summary>
            添加发票授权配置
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.Edit(PandaServer.System.Services.Student.Dto.Invoice.JxInvoiceAuthInPut)">
            <summary>
            编辑发票授权配置
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.Delete(System.Guid)">
            <summary>
            删除发票授权配置
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.GetAuthorizationToken(System.Guid)">
            <summary>
            获取授权Token
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.LoginPlatform(System.Guid,System.String)">
            <summary>
            登录数电发票平台
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.RefreshToken(System.Guid)">
            <summary>
            刷新授权Token
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.IsTokenValid(System.Guid)">
            <summary>
            检查Token是否有效
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.TestConnection(System.Guid)">
            <summary>
            测试API连接
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceAuthController.GetValidAuths(System.Guid)">
            <summary>
            获取有效的授权配置列表
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController">
            <summary>
            数电蓝票开具控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.Page(PandaServer.System.Services.Student.Dto.Invoice.JxInvoiceBluePageInPut)">
            <summary>
            分页查询蓝票信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.GetDetail(System.Guid,System.Guid)">
            <summary>
            获取蓝票详细信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.IssueBlueInvoice(PandaServer.System.Services.Student.Dto.Invoice.JxInvoiceBlueInPut)">
            <summary>
            开具蓝票
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.GetInvoicePdfFile(System.Guid)">
            <summary>
            获取已开票PDF文件
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.GetInvoiceStatus(System.Guid)">
            <summary>
            查询蓝票开具状态
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.PreviewInvoice(PandaServer.System.Services.Student.Dto.Invoice.JxInvoiceBlueInPut)">
            <summary>
            预览发票信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.ValidateBuyerInfo(System.String)">
            <summary>
            验证购买方信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.ReissueInvoice(System.Guid)">
            <summary>
            重新开具蓝票
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.VoidInvoice(System.Guid,System.String)">
            <summary>
            作废蓝票
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.BatchIssueInvoices(System.Collections.Generic.List{PandaServer.System.Services.Student.Dto.Invoice.JxInvoiceBlueInPut})">
            <summary>
            批量开具发票
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.GetInvoiceStatistics(System.Guid,System.DateTime,System.DateTime)">
            <summary>
            获取发票统计信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.Add(PandaServer.System.Services.Student.Dto.Invoice.JxInvoiceBlueInPut)">
            <summary>
            添加蓝票记录
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.Edit(PandaServer.System.Services.Student.Dto.Invoice.JxInvoiceBlueInPut)">
            <summary>
            编辑蓝票信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Invoice.InvoiceBlueController.Delete(System.Guid)">
            <summary>
            删除蓝票记录
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Client.JxController">
            <summary>
                驾校客户端 客户端关于 基础信息的 相关方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Client.PayController">
            <summary>
                驾校客户端 的相关方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.JxClient.JxDeviceController">
            <summary>
                驾校客户端 客户端关于 基础信息的 相关方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.JxClient.PushMoneyController">
            <summary>
                驾校客户端 提成相关的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.JxClient.SaleController">
            <summary>
                驾校客户端 提成相关的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.OrderCar.OrderConfigController">
            <summary>
                预约练车  约车配置
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.OrderCar.OrderRecordController">
            <summary>
                预约练车  约车记录
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.OrderCar.RankClassController">
            <summary>
                预约练车  排班
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.OrderCar.RankClassController.GetRankClassInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.CarController">
            <summary>
                计时服务中  教练的相关服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.CoachController">
            <summary>
                计时服务中  教练的相关服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.FaceFeatureController">
            <summary>
                人脸特征 数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.FaceFeatureController.GetSysIds(PandaServer.System.Services.Student.Dto.JxStudentFaceFeaturePageInPut)">
            <summary>
                获取学员的Id列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.FaceFeatureController.GetFaceFeatureBySysId(PandaServer.System.Services.Student.Dto.JxStudentFaceFeatureInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.DeviceApiController">
            <summary>
                计时服务中  设备接口相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.DeviceApiController.DeviceLogOn(System.String)">
            <summary>
                设备的 登录
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.DeviceApiController.HeartBeat(System.String,System.Guid,System.String,System.Guid,System.String,System.String,System.String)">
            <summary>
                设备的 心跳
            </summary>
            <param name="imei"></param>
            <param name="carId"></param>
            <param name="carNumber"></param>
            <param name="tenantId"></param>
            <param name="lat">维度</param>
            <param name="lng">经度</param>
            <param name="acc">精度</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.DeviceApiController.CoachLogOn(Microsoft.AspNetCore.Http.IFormFile,System.Guid,System.String,System.Decimal,System.Decimal,System.Guid)">
            <summary>
                教练登签
            </summary>
            <param name="file"></param>
            <param name="carId"></param>
            <param name="imei"></param>
            <param name="lat"></param>
            <param name="lng"></param>
            <param name="tenantId"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.DeviceApiController.CoachLogOut">
            <summary>
                教练的登出
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.DeviceApiController.StudentLogOn">
            <summary>
                学员的登签
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.DeviceApiController.StudentLogOut">
            <summary>
                学员的登出
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.DeviceApiController.StudentLogOnBarCode">
            <summary>
                学员登签的 二维码
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.JxDeviceController">
            <summary>
                计时服务中  设备相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.SaleController">
            <summary>
                计时服务中  学员的相关服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.StudentController">
            <summary>
                计时服务中  学员的相关服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.StudyController">
            <summary>
                计时服务中  教练的相关服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.StudyExamSiteController">
            <summary>
                计时服务中  教练的相关服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.Study.StudyExamSiteController.StudyDown(PandaServer.Core.BaseIdInput)">
            <summary>
            下车操作
            </summary>
            <param name="input">训练记录ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.Study.WxStudyController">
            <summary>
                计时服务中  微信小程序的 扫脸计时
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiao.WxLoginController">
            <summary>
                驾校 微信登录相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiao.WxLoginController.LogOn">
            <summary>
                登录系统 生成 accessToken
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JiaXiao.AssignCoachController">
            <summary>
                驾校服务  分车记录
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JiaXiao.DoorFaceController">
            <summary>
                驾校服务  刷脸的闸机
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JiaXiao.Face.FaceLogController">
            <summary>
                驾校服务  人脸日志
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.Face.FaceLogController.GetLogList(PandaServer.System.Services.Student.Dto.Jx.JxStudentFaceLogPageInPut)">
            <summary>
                返回 日志列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JiaXiao.Face.StudentController">
            <summary>
                驾校服务  学员人脸搜索服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.Face.StudentController.ExtractFramesFromVideo(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            从视频中提取帧
            </summary>
            <param name="videoFile">视频文件</param>
            <returns>提取的帧字节数组列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.Face.StudentController.ExtractFramesSimple(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            简单的视频帧提取方法（备用方案）
            </summary>
            <param name="videoFile">视频文件</param>
            <returns>提取的帧字节数组列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.Face.StudentController.SaveVideoToTempFolder(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            将视频保存到temp文件夹
            </summary>
            <param name="videoFile">视频文件</param>
            <returns>保存后的文件路径（相对于temp文件夹）</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JiaXiao.JxContractController">
            <summary>
                驾校 合同 模板
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.JxContractController.GetWord(System.String)">
            <summary>
            </summary>
            <param name="character"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.JxContractController.MakeJxContract(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            </summary>
            <param name="file"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JiaXiao.JxContractTemplateController">
            <summary>
                驾校 合同 模板
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.JxContractTemplateController.GetJxContractTemplateList(PandaServer.System.Services.Student.Dto.Contract.JxContractTemplatePageInPut)">
            <summary>
                合同 模板列表
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.JxContractTemplateController.SaveContractTemplateControls(System.Guid,PandaServer.System.Services.Student.Dto.Contract.JxContractTemplateControlsInPut)">
            <summary>
                保存合同模板控件
            </summary>
            <param name="id"></param>
            <param name="input">合同模板控件信息</param>
            <returns>保存结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.JxContractTemplateController.TestPdfTextWriting">
            <summary>
            测试 PDF 文本写入功能
            </summary>
            <returns>测试结果</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentDocStatusController">
            <summary>
                驾校服务  档案状态的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentDocStatusController.GetDocStatusList(PandaServer.System.Services.Student.Dto.JxStudentDocStatusPageInPut)">
            <summary>
                翻页
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentDocStatusController.GetDocStatusSelectList(PandaServer.System.Services.Student.Dto.JxStudentDocStatusPageInPut)">
            <summary>
                翻页
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentDocStatusController.GetDocStatusInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentDocStatusController.SetDocStatusInfo(PandaServer.System.Services.Student.Dto.JxStudentDocStatusInPut)">
            <summary>
                保存
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentDocStatusController.DeleteDocStatusInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentSaleController">
            <summary>
                驾校的 优惠活动
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentSaleController.GetSaleList(PandaServer.System.Services.Student.Dto.Pay.JxStudentSalePageInPut)">
            <summary>
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentSaleController.GetSaleSelectList(PandaServer.System.Services.Student.Dto.Pay.JxStudentSalePageInPut)">
            <summary>
                训练场地的下拉列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentSaleController.GetSaleInfo(PandaServer.System.Services.Student.Dto.Pay.JxStudentSaleInPut)">
            <summary>
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentSaleController.GetDiscountMoney(PandaServer.System.Services.Student.Dto.Pay.JxStudentSaleInPut)">
            <summary>
                获取优惠的金额
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentSaleController.SetSaleInfo(PandaServer.System.Services.Student.Dto.Pay.JxStudentSaleInPut)">
            <summary>
                修改活动
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.StudentSaleController.DeleteSaleInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                删除活动
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JiaXiao.WxStudentController">
            <summary>
                驾校学员 微信报名
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.WxStudentController.GetJxFieldSelectList(PandaServer.System.Services.Student.Dto.Jx.JxFieldPageInPut)">
            <summary>
                获取 当前用户的所属公司的培训场地 全部的下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.WxStudentController.GetJxDeptInfo(PandaServer.System.Services.Student.Dto.Jx.JxDeptInPut)">
            <summary>
                获取 当前用户的 报名站点的信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.WxStudentController.GetCarTypeSelectList(PandaServer.System.Services.Student.Dto.Jx.JxClassCarTypePageInPut)">
            <summary>
                通过 各个 业务项目 获取 车型列表
            </summary>
            <param name="inPut"></param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.WxStudentController.GetJxClassSelectList(PandaServer.System.Services.Student.Dto.Jx.JxClassPageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.JiaXiao.WxStudentController.GetMyCreateStudentQCode">
            <summary>
                获取当前登录用户的
            </summary>
            <returns></returns>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Jx.JxField.Dto.FaceInPut.FaceImage">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JxField.FaceController">
            <summary>
            场地 人脸
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.JxField.JxDeviceController">
            <summary>
                驾校场地管理 主要是 闸机相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.CostController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.CostTypeController">
            <summary>
                驾校服务  费用类型
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.JxPayInfoController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ProcessPaymentByType(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            根据不同类型处理支付
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ValidatePermissionsAndInput(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            验证权限和输入参数
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ValidateCostTypePermission(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            验证费用类型权限
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ValidatePaymentInput(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            验证缴费输入参数
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ValidatePaymentFields(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            验证支付字段
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ValidatePaymentAmountConsistency(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            验证支付金额一致性
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ValidateRequiredSelections(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            验证必选项
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ValidatePaymentPermissions(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            验证支出/缴费权限
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ValidateExpensePermission">
            <summary>
            验证支出权限
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ValidateIncomePermission">
            <summary>
            验证收入权限
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.HandleTuitionPayment(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            处理学费相关逻辑
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ProcessTuitionPayment(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut,System.Guid)">
            <summary>
            处理学费缴费
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.CreatePayInput(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut,System.Guid)">
            <summary>
            创建学费缴费输入对象
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.HandleNewPayment(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            处理新增挂账信息
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.ProcessDirectPayment(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            处理直接缴费
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.AddPaymentRecord(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            添加挂账记录
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.CreatePayInput(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            创建缴费输入对象
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPayController.HandleUpdatePayment(PandaServer.System.Services.Student.Dto.Pay.JxShouldPayInPut)">
            <summary>
            处理更新挂账信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.JxShouldPaysController">
            <summary>
                批量挂账的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.My.JxPayController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.My.OrderController">
            <summary>
                订单相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.PayController">
            <summary>
            驾校支付控制器
            处理驾校相关的支付请求
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.PaysController">
            <summary>
                驾校批量缴费的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Pay.PushMoneyController">
            <summary>
                提成相关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Jx.Wages.DesignController">
            <summary>
                驾校  工资  设计
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.JiaXiaoOldData.CompanyController">
            <summary>
                驾校老数据导入
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.CompanyController.GetCompanySelectList(PandaServer.System.Services.JiaXiaoOA.Dto.BaseCompanyPageInPut)">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut.Name">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut.Type">
            <summary>
            导入的类型
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut.CGUID">
            <summary>
                原数据库的  Compnay CGUID
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut.TenantId">
            <summary>
            
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut.JxDeptId">
            <summary>
            
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut.JxDeptIds">
            <summary>
            
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut.ClearBeforeImport">
            <summary>
            导入前是否清除原有数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.InitializeStudentTable(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            初始化学员主表
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.InitializeImageTable(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            初始化图片表
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.InitializeLogTable(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            初始化日志表
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.InitializePayTable(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            初始化支付表
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.InitializeShouldPayTable(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            初始化应付款表
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.InitializeShouldPayDetailTable(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            初始化应付款明细表
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.InitializeStudentExamTable(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            初始化学员考试表
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.ImportStudentMainData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            导入学员主表数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.UpdateDepartmentData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            更新部门数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.GetOrCreateUser(PandaServer.System.Entity.UserEntity,System.Guid)">
            <summary>
            获取或创建用户
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.ImportStudentImageData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            导入学员图片数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.ImportLogData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            导入日志数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.ImportPayData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            导入支付数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.ImportShouldPayData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            导入应付款数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.ImportShouldPayDetailData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            导入应付款明细数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.ImportStudentExamData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            导入学员考试数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.UpdatePayTypeData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            更新支付方式数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.UpdateCostTypeData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            更新费用类型数据
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.JiaXiaoOldData.ImportStudentController.UpdateClassData(PandaServer.Web.Core.Controllers.JiaXiaoOldData.Dto.CompanyInPut)">
            <summary>
            更新班级数据
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Parking.CarController">
            <summary>
                停车场 关于 Car  服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.CarController.GetListByCarId(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.CarController.SetParkingCarInfo(PandaServer.System.Services.Parking.Dto.ParkingCarInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Parking.CarGroupController">
            <summary>
                停车场 关于 CarGroup  服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.CarGroupController.GetListByCarGroupId(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.CarGroupController.SetParkingCarGroupInfo(PandaServer.System.Services.Parking.Dto.ParkingCarGroupInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Parking.GateController">
            <summary>
                挺差成场地的相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.GateController.GetListByPlaceId(PandaServer.System.Services.Parking.Dto.ParkingGatePageInPut)">
            <summary>
                通过场地 Id 获取闸机列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.GateController.GetSelectListByPlaceId(PandaServer.Core.BaseIdInput)">
            <summary>
                通过 场地 Id 获取相关的试题列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.GateController.GetGateInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                获取 闸机的相关信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.GateController.SetGateInfo(PandaServer.System.Services.Parking.Dto.ParkingGateInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.GateController.DeleteGateInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                删除闸机信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Parking.LogController">
            <summary>
                停车场的日志服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.LogController.Page(PandaServer.System.Services.Parking.Dto.ParkingLogPageInPut)">
            <summary>
                日志 分页查询
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Parking.ParkingController">
            <summary>
                停车场服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.ParkingController.ReceiveDeviceInfo(System.String,System.String)">
            <summary>
                接受 心跳的信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.ParkingController.PlateResult">
            <summary>
                识别车牌推送结果
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Parking.ParkingDataController">
            <summary>
                停车场专门同步数据的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.ParkingDataController.ImportDataFromOldData(PandaServer.Core.BaseIdInput)">
            <summary>
                从老系统 导入
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Parking.PayController">
            <summary>
                停车场的缴费服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Parking.PlaceController">
            <summary>
                挺差成场地的相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.PlaceController.GetPlaceList(PandaServer.System.Services.Parking.Dto.ParkingPlacePageInPut)">
            <summary>
                翻页
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.PlaceController.GetPlaceSelectList">
            <summary>
                获取 下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.PlaceController.GetPlaceInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                通过 Id  返回 实体信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.PlaceController.SetPlaceInfo(PandaServer.System.Services.Parking.Dto.ParkingPlaceInPut)">
            <summary>
                数据保存
            </summary>
            <param name="inPut"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Parking.PlaceController.DeletePlaceInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                删除场地信息
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.Base.AccountController">
            <summary>
                系统结算账户管理
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.Base.CostTypeController">
            <summary>
                费用类型
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.Base.PayAccountController">
            <summary>
                支付账号相关的配置
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.Base.PayTypeController">
            <summary>
                费用类型
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Base.PayTypeController.GetPayTypeList(PandaServer.System.Services.Pay.Dto.PayTypePageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Base.PayTypeController.GetPayTypeSelectList(PandaServer.System.Services.Pay.Dto.PayTypePageInPut)">
            <summary>
                返回下拉
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Base.PayTypeController.GetPayTypeInfo(PandaServer.System.Services.Pay.Dto.PayTypePageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Base.PayTypeController.SetPayTypeInfo(PandaServer.System.Services.Pay.Dto.PayTypePageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.CallBack.FuBei.HnjxPayController">
            <summary>
                付呗支付相关  驾校相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.CallBack.FuBei.HnjxPayController.CallBack(PandaServer.System.Pay.FuBei.Models.Parameter.FubeiNotificationParam,System.String)">
            <summary>
                付呗支付 回调的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.CallBack.FuBei.JxCostTypeController">
            <summary>
                付呗支付相关  驾校相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.CallBack.FuBei.JxCostTypeController.CallBack(PandaServer.System.Pay.FuBei.Models.Parameter.FubeiNotificationParam,System.String)">
            <summary>
                付呗支付 回调的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.CallBack.FuBei.JxShouldPayController">
            <summary>
                付呗支付相关  驾校相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.CallBack.FuBei.JxShouldPayController.CallBack(PandaServer.System.Pay.FuBei.Models.Parameter.FubeiNotificationParam,System.String)">
            <summary>
                付呗支付 回调的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.CallBack.FuBei.ExamSiteItemController">
            <summary>
                付呗支付相关  订单相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.CallBack.FuBei.ExamSiteItemController.CallBack(PandaServer.System.Pay.FuBei.Models.Parameter.FubeiNotificationParam,System.String)">
            <summary>
                付呗支付 回调的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.CallBack.FuBeiController">
            <summary>
                付呗支付相关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.CallBack.JlPay.WxExamSiteItemController">
            <summary>
                嘉联支付相关(小程序的  收银托管的回调)
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.JlPayCallBack.JxShouldPayController">
            <summary>
                嘉联支付相关  驾校相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.JlPayCallBack.JxShouldPayController.CallBack">
            <summary>
                嘉联支付 回调的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.FbPayController">
            <summary>
                付呗支付 相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.FbPayController.ConfigWxJsapiPath(System.Guid,PandaServer.System.Services.Pay.Dto.PayAccountInPut)">
            <summary>
                配置微信JSAPI路径
            </summary>
            <param name="Id">支付账户ID</param>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.FbPayController.FreePay(System.Object)">
            <summary>
                付呗支付下单
            </summary>
            <param name="input">支付参数</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.Invoice.InvoiceController">
            <summary>
                发票管理
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Invoice.InvoiceController.GetInvoiceList(PandaServer.System.Services.Pay.Dto.Invoice.InvoicePageInPut)">
            <summary>
                获取发票分页列表
            </summary>
            <param name="input">分页查询参数</param>
            <returns>发票分页列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Invoice.InvoiceController.GetInvoiceInfo(System.Guid)">
            <summary>
                根据ID获取发票信息
            </summary>
            <param name="id">包含发票ID的输入参数</param>
            <returns>发票详细信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Invoice.InvoiceController.SetInvoiceInfo(System.Guid,PandaServer.System.Services.Pay.Dto.Invoice.InvoiceInPut)">
            <summary>
                新增或修改发票信息
            </summary>
            <param name="input">发票信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Invoice.InvoiceController.DeleteInvoice(PandaServer.Core.BaseIdInput)">
            <summary>
                删除发票信息
            </summary>
            <param name="input">包含发票ID的输入参数</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Invoice.InvoiceController.GetInvoiceByNsrsbh(System.String)">
            <summary>
                根据纳税人识别号获取发票配置
            </summary>
            <param name="nsrsbh">纳税人识别号</param>
            <returns>发票配置信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Invoice.InvoiceController.GetInvoiceByUsername(System.String)">
            <summary>
                根据用户名获取发票配置
            </summary>
            <param name="username">用户名</param>
            <returns>发票配置信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Invoice.InvoiceController.ValidateInvoiceConfig(PandaServer.System.Services.Pay.Dto.Invoice.InvoiceInPut)">
            <summary>
                验证发票配置
            </summary>
            <param name="input">发票配置信息</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.Invoice.InvoiceController.GetInvoiceSelectList(PandaServer.System.Services.Pay.Dto.Invoice.InvoicePageInPut)">
            <summary>
                获取发票下拉选择列表
            </summary>
            <param name="input">查询参数</param>
            <returns>下拉选择列表</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.ItemController">
            <summary>
                商品相关的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.JlPayController">
            <summary>
                嘉联支付 相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.PayController">
            <summary>
                费用类型
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.PayController.GetPayList(PandaServer.System.Services.Pay.Dto.OrderPageInPut)">
            <summary>
                三方订单的查询结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.PayController.ExportPayList(PandaServer.System.Services.Pay.Dto.OrderPageInPut)">
            <summary>
                三方订单的查询结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.PayController.SyncPayInfo(PandaServer.Core.BaseIdInput)">
            <summary>
                同步 缴费信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.PayController.GetOrderDetailList(PandaServer.Core.BaseIdInput)">
            <summary>
                获取 订单的明细
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.PayController.UpdateOrderSn(PandaServer.System.Services.Pay.Dto.OrderInPut)">
            <summary>
                更新订单号
            </summary>
            <param name="input">包含订单ID和新的订单号</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Pay.WxPayController">
            <summary>
                手机付款的接口
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.WxPayController.CreateOrder(PandaServer.System.Services.Pay.Dto.PayInPut)">
            <summary>
                创建订单
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Pay.WxPayController.GetStatus(PandaServer.System.Services.Pay.Dto.PayInPut)">
            <summary>
                获取订单状态
            </summary>
            <param name="input"></param>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto">
            <summary>
            UKey信息DTO
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto.HardwareId">
            <summary>
            硬件ID  主键
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto.Cookie">
            <summary>
            Cookie
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto.Domain">
            <summary>
            域名
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto.LoginTime">
            <summary>
            登录时间
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto.LastRefreshTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto.Jxmc">
            <summary>
            驾校名称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto.Sjhm">
            <summary>
            手机号
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageInput">
            <summary>
            UKey分页查询输入参数
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageInput.Jxmc">
            <summary>
            驾校名称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageInput.Sjhm">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageInput.HardwareId">
            <summary>
            硬件ID
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageInput.LoginTimes">
            <summary>
            登录时间范围
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageInput.RefreshTimes">
            <summary>
            最后刷新时间范围
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageOutput">
            <summary>
            UKey分页查询输出
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageOutput.RowIndex">
            <summary>
            行号
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageOutput.UserRealName">
            <summary>
            用户真实姓名
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageOutput.SessionStatus">
            <summary>
            会话状态
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageOutput.OnlineDuration">
            <summary>
            在线时长（分钟）
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Police.Dto.QueryXzqhInput">
            <summary>
            查询行政区划请求参数
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.QueryXzqhInput.Xzqhmc">
            <summary>
            行政区划名称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.QueryXzqhInput.HardwareId">
            <summary>
            硬件ID
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Police.Dto.CustomApiInput">
            <summary>
            自定义API调用请求参数
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.CustomApiInput.Method">
            <summary>
            API方法路径
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.CustomApiInput.Params">
            <summary>
            请求参数JSON字符串
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Police.Dto.CustomApiInput.HardwareId">
            <summary>
            硬件ID
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Police.UKeyController">
            <summary>
            UKey接口服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.GetUKeyPage(PandaServer.Web.Core.Controllers.Police.Dto.UKeyPageInput)">
            <summary>
            UKey分页查询
            </summary>
            <param name="input">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.GetSessionStatus(System.DateTime)">
            <summary>
            获取会话状态
            </summary>
            <param name="lastRefreshTime">最后刷新时间</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.SetUKey(PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto)">
            <summary>
            设置UKey信息
            </summary>
            <param name="input">UKey信息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.GetUKey(System.String)">
            <summary>
            获取UKey信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.GetMyUKey">
            <summary>
            获取所有UKey信息（管理员用）
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.GetAllUKey">
            <summary>
            获取所有UKey信息（管理员用）
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.GetAllUKeySelectList">
            <summary>
            获取所有UKey信息（管理员用）
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.RefreshSession(System.String)">
            <summary>
            刷新UKey会话
            </summary>
            <param name="hardwareId"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.QueryXzqhByMc(PandaServer.Web.Core.Controllers.Police.Dto.QueryXzqhInput)">
            <summary>
            查询行政区划信息
            </summary>
            <param name="input">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.ExecuteCustomApi(PandaServer.Web.Core.Controllers.Police.Dto.CustomApiInput)">
            <summary>
            执行自定义API调用
            </summary>
            <param name="input">自定义API调用参数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.GetUKeyByHardwareId(System.String,System.Boolean)">
            <summary>
            根据硬件ID获取UKey信息
            </summary>
            <param name="hardwareId">硬件ID</param>
            <param name="checkTenantId">是否检查租户ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.ExecuteApiRequest(PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto,System.String)">
            <summary>
            执行API请求
            </summary>
            <param name="userUKey">UKey信息</param>
            <param name="requestBody">请求体</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.RemoveUKeyFromCache(System.String)">
            <summary>
            从缓存中删除UKey记录
            </summary>
            <param name="hardwareId">硬件ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.UpdateUKeyLastRefreshTime(PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto)">
            <summary>
            更新UKey的最后刷新时间
            </summary>
            <param name="userUKey">UKey信息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.CreateConfiguredHttpClient(PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto)">
            <summary>
            创建和配置HttpClient
            </summary>
            <param name="userUKey">UKey信息</param>
            <returns>配置好的HttpClient</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Police.UKeyController.UpdateUserKeyFromResponseData(System.Text.Json.JsonElement,PandaServer.Web.Core.Controllers.Police.Dto.UKeyDto)">
            <summary>
            从响应数据中提取并更新UKey用户信息
            </summary>
            <param name="jsonResponse">JSON响应对象</param>
            <param name="userUKey">要更新的UKey对象</param>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.SystemManage.CategoryController">
            <summary>
                分类管理
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.CategoryController.GetCategoryList">
            <summary>
                返回 分类列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.CategoryController.GetCategoryDetailList(PandaServer.System.Services.SystemManage.Dtos.UserCategoryPageInPut)">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.CategoryController.GetCategoryDetailInfo(PandaServer.Core.BaseIdInput)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.CategoryController.SetCategoryDetailInfo(PandaServer.System.Services.SystemManage.Dtos.UserCategoryInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.CategoryController.GetParentSysIds(PandaServer.Core.BaseIdInput)">
            <summary>
                返回 父类 的 SysIds 数组
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.CategoryController.GetCategoryTreeList(PandaServer.System.Services.SystemManage.Dtos.UserCategoryPageInPut)">
            <summary>
                返回的 类别下面的树
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.CategoryController.GetCategoryIdListByUserId(System.Guid)">
            <summary>
                通过 用户 Id 获取到他选择了的 分类，如果没有传入用户 Id  就用当前登录的 用户 Id
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.SystemManage.DbController">
            <summary>
                数据库的相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.DbController.GetTableList(PandaServer.System.Services.SystemManage.Dtos.TablePageInPut)">
            <summary>
                返回 表的列表
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.DbController.UpdateDbTable(PandaServer.System.Services.SystemManage.Dtos.TablePageInPut)">
            <summary>
            </summary>
            <param name="inPut"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.SystemManage.LogController">
            <summary>
                日志管理
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.LogController.GetMyLog(PandaServer.System.Services.SystemSecurity.Dto.EasyLogPageInPut)">
            <summary>
                当前外部对象的日志
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.LogController.GetLogList(PandaServer.System.Services.SystemSecurity.Dto.EasyLogPageInPut)">
            <summary>
                当前外部对象的日志
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.SystemManage.ThirdConfigController">
            <summary>
                三方配置
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.SystemManage.ExcelDesignController">
            <summary>
                报表设计
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.User.JxUserInfoController.AddKeMuId(PandaServer.System.Entity.Student.User.Dtos.JxUserKeMuIdInPut)">
            <summary>
            添加用户科目ID
            </summary>
            <param name="input">用户ID</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.SystemManage.User.JxUserInfoController.DeleteKeMuId(PandaServer.System.Entity.Student.User.Dtos.JxUserKeMuIdInPut)">
            <summary>
            删除用户科目ID
            </summary>
            <param name="input">用户ID</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.SystemManage.User.UserAuditController">
            <summary>
                用户审核相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.SystemManage.PageDesignController">
            <summary>
                表格显示设计
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.SystemManage.UserSearchController">
            <summary>
                用户 页面搜索的地方 关于 搜索项目的设置
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.CopyMenuInput.TenantId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.CopyMenuInput.FromTenantId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.Id">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.ManageTenantId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.TenantName">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.CreatorTime">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.ServiceEndTime">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.TenantType">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.AccountRule">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.ProvinceId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.CityId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.AreaId">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.Address">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.Remark">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.AdminName">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.Phone">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.Email">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.Host">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.Connection">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantInfo.PlusEndTime">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListInPut">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListInPut.Id">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListInPut.TenantName">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListOutPut.Id">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListOutPut.TenantName">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListOutPut.AccountRule">
            <summary>
                账号名字
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListOutPut.CreatorTime">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListOutPut.ServiceEndTime">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListOutPut.TenantType">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListOutPut.TenantTypeName">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Tenant.Dto.TenantListOutPut.Status">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Tenant.ManageTenantController">
            <summary>
                管理相关的
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Tenant.PayController">
            <summary>
                费用类型
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Tenant.TenantController">
            <summary>
                登录授权相关服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Tenant.TenantInfoController">
            <summary>
                登录授权相关服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Tenant.TenantInfoUpdateController">
            <summary>
                登录授权相关服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.TheoreticalExam.TheoreticalExamController">
            <summary>
                理论在线测试的接口
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.TheoreticalExam.TheoreticalExamController.SyncQuestions(PandaServer.System.Services.TheoreticalExam.Dto.TheoreticalExamQuestionInput)">
            <summary>
                同步理论考试题目
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.TheoreticalExam.TheoreticalExamController.GetQuestions(PandaServer.System.Services.TheoreticalExam.Dto.TheoreticalExamQuestionPageInput)">
            <summary>
                获取理论考试题目
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.WxWork.GroupController">
            <summary>
            群聊的接口
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WxWork.GroupController.GetAccessToken">
            <summary>
            获取企业微信Access Token（用于调试）
            </summary>
            <returns>Access Token</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WxWork.GroupController.AddJoinWay(PandaServer.System.Services.Wx.WeWorkAddJoinWayRequest)">
            <summary>
            配置客户群进群方式
            </summary>
            <param name="request">进群方式配置请求</param>
            <returns>配置ID</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WxWork.GroupController.GetJoinWay(System.String)">
            <summary>
            获取客户群进群方式配置
            </summary>
            <param name="configId">配置ID</param>
            <returns>进群方式配置</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WxWork.GroupController.UpdateJoinWay(System.String,PandaServer.System.Services.Wx.WeWorkAddJoinWayRequest)">
            <summary>
            更新客户群进群方式配置
            </summary>
            <param name="configId">配置ID</param>
            <param name="request">更新请求</param>
            <returns>是否更新成功</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WxWork.GroupController.DeleteJoinWay(System.String)">
            <summary>
            删除客户群进群方式配置
            </summary>
            <param name="configId">配置ID</param>
            <returns>是否删除成功</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WxWork.GroupController.GenerateJoinWayQrCode(System.Collections.Generic.List{System.String},System.String,System.String)">
            <summary>
            生成客户群进群二维码
            </summary>
            <param name="chatIdList">客户群ID列表</param>
            <param name="remark">备注信息</param>
            <param name="state">自定义状态参数</param>
            <returns>配置ID和二维码信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WxWork.GroupController.GenerateQuickJoinWayQrCode(System.String)">
            <summary>
            快速生成客户群进群二维码（简化版）
            </summary>
            <param name="chatId">单个客户群ID</param>
            <returns>二维码信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WxWork.GroupController.CreateExternalGroup(PandaServer.Web.Core.Controllers.WxWork.CreateExternalGroupRequest)">
            <summary>
            创建企业微信外部群
            </summary>
            <param name="request">外部群创建请求</param>
            <returns>外部群配置ID和二维码信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WxWork.GroupController.CreateQuickExternalGroup(System.String,System.Int32,System.String)">
            <summary>
            快速创建企业微信外部群（简化版）
            </summary>
            <param name="groupName">群名称（可选，默认为：租户名称 - 外部群）</param>
            <param name="maxMembers">最大成员数（可选，默认为200）</param>
            <param name="welcomeMessage">欢迎语（可选）</param>
            <returns>外部群配置ID和二维码信息</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.WxWork.CreateExternalGroupRequest">
            <summary>
            创建外部群请求
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.WxWork.CreateExternalGroupRequest.GroupName">
            <summary>
            群名称（可选，默认为：租户名称 - 外部群）
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.WxWork.CreateExternalGroupRequest.MaxMembers">
            <summary>
            最大成员数（默认为200）
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.WxWork.CreateExternalGroupRequest.WelcomeMessage">
            <summary>
            欢迎语（可选）
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.WxWork.CreateExternalGroupRequest.AutoCreateRoom">
            <summary>
            是否自动创建群（默认为true）
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.WxWork.CreateExternalGroupRequest.State">
            <summary>
            自定义状态参数（可选）
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.WeWork.MPMessageController">
            <summary>
                企业微信 消息回调接口
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WeWork.MPMessageController.Api_Get(System.String,System.String,System.String,System.String)">
            <summary>
            企业微信回调URL验证
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WeWork.MPMessageController.Api_Post(System.String,System.String,System.String)">
            <summary>
            企业微信消息推送处理
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.WeWork.MPMessageController.ValidateSignature(System.String,System.String,System.String,System.String)">
            <summary>
            验证签名
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.Dtos.CheckSubscribeInPut">
            <summary>
            检查公众号关注状态的输入参数
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.CheckSubscribeInPut.OpenId">
            <summary>
            小程序的OpenId
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.CheckSubscribeInPut.MpAppId">
            <summary>
            公众号的AppId
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.Dtos.CheckSubscribeOutPut">
            <summary>
            检查公众号关注状态的输出结果
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.CheckSubscribeOutPut.IsSubscribed">
            <summary>
            是否已关注
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.CheckSubscribeOutPut.SubscribeTime">
            <summary>
            关注时间
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.CheckSubscribeOutPut.MpOpenId">
            <summary>
            公众号的OpenId
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.Dtos.GenerateQrCodeInPut">
            <summary>
            生成公众号关注二维码的输入参数
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.GenerateQrCodeInPut.MpAppId">
            <summary>
            公众号的AppId
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.Dtos.GenerateQrCodeOutPut">
            <summary>
            生成公众号关注二维码的输出结果
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.GenerateQrCodeOutPut.QrCodeBase64">
            <summary>
            二维码图片的Base64编码字符串
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.GenerateQrCodeOutPut.QrCodeUrl">
            <summary>
            二维码的URL
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.GenerateQrCodeOutPut.ExpireSeconds">
            <summary>
            二维码的有效期（秒）
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.GetAccountListInPut.PhoneId">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.Dtos.GetAccountListInPut.Phone">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.JxUserController">
            <summary>
                微信用户登录相关
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.LoginController">
            <summary>
                微信用户登录相关
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.LoginController.GetUserPhones">
            <summary>
            获取用户绑定的手机号列表
            </summary>
            <returns>手机号列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.LoginController.SwitchBinding(PandaServer.System.Services.Auth.Dto.LoginInput)">
            <summary>
            切换微信账号绑定
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.OpenIdController">
            <summary>
            处理微信OpenId相关的接口
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.OpenIdController.GetOpenId(PandaServer.Web.Core.Controllers.Wx.Dtos.GetOpenIdInPut)">
            <summary>
            通过code获取OpenId
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.OpenIdController.GetMpOpenId">
            <summary>
            获取公众号OpenId
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.PhoneController">
            <summary>
            微信用户手机号相关接口
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.PhoneController.GetPhoneList">
            <summary>
            获取当前OpenId绑定的手机号列表
            </summary>
            <returns>绑定的手机号列表</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.PhoneController.VerifyPhoneNumber(PandaServer.Core.BaseWxInPut)">
            <summary>
            获取微信用户手机号
            </summary>
            <param name="input">请求参数</param>
            <returns>手机号信息</returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.SysController">
            <summary>
                微信 Token  系统相关的模块的接口
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.WxAuthController">
            <summary>
                微信授权相关的方法
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.WxAuthController.#ctor(PandaServer.System.Services.Wx.IWxUserService,PandaServer.System.Services.Auth.IAuthService,PandaServer.System.Services.Wx.IWxConfigService)">
            <summary>
            初始化<see cref="T:PandaServer.Web.Core.Controllers.Wx.WxAuthController"/>类的新实例
            </summary>
            <param name="wxUserService">微信用户服务</param>
            <param name="authService">认证服务</param>
            <param name="wxConfigService">微信配置服务</param>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.WxAuthController.AuthLoginAsync(System.String)">
            <summary>
                微信平台 先登录系统
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.WxAuthController.GetWxUser">
            <summary>
                通过 OpenId 获取到 用户信息
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.WxConfigController">
            <summary>
                微信授权相关的方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.WxMediaController">
            <summary>
                微信登录的相关方法
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.WxMediaController.GetMaterialListRequest.Type">
            <summary>
            素材的类型，图片（image）、视频（video）、语音（voice）、图文（news）
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.WxMediaController.GetMaterialListRequest.Current">
            <summary>
            当前页码，从1开始
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Controllers.Wx.WxMediaController.GetMaterialListRequest.PageSize">
            <summary>
            每页数量，最大20
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.WxMediaController.GetMaterialList(PandaServer.Web.Core.Controllers.Wx.WxMediaController.GetMaterialListRequest)">
            <summary>
            获取永久素材列表
            </summary>
            <remarks>
            示例请求:
            POST /Wx/WxMedia/material/list
            {
                "type": "image",
                "current": 1,      // 当前页码，从1开始
                "pageSize": 15     // 每页数量，最大20
            }
            </remarks>
        </member>
        <member name="T:PandaServer.Web.Core.Controllers.Wx.WxSubscribeController">
            <summary>
            微信公众号关注相关的控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.WxSubscribeController.#ctor(PandaServer.System.Services.Wx.IWxUserService,PandaServer.System.Services.Wx.IWxConfigService,PandaServer.System.Services.Wx.WxLongTicketService)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.WxSubscribeController.CheckSubscribe(PandaServer.Web.Core.Controllers.Wx.Dtos.CheckSubscribeInPut)">
            <summary>
            检查用户是否关注了指定的公众号
            </summary>
            <param name="input">包含小程序OpenId和公众号AppId的输入参数</param>
            <returns>关注状态信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.WxSubscribeController.GetMpOpenId(PandaServer.Web.Core.Controllers.Wx.Dtos.CheckSubscribeInPut)">
            <summary>
            获取用户在公众号的OpenId
            </summary>
            <param name="input">包含小程序OpenId和公众号AppId的输入参数</param>
            <returns>公众号的OpenId</returns>
        </member>
        <member name="M:PandaServer.Web.Core.Controllers.Wx.WxSubscribeController.GenerateSubscribeQrCode(PandaServer.Web.Core.Controllers.Wx.Dtos.GenerateQrCodeInPut)">
            <summary>
            生成普通关注公众号的二维码
            </summary>
            <param name="input">包含公众号 AppId 的输入参数</param>
            <returns>二维码信息</returns>
        </member>
        <member name="T:PandaServer.Web.Core.WxBindUserController">
            <summary>
                微信绑定用户
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.PlanDetailController">
            <summary>
                考场排队计划 操作的相关方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.PlanDetailSpecialController">
            <summary>
                考场排队计划
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.PlanDetailSpecialController.TogglePlanDetail(PandaServer.System.PlanDetailSpecialInPut)">
            <summary>
            切换排班明细状态
            </summary>
            <param name="inPut">特殊配置输入参数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.PlanDetailSpecialController.SetMaxPeople(PandaServer.System.PlanDetailSpecialInPut)">
            <summary>
            设置最大人数
            </summary>
            <param name="inPut">特殊配置输入参数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.PlanDetailSpecialController.GetCount(PandaServer.System.PlanDetailSpecialInPut)">
            <summary>
            获取预约人数信息
            </summary>
            <param name="inPut">特殊配置输入参数</param>
            <returns>预约人数信息</returns>
        </member>
        <member name="T:PandaServer.Web.Core.PlanQueenController">
            <summary>
                考场排队结果
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.SaleController">
            <summary>
                考场收费相关的信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.BaseController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.StudentImageInPut.StudentId">
            <summary>
            
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.StudentImageInPut.ImageId">
            <summary>
            
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.StudentImageInPut.Img1">
            <summary>
            
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.StudentImageInPut.Img2">
            <summary>
            
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.IdCardController">
            <summary>
            身份证识别控制器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.IdCardController.GetIdCardInfo(Microsoft.AspNetCore.Http.IFormFile,System.Int32)">
            <summary>
            识别并获取身份证信息
            </summary>
            <param name="file">身份证图片文件</param>
            <param name="side">识别方向：-1自动检测，0正面，1反面</param>
            <returns>身份证信息</returns>
        </member>
        <member name="M:PandaServer.Web.Core.IdCardController.ConvertImageToBase64(SixLabors.ImageSharp.Image)">
            <summary>
            将图像转换为Base64字符串
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.ImageController">
            <summary>
            图片相关的
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.ImageController.UploadTempImage(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传临时图片
            </summary>
            <param name="file">图片文件</param>
            <returns>图片保存路径</returns>
        </member>
        <member name="M:PandaServer.Web.Core.ImageController.CleanupOldTempFolders(System.String)">
            <summary>
            清理昨天及以前的临时文件夹
            </summary>
            <param name="tempBasePath">临时文件夹基础路径</param>
        </member>
        <member name="T:PandaServer.Web.Core.StudentImageController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.StudentImageController.DownloadImage(System.String)">
            <summary>
            </summary>
            <param name="imagePath"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.StudentImageController.MakeMyIdCard(PandaServer.Core.BaseIdInput)">
            <summary>
                合成 身份证的 正反面
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.StudentImageController.UploadStudentImageData(PandaServer.System.Services.Student.Dto.JxStudentImageInPut)">
            <summary>
                上传 Base 64 的图片
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.StudentImageController.DeleteImage(PandaServer.Core.BaseIdInput)">
            <summary>
                删除照片
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.JxShouldPayController">
            <summary>
                驾校学员服务
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.MPMessageController">
            <summary>
                微信 公众号 消息  的相关方法
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.MyActionFilter">
            <summary>
                操作筛选器
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.MyAuthorizationFilter">
            <summary>
                自定义授权筛选器
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.JwtHandler">
            <summary>
            JWT 授权处理程序，负责处理JWT令牌的验证、刷新和授权检查
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.JwtHandler.HandleAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,Microsoft.AspNetCore.Http.DefaultHttpContext)">
            <summary>
            处理授权请求，验证令牌有效性并自动刷新令牌
            </summary>
            <param name="context">授权处理上下文</param>
            <param name="httpContext">HTTP上下文</param>
            <returns>处理任务</returns>
        </member>
        <member name="M:PandaServer.Web.Core.JwtHandler.CheckTokenFromRedis(Microsoft.AspNetCore.Http.DefaultHttpContext,System.Int32)">
            <summary>
            检查token有效性，从Redis中获取并验证token信息
            如果令牌被刷新，会更新Redis中存储的token信息
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="expire">token有效期(分钟)</param>
            <returns>如果token有效则返回true，否则返回false</returns>
        </member>
        <member name="M:PandaServer.Web.Core.JwtHandler.PipelineAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,Microsoft.AspNetCore.Http.DefaultHttpContext)">
            <summary>
            授权判断逻辑，调用CheckAuthorization进行具体的授权检查
            JWT Token的有效性已经在此方法之前自动验证
            </summary>
            <param name="context">授权处理上下文</param>
            <param name="httpContext">HTTP上下文</param>
            <returns>授权通过返回true，否则返回false</returns>
        </member>
        <member name="M:PandaServer.Web.Core.JwtHandler.CheckAuthorization(Microsoft.AspNetCore.Http.DefaultHttpContext)">
            <summary>
            检查用户权限，根据用户角色和接口特性判断是否有权访问
            包括超级管理员权限检查和基于角色的权限检查
            </summary>
            <param name="httpContext">HTTP上下文</param>
            <returns>如果有权限则返回true，否则返回false</returns>
        </member>
        <member name="T:PandaServer.Web.Core.LogExceptionHandler">
            <summary>
                全局异常处理提供器
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.DatabaseLoggingWriter">
            <summary>
                数据库写入器
            </summary>
        </member>
        <member name="M:PandaServer.Web.Core.DatabaseLoggingWriter.CreateVisitLog(System.String,PandaServer.Web.Core.LoggingMonitorJson,UAParser.ClientInfo)">
            <summary>
                创建访问日志
            </summary>
            <param name="operation">访问类型</param>
            <param name="loggingMonitor">loggingMonitor</param>
            <param name="clientInfo">客户端信息</param>
        </member>
        <member name="M:PandaServer.Web.Core.DatabaseLoggingWriter.CreateOperationLog(System.String,System.String,PandaServer.Web.Core.LoggingMonitorJson,UAParser.ClientInfo)">
            <summary>
                创建操作日志
            </summary>
            <param name="operation">操作名称</param>
            <param name="path">请求地址</param>
            <param name="loggingMonitor">loggingMonitor</param>
            <param name="clientInfo">客户端信息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Web.Core.DatabaseLoggingWriter.GetLoginAddress(System.String)">
            <summary>
                解析IP地址
            </summary>
            <param name="ip"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Web.Core.LoggingConst">
            <summary>
                日志常量
            </summary>
        </member>
        <member name="F:PandaServer.Web.Core.LoggingConst.Operation">
            <summary>
                操作名称
            </summary>
        </member>
        <member name="F:PandaServer.Web.Core.LoggingConst.Client">
            <summary>
                客户端信息
            </summary>
        </member>
        <member name="F:PandaServer.Web.Core.LoggingConst.Path">
            <summary>
                请求地址
            </summary>
        </member>
        <member name="F:PandaServer.Web.Core.LoggingConst.Method">
            <summary>
                请求方法：POST/GET
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.LoggingMonitorJson">
            <summary>
                请求信息格式化
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.Title">
            <summary>
                标题
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.DisplayTitle">
            <summary>
                操作名称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.ControllerName">
            <summary>
                控制器名
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.ActionName">
            <summary>
                方法名称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.DisplayName">
            <summary>
                类名称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.LocalIPv4">
            <summary>
                服务端
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.RemoteIPv4">
            <summary>
                客户端IPV4地址
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.HttpMethod">
            <summary>
                请求方法
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.RequestUrl">
            <summary>
                请求地址
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.UserAgent">
            <summary>
                浏览器标识
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.OsDescription">
            <summary>
                系统名称
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.OsArchitecture">
            <summary>
                系统架构
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.Environment">
            <summary>
                环境
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.AuthorizationClaims">
            <summary>
                认证信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.Parameters">
            <summary>
                参数列表
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.ReturnInformation">
            <summary>
                返回信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.Exception">
            <summary>
                异常信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.Validation">
            <summary>
                验证错误信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.LoggingMonitorJson.LogDateTime">
            <summary>
                日志时间
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.AuthorizationClaims">
            <summary>
                认证信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.AuthorizationClaims.Type">
            <summary>
                类型
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.AuthorizationClaims.Value">
            <summary>
                值
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Parameters">
            <summary>
                请求参数
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Parameters.Name">
            <summary>
                参数名
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Parameters.Value">
            <summary>
                值
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.ReturnInformation">
            <summary>
                返回信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.ReturnInformation.Value">
            <summary>
                返回值
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.ReturnInformation.RetrunValue.Code">
            <summary>
                返回码
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.ReturnInformation.RetrunValue.Msg">
            <summary>
                消息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.ReturnInformation.RetrunValue.extras">
            <summary>
                额外信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.ReturnInformation.RetrunValue.Data">
            <summary>
                内如
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.ReturnInformation.RetrunValue.Time">
            <summary>
                时间
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Exception">
            <summary>
                异常信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Exception.Type">
            <summary>
                异常类型
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Exception.Message">
            <summary>
                异常内容
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Exception.StackTrace">
            <summary>
                堆栈信息
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Validation">
            <summary>
                验证失败信息
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.Validation.Message">
            <summary>
                错误详情
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.WebSettingsOptions">
            <summary>
                系统配置选项
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.WebSettingsOptions.EnvPoc">
            <summary>
                是否演示环境
            </summary>
        </member>
        <member name="P:PandaServer.Web.Core.WebSettingsOptions.ClearRedis">
            <summary>
                是否清除Redis缓存
            </summary>
        </member>
        <member name="T:PandaServer.Web.Core.Startup">
            <summary>
                Web启动项配置
            </summary>
        </member>
    </members>
</doc>
