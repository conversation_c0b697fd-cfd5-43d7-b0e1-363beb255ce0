using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Spire.Barcode;
using System;
using System.Drawing;
using System.IO;
using Furion.FriendlyException;

namespace PandaServer.Web.Core.Controllers.Base;

/// <summary>
///     图片相关
/// </summary>
[ApiDescriptionSettings(Name = "Base/Image", Order = 200)]
[Route("Base/Image")]
public class ImageController : IDynamicApiController
{
    /// <summary>
    ///     识别图片中的二维码内容
    /// </summary>
    /// <param name="file">图片文件</param>
    /// <returns>二维码内容</returns>
    [HttpPost("DecodeQrCode")]
    public string DecodeQrCode(IFormFile file)
    {
        try
        {
            // 验证文件
            if (file == null || file.Length == 0)
            {
                throw Oops.Bah("请上传有效的图片文件");
            }

            // 验证文件类型
            string fileExtension = Path.GetExtension(file.FileName).ToLower();
            string[] allowedExtensions = { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };
            if (!Array.Exists(allowedExtensions, ext => ext == fileExtension))
            {
                throw Oops.Bah("请上传有效的图片文件（jpg, jpeg, png, bmp, gif）");
            }

            // 保存图片到临时文件
            string tempFilePath = Path.GetTempFileName() + fileExtension;

            try
            {
                // 将上传的文件保存到临时文件
                using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                {
                    file.CopyTo(fileStream);
                }

                // 使用静态方法扫描二维码
                string[] result = BarcodeScanner.Scan(tempFilePath);

                // 检查扫描结果
                if (result == null || result.Length == 0)
                {
                    throw Oops.Bah("未识别到二维码，请确保图片清晰且包含完整的二维码");
                }

                // 返回第一个识别结果
                return result[0];
            }
            catch (global::System.Exception ex)
            {
                // 处理扫描过程中的异常
                // 直接抛出异常，由全局异常处理器处理
                throw Oops.Bah($"二维码识别失败: {ex.Message}");
            }
            finally
            {
                // 删除临时文件
                if (File.Exists(tempFilePath))
                {
                    File.Delete(tempFilePath);
                }
            }
        }
        catch (global::System.Exception ex)
        {
            // 捕获所有异常，转换为友好的错误消息
            throw Oops.Bah($"二维码识别失败: {ex.Message}");
        }
    }
}
