using System;
using PandaServer.Core.Utils.Excel;
using PandaServer.System.Services.ImportExport;
using PandaServer.System.Services.SystemManage;
using PandaServer.System.Services.SystemManage.Dtos;
using PandaServer.System.Services.SystemOrganize;
using PandaServer.System.Services.SystemOrganize.Dtos;
using System.Data;

namespace PandaServer.Web.Core.Controllers.SystemManage;

[ApiDescriptionSettings("系统管理", GroupName = "SystemManage", Name = "SystemManage/User", Order = 200)]
[Route("SystemManage/User")]
public class UserController : IDynamicApiController
{
    private readonly IUserService _userService;
    private readonly IUserInfoService _userInfoService;
    private readonly IImportExportService _importExportService;
    private readonly IUserCategoryService _userCategoryService;
    private readonly IExcelDesignService _excelDesignService;


    public UserController(IUserService userService, IUserInfoService userInfoService,
        IImportExportService importExportService, IUserCategoryService userCategoryService,
        IExcelDesignService excelDesignService)
    {
        _userService = userService;
        _userInfoService = userInfoService;
        _importExportService = importExportService;
        _userCategoryService = userCategoryService;
        _excelDesignService = excelDesignService;
    }

    [HttpPost("getUserList")]
    [Description("获得用户的列表")]
    public async Task<dynamic> GetUserList(UserPageInPut input)
    {
        input.TenantId = UserManager.TenantId;
        var data = await _userInfoService.Page(input);

        return data;
    }


    [HttpPost("exportUserList")]
    [Description("导出用户的列表")]
    public async Task<dynamic> ExportUserList(UserPageInPut input)
    {
        input.TenantId = UserManager.TenantId;
        input.Size = 99999;
        input.Current = 1;

        var carData = await _userInfoService.Page(input);

        // 获取用户列表
        var userList = carData.Records.ToList();

        // 步骤1: 获取列配置
        var columnConfigs = await _excelDesignService.GetTableByDesignId(input.DesignId, input.TableId);

        // 步骤2: 创建DataTable
        var dataTable = _importExportService.CreateDataTable(userList, columnConfigs);

        var userCategories = await _userCategoryService.GetList(UserManager.TenantId);
        var rootCategories = userCategories.Where(m => m.ParentId == Guid.Empty).ToList();

        foreach (var rootCategorie in rootCategories)
        {
            dataTable.Columns.Add(rootCategorie.Name);
        }

        for (int i = 0; i < dataTable.Rows.Count; i++)
        {
            foreach (var rootCategorie in rootCategories)
            {
                var matchingCategories = userList[i].CategoryDatas?.Where(x => x.CategoryId == rootCategorie.Id).ToList();
                string categoryNames = matchingCategories != null && matchingCategories.Any()
                    ? string.Join(", ", matchingCategories.Select(x => x.CategoryName))
                    : "";
                dataTable.Rows[i][rootCategorie.Name] = categoryNames;
            }
        }

        // 步骤3: 将DataTable转换为文件流
        var result = await _importExportService.Export(dataTable, "账号信息表");

        return result;
    }


    [HttpPost("getUserSelectList")]
    [Description("用户的搜索下拉数据")]
    public async Task<dynamic> GetUserSelectList(UserPageInPut input)
    {
        input.Current = 1;
        input.Size = 20;
        var result = new List<object>();

        input.TenantId = UserManager.TenantId;
        var data = await _userInfoService.Page(input);
        result.AddRange(data.Records.Select(m => new BaseSelectorOutput
        {
            Value = m.Id,
            Label = m.RealName + (m.Phone.Length == 11
                ? "  " + m.Phone
                : "") + " " + m.PinYin.ToUpper(),
            Text = m.RealName + (m.Phone.Length == 11
                ? "  " + m.Phone
                : "") + " " + m.PinYin.ToUpper()
        }));

        if (!string.IsNullOrEmpty(input.Id))
        {
            var userIds = input.Id.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).ToList();

            foreach (var userId in userIds)
                if (userId.ParseToGuid() != Guid.Empty &&
                    data.Records.Where(m => m.Id == userId.ParseToGuid()).Count() == 0)
                {
                    var user = await _userInfoService.GetById(userId.ParseToGuid());
                    if (user != null)
                        result.Add(new BaseSelectorOutput
                        {
                            Value = user.Id,
                            Label = user.RealName + (user.Phone.Length == 11
                                ? "  " + user.Phone
                                : "") + " " + user.PinYin.ToUpper(),
                            Text = user.RealName + (user.Phone.Length == 11
                                ? "  " + user.Phone
                                : "") + " " + user.PinYin.ToUpper()
                        });
                }
        }

        return result;
    }


    [HttpPost("uploadExcel")]
    [Description("直接导入账号的 Excel")]
    public async Task<dynamic> UploadExcel(IFormFile file)
    {
        var userTable = ExcelData.GetTableByFile(file);

        if (!await _userInfoService.Add(userTable))
            throw Oops.Bah("导入数据失败，稍后重试");

        return "导入成功";
    }


    [HttpDelete("deleteUserInfo")]
    [Description("删除用户")]
    public async Task<string> DeleteUserInfo(BaseIdInput input)
    {
        if (await _userInfoService.UpdateDeleteByUserId(input.Id))
            return "账号信息删除成功";
        throw Oops.Bah("删除账号信息失败");
    }

    [HttpPost("uploadImage")]
    [Description("上传寸照")]
    public async Task<dynamic> UploadImage(Guid id, IFormFile file)
    {
        var user = await _userInfoService.GetById(id);

        if (user == null)
            throw Oops.Bah("用户信息为空，刷新页面重试");

        await _userService.Upload(user, file);
        return "上传成功";
    }

    [HttpPost("getUserIdList")]
    [Description("通过搜索条件 获取 全部的用户的Id")]
    public async Task<dynamic> GetUserIdList(UserPageInPut input)
    {
        input.Size = 99999;
        input.TenantId = UserManager.TenantId;
        var data = await _userInfoService.Page(input);

        var result = data.Records.Select(m => m.Id).ToList();

        return result;
    }

    [HttpPut("openUser")]
    [Description("打开用户的状态")]
    public async Task<dynamic> OpenUser(BaseIdInput input)
    {
        var user = await _userInfoService.GetById(input.Id);

        if (user == null)
            throw Oops.Bah("用户信息为空，刷新页面重试");

        user.IsEnabled = true;
        user.Modify();

        if (!await _userInfoService.Update(user))
            throw Oops.Bah("更新失败");

        return "更新成功";
    }

    [HttpPut("closeUser")]
    [Description("打开用户的状态")]
    public async Task<dynamic> CloseUser(BaseIdInput input)
    {
        var user = await _userInfoService.GetById(input.Id);

        if (user == null)
            throw Oops.Bah("用户信息为空，刷新页面重试");

        user.IsEnabled = false;
        user.Modify();

        if (!await _userInfoService.Update(user))
            throw Oops.Bah("更新失败");

        return "更新成功";
    }
}
