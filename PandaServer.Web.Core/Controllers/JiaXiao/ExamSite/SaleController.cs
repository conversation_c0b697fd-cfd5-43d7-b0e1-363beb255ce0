
using PandaServer.System.Services.Shop.Dtos;
using PandaServer.System.Services.SystemManage;

namespace PandaServer.Web.Core;

/// <summary>
///     考场收费相关的信息
/// </summary>
[ApiDescriptionSettings("考场服务", GroupName = "ExamSite", Name = "Jx/ExamSite/Sale", Order = 200)]
[Route("Jx/ExamSite/Sale")]
public class SaleController : IDynamicApiController
{
    private readonly ISaleService _saleService;
    private readonly IImportExportService _importExportService;
    private readonly ISalePrintLogService _salePrintLogService;
    private readonly IUserRoleService _userRoleService;

    public SaleController(ISaleService saleService, IImportExportService importExportService, ISalePrintLogService salePrintLogService, IUserRoleService userRoleService)
    {
        _saleService = saleService;
        _importExportService = importExportService;
        _salePrintLogService = salePrintLogService;
        _userRoleService = userRoleService;
    }



    [HttpPost("getMySaleList")]
    [Description("获取商品的销售列表")]
    [AllowAnonymous]
    public async Task<dynamic> GetMySaleList(SalePageInPut input)
    {
        var result = await _saleService.Page(input);

        return result;
    }

    [HttpPost("getSaleList")]
    [Description("获取商品的销售列表")]
    public async Task<dynamic> GetSaleList(SalePageInPut input)
    {
        var result = await _saleService.Page(input);

        var summary = await _saleService.GetSummary(input);

        return new
        {
            data = result,
            summary = summary
        };
    }


    [HttpPost("exportSaleList")]
    [Description("获取商品的销售列表")]
    public async Task<dynamic> ExportSaleList(SalePageInPut input)
    {
        if (!UserManager.IsTenantAdmin && !await _userRoleService.Exists(PermissionConst.EXAM_APPOINTMENT_EXAM_FINANCE_EXPORT))
        {
            throw Oops.Bah("没有权限");
        }

        input.Size = 99999;
        input.Current = 1;
        var payData = await _saleService.Page(input);

        var result =
            await _importExportService.Export(payData.Records.ToList(), input.DesignId, "销售记录", input.TableId);

        return result;
    }



    [HttpPost("Info/{id}")]
    [Description("获取商品的销售列表")]
    public async Task<dynamic> GetSaleInfo(Guid id)
    {
        var result = await _saleService.GetById(id, UserManager.TenantId);

        return result;
    }

    [HttpPut("resetPrint")]
    [Description("重置打印状态")]
    public async Task<dynamic> ResetPrint(BaseIdInput input)
    {
        if (input.Id == Guid.Empty)
        {
            throw Oops.Bah("销售ID不能为空");
        }

        // 验证销售记录是否存在
        var sale = await _saleService.GetById(input.Id);
        if (sale == null)
        {
            throw Oops.Bah("销售记录不存在");
        }

        // 重置打印状态
        if (await _salePrintLogService.ResetPrint(input.Id))
        {
            return "重置打印状态成功";
        }

        throw Oops.Bah("重置打印状态失败");
    }
}