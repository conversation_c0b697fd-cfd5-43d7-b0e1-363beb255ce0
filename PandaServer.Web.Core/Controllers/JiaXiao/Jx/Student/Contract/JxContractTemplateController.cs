using Mapster;
using PandaServer.System.Cloud.Tencent;
using PandaServer.Core.Utils;
using Spire.Pdf;

namespace PandaServer.Web.Core.Controllers.Jx.JiaXiao;

/// <summary>
///     驾校 合同 模板
/// </summary>
[ApiDescriptionSettings("驾校服务", GroupName = "JiaXiao", Name = "Jx/Student/Contract/JxContractTemplate", Order = 200)]
[Route("Jx/Student/Contract/JxContractTemplate")]
public class JxContractTemplateController : IDynamicApiController
{
    private readonly IJxContractTemplateCarTypeService _jxContractTemplateCarTypeService;
    private readonly IJxContractTemplateJxClassService _jxContractTemplateJxClassService;
    private readonly IJxContractTemplateJxDeptService _jxContractTemplateJxDeptService;
    private readonly IJxContractTemplateJxFieldService _jxContractTemplateJxFieldService;
    private readonly IJxContractTemplateService _jxContractTemplateService;

    public JxContractTemplateController(IJxContractTemplateService jxContractTemplateService,
        IJxContractTemplateCarTypeService jxContractTemplateCarTypeService,
        IJxContractTemplateJxClassService jxContractTemplateJxClassService,
        IJxContractTemplateJxDeptService jxContractTemplateJxDeptService,
        IJxContractTemplateJxFieldService jxContractTemplateJxFieldService)
    {
        _jxContractTemplateService = jxContractTemplateService;

        _jxContractTemplateCarTypeService = jxContractTemplateCarTypeService;
        _jxContractTemplateJxClassService = jxContractTemplateJxClassService;
        _jxContractTemplateJxDeptService = jxContractTemplateJxDeptService;
        _jxContractTemplateJxFieldService = jxContractTemplateJxFieldService;
    }


    /// <summary>
    ///     合同 模板列表
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("getJxContractTemplateList")]
    public async Task<dynamic> GetJxContractTemplateList(JxContractTemplatePageInPut input)
    {
        return await _jxContractTemplateService.Page(input);
    }


    [HttpPost("getContractTemplateInfo/{id}")]
    public async Task<dynamic> GetContractTemplateInfo(Guid id)
    {
        var data = await _jxContractTemplateService.GetById(id);
        var item = data.Adapt<JxContractTemplatePageInPut>();
        item.CarTypes = (await _jxContractTemplateCarTypeService.GetListByTemplateId(id)).Select(m => m.CarType)
            .ToList();
        item.JxClassIds = (await _jxContractTemplateJxClassService.GetListByTemplateId(id))
            .Select(m => m.JxClassId)
            .ToList();
        item.JxDeptIds = (await _jxContractTemplateJxDeptService.GetListByTemplateId(id)).Select(m => m.JxDeptId)
            .ToList();
        item.JxFieldIds = (await _jxContractTemplateJxFieldService.GetListByTemplateId(id))
            .Select(m => m.JxFieldId)
            .ToList();

        return item;
    }

    [HttpDelete("deleteContractTemplateInfo/{id}")]
    public async Task<dynamic> DeleteContractTemplateInfo(Guid id)
    {
        if (await _jxContractTemplateService.UpdateIsDelete(id))
            return "删除成功";
        throw Oops.Bah("删除数据失败");
    }

    [HttpPut("setContractTemplateInfo/{id}")]
    public async Task<string> SetContractTemplateInfo(JxContractTemplatePageInPut input, Guid id)
    {
        input.Id = id;
        if (input.Id == Guid.Empty)
        {
            await _jxContractTemplateService.Add(input);
            return "添加成功";
        }

        if (await _jxContractTemplateService.Update(input))
            return "更新成功";

        throw Oops.Bah("更新失败，稍后重试");
    }

    [HttpPost("uploadContract/{id}")]
    public async Task<dynamic> UploadDocumentAsync(IFormFile file, Guid id)
    {
        var fileExtension = file.FileName.Substring(file.FileName.LastIndexOf(".") + 1);

        if (fileExtension.ToLower() != ".pdf")
        {
            var transferUploadObjectModel = new TransferUploadObjectModel();

            // Convert Chinese characters in filename to pinyin
            var fileNameWithoutExtension = file.FileName.Substring(0, file.FileName.LastIndexOf("."));
            var fileNameExtension = file.FileName.Substring(file.FileName.LastIndexOf("."));
            var pinyinFileName = PingYinHelper.ConvertToAllSpell(fileNameWithoutExtension) + fileNameExtension;

            var filePath = transferUploadObjectModel.TransferUploadBytes(
                "/JxContractTemplate/" + UserManager.TenantId.ToString("N") + "/" +
                DateTime.Now.ToString("yyyy/MM/dd/") + Guid.NewGuid().ToString("N") + "/" + pinyinFileName,
                file.ParseToBytes());

            var data = await _jxContractTemplateService.GetById(id);
            data.Url = filePath;
            await _jxContractTemplateService.Update(data.Adapt<JxContractTemplatePageInPut>());

            return "上传完成，请重新编辑合同";
        }

        throw Oops.Bah("模板文件只支持 pdf 格式的文件!");
    }

    [HttpPost("getContractPage/{id}/{pageNo}")]
    public async Task<dynamic> GetContractPage(Guid id, int pageNo = 1)
    {
        var data = await _jxContractTemplateService.GetById(id);
        if (data == null || string.IsNullOrEmpty(data.Url))
        {
            throw Oops.Bah("未找到合同模板文件");
        }

        using var httpClient = new HttpClient();
        var response = await httpClient.GetAsync(data.Url);
        response.EnsureSuccessStatusCode();
        var pdfBytes = await response.Content.ReadAsByteArrayAsync();

        using var pdfDocument = new PdfDocument();
        pdfDocument.LoadFromBytes(pdfBytes);

        try
        {
            if (pageNo <= 0 || pageNo > pdfDocument.Pages.Count)
            {
                throw new ArgumentException("无效的页码");
            }

            using var ms = new MemoryStream();
            var imageStream = pdfDocument.SaveAsImage(pageNo - 1);
            imageStream.CopyTo(ms);

            return new
            {
                ImageBytes = ms.ToArray(),
                TotalPages = pdfDocument.Pages.Count,
                Controls = await _jxContractTemplateService.GetContractTemplateControlsList(id)
            };
        }
        catch (ArgumentException ex)
        {
            // 重新抛出参数异常，保持原有的错误信息格式
            throw Oops.Bah(ex.Message);
        }
    }

    /// <summary>
    ///     保存合同模板控件
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input">合同模板控件信息</param>
    /// <returns>保存结果</returns>
    [HttpPost("saveContractTemplateControls/{id}")]
    public async Task<dynamic> SaveContractTemplateControls(Guid id, JxContractTemplateControlsInPut input)
    {
        if (input == null)
        {
            throw Oops.Bah("输入参数不能为空");
        }


        input.TemplateId = id;
        input.TenantId = UserManager.TenantId;

        // 保存合同模板控件信息
        var result = await _jxContractTemplateService.SaveContractTemplateControls(input);

        if (result)
        {
            return "保存成功";
        }

        throw Oops.Bah("保存失败，请稍后重试");
    }

    /// <summary>
    /// 测试 PDF 文本写入功能
    /// </summary>
    /// <returns>测试结果</returns>
    [HttpGet("TestPdfTextWriting")]
    public async Task<string> TestPdfTextWriting()
    {
        return await _jxContractTemplateService.TestPdfTextWriting();
    }
}