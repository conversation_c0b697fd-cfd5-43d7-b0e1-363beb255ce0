
using PandaServer.Core.Utils.Pdf;
using Spire.Pdf;

namespace PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Contract;

/// <summary>
///     驾校 合同 模板
/// </summary>
[ApiDescriptionSettings("驾校服务", GroupName = "JiaXiao", Name = "Jx/Student/Contract/WxContract", Order = 200)]
[Route("Jx/Student/Contract/WxContract")]
public class WxContractController : IDynamicApiController
{
    private readonly IJxContractTemplateService _jxContractTemplateService;
    private readonly IJxStudentQueryService _jxStudentQueryService;
    private readonly IJxStudentContractService _jxStudentContractService;

    private readonly IJxCompanyService _jxCompanyService;

    public WxContractController(IJxStudentQueryService jxStudentQueryService, IJxContractTemplateService jxContractTemplateService, IJxStudentContractService jxStudentContractService, IJxCompanyService jxCompanyService)
    {
        _jxStudentQueryService = jxStudentQueryService;
        _jxContractTemplateService = jxContractTemplateService;
        _jxStudentContractService = jxStudentContractService;
        _jxCompanyService = jxCompanyService;
    }

    [HttpPost("getContract")]
    [Description("获取合同")]
    public async Task<dynamic> GetContract()
    {

        if (UserManager.StudentId == Guid.Empty)
        {
            return Oops.Bah("学员信息为空，请重新登录");
        }
        var student = await _jxStudentQueryService.GetDetailById(UserManager.StudentId, UserManager.TenantId);

        if (student == null)
        {
            return Oops.Bah("学员信息为空，请重新登录");
        }

        var contract = await _jxStudentContractService.GetByStudentIdAsync(UserManager.StudentId);

        if (contract == null)
        {
            var template = await _jxContractTemplateService.GetContract(student);
            if (template == null)
            {
                return Oops.Bah("合同模板为空，请联系驾校添加合同模板");
            }
            else
            {
                PdfDocument pdfDocument = await _jxContractTemplateService.MakeContract(template, "", "", student);

                var imageData = PDFHelper.ConvertDocumentToImage(pdfDocument);

                return new
                {
                    isSigned = false,
                    Contract = imageData,
                    xm = student.xm
                };
            }
        }
        else
        {
            return Oops.Bah("合同已签署");
        }
    }

    /// <summary>
    /// 上传签字信息
    /// </summary>
    [HttpPost("uploadSignature")]
    [Description("上传签字信息")]
    public async Task<dynamic> UploadSignature(IFormFile signatureImage)
    {
        // 验证学员信息
        if (UserManager.StudentId == Guid.Empty)
        {
            return Oops.Bah("学员信息为空，请重新登录");
        }

        // 验证文件
        if (signatureImage == null || signatureImage.Length == 0)
        {
            return Oops.Bah("签字文件不能为空");
        }

        // 验证文件类型
        var allowedExtensions = new[] { ".png", ".jpg", ".jpeg", ".gif", ".bmp" };
        var fileExtension = Path.GetExtension(signatureImage.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(fileExtension))
        {
            return Oops.Bah("仅支持图片格式：png, jpg, jpeg, gif, bmp");
        }

        // 创建保存目录
        string currentDir = Directory.GetCurrentDirectory();
        string dateFolder = DateTime.Now.ToString("yyyyMMdd");
        string saveDir = Path.Combine(currentDir, "uploads", "signatures", dateFolder);

        // 确保目录存在
        if (!Directory.Exists(saveDir))
        {
            Directory.CreateDirectory(saveDir);
        }

        // 生成唯一文件名（使用学员ID和时间戳）
        string fileName = $"signature_{UserManager.StudentId}_{DateTime.Now:HHmmss}_{Guid.NewGuid():N}"[..^24] + fileExtension;
        string filePath = Path.Combine(saveDir, fileName);

        // 保存文件
        using (var stream = new FileStream(filePath, FileMode.Create))
        {
            await signatureImage.CopyToAsync(stream);
        }

        return filePath;
    }

    /// <summary>
    /// 上传拍照信息
    /// </summary>
    [HttpPost("uploadPhoto")]
    [Description("上传拍照信息")]
    public async Task<dynamic> UploadPhoto(JxStudentContractPhotoInput input)
    {
        // 验证学员信息
        if (UserManager.StudentId == Guid.Empty)
        {
            return Oops.Bah("学员信息为空，请重新登录");
        }

        // 上传拍照信息
        var contract = await _jxStudentContractService.UploadPhotoAsync(input);

        return new
        {
            success = true,
            contractId = contract.Id,
            message = "拍照信息上传成功"
        };
    }
    /// <summary>
    /// 提交合同签署信息
    /// </summary>
    [HttpPost("submitContractSign")]
    [Description("提交合同签署信息")]
    public async Task<dynamic> SubmitContractSign(JxStudentContractSubmitInput input)
    {
        // 验证学员信息
        if (UserManager.StudentId == Guid.Empty)
        {
            return Oops.Bah("学员信息为空，请重新登录");
        }

        var student = await _jxStudentQueryService.GetDetailById(UserManager.StudentId, UserManager.TenantId);
        if (student == null)
        {
            return Oops.Bah("学员信息为空，请重新登录");
        }

        var contract = await _jxStudentContractService.GetByStudentIdAsync(UserManager.StudentId);

        if (contract == null)
        {
            var template = await _jxContractTemplateService.GetContract(student);
            if (template == null)
            {
                return Oops.Bah("合同模板为空，请联系驾校添加合同模板");
            }
            else
            {
                var jxCompany = await _jxCompanyService.GetById(student.JxCompanyId);

                if (jxCompany == null)
                {
                    var jxCompanys = await _jxCompanyService.GetListByTenatId(UserManager.TenantId);

                    if (jxCompanys.Count == 0)
                    {
                        throw Oops.Bah("请先联系添加相应的资质信息");
                    }
                    jxCompany = jxCompanys[0];
                }

                if (string.IsNullOrEmpty(jxCompany.SealFilePath))
                {
                    throw Oops.Bah("请先联系添加相应的资质信息的印章图片");
                }

                PdfDocument pdfDocument = await _jxContractTemplateService.MakeContract(template, input.SignFilePath, jxCompany.SealFilePath, student);

                // 创建保存目录（与签名图片使用相同的目录结构）
                string currentDir = Directory.GetCurrentDirectory();
                string dateFolder = DateTime.Now.ToString("yyyyMMdd");
                string saveDir = Path.Combine(currentDir, "uploads", "contracts", dateFolder);

                // 确保目录存在
                if (!Directory.Exists(saveDir))
                {
                    Directory.CreateDirectory(saveDir);
                }

                // 生成唯一文件名（使用学员ID和时间戳）
                string fileName = $"contract_{UserManager.StudentId}_{DateTime.Now:HHmmss}_{Guid.NewGuid():N}"[..^24] + ".pdf";
                string filePath = Path.Combine(saveDir, fileName);

                // 保存PDF文件到本地
                pdfDocument.SaveToFile(filePath);

                // 释放PDF文档资源
                pdfDocument.Close();
            }
            return "签署成功";
        }
        else
        {
            return Oops.Bah("合同已签署");
        }
    }
}
