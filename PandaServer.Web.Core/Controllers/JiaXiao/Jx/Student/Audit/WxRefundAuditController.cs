using System;
using System.Threading.Tasks;
using Furion.DynamicApiController;
using Microsoft.AspNetCore.Authorization;
using PandaServer.System.Services.Wx;
using PandaServer.Core.Utils;
using PandaServer.System.Services.Student;
using PandaServer.System.Services.Config;
using PandaServer.System.Services.Student.Audit;
using PandaServer.System.Services.Oss;
using Microsoft.AspNetCore.Http;
using PandaServer.Core.Extend;
using Mapster;
using PandaServer.System.Services.Student.Dto.Audit;

namespace PandaServer.Web.Core.Controllers.JiaXiao.Jx.Student.Audit;

[ApiDescriptionSettings(Name = "Jx/Student/Audit/WxRefundAudit", Order = 201)]
[Route("Jx/Student/Audit/WxRefundAudit")]
public class WxRefundAuditController : IDynamicApiController
{
    private readonly IJxStudentService _jxStudentService;
    private readonly IWeChatRefundApplicationService _weChatRefundApplicationService;
    private readonly IQCloudOssService _qCloudOssService;
    private readonly IImportExportService _importExportService;

    public WxRefundAuditController(IJxStudentService jxStudentService, IWeChatRefundApplicationService weChatRefundApplicationService, IQCloudOssService qCloudOssService, IImportExportService importExportService)
    {
        _jxStudentService = jxStudentService;
        _weChatRefundApplicationService = weChatRefundApplicationService;
        _qCloudOssService = qCloudOssService;
        _importExportService = importExportService;
    }


    #region 微信操作的 相关的方法
    /// <summary>
    /// 通过身份证号获取学员信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns>学员信息</returns>
    [HttpPost("getStudentByIDCard")]
    [DisplayName("通过身份证号获取学员信息")]
    public async Task<dynamic> GetStudentByIDCard(BaseIdInput input)
    {
        if (string.IsNullOrEmpty(input.sfzmhm))
        {
            throw Oops.Bah("身份证号不能为空");
        }

        if (input.TenantId == Guid.Empty)
        {
            throw Oops.Bah("公司ID不能为空");
        }

        var student = await _jxStudentService.GetBySfzmhm(input.sfzmhm, input.TenantId);
        if (student == null)
        {
            throw Oops.Bah("未找到该学员信息");
        }

        var application = await _weChatRefundApplicationService.GetByStudentId(student.Id, input.TenantId);
        if (application != null)
        {
            if (application.Status == AuditEnum.WaitAudit || application.Status == AuditEnum.AuditPass)
            {
                if (!application.IsPaid && string.IsNullOrEmpty(application.OpenId))
                {
                    application.OpenId = HttpNewUtil.GetHeader("openid");
                    await _weChatRefundApplicationService.Update(application);

                    throw Oops.Bah("系统已经更新学员的openid，请耐心等待后台处理");
                }
            }
        }
        return student;
    }

    /// <summary>
    /// 提交退款审核
    /// </summary>
    /// <returns></returns>
    [HttpPost("submit")]
    [DisplayName("提交退款审核")]
    public async Task<dynamic> Submit(WeChatRefundApplicationInPut input)
    {
        if (HttpNewUtil.GetHeader("openId") == null || HttpNewUtil.GetHeader("openId") == "")
        {
            throw Oops.Bah("请先登录");
        }
        if (input.TenantId == Guid.Empty)
        {
            throw Oops.Bah("公司ID不能为空");
        }
        var result = await _weChatRefundApplicationService.Create(input);


        return result.Id;
    }

    /// <summary>
    /// 上传退款申请截图
    /// </summary>
    /// <param name="id">退款申请ID</param>
    /// <param name="screenshot">截图图片</param>
    /// <returns></returns>
    [HttpPost("uploadScreenshot")]
    [DisplayName("上传退款申请截图")]
    public async Task<dynamic> UploadScreenshot(Guid id, IFormFile screenshot)
    {
        if (HttpNewUtil.GetHeader("openId") == null || HttpNewUtil.GetHeader("openId") == "")
        {
            throw Oops.Bah("请先登录");
        }
        if (id == Guid.Empty)
        {
            throw Oops.Bah("退款申请ID不能为空");
        }

        if (screenshot == null)
        {
            throw Oops.Bah("请选择要上传的截图");
        }

        var application = await _weChatRefundApplicationService.Get(id);
        if (application == null)
        {
            throw Oops.Bah("未找到退款申请记录");
        }

        var screenshotBytes = screenshot.ParseToBytes();
        var screenshotUrl = _qCloudOssService.UploadBytes(
            "/WxRefund/Screenshot/" + DateTime.Now.ToString("yyyy/MM/dd/") + "/" + Guid.NewGuid() + ".jpg",
            screenshotBytes);
        application.ScreenshotUrl = screenshotUrl;

        var input = application.Adapt<WeChatRefundApplicationInPut>();
        await _weChatRefundApplicationService.Update(input);

        return "截图上传成功";
    }

    /// <summary>
    /// 上传退款申请银行卡截图
    /// </summary>
    /// <param name="id">退款申请ID</param>
    /// <param name="bankCardScreenshot">银行卡截图图片</param>
    /// <returns></returns>
    [HttpPost("uploadBankCardScreenshot")]
    [DisplayName("上传退款申请银行卡截图")]
    public async Task<dynamic> UploadBankCardScreenshot(Guid id, IFormFile bankCardScreenshot)
    {
        if (HttpNewUtil.GetHeader("openId") == null || HttpNewUtil.GetHeader("openId") == "")
        {
            throw Oops.Bah("请先登录");
        }
        if (id == Guid.Empty)
        {
            throw Oops.Bah("退款申请ID不能为空");
        }

        if (bankCardScreenshot == null)
        {
            throw Oops.Bah("请选择要上传的银行卡截图");
        }

        var application = await _weChatRefundApplicationService.Get(id);
        if (application == null)
        {
            throw Oops.Bah("未找到退款申请记录");
        }

        var bankCardBytes = bankCardScreenshot.ParseToBytes();
        var bankCardUrl = _qCloudOssService.UploadBytes(
            "/WxRefund/BankCard/" + DateTime.Now.ToString("yyyy/MM/dd/") + "/" + Guid.NewGuid() + ".jpg",
            bankCardBytes);
        application.BankCardScreenshotUrl = bankCardUrl;

        var input = application.Adapt<WeChatRefundApplicationInPut>();
        await _weChatRefundApplicationService.Update(input);

        return "银行卡截图上传成功";
    }

    /// <summary>
    /// 分页查询退款申请记录
    /// </summary>
    /// <param name="input">分页查询参数</param>
    /// <returns>分页查询结果</returns>
    [HttpPost("page")]
    [DisplayName("分页查询退款申请记录")]
    public async Task<dynamic> Page(WeChatRefundApplicationPageInPut input)
    {
        if (input.TenantId == Guid.Empty)
        {
            input.TenantId = UserManager.TenantId;
        }
        var result = await _weChatRefundApplicationService.Page(input);
        var summary = await _weChatRefundApplicationService.GetRefundTotals(input);
        return new
        {
            data = result,
            summary = summary
        };
    }

    /// <summary>
    /// 导出退款申请记录
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>导出文件</returns>
    [HttpPost("export")]
    [DisplayName("导出退款申请记录")]
    public async Task<dynamic> Export(WeChatRefundApplicationPageInPut input)
    {
        if (input.TenantId == Guid.Empty)
        {
            input.TenantId = UserManager.TenantId;
        }

        input.Size = 99999;
        input.Current = 1;
        var result = await _weChatRefundApplicationService.Page(input);

        var exportResult = await _importExportService.Export(
            result.Records.ToList(),
            input.DesignId,
            "退款申请记录",
            input.TableId);

        return exportResult;
    }



    [HttpPost("getMyApplicationList")]
    [DisplayName("获取我的退款申请记录")]
    public async Task<dynamic> GetMyApplicationList(WeChatRefundApplicationPageInPut input)
    {
        if (input.TenantId == Guid.Empty)
        {
            input.TenantId = UserManager.TenantId;
        }

        if (UserManager.StudentId == Guid.Empty)
        {
            throw Oops.Bah("请先登录");
        }
        input.StudentId = UserManager.StudentId;

        var result = await _weChatRefundApplicationService.Page(input);

        return result;
    }

    /// <summary>
    /// 获取退款申请详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns>退款申请详情</returns>
    [HttpPost("getRefundDetail")]
    [DisplayName("获取退款申请详情")]
    public async Task<dynamic> GetRefundDetail(BaseIdInput input)
    {
        if (input.Id == Guid.Empty)
        {
            throw Oops.Bah("退款申请ID不能为空");
        }

        var application = await _weChatRefundApplicationService.Get(input.Id);
        if (application == null)
        {
            throw Oops.Bah("未找到退款申请记录");
        }

        return application;
    }

    /// <summary>
    /// 更新退款金额
    /// </summary>
    /// <param name="input">退款申请ID</param>
    /// <returns>更新结果</returns>
    [HttpPut("updateRefundAmount")]
    [DisplayName("更新退款金额")]
    public async Task<dynamic> UpdateRefundAmount(WeChatRefundApplicationInPut input)
    {
        if (input.Id == Guid.Empty)
        {
            throw Oops.Bah("退款申请ID不能为空");
        }
        if (!UserManager.IsTenantAdmin)
        {
            throw Oops.Bah("没有权限更新退款金额");
        }

        var application = await _weChatRefundApplicationService.Get(input.Id);
        if (application == null)
        {
            throw Oops.Bah("未找到退款申请记录");
        }

        application.RefundAmount = input.RefundAmount;
        await _weChatRefundApplicationService.Update(application);

        return "退款金额更新成功";
    }

    #endregion 微信操作的 相关的方法


    #region 退款审核操作的 相关的方法

    /// <summary>
    /// 审核通过退款申请
    /// </summary>
    /// <param name="input">退款申请</param>
    /// <returns>审核结果</returns>
    [HttpPost("approve")]
    [DisplayName("审核通过退款申请")]
    public async Task<dynamic> Approve(WeChatRefundApplicationInPut input)
    {
        if (input.Id == Guid.Empty)
        {
            throw Oops.Bah("退款申请ID不能为空");
        }

        input.Status = AuditEnum.AuditPass;

        await _weChatRefundApplicationService.Audit(input);


        return "审核通过成功";
    }

    /// <summary>
    /// 审核拒绝退款申请
    /// </summary>
    /// <param name="input">退款申请</param>
    /// <returns>审核结果</returns>
    [HttpPost("reject")]
    [DisplayName("审核拒绝退款申请")]
    public async Task<dynamic> Reject(WeChatRefundApplicationInPut input)
    {
        if (input.Id == Guid.Empty)
        {
            throw Oops.Bah("退款申请ID不能为空");
        }
        input.Status = AuditEnum.AuditFail;
        input.AuditTime = DateTime.Now;
        await _weChatRefundApplicationService.Audit(input);


        return "审核拒绝操作完成";
    }


    #endregion 退款审核操作的 相关的方法
}
