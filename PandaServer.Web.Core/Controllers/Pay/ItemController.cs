using PandaServer.System.Services.Shop;
using PandaServer.System.Services.Shop.Dtos;


namespace PandaServer.Web.Core.Controllers.Pay;

/// <summary>
///     商品相关的方法
/// </summary>
[ApiDescriptionSettings("考场服务", GroupName = "ExamSite", Name = "Pay/Item", Order = 200)]
[Route("Pay/Item")]
public class ItemController : IDynamicApiController
{
    private readonly IItemService _itemService;

    public ItemController(IItemService itemService)
    {
        _itemService = itemService;
    }


    [HttpPost("getItemList")]
    [Description("返回商品列表")]
    public async Task<dynamic> GetItemList(ItemPageInPut input)
    {
        var result = await _itemService.Page(input);

        return result;
    }





    [HttpPost("getItemSelectList")]
    [Description("返回商品下拉列表")]
    public async Task<dynamic> GetItemSelectList(ItemPageInPut input)
    {
        var data = await _itemService.GetItemCache(UserManager.TenantId);

        var result = data
        .Where(m => m.IsDelete == false)
        .WhereIF(!string.IsNullOrEmpty(input.SearchKey), m => m.ItemName.Contains(input.SearchKey.Trim()))
        .OrderByDescending(m => m.EndTime < DateTime.Now ? 0 : m.StartTime > DateTime.Now ? 1 : 2)
        .OrderByDescending(m => m.Price)
        .OrderByDescending(m => m.SortCode)
        .OrderByDescending(m => m.CreateTime)
        .Select(m => new BaseSelectorOutput
        {
            Value = m.Id,
            Text = m.ItemName + "[￥ " + m.Price.ToString("F2") + "]",
            Label = m.ItemName + "[￥ " + m.Price.ToString("F2") + "]"
        }).ToList();


        return result;
    }


    [HttpPost("getBuyItemSelectList")]
    [Description("返回商品下拉列表")]
    [AllowAnonymous]
    public async Task<dynamic> GetBuyItemSelectList(ItemPageInPut input)
    {
        var isUser = false;
        var isAdmin = false;
        if (UserManager.UserId != Guid.Empty) isUser = true;
        if (UserManager.IsTenantAdmin) isAdmin = true;
        input.IsUser = isUser;
        input.IsAdmin = isAdmin;

        var data = await _itemService.Page(input);

        var result = new List<BaseSelectorOutput>();
        foreach (var item in data.Records)
            if (item.FirstItemId == Guid.Empty)
                result.Add(new BaseSelectorOutput
                {
                    Value = item.Id,
                    Text = item.ItemName,
                    Label = item.ItemName
                });

        return result;
    }


    [HttpDelete("deleteItemInfo")]
    [Description("删除相关的产品")]
    public async Task<dynamic> DeleteItemInfo(BaseIdInput input)
    {
        if (!await _itemService.UpdateIsDelete(input.Id))
            throw Oops.Bah("删除失败，刷新页面重试");

        return "删除成功";
    }


    [HttpPost("getItemInfo")]
    [Description("获取商品的详细信息")]
    public async Task<dynamic> GetItemInfo(BaseIdInput input)
    {
        return await _itemService.GetById(input.Id);
    }


    [HttpPut("setItemInfo")]
    [Description("提交保存商品信息")]
    public async Task<dynamic> SetItemInfo(ItemInPut input)
    {
        if (input.Id == Guid.Empty)
        {
            var data = await _itemService.Add(input);

            if (data == null)
                throw Oops.Bah("新增商品信息失败，刷新页面重试");

            return "保存成功";
        }

        if (!await _itemService.Update(input))
            throw Oops.Bah("修改商品信息失败");

        return "修改商品信息成功";
    }



    [HttpPut("setItemPayAccountInfo")]
    [Description("提交保存商品信息")]
    public async Task<dynamic> SetItemPayAccountInfo(ItemInPut input)
    {
        if (input.Ids.Count == 0)
        {
            throw Oops.Bah("请先选择你要操作的商品");
        }
        else if (input.AccountId == Guid.Empty)
        {
            throw Oops.Bah("请选择你要保存的账户");
        }
        else if (!await _itemService.UpdatePayAccountId(input))
        {
            throw Oops.Bah("修改收款账户失败");
        }
        return "修改收款账户成功";
    }


    [HttpPost("updateItemStatus")]
    [Description("更新商品状态")]
    public async Task<dynamic> UpdateItemStatus(ItemInPut input)
    {
        if (input.Id == Guid.Empty)
            throw Oops.Bah("请选择要操作的商品");

        if (!await _itemService.UpdateStatus(input.Id, input.Status == "Run"))
            throw Oops.Bah("更新商品状态失败");

        return "更新商品状态成功";
    }
}