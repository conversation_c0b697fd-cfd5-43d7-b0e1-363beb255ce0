﻿<Project Sdk="Microsoft.NET.Sdk">
	
	<PropertyGroup>
		<NoWarn>1701;1702;8616;1591;8618;8619;8629;8602;8603;8604;8625;8765</NoWarn>
		<DocumentationFile>PandaServer.Cache.xml</DocumentationFile>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<TargetFramework>net9.0</TargetFramework>
	</PropertyGroup>
	
	<ItemGroup>
		<PackageReference Include="StackExchange.Redis" Version="2.7.33" />
		<!-- <PackageReference Include="SimpleRedis" Version="1.1.9" /> -->
		<!-- <PackageReference Include="NewLife.Core" Version="11.4.2025.201" /> -->
		<!-- <PackageReference Include="NewLife.Redis" Version="6.0.2025.101"/> -->
		<!-- <PackageReference Include="NewLife.Redis" Version="6.1.2025.209" /> -->
		
	</ItemGroup>
	
	<ItemGroup>
		<ProjectReference Include="..\PandaServer.Core\PandaServer.Core.csproj" />
	</ItemGroup>
	
	<ItemGroup>
		<None Update="Cache.Development.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Cache.Production.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	
	<ItemGroup>
		<Reference Include="Spire.Doc">
			<HintPath>..\DLL\netstandard2.0\Spire.Doc.dll</HintPath>
		</Reference>
		<Reference Include="Spire.Pdf">
			<HintPath>..\DLL\netstandard2.0\Spire.Pdf.dll</HintPath>
		</Reference>
		<Reference Include="Spire.XLS">
			<HintPath>..\DLL\netstandard2.0\Spire.XLS.dll</HintPath>
		</Reference>
	</ItemGroup>

</Project>
