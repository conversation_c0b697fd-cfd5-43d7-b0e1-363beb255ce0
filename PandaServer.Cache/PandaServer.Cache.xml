<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PandaServer.Cache</name>
    </assembly>
    <members>
        <member name="T:PandaServer.Cache.CacheConst">
            <summary>
                Redis常量
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Prefix_Web">
            <summary>
                Redis Key前缀(可删除)
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Prefix">
            <summary>
                Redis Key前缀(需要持久化，不随系统重启删除)
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JT808">
            <summary>
                Redis Key前缀(需要持久化，不随系统重启删除)
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Hash">
            <summary>
                Redis Hash类型
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_DataBaseIndex">
            <summary>
            数据库创建索引
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_DevConfig">
            <summary>
                系统配置表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Captcha">
            <summary>
                登录验证码缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_CityData">
            <summary>
                城市的列表的一个封装
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Tenant">
            <summary>
                租户表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_TenantConfig">
            <summary>
                租户配置表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxDevice">
            <summary>
                驾校的相关设备的缓存表
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxCompany">
            <summary>
                驾校的资质
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxArea">
            <summary>
                驾校的区域
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxStudentSource">
            <summary>
                驾校的报名来源
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxStudentImformationStatus">
            <summary>
                驾校的 学员 资料状态
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxClass">
            <summary>
                驾校的报名班型
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxClassCarType">
            <summary>
                驾校的报名班型
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxDept">
            <summary>
                驾校的报名场地
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxStudentSale">
            <summary>
                驾校的学员销售活动
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxStudentStatusSelectList">
            <summary>
                学员状态枚举选择列表
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxField">
            <summary>
                驾校的训练场地
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_PayType">
            <summary>
                驾校的支付类型
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_BaseAccount">
            <summary>
                驾校的结算账户
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Nation">
            <summary>
                国家列表 的缓存
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_CarModel">
            <summary>
                车辆类型 的缓存
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_UserCategory">
            <summary>
                用户分类的缓存
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Item">
            <summary>
                商品的缓存
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxDeviceByPhone">
            <summary>
                驾校的相关设备的缓存表(通过手机号码)
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxDeviceBySerialNumber">
            <summary>
                驾校的相关设备的序列号
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Field">
            <summary>
                考场信息的缓存
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Field_Single">
            <summary>
                考场信息的缓存
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_ParkingGate">
            <summary>
                停车场闸机缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_ParkingCarLog">
            <summary>
                停车场 车辆日志
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_ParkingGateHeart">
            <summary>
                停车场闸机 心跳记录的时间
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_ParkingGateLastHello">
            <summary>
                停车场闸机 上次 Hello 的时间
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_PayOrder_Single">
            <summary>
                订单生成的以后 会存入
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_PayOrder">
            <summary>
                订单支付成功以后 会存入
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_PayAccount">
            <summary>
                收款账户的信息
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_CostType">
            <summary>
                费用类型
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_ParkingGateMessage">
            <summary>
                停车场闸机缓存消息
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_User">
            <summary>
                用户表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_TenantUser">
            <summary>
                租户用户列表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxUser">
            <summary>
                驾校用户表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_SysUserPhone">
            <summary>
                用户手机号关系缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_SysUserAccount">
            <summary>
                用户手机号关系缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_SysResource">
            <summary>
                资源表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_DevDict">
            <summary>
                字典表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_SysRelation">
            <summary>
                关系表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_SysRole">
            <summary>
                角色表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_SysPosition">
            <summary>
                职位表缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_MqttClientUser">
            <summary>
                mqtt认证登录信息key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_JxMyStudentColumnConfig">
            <summary>
                学员自定义字段配置的 Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_UserToken">
            <summary>
                用户Token缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_UserMenu">
            <summary>
                用户菜单缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_LoginUser">
            <summary>
                当前登录用户信息
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_UserLocalStorage">
            <summary>
                用户本地存储缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_WxConfig">
            <summary>
                微信配置的相关的
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_WxAccessToken">
            <summary>
                微信 AccessToken 的相关缓存
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_BaiduFaceV2AccessToken">
            <summary>
                Baidu AccessToken 的相关缓存
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_WxUser">
            <summary>
                微信用户的数据存储
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_BaiduFaceToken">
            <summary>
                百度人脸识别的 Token
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.JT808_PrivateKey">
            <summary>
                JT808 PrivateKey
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.JT808_JxDeviceGPS">
            <summary>
                JT808 PrivateKey
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_UseSale_SaleId">
            <summary>
                消券的时候  为了防止 重复消券  先记录下
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_PrintSale_SaleId">
            <summary>
                消券的时候  为了防止 重复消券  先记录下
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_UserSearchConfig">
            <summary>
                用户搜索配置的缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_UserSearchItems">
            <summary>
                
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_PageDesignDetail">
            <summary>
                用户搜索配置的缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_FieldSelfLoginQrCode">
            <summary>
                场地自助登录二维码缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_TenantSelfLoginQrCode">
            <summary>
                公司统一登录二维码缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_StudentSMSConfig">
            <summary>
                学员短信配置缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_Car">
            <summary>
                车辆信息的缓存
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_WxTemplateMessages">
            <summary>
                微信模板消息缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_StudentIdCardLock">
            <summary>
                学员身份证号处理锁缓存Key
            </summary>
        </member>
        <member name="F:PandaServer.Cache.CacheConst.Cache_UKey">
            <summary>
                UKey信息缓存Key
            </summary>
        </member>
        <member name="T:PandaServer.Cache.ISimpleCacheService">
            <summary>
                缓存服务
            </summary>
            <summary>
                缓存服务
            </summary>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.HashAdd``1(System.String,System.String,``0)">
            <summary>
                添加一条数据到HashMap
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key">键</param>
            <param name="hashKey">hash列表里的Key</param>
            <param name="value">值</param>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.HashAdd``1(System.String,System.String,``0,System.Nullable{System.TimeSpan})">
            <summary>
            添加一条数据到HashMap  可以设置超时时间
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key"></param>
            <param name="hashKey"></param>
            <param name="value"></param>
            <param name="expiration"></param>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.HashSet``1(System.String,System.Collections.Generic.Dictionary{System.String,``0})">
            <summary>
                添加多条数据到HashMap
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key">键</param>
            <param name="dic">键值对字典</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.HashDel``1(System.String,System.String[])">
            <summary>
                从HashMap中删除数据
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key">键</param>
            <param name="fields">hash键列表</param>
            <returns>执行结果</returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.HashGet``1(System.String,System.String[])">
            <summary>
                根据键获取hash列表中的值
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key">键</param>
            <param name="fields">hash键列表</param>
            <returns>数据列表</returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.HashGetOne``1(System.String,System.String)">
            <summary>
                根据键获取hash列表中的值
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key">键</param>
            <param name="field">hash键</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.HashGetAll``1(System.String)">
            <summary>
                获取所有键值对
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key">键</param>
            <returns>数据字典</returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.ContainsKey(System.String)">
            <summary>是否包含缓存项</summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Set``1(System.String,``0,System.Int32)">
            <summary>设置缓存项</summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <param name="expire">过期时间，秒。小于0时采用默认缓存时间</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Set``1(System.String,``0,System.TimeSpan)">
            <summary>设置缓存项</summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <param name="expire">过期时间</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Get``1(System.String)">
            <summary>获取缓存项</summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Remove(System.String[])">
            <summary>批量移除缓存项</summary>
            <param name="keys">键集合</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Remove(System.String)">
            <summary>批量移除缓存项</summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Clear">
            <summary>清空所有缓存项</summary>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.SetExpire(System.String,System.TimeSpan)">
            <summary>设置缓存项有效期</summary>
            <param name="key">键</param>
            <param name="expire">过期时间</param>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.GetExpire(System.String)">
            <summary>获取缓存项有效期</summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.DelByPattern(System.String)">
            <summary>
                模糊删除
            </summary>
            <param name="pattern">匹配关键字</param>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.GetAll``1(System.Collections.Generic.IEnumerable{System.String})">
            <summary>批量获取缓存项</summary>
            <typeparam name="T"></typeparam>
            <param name="keys"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.SetAll``1(System.Collections.Generic.IDictionary{System.String,``0},System.Int32)">
            <summary>批量设置缓存项</summary>
            <typeparam name="T"></typeparam>
            <param name="values"></param>
            <param name="expire">过期时间，秒。小于0时采用默认缓存时间</param>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.GetList``1(System.String)">
            <summary>获取列表</summary>
            <typeparam name="T">元素类型</typeparam>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.GetDictionary``1(System.String)">
            <summary>获取哈希</summary>
            <typeparam name="T">元素类型</typeparam>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.GetSet``1(System.String)">
            <summary>获取Set</summary>
            <typeparam name="T"></typeparam>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Add``1(System.String,``0,System.Int32)">
            <summary>添加，已存在时不更新</summary>
            <typeparam name="T">值类型</typeparam>
            <param name="key">键</param>
            <param name="value">值</param>
            <param name="expire">过期时间，秒。小于0时采用默认缓存时间</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Replace``1(System.String,``0)">
            <summary>设置新值并获取旧值，原子操作</summary>
            <remarks>
                常常配合Increment使用，用于累加到一定数后重置归零，又避免多线程冲突。
            </remarks>
            <typeparam name="T">值类型</typeparam>
            <param name="key">键</param>
            <param name="value">值</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.TryGetValue``1(System.String)">
            <summary>尝试获取指定键，返回是否包含值。有可能缓存项刚好是默认值，或者只是反序列化失败，解决缓存穿透问题</summary>
            <typeparam name="T">值类型</typeparam>
            <param name="key">键</param>
            <returns>返回是否包含值，即使反序列化失败</returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Increment(System.String,System.Int64)">
            <summary>累加，原子操作</summary>
            <param name="key">键</param>
            <param name="value">变化量</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Increment(System.String,System.Double)">
            <summary>累加，原子操作</summary>
            <param name="key">键</param>
            <param name="value">变化量</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Decrement(System.String,System.Int64)">
            <summary>递减，原子操作</summary>
            <param name="key">键</param>
            <param name="value">变化量</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Decrement(System.String,System.Double)">
            <summary>递减，原子操作</summary>
            <param name="key">键</param>
            <param name="value">变化量</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Cache.ISimpleCacheService.Commit">
            <summary>提交变更。部分提供者需要刷盘</summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Cache.CacheSettingsOptions">
            <summary>
                缓存设置
            </summary>
        </member>
        <member name="P:PandaServer.Cache.CacheSettingsOptions.UseRedis">
            <summary>
                使用Redis
            </summary>
        </member>
        <member name="P:PandaServer.Cache.CacheSettingsOptions.RedisSettings">
            <summary>
                是否每次启动都清空
            </summary>
        </member>
        <member name="T:PandaServer.Cache.RedisSettings">
            <summary>
                Redis设置
            </summary>
        </member>
        <member name="P:PandaServer.Cache.RedisSettings.Address">
            <summary>
                连接地址
            </summary>
        </member>
        <member name="P:PandaServer.Cache.RedisSettings.Password">
            <summary>
                密码
            </summary>
        </member>
        <member name="P:PandaServer.Cache.RedisSettings.Db">
            <summary>
                数据库
            </summary>
        </member>
        <member name="P:PandaServer.Cache.RedisSettings.ClearRedis">
            <summary>
                是否每次启动都清空
            </summary>
        </member>
        <member name="T:PandaServer.Cache.RedisCacheService">
            <summary>
                Redis缓存实现
            </summary>
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Get``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Remove(System.String[])">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Remove(System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Set``1(System.String,``0,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Set``1(System.String,``0,System.TimeSpan)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.SetExpire(System.String,System.TimeSpan)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.GetExpire(System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.ContainsKey(System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Clear">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.DelByPattern(System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.GetAll``1(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.SetAll``1(System.Collections.Generic.IDictionary{System.String,``0},System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.GetDictionary``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.GetSet``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Add``1(System.String,``0,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.GetList``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Replace``1(System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.TryGetValue``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Decrement(System.String,System.Int64)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Decrement(System.String,System.Double)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Increment(System.String,System.Int64)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Increment(System.String,System.Double)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.Commit">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.AcquireLock(System.String,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.AcquireLock(System.String,System.Int32,System.Int32,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.HashAdd``1(System.String,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.HashAdd``1(System.String,System.String,``0,System.Nullable{System.TimeSpan})">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.HashSet``1(System.String,System.Collections.Generic.Dictionary{System.String,``0})">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.HashDel``1(System.String,System.String[])">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.HashGet``1(System.String,System.String[])">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.HashGetOne``1(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:PandaServer.Cache.RedisCacheService.HashGetAll``1(System.String)">
            <inheritdoc />
        </member>
        <member name="T:PandaServer.Cache.RedisLock">
            <summary>
            Redis分布式锁实现
            </summary>
        </member>
        <member name="T:PandaServer.Cache.Startup">
            <summary>
                AppStartup启动类
            </summary>
        </member>
        <member name="M:PandaServer.Cache.Startup.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
                ConfigureServices中不能解析服务，比如App.GetService()，尤其是不能在ConfigureServices中获取诸如缓存等数据进行初始化，应该在Configure中进行
                服务都还没初始化完成，会导致内存中存在多份 IOC 容器！！
                正确应该在 Configure 中，这个时候服务（IServiceCollection 已经完成 BuildServiceProvider() 操作了
            </summary>
            <param name="services"></param>
        </member>
    </members>
</doc>
