﻿namespace PandaServer.Cache;

/// <summary>
///     Redis常量
/// </summary>
public class CacheConst
{
    /// <summary>
    ///     Redis Key前缀(可删除)
    /// </summary>
    public const string Cache_Prefix_Web = "PandaServerWeb:";

    /// <summary>
    ///     Redis Key前缀(需要持久化，不随系统重启删除)
    /// </summary>
    public const string Cache_Prefix = "PandaServer:";

    /// <summary>
    ///     Redis Key前缀(需要持久化，不随系统重启删除)
    /// </summary>
    public const string Cache_JT808 = "JT808:";

    /// <summary>
    ///     Redis Hash类型
    /// </summary>
    public const string Cache_Hash = "Hash";


    /// <summary>
    /// 数据库创建索引
    /// </summary>
    public const string Cache_DataBaseIndex = "DataBaseIndex";

    /// <summary>
    ///     系统配置表缓存Key
    /// </summary>
    public const string Cache_DevConfig = Cache_Prefix_Web + "DevConfig:";

    /// <summary>
    ///     登录验证码缓存Key
    /// </summary>
    public const string Cache_Captcha = Cache_Prefix_Web + "Captcha:";

    /// <summary>
    ///     城市的列表的一个封装
    /// </summary>
    public const string Cache_CityData = Cache_Prefix_Web + "CityData";

    /// <summary>
    ///     租户表缓存Key
    /// </summary>
    public const string Cache_Tenant = Cache_Prefix_Web + "Tenant";

    /// <summary>
    ///     租户配置表缓存Key
    /// </summary>
    public const string Cache_TenantConfig = Cache_Prefix_Web + "TenantConfig";

    /// <summary>
    ///     驾校的相关设备的缓存表
    /// </summary>
    public const string Cache_JxDevice = Cache_Prefix_Web + "JxDevice";

    /// <summary>
    ///     驾校的资质
    /// </summary>
    public const string Cache_JxCompany = Cache_Prefix_Web + "JxCompany";

    /// <summary>
    ///     驾校的区域
    /// </summary>
    public const string Cache_JxArea = Cache_Prefix_Web + "JxArea";

    /// <summary>
    ///     驾校的报名来源
    /// </summary>
    public const string Cache_JxStudentSource = Cache_Prefix_Web + "JxStudentSource";

    /// <summary>
    ///     驾校的 学员 资料状态
    /// </summary>
    public const string Cache_JxStudentImformationStatus = Cache_Prefix_Web + "JxStudentImformationStatus";

    /// <summary>
    ///     驾校的报名班型
    /// </summary>
    public const string Cache_JxClass = Cache_Prefix_Web + "JxClass";

    /// <summary>
    ///     驾校的报名班型
    /// </summary>
    public const string Cache_JxClassCarType = Cache_Prefix_Web + "JxClassCarType";

    /// <summary>
    ///     驾校的报名场地
    /// </summary>
    public const string Cache_JxDept = Cache_Prefix_Web + "JxDept";

    /// <summary>
    ///     驾校的学员销售活动
    /// </summary>
    public const string Cache_JxStudentSale = Cache_Prefix_Web + "JxStudentSale";

    /// <summary>
    ///     学员状态枚举选择列表
    /// </summary>
    public const string Cache_JxStudentStatusSelectList = Cache_Prefix_Web + "JxStudentStatusSelectList";

    /// <summary>
    ///     驾校的训练场地
    /// </summary>
    public const string Cache_JxField = Cache_Prefix_Web + "JxField";

    /// <summary>
    ///     驾校的支付类型
    /// </summary>
    public const string Cache_PayType = Cache_Prefix_Web + "PayType";

    /// <summary>
    ///     驾校的结算账户
    /// </summary>
    public const string Cache_BaseAccount = Cache_Prefix_Web + "BaseAccount";

    /// <summary>
    ///     国家列表 的缓存
    /// </summary>
    public const string Cache_Nation = Cache_Prefix_Web + "Nation";

    /// <summary>
    ///     车辆类型 的缓存
    /// </summary>
    public const string Cache_CarModel = Cache_Prefix_Web + "CarModel";

    /// <summary>
    ///     用户分类的缓存
    /// </summary>
    public const string Cache_UserCategory = Cache_Prefix_Web + "UserCategory";

    /// <summary>
    ///     商品的缓存
    /// </summary>
    public const string Cache_Item = Cache_Prefix_Web + "Item";

    /// <summary>
    ///     驾校的相关设备的缓存表(通过手机号码)
    /// </summary>
    public const string Cache_JxDeviceByPhone = Cache_Prefix_Web + "JxDeviceByPhone";

    /// <summary>
    ///     驾校的相关设备的序列号
    /// </summary>
    public const string Cache_JxDeviceBySerialNumber = Cache_Prefix_Web + "JxDeviceBySerialNumber";

    /// <summary>
    ///     考场信息的缓存
    /// </summary>
    public const string Cache_Field = Cache_Prefix_Web + "Field";

    /// <summary>
    ///     考场信息的缓存
    /// </summary>
    public const string Cache_Field_Single = Cache_Prefix_Web + "Field_Single";

    /// <summary>
    ///     停车场闸机缓存Key
    /// </summary>
    public const string Cache_ParkingGate = Cache_Prefix_Web + "ParkingGate";

    /// <summary>
    ///     停车场 车辆日志
    /// </summary>
    public const string Cache_ParkingCarLog = Cache_Prefix_Web + "ParkingCarLog";

    /// <summary>
    ///     停车场闸机 心跳记录的时间
    /// </summary>
    public const string Cache_ParkingGateHeart = Cache_Prefix_Web + "ParkingGateHeart";

    /// <summary>
    ///     停车场闸机 上次 Hello 的时间
    /// </summary>
    public const string Cache_ParkingGateLastHello = Cache_Prefix_Web + "ParkingGateLastHello";

    /// <summary>
    ///     订单生成的以后 会存入
    /// </summary>
    public const string Cache_PayOrder_Single = Cache_Prefix_Web + "PayOrder_Single";

    /// <summary>
    ///     订单支付成功以后 会存入
    /// </summary>
    public const string Cache_PayOrder = Cache_Prefix_Web + "PayOrder";

    /// <summary>
    ///     收款账户的信息
    /// </summary>
    public const string Cache_PayAccount = Cache_Prefix_Web + "PayAccount";

    /// <summary>
    ///     费用类型
    /// </summary>
    public const string Cache_CostType = Cache_Prefix_Web + "CostType";

    /// <summary>
    ///     停车场闸机缓存消息
    /// </summary>
    public const string Cache_ParkingGateMessage = Cache_Prefix_Web + "ParkingGateMessage";

    /// <summary>
    ///     用户表缓存Key
    /// </summary>
    public const string Cache_User = Cache_Prefix_Web + "User";

    /// <summary>
    ///     租户用户列表缓存Key
    /// </summary>
    public const string Cache_TenantUser = Cache_Prefix_Web + "TenantUser";

    /// <summary>
    ///     驾校用户表缓存Key
    /// </summary>
    public const string Cache_JxUser = Cache_Prefix_Web + "JxUser";

    /// <summary>
    ///     用户手机号关系缓存Key
    /// </summary>
    public const string Cache_SysUserPhone = Cache_Prefix_Web + "SysUserPhone";

    /// <summary>
    ///     用户手机号关系缓存Key
    /// </summary>
    public const string Cache_SysUserAccount = Cache_Prefix_Web + "SysUserAccount";

    /// <summary>
    ///     资源表缓存Key
    /// </summary>
    public const string Cache_SysResource = Cache_Prefix_Web + "SysResource:";

    /// <summary>
    ///     字典表缓存Key
    /// </summary>
    public const string Cache_DevDict = Cache_Prefix_Web + "DevDict";

    /// <summary>
    ///     关系表缓存Key
    /// </summary>
    public const string Cache_SysRelation = Cache_Prefix_Web + "SysRelation:";

    /// <summary>
    ///     角色表缓存Key
    /// </summary>
    public const string Cache_SysRole = Cache_Prefix_Web + "SysRole";

    /// <summary>
    ///     职位表缓存Key
    /// </summary>
    public const string Cache_SysPosition = Cache_Prefix_Web + "SysPosition";

    /// <summary>
    ///     mqtt认证登录信息key
    /// </summary>
    public const string Cache_MqttClientUser = Cache_Prefix_Web + "MqttClientUser:";

    /// <summary>
    ///     学员自定义字段配置的 Key
    /// </summary>
    public const string Cache_JxMyStudentColumnConfig = Cache_Prefix_Web + "JxMyStudentColumnConfig";

    /// <summary>
    ///     用户Token缓存Key
    /// </summary>
    public const string Cache_UserToken = Cache_Prefix_Web + "UserToken";


    /// <summary>
    ///     用户菜单缓存Key
    /// </summary>
    public const string Cache_UserMenu = Cache_Prefix_Web + "UserMenu";

    /// <summary>
    ///     当前登录用户信息
    /// </summary>
    public const string Cache_LoginUser = Cache_Prefix_Web + "LoginUser";

    /// <summary>
    ///     用户本地存储缓存Key
    /// </summary>
    public const string Cache_UserLocalStorage = Cache_Prefix_Web + "UserLocalStorage";

    /// <summary>
    ///     微信配置的相关的
    /// </summary>
    public const string Cache_WxConfig = Cache_Prefix_Web + "Cache_WxConfig";

    /// <summary>
    ///     微信 AccessToken 的相关缓存
    /// </summary>
    public const string Cache_WxAccessToken = Cache_Prefix_Web + "Cache_WxAccessToken";

    /// <summary>
    ///     Baidu AccessToken 的相关缓存
    /// </summary>
    public const string Cache_BaiduFaceV2AccessToken = Cache_Prefix_Web + "Cache_BaiduFaceV2AccessToken";

    /// <summary>
    ///     微信用户的数据存储
    /// </summary>
    public const string Cache_WxUser = Cache_Prefix_Web + "Cache_WxUser";

    /// <summary>
    ///     百度人脸识别的 Token
    /// </summary>
    public const string Cache_BaiduFaceToken = Cache_Prefix_Web + "Cache_BaiduFaceToken";

    /// <summary>
    ///     JT808 PrivateKey
    /// </summary>
    public const string JT808_PrivateKey = Cache_JT808 + "PrivateKey";

    /// <summary>
    ///     JT808 PrivateKey
    /// </summary>
    public const string JT808_JxDeviceGPS = Cache_JT808 + "JxDeviceGPS";

    /// <summary>
    ///     消券的时候  为了防止 重复消券  先记录下
    /// </summary>
    public const string Cache_UseSale_SaleId = Cache_Prefix_Web + "Cache_UseSale_SaleId";

    /// <summary>
    ///     消券的时候  为了防止 重复消券  先记录下
    /// </summary>
    public const string Cache_PrintSale_SaleId = Cache_Prefix_Web + "Cache_PrintSale_SaleId";

    /// <summary>
    ///     用户搜索配置的缓存Key
    /// </summary>
    public const string Cache_UserSearchConfig = Cache_Prefix_Web + "Cache_UserSearchConfig";

    /// <summary>
    ///     
    /// </summary>
    public const string Cache_UserSearchItems = Cache_Prefix_Web + "Cache_UserSearchItems";

    /// <summary>
    ///     用户搜索配置的缓存Key
    /// </summary>
    public const string Cache_PageDesignDetail = Cache_Prefix_Web + "Cache_PageDesignDetail";

    /// <summary>
    ///     场地自助登录二维码缓存Key
    /// </summary>
    public const string Cache_FieldSelfLoginQrCode = Cache_Prefix_Web + "Cache_FieldSelfLoginQrCode";

    /// <summary>
    ///     公司统一登录二维码缓存Key
    /// </summary>
    public const string Cache_TenantSelfLoginQrCode = Cache_Prefix_Web + "Cache_TenantSelfLoginQrCode";

    /// <summary>
    ///     学员短信配置缓存Key
    /// </summary>
    public const string Cache_StudentSMSConfig = Cache_Prefix_Web + "Cache_StudentSMSConfig";

    /// <summary>
    ///     车辆信息的缓存
    /// </summary>
    public const string Cache_Car = Cache_Prefix_Web + "Car";

    /// <summary>
    ///     微信模板消息缓存Key
    /// </summary>
    public const string Cache_WxTemplateMessages = Cache_Prefix_Web + "WxTemplateMessages";

    /// <summary>
    ///     学员身份证号处理锁缓存Key
    /// </summary>
    public const string Cache_StudentIdCardLock = Cache_Prefix_Web + "StudentIdCardLock:";

    /// <summary>
    ///     UKey信息缓存Key
    /// </summary>
    public const string Cache_UKey = Cache_Prefix_Web + "UKey";
}