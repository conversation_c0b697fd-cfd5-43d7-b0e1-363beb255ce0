﻿using System.Collections;
using System.Globalization;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats;
using SixLabors.ImageSharp.Formats.Jpeg;

/*******************************************************************************
 * Copyright © 2016 WaterCloud.Framework 版权所有
 * Author: WaterCloud
 * Description: WaterCloud快速开发平台
 * Website：
 *********************************************************************************/

namespace PandaServer.Core.Extend;

public static partial class Extensions
{
    #region 强制转换类型

    /// <summary>
    ///     强制转换类型
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="source"></param>
    /// <returns></returns>
    public static IEnumerable<TResult> CastSuper<TResult>(this IEnumerable source)
    {
        foreach (var item in source) yield return (TResult)Convert.ChangeType(item, typeof(TResult));
    }

    #endregion

    #region 转换为Guid

    /// <summary>
    ///     将string转换为Guid，若转换失败，则返回Guid.Empty。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static Guid ParseToGuid(this object str)
    {
        try
        {
            if (str.ToString().Length == 32)
            {
                // 将 32 个字符的十六进制字符串转换为字节数组
                var bytes = new byte[16];
                for (var i = 0; i < 16; i++) bytes[i] = Convert.ToByte(str.ToString().Substring(i * 2, 2), 16);

                // 使用字节数组创建 Guid 对象
                return new Guid(bytes);
            }

            return new Guid(str.ToString());
        }
        catch
        {
            return Guid.Empty;
        }
    }


    /// <summary>
    ///     将string转换为Guid，若转换失败，则返回Guid.Empty。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static List<T> ParseToList<T>(this object str)
    {
        try
        {
            List<T> guids = new List<T>();

            string[] values = str.ToString().Replace(",", ";").Split(";".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);

            foreach (string value in values)
            {
                if (value.ParseToGuid() != Guid.Empty)
                {
                    guids.Add((T)Convert.ChangeType(value.ParseToGuid(), typeof(T)));
                }
            }
            return guids;
        }
        catch
        {
            return new List<T>();
        }
    }
    #endregion

    /// <summary>
    ///     安全返回值
    /// </summary>
    /// <param name="value">可空值</param>
    public static T SafeValue<T>(this T? value) where T : struct
    {
        return value ?? default(T);
    }

    /// <summary>
    ///     字典类型转URL参数字符串
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    public static string ParseToUrlParams(this Dictionary<string, string> param)
    {
        var builder = new StringBuilder();
        foreach (var entry in param)
        {
            if (string.IsNullOrWhiteSpace(Convert.ToString(entry.Key)) || null == entry.Value) continue;
            builder.Append(Convert.ToString(entry.Key) + "=" + Convert.ToString(entry.Value) + "&");
        }

        return builder.ToString().Substring(0, builder.ToString().LastIndexOf("&"));
        ;
    }


    /// <summary>
    ///     IFormFile 转成 图片
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    public static Image ParseToImage(this IFormFile file)
    {
        if (file == null)
            throw Oops.Bah("上传图片为空，请重新上传!");

        byte[] fileBytes;
        using (var stream = new MemoryStream())
        {
            file.CopyTo(stream);
            fileBytes = stream.ToArray();
        }

        var ms = new MemoryStream(fileBytes);
        var image = Image.Load(fileBytes);

        return image;
    }

    /// <summary>
    ///     IFormFile 转成 图片
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    public static byte[] ParseToBytes(this IFormFile file)
    {
        byte[] fileBytes;
        using (var stream = new MemoryStream())
        {
            file.CopyTo(stream);
            fileBytes = stream.ToArray();
        }

        return fileBytes;
    }

    /// <summary>
    ///     Image 转成 数组
    /// </summary>
    /// <param name="image"></param>
    /// <returns></returns>
    public static byte[] ParseToBytes(this Image image)
    {
        using (var ms = new MemoryStream())
        {
            IImageEncoder
                imagencoder = new JpegEncoder();
            image.Save(ms, imagencoder);
            var buffer = new byte[ms.Length];

            ms.Seek(0, SeekOrigin.Begin);
            ms.Read(buffer, 0, buffer.Length);
            return buffer;
        }
    }


    /// <summary>
    ///     Base64 转  Image
    /// </summary>
    /// <param name="img"></param>
    /// <returns></returns>
    public static Image ParseToImage(this string img)
    {
        // if(!img.Contains("data:image/"))
        // {
        //     img = "data:image/png;base64," + img;
        // }
        var fileBytes = Convert.FromBase64String(img);

        var image = Image.Load(fileBytes);

        return image;
    }

    #region 数值转换

    /// <summary>
    ///     转换为整型
    /// </summary>
    /// <param name="data">数据</param>
    public static int ToInt(this object data)
    {
        if (data == null)
            return 0;
        int result;
        var success = int.TryParse(data.ToString(), out result);
        if (success)
            return result;
        try
        {
            return Convert.ToInt32(ToDouble(data, 0));
        }
        catch (Exception)
        {
            return 0;
        }
    }

    /// <summary>
    ///     将object转换为long，若转换失败，则返回0。不抛出异常。
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static long ToLong(this object obj)
    {
        try
        {
            return long.Parse(obj.ToString());
        }
        catch
        {
            return 0L;
        }
    }

    /// <summary>
    ///     转换为可空整型
    /// </summary>
    /// <param name="data">数据</param>
    public static int? ToIntOrNull(this object data)
    {
        if (data == null)
            return null;
        int result;
        var isValid = int.TryParse(data.ToString(), out result);
        if (isValid)
            return result;
        return null;
    }

    /// <summary>
    ///     转换为双精度浮点数
    /// </summary>
    /// <param name="data">数据</param>
    public static double ToDouble(this object data)
    {
        if (data == null)
            return 0;
        double result;
        return double.TryParse(data.ToString(), out result) ? result : 0;
    }

    /// <summary>
    ///     转换为双精度浮点数,并按指定的小数位4舍5入
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="digits">小数位数</param>
    public static double ToDouble(this object data, int digits)
    {
        return Math.Round(ToDouble(data), digits);
    }

    /// <summary>
    ///     转换为可空双精度浮点数
    /// </summary>
    /// <param name="data">数据</param>
    public static double? ToDoubleOrNull(this object data)
    {
        if (data == null)
            return null;
        double result;
        var isValid = double.TryParse(data.ToString(), out result);
        if (isValid)
            return result;
        return null;
    }

    /// <summary>
    ///     转换为高精度浮点数
    /// </summary>
    /// <param name="data">数据</param>
    public static decimal ToDecimal(this object data)
    {
        if (data == null)
            return 0;
        decimal result;
        return decimal.TryParse(data.ToString(), out result) ? result : 0;
    }

    /// <summary>
    ///     转换为高精度浮点数,并按指定的小数位4舍5入
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="digits">小数位数</param>
    public static decimal ToDecimal(this object data, int digits)
    {
        return Math.Round(ToDecimal(data), digits);
    }

    /// <summary>
    ///     转换为可空高精度浮点数
    /// </summary>
    /// <param name="data">数据</param>
    public static decimal? ToDecimalOrNull(this object data)
    {
        if (data == null)
            return null;
        decimal result;
        var isValid = decimal.TryParse(data.ToString(), out result);
        if (isValid)
            return result;
        return null;
    }

    /// <summary>
    ///     转换为可空高精度浮点数,并按指定的小数位4舍5入
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="digits">小数位数</param>
    public static decimal? ToDecimalOrNull(this object data, int digits)
    {
        var result = ToDecimalOrNull(data);
        if (result == null)
            return null;
        return Math.Round(result.Value, digits);
    }

    #endregion

    #region 日期转换

    /// <summary>
    ///     转换为日期
    /// </summary>
    /// <param name="data">数据</param>
    public static DateTime ToDate(this object data)
    {
        if (data == null)
            return DateTime.MinValue;
        DateTime result;
        return DateTime.TryParse(data.ToString(), out result) ? result : DateTime.MinValue;
    }

    /// <summary>
    ///     转换为可空日期
    /// </summary>
    /// <param name="data">数据</param>
    public static DateTime? ToDateOrNull(this object data)
    {
        if (data == null)
            return null;
        DateTime result;
        var isValid = DateTime.TryParse(data.ToString(), out result);
        if (isValid)
            return result;
        return null;
    }

    #endregion

    #region 布尔转换

    /// <summary>
    ///     转换为布尔值
    /// </summary>
    /// <param name="data">数据</param>
    public static bool ToBool(this object data)
    {
        if (data == null)
            return false;
        var value = GetBool(data);
        if (value != null)
            return value.Value;
        bool result;
        return bool.TryParse(data.ToString(), out result) && result;
    }

    /// <summary>
    ///     获取布尔值
    /// </summary>
    private static bool? GetBool(this object data)
    {
        switch (data.ToString().Trim().ToLower())
        {
            case "0":
                return false;
            case "1":
                return true;
            case "是":
                return true;
            case "否":
                return false;
            case "yes":
                return true;
            case "no":
                return false;
            default:
                return null;
        }
    }

    /// <summary>
    ///     转换为可空布尔值
    /// </summary>
    /// <param name="data">数据</param>
    public static bool? ToBoolOrNull(this object data)
    {
        if (data == null)
            return null;
        var value = GetBool(data);
        if (value != null)
            return value.Value;
        bool result;
        var isValid = bool.TryParse(data.ToString(), out result);
        if (isValid)
            return result;
        return null;
    }

    #endregion

    #region 是否为空

    /// <summary>
    ///     是否为空
    /// </summary>
    /// <param name="value">值</param>
    public static bool IsEmpty(this string value)
    {
        return string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    ///     是否为空
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public static bool IsEmpty(this object value)
    {
        if (value != null && !string.IsNullOrEmpty(value.ToString()))
            return false;
        return true;
    }

    /// <summary>
    ///     是否为空
    /// </summary>
    /// <param name="value">值</param>
    public static bool IsEmpty(this Guid? value)
    {
        if (value == null)
            return true;
        return IsEmpty(value.Value);
    }

    /// <summary>
    ///     是否为空
    /// </summary>
    /// <param name="value">值</param>
    public static bool IsEmpty(this Guid value)
    {
        if (value == Guid.Empty)
            return true;
        return false;
    }

    #endregion

    #region 转换为long

    /// <summary>
    ///     将object转换为long，若转换失败，则返回0。不抛出异常。
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static long ParseToLong(this object obj)
    {
        try
        {
            return long.Parse(obj.ToString());
        }
        catch
        {
            return 0L;
        }
    }

    /// <summary>
    ///     将object转换为long，若转换失败，则返回指定值。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static long ParseToLong(this string str, long defaultValue)
    {
        try
        {
            return long.Parse(str);
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion

    #region 转换为int

    /// <summary>
    ///     将object转换为int，若转换失败，则返回0。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static int ParseToInt(this object str)
    {
        try
        {
            return Convert.ToInt32(str);
        }
        catch
        {
            return 0;
        }
    }


    /// <summary>
    ///     转换成  整形数组
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static int[] ParseToIntArray(this object[] str)
    {
        var result = new List<int>();

        for (var i = 0; i < str.Length; i++) result.Add(str[i].ParseToInt());

        return result.ToArray();
    }

    /// <summary>
    ///     将object转换为int，若转换失败，则返回指定值。不抛出异常。
    ///     null返回默认值
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static int ParseToInt(this object str, int defaultValue)
    {
        if (str == null) return defaultValue;
        try
        {
            return Convert.ToInt32(str);
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion

    #region 转换为short

    /// <summary>
    ///     将object转换为short，若转换失败，则返回0。不抛出异常。
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static short ParseToShort(this object obj)
    {
        try
        {
            return short.Parse(obj.ToString());
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    ///     将object转换为short，若转换失败，则返回指定值。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static short ParseToShort(this object str, short defaultValue)
    {
        try
        {
            return short.Parse(str.ToString());
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion

    #region 转换为demical

    /// <summary>
    ///     将object转换为demical，若转换失败，则返回指定值。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static decimal ParseToDecimal(this object str, decimal defaultValue)
    {
        try
        {
            return decimal.Parse(str.ToString());
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    ///     判断是否是  Decimal 类型
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static bool IsDecimal(this object str)
    {
        try
        {
            decimal.Parse(str.ToString());

            return true;
        }
        catch
        {
            return false;
        }
    }
    /// <summary>
    ///     将object转换为demical，若转换失败，则返回0。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static decimal ParseToDecimal(this object str)
    {
        try
        {
            return decimal.Parse(str.ToString());
        }
        catch
        {
            return 0;
        }
    }

    #endregion

    #region 转化为bool

    /// <summary>
    ///     将object转换为bool，若转换失败，则返回false。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static bool ParseToBool(this object str)
    {
        try
        {
            return bool.Parse(str.ToString().ToLower());
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     将object转换为bool，若转换失败，则返回指定值。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    public static bool ParseToBool(this object str, bool result)
    {
        try
        {
            return bool.Parse(str.ToString());
        }
        catch
        {
            return result;
        }
    }

    #endregion

    #region 转换为float

    /// <summary>
    ///     将object转换为float，若转换失败，则返回0。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static float ParseToFloat(this object str)
    {
        try
        {
            return float.Parse(str.ToString());
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    ///     将object转换为float，若转换失败，则返回指定值。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    public static float ParseToFloat(this object str, float result)
    {
        try
        {
            return float.Parse(str.ToString());
        }
        catch
        {
            return result;
        }
    }

    #endregion

    #region 转换为DateTime

    /// <summary>
    ///     将string转换为DateTime，若转换失败，则返回日期最小值。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static DateTime ParseToDateTime(this object str)
    {
        try
        {
            if (str.GetType() == typeof(long))
            {
                //create a new DateTime value based on the Unix Epoch
                var converted = new DateTime(1970, 1, 1, 0, 0, 0, 0);

                //add the timestamp to the value
                var newDateTime = converted.AddSeconds(Convert.ToInt64(str));

                //return the value in string format
                return newDateTime.ToLocalTime();
            }

            str = str.ToString().Replace(".", "-");
            if (string.IsNullOrWhiteSpace(str.ToString())) return DateTime.MinValue;
            if (str.ToString().Contains("-") || str.ToString().Contains("/")) return DateTime.Parse(str.ToString());

            var length = str.ToString().Length;
            switch (length)
            {
                case 4:
                    return DateTime.ParseExact(str.ToString(), "yyyy", CultureInfo.CurrentCulture);
                case 6:
                    return DateTime.ParseExact(str.ToString(), "yyyyMM", CultureInfo.CurrentCulture);
                case 8:
                    return DateTime.ParseExact(str.ToString(), "yyyyMMdd", CultureInfo.CurrentCulture);
                case 10:
                    return DateTime.ParseExact(str.ToString(), "yyyyMMddHH", CultureInfo.CurrentCulture);
                case 12:
                    return DateTime.ParseExact(str.ToString(), "yyyyMMddHHmm", CultureInfo.CurrentCulture);
                case 14:
                    return DateTime.ParseExact(str.ToString(), "yyyyMMddHHmmss", CultureInfo.CurrentCulture);
                default:
                    return DateTime.ParseExact(str.ToString(), "yyyyMMddHHmmss", CultureInfo.CurrentCulture);
            }
        }
        catch
        {
            return DateTime.MinValue;
        }
    }

    /// <summary>
    ///     将string转换为DateTime，若转换失败，则返回默认值。
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static DateTime ParseToDateTime(this string str, DateTime? defaultValue)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(str)) return defaultValue.GetValueOrDefault();
            if (str.Contains("-") || str.Contains("/")) return DateTime.Parse(str);

            var length = str.Length;
            switch (length)
            {
                case 4:
                    return DateTime.ParseExact(str, "yyyy", CultureInfo.CurrentCulture);
                case 6:
                    return DateTime.ParseExact(str, "yyyyMM", CultureInfo.CurrentCulture);
                case 8:
                    return DateTime.ParseExact(str, "yyyyMMdd", CultureInfo.CurrentCulture);
                case 10:
                    return DateTime.ParseExact(str, "yyyyMMddHH", CultureInfo.CurrentCulture);
                case 12:
                    return DateTime.ParseExact(str, "yyyyMMddHHmm", CultureInfo.CurrentCulture);
                case 14:
                    return DateTime.ParseExact(str, "yyyyMMddHHmmss", CultureInfo.CurrentCulture);
                default:
                    return DateTime.ParseExact(str, "yyyyMMddHHmmss", CultureInfo.CurrentCulture);
            }
        }
        catch
        {
            return defaultValue.GetValueOrDefault();
        }
    }

    #endregion

    #region 转换为string

    /// <summary>
    ///     将object转换为string，若转换失败，则返回""。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static string ParseToString(this object obj)
    {
        try
        {
            if (obj == null)
                return string.Empty;
            return obj.ToString().Trim();
        }
        catch
        {
            return string.Empty;
        }
    }

    public static List<string> ParseToStrings(this object obj)
    {
        try
        {
            if (obj == null) return new List<string>();

            // 如果是 IEnumerable 类型但不是字符串
            if (obj is IEnumerable enumerable && !(obj is string))
            {
                return enumerable.Cast<object>()
                               .Select(x => x?.ToString() ?? string.Empty)
                               .Where(x => !string.IsNullOrWhiteSpace(x)) // 过滤掉空字符串
                               .ToList();
            }

            // 如果是单个对象，且不为空，则将其转换为只包含一个元素的列表
            var str = obj.ToString();
            return string.IsNullOrWhiteSpace(str)
                ? new List<string>()
                : new List<string> { str };
        }
        catch
        {
            return new List<string>();
        }
    }

    #endregion

    #region 转换为double

    /// <summary>
    ///     将object转换为double，若转换失败，则返回0。不抛出异常。
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static double ParseToDouble(this object obj)
    {
        try
        {
            return double.Parse(obj.ToString());
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    ///     将object转换为double，若转换失败，则返回指定值。不抛出异常。
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static double ParseToDouble(this object str, double defaultValue)
    {
        try
        {
            return double.Parse(str.ToString());
        }
        catch
        {
            return defaultValue;
        }
    }
    #endregion



    /// <summary>
    /// 获取可空字符串的值，如果为null则返回默认值
    /// </summary>
    /// <param name="str">可空字符串</param>
    /// <param name="defaultValue">默认值（可选）</param>
    /// <returns>处理后的字符串</returns>
    public static string GetValueOrDefault(this string? str, string defaultValue = "")
    {
        return string.IsNullOrWhiteSpace(str) ? defaultValue : str.Trim();
    }

    /// <summary>
    /// 安全地获取字符串的值，支持自定义空值判断条件
    /// </summary>
    /// <param name="str">源字符串</param>
    /// <param name="emptyValues">被视为空的值（可选）</param>
    /// <param name="defaultValue">默认值（可选）</param>
    /// <returns>处理后的字符串</returns>
    public static string GetValueOrDefault(this string str, string[] emptyValues, string defaultValue = "")
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        return emptyValues.Contains(str.Trim()) ? defaultValue : str.Trim();
    }

    /// <summary>
    /// 安全地获取字符串的值，支持自定义处理函数
    /// </summary>
    /// <param name="str">源字符串</param>
    /// <param name="processFunc">自定义处理函数</param>
    /// <param name="defaultValue">默认值（可选）</param>
    /// <returns>处理后的字符串</returns>
    public static string GetValueOrDefault(this string str, Func<string, string> processFunc, string defaultValue = "")
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        try
        {
            return processFunc(str.Trim());
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// 获取指定长度的字符串，超出部分将被截断
    /// </summary>
    /// <param name="str">源字符串</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="defaultValue">默认值（可选）</param>
    /// <returns>处理后的字符串</returns>
    public static string GetValueOrDefault(this string str, int maxLength, string defaultValue = "")
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        str = str.Trim();
        return str.Length > maxLength ? str[..maxLength] : str;
    }

    /// <summary>
    /// 将字符串转换为指定类型的值，转换失败时返回默认值
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    /// <param name="str">源字符串</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>转换后的值</returns>
    public static T GetValueOrDefault<T>(this string str, T defaultValue)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        try
        {
            var converter = System.ComponentModel.TypeDescriptor.GetConverter(typeof(T));
            if (converter != null && converter.CanConvertFrom(typeof(string)))
            {
                return (T)converter.ConvertFrom(str.Trim())!;
            }
        }
        catch
        {
            // 转换失败时返回默认值
        }

        return defaultValue;
    }
}