namespace PandaServer.Core;

public enum EventTypeEnum
{
    /// <summary>
    ///     教练登签
    /// </summary>
    [Description("教练登签失败")] CoachLogOnFailure = 0,

    /// <summary>
    ///     教练登签
    /// </summary>
    [Description("教练登签成功")] CoachLogOnSuccess = 1,

    /// <summary>
    ///     学员登签失败
    /// </summary>
    [Description("学员登签失败")] StudentLogOnFailure = 2,

    /// <summary>
    ///     学员登签成功
    /// </summary>
    [Description("学员登签成功")] StudentLogOnSuccess = 3,

    /// <summary>
    ///     日常训练
    /// </summary>
    [Description("日常训练")] DailyTrainSuccess = 4,

    /// <summary>
    ///     教练签退
    /// </summary>
    [Description("教练签退")] CoachLogOut = 5,

    /// <summary>
    ///     学员签退
    /// </summary>
    [Description("学员签退")] StudentLogOut = 6,

    /// <summary>
    ///     心跳回报
    /// </summary>
    [Description("心跳回报")] HeartMove = 7
}

public enum JxDeviceTypeEnum
{
    /// <summary>
    ///     车载计时 设备
    /// </summary>
    [Description("车载计时设备")]
    CarStudyDevice = 0,

    /// <summary>
    ///     闸机 设备
    /// </summary>
    [Description("人脸闸机设备 驾校人脸识别")]
    DoorDevice = 1,

    /// <summary>
    ///     考场身份闸机 设备
    /// </summary>
    [Description("考场身份闸机 当天考试认证")]
    ExamSiteDoorDevice = 2,

    /// <summary>
    ///     考场销售设备
    /// </summary>
    [Description("考场销售设备")]
    ExamSiteCashierDevice = 3,

    /// <summary>
    ///     考场车辆摄像头
    /// </summary>
    [Description("考场车辆摄像头")]
    ExamSiteCarCamera = 4,

    /// <summary>
    ///     考场训练闸机 设备
    /// </summary>
    [Description("考场训练闸机 约考即可进场")]
    ExamSiteStudyDoorDevice = 20,

    /// <summary>
    ///     考场训练闸机 设备
    /// </summary>
    [Description("考场训练闸机 买票即可进场")]
    ExamSiteBuyStudyDoorDevice = 21,

    /// <summary>
    ///     长沙自助点评设备
    /// </summary>
    [Description("长沙自助点评设备")]
    ExamSiteSelfCommentDevice = 31,
}

public enum JxDoorDirectionEnum
{
    /// <summary>
    ///     无方向
    /// </summary>
    [Description("无方向")] NoDirection = 0,

    /// <summary>
    ///     无方向
    /// </summary>
    [Description("入口方向")] Entrance = 1,

    /// <summary>
    ///     无方向
    /// </summary>
    [Description("出口方向")] Exit = 2
}

public enum JxDoorBrandModelEnum
{
    /// <summary>
    ///     无方向
    /// </summary>
    [Description("S8000")] S8000 = 0,

    /// <summary>
    ///     富利拓闸机
    /// </summary>
    [Description("富利拓闸机")] FuLiTuo = 1,

    /// <summary>
    ///     罗拉闸机
    /// </summary>
    [Description("罗拉闸机")] LuoLa = 2
}