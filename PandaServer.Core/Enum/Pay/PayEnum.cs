﻿namespace PandaServer.Core;

/// <summary>
///     交易方式  交通通道的 枚举
/// </summary>
public enum PayMethodEnum
{
    /// <summary>
    ///     付呗支付
    /// </summary>
    [Description("付呗支付")]
    FuBei = 0,



    /// <summary>
    ///     嘉联支付
    /// </summary>
    [Description("嘉联支付")]
    JlPay = 2,

    // /// <summary>
    // ///     微信红包
    // /// </summary>
    // [Description("微信红包")]
    // WxPaySendCoupon = 3
}

/// <summary>
///     交易的  外部关联表 枚举
/// </summary>
public enum PayOutTableEnum
{
    FreePay = 0,

    JxShouldPay = 1,

    HnjxPayOrderDetail = 3,

    StudentPayOrder = 4,

    ChildPayOrder = 5,

    ChildCostType = 6,

    HnjxPayOrder = 7,

    JxCostType = 8,

    Sale = 9,

    Item = 10,

    ItemKit = 11
}

/// <summary>
///     交易  支付通道
/// </summary>
public enum PayChannelEnum
{
    /// <summary>
    ///     未付款
    /// </summary>
    NoPay,

    /// <summary>
    ///     现金
    /// </summary>
    Cash,

    /// <summary>
    ///     微信
    /// </summary>
    WxPay,

    /// <summary>
    ///     支付宝
    /// </summary>
    AliPay,

    /// <summary>
    ///     银联
    /// </summary>
    UnionPay,

    /// <summary>
    ///     未知
    /// </summary>
    Unknown
}

/// <summary>
/// </summary>
public enum OrderStatusEnum
{
    // 等待付款
    [Description("等待付款")] WaitPay,

    // 付款成功
    [Description("付款成功")] Success,

    // 付款取消
    [Description("付款取消")] Cancel
}