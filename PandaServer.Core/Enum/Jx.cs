namespace PandaServer.Core;

/// <summary>
///     学员 状态的 枚举
/// </summary>
public enum JxStudentStatusEnum
{
    /// <summary>
    ///     待培
    /// </summary>
    [Description("待培")] WaitStudy = 0,

    /// <summary>
    ///     在培
    /// </summary>
    [Description("在培")] OnStudy = 1,

    /// <summary>
    ///     毕业
    /// </summary>
    [Description("毕业")] Graduate = 2,

    /// <summary>
    ///     退学
    /// </summary>
    [Description("退学")] Quit = 3,

    /// <summary>
    ///     转出
    /// </summary>
    [Description("转出")] Out = 4,

    /// <summary>
    ///     结业
    /// </summary>
    [Description("结业")] BeGraduate = 5,

    /// <summary>
    ///     考爆
    /// </summary>
    [Description("考爆")] OverFive = 6,

    /// <summary>
    ///     过期
    /// </summary>
    [Description("过期")] Expired = 7,

    /// <summary>
    ///     空号
    /// </summary>
    [Description("空号")] EmptyNumber = 9,

    /// <summary>
    ///     退款
    /// </summary>
    [Description("退款")] Refund = 10,

    /// <summary>
    ///     注销
    /// </summary>
    [Description("注销")] LogOut = 11,

    /// <summary>
    ///     申退
    /// </summary>
    [Description("申退")] ApplyQuit = 12,

    /// <summary>
    ///     跑单
    /// </summary>
    [Description("跑单")] RunOut = 13
}

/// <summary>
///     学员 考试成绩 的枚举
/// </summary>
public enum JxExamResultEnum
{
    /// <summary>
    ///     未考
    /// </summary>
    [Description("未考")] NoExam = 0,

    /// <summary>
    ///     合格
    /// </summary>
    [Description("合格")] Pass = 2,

    /// <summary>
    ///     不合格
    /// </summary>
    [Description("不合格")] NoPass = 3,

    /// <summary>
    ///     缺考
    /// </summary>
    [Description("缺考")] Absent = 4,

    /// <summary>
    ///     仪器故障
    /// </summary>
    [Description("仪器故障")] Error = 5,

    /// <summary>
    ///     取消
    /// </summary>
    [Description("取消")] Cancel = 6,

    /// <summary>
    ///     待考
    /// </summary>
    [Description("待考")] WaitExam = 7
}

public enum JxExamKeMuEnum
{
    /// <summary>
    ///     科目一
    /// </summary>
    [Description("科目一")] KeMu1 = 1,

    /// <summary>
    ///     科目二
    /// </summary>
    [Description("科目二")] KeMu2 = 2,

    /// <summary>
    ///     科目三
    /// </summary>
    [Description("科目三")] KeMu3 = 3,

    /// <summary>
    ///     科目四
    /// </summary>
    [Description("科目四")] KeMu4 = 4,


    /// <summary>
    ///     科目二三
    /// </summary>
    [Description("科目二三")] KeMu9 = 9,


    /// <summary>
    ///     科目二
    /// </summary>
    [Description("科目二模拟")] KeMu20 = 20,

    /// <summary>
    ///     科目三
    /// </summary>
    [Description("科目三模拟")] KeMu30 = 30
}

public enum JxWagesKeMuEnum
{
    /// <summary>
    /// 
    /// </summary>
    [Description("报名结算")]
    BaoMingJieSuan = 0,

    /// <summary>
    /// 
    /// </summary>
    [Description("科一合格结算")]
    KeYiHeGeLiJieSuan = 1,

    /// <summary>
    /// 
    /// </summary>
    [Description("科二合格结算")]
    KeTwoHeGeLiJieSuan = 2,

    /// <summary>
    /// 
    /// </summary>
    [Description("科三合格结算")]
    KeSanHeGeLiJieSuan = 3,

    /// <summary>
    /// 
    /// </summary>
    [Description("科四合格结算")]
    KeSiHeGeLiJieSuan = 4,

    /// <summary>
    /// 
    /// </summary>
    [Description("学费缴清结算")]
    XueFeiJiaoQingJieSuan = 10,

    /// <summary>
    /// 
    /// </summary>
    [Description("受理结算")]
    ShouLiJieSuan = 11,

    /// <summary>
    /// 
    /// </summary>
    [Description("科二五次不合格结算")]
    KeTwoWuCiBuHeGeJieSuan = 21,


    /// <summary>
    /// 
    /// </summary>
    [Description("科二不合格结算")]
    KeTwoBuHeGeJieSuan = 22,


    /// <summary>
    /// 
    /// </summary>
    [Description("科三五次不合格结算")]
    KeSanWuCiBuHeGeJieSuan = 31,


    /// <summary>
    /// 
    /// </summary>
    [Description("科三不合格结算")]
    KeSanBuHeGeJieSuan = 32,


    /// <summary>
    /// 
    /// </summary>
    [Description("综合结算")]
    ZongHeJieSuan = -1
}

/// <summary>
/// 工资的明细里面的结算类型
/// </summary>
public enum JxWagesType
{
    [Description("补考结算")]
    MakeUp = 0,        // 补考结算


    [Description("合格结算")]
    Qualified = 1,   // 合格结算


    [Description("招生结算")]
    Recruitment = 2, // 招生结算

    [Description("年底结算")]
    YearEnd = 3,      // 年底结算


    [Description("其他结算")]
    Other = 4           // 其他结算
}



/// <summary>
///     照片 类型的 枚举
/// </summary>
public enum JxStudentImageEnum
{
    /// <summary>
    ///     寸照
    /// </summary>
    [Description("寸照")] image0 = 0,

    /// <summary>
    ///     身份证电子照片
    /// </summary>
    [Description("身份证电子照片")] image1 = 1,

    /// <summary>
    ///     现场拍照
    /// </summary>
    [Description("现场拍照")] image2 = 2,

    /// <summary>
    ///     数码照片
    /// </summary>
    [Description("数码照片")] image8 = 8,

    /// <summary>
    ///     身份证明正反面
    /// </summary>
    [Description("身份证明正反面")] image4 = 4,

    /// <summary>
    ///     身份证复印件
    /// </summary>
    [Description("身份证复印件")] image14 = 14,

    /// <summary>
    ///     身份证正面
    /// </summary>
    [Description("证件正面（人像面）")] image40 = 40,

    /// <summary>
    ///     身份证反面
    /// </summary>
    [Description("身份证国徽面")] image41 = 41,

    /// <summary>
    ///     驾驶证申请表
    /// </summary>
    [Description("驾驶证申请表")] image6 = 6,

    /// <summary>
    ///     体检表
    /// </summary>
    [Description("体检表")] image7 = 7,

    /// <summary>
    ///     暂住证
    /// </summary>
    [Description("暂住证")] image5 = 5,

    /// <summary>
    ///     申请大中型客货车驾驶证业务告知书
    /// </summary>
    [Description("申请大中型客货车驾驶证业务告知书")] image9 = 9,

    /// <summary>
    ///     体检头像
    /// </summary>
    [Description("体检头像")] image10 = 10,

    /// <summary>
    ///     体检上肢
    /// </summary>
    [Description("体检上肢")] image11 = 11,

    /// <summary>
    ///     原驾驶证照片
    /// </summary>
    [Description("原驾驶证照片")] image20 = 20,

    /// <summary>
    ///     其他资料《一》
    /// </summary>
    [Description("其他资料")] image21 = 21,

    /// <summary>
    ///     其他资料《一》
    /// </summary>
    [Description("其他资料《二》")] image22 = 22,

    /// <summary>
    ///     其他资料《三》
    /// </summary>
    [Description("其他资料《三》")] image23 = 23,

    /// <summary>
    ///     人脸库预留照片
    /// </summary>
    [Description("人脸库预留照片")] image110 = 110,

    /// <summary>
    /// </summary>
    [Description("合同签署现场")] image200 = 200,

    /// <summary>
    /// </summary>
    [Description("合同第一页")] image201 = 201,

    /// <summary>
    /// </summary>
    [Description("合同第二页")] image202 = 202,

    /// <summary>
    /// </summary>
    [Description("合同第三页")] image203 = 203,

    /// <summary>
    /// </summary>
    [Description("合同第四页")] image204 = 204,

    /// <summary>
    /// </summary>
    [Description("合同第五页")] image205 = 205,

    /// <summary>
    /// </summary>
    [Description("合同第六页")] image206 = 206,

    /// <summary>
    /// </summary>
    [Description("合同第七页")] image207 = 207,

    /// <summary>
    /// </summary>
    [Description("合同第八页")] image208 = 208,

    /// <summary>
    /// </summary>
    [Description("合同第九页")] image209 = 209,

    /// <summary>
    /// </summary>
    [Description("合同第十页")] image210 = 210,

    /// <summary>
    /// </summary>
    [Description("合同拍照")] image220 = 220,

    /// <summary>
    ///     保险理赔授权书
    /// </summary>
    [Description("保险理赔授权书")] image300 = 300,

    /// <summary>
    ///     学生证
    /// </summary>
    [Description("学生证")] image400 = 400,

    /// <summary>
    ///     人脸识别
    /// </summary>
    [Description("人脸识别")] image500 = 500
}

/// <summary>
///     学员审核的状态
/// </summary>
public enum JxStudentDocSatusEnum
{
    /// <summary>
    ///     等待审核
    /// </summary>
    [Description("等待审核")] Wait = 0,


    /// <summary>
    ///     审核通过
    /// </summary>
    [Description("审核通过")] Success = 1,


    /// <summary>
    ///     审核失败
    /// </summary>
    [Description("审核失败")] Failure = 2
}

public enum JxCompanyImageEnum
{
    /// <summary>
    ///     营业执照
    /// </summary>
    [Description("营业执照")] image0 = 0,

    /// <summary>
    ///     道路运输许可证
    /// </summary>
    [Description("道路运输许可证")] image1 = 1,

    /// <summary>
    ///     法人身份证正面
    /// </summary>
    [Description("法人身份证正面")] image11 = 11,

    /// <summary>
    ///     法人身份证反面
    /// </summary>
    [Description("法人身份证反面")] image12 = 12,

    /// <summary>
    ///     负责人身份证正面
    /// </summary>
    [Description("负责人身份证正面")] image21 = 21,

    /// <summary>
    ///     负责人身份证反面
    /// </summary>
    [Description("负责人身份证反面")] image22 = 22,

    /// <summary>
    ///     联络人身份证正面
    /// </summary>
    [Description("联络人身份证正面")] image31 = 31,

    /// <summary>
    ///     联络人身份证反面
    /// </summary>
    [Description("联络人身份证反面")] image32 = 32,

    /// <summary>
    ///     门头照片
    /// </summary>
    [Description("门头照片")] image50 = 50,

    /// <summary>
    ///     场地照片
    /// </summary>
    [Description("门头照片")] image51 = 51,

    /// <summary>
    ///     公章签章
    /// </summary>
    [Description("公章签章")] image100 = 100,

    /// <summary>
    ///     法人签字
    /// </summary>
    [Description("法人签字")] image101 = 101
}