namespace PandaServer.Core;

/// <summary>
///     车辆状态的枚举
/// </summary>
public enum CarStatusEnum
{
    /// <summary>
    ///     正常
    /// </summary>
    [Description("正常状态")] Normal = 0,

    /// <summary>
    ///     异常
    /// </summary>
    [Description("异常状态")] Abnormal = 1,

    /// <summary>
    ///     失联失控
    /// </summary>
    [Description("失联失控")] OutOfControl = 2,

    /// <summary>
    ///     等待报废
    /// </summary>
    [Description("等待报废")] WaitingForScrap = 3,

    /// <summary>
    ///     已经报废
    /// </summary>
    [Description("已经报废")] Scrapped = 4,

    /// <summary>
    ///     车辆转出
    /// </summary>
    [Description("车辆转出")] TransferredOut = 5
}

/// <summary>
///     车辆照片 枚举
/// </summary>
public enum CarImageEnum
{
    /// <summary>
    ///     车身照片
    /// </summary>
    [Description("车身照片《一》")] image0 = 0,

    /// <summary>
    ///     车身照片
    /// </summary>
    [Description("车身照片《二》")] image1 = 1,

    /// <summary>
    ///     车身照片
    /// </summary>
    [Description("车身照片《三》")] image2 = 2,

    /// <summary>
    ///     行驶证照片
    /// </summary>
    [Description("行驶证照片")] image7 = 7,

    /// <summary>
    ///     行驶证照片 副本
    /// </summary>
    [Description("行驶证照片 副本")] image71 = 71,

    /// <summary>
    ///     行驶证照片 副本背面
    /// </summary>
    [Description("行驶证照片 副本背面")] image72 = 72,

    /// <summary>
    ///     登记证书照片
    /// </summary>
    [Description("登记证书照片")] image8 = 8,

    /// <summary>
    ///     发票照片
    /// </summary>
    [Description("发票照片")] image9 = 9,

    /// <summary>
    ///     保险照片
    /// </summary>
    [Description("保险照片《一》")] image10 = 10,

    /// <summary>
    ///     保险照片
    /// </summary>
    [Description("保险照片《二》")] image11 = 11,

    /// <summary>
    ///     保险照片
    /// </summary>
    [Description("保险照片《三》")] image12 = 12,

    /// <summary>
    ///     保险照片
    /// </summary>
    [Description("保险照片《四》")] image13 = 13,

    /// <summary>
    ///     保险照片
    /// </summary>
    [Description("保险照片《五》")] image14 = 14
}

/// <summary>
///     计时 设别状态  枚举
/// </summary>
public enum StudyDeviceStatusEnum
{
    /// <summary>
    ///     正常状态
    /// </summary>
    [Description("正常状态")] Normal = 1,

    /// <summary>
    ///     无法开机
    /// </summary>
    [Description("无法开机")] PowerError = 2,

    /// <summary>
    ///     软件错误
    /// </summary>
    [Description("软件错误")] SoftError = 3,

    /// <summary>
    ///     软件错误
    /// </summary>
    [Description("无法计时")] NoStudy = 4,

    /// <summary>
    ///     摄像头损坏
    /// </summary>
    [Description("摄像头损坏")] CameraError = 5,

    /// <summary>
    ///     无法上网
    /// </summary>
    [Description("无法上网")] SimError = 6,

    /// <summary>
    ///     无法确认
    /// </summary>
    [Description("无法确认")] UnableToConfirm = 10,

    /// <summary>
    ///     其他问题
    /// </summary>
    [Description("其他问题")] Other = 30
}