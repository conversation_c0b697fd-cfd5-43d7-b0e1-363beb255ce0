<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
  <NoWarn>$(NoWarn);all</NoWarn>
		<DocumentationFile>PandaServer.Core.xml</DocumentationFile>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<TargetFramework>net9.0</TargetFramework>
	</PropertyGroup>
	<ItemGroup>
    	<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="ExcelDataReader" Version="3.7.0" />
		<PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />

		<PackageReference Include="Furion.Extras.Authentication.JwtBearer" Version="4.9.7.12" />
		<PackageReference Include="Furion.Extras.ObjectMapper.Mapster" Version="4.9.7.12" />
		<PackageReference Include="Furion.Pure" Version="4.9.7.12" />
<!-- 
		<PackageReference Include="Furion.Extras.Authentication.JwtBearer" Version="4.9.5.8" />
		<PackageReference Include="Furion.Extras.ObjectMapper.Mapster" Version="4.9.5.8" />
		<PackageReference Include="Furion.Pure" Version="4.9.2.3" /> -->

		<PackageReference Include="Lazy.Captcha.Core" Version="2.0.7" />
		<PackageReference Include="Masuit.Tools.Core" Version="2.6.9.8" />
		<!-- <PackageReference Include="BouncyCastle.NetCore" Version="2.2.1" /> -->
		<PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
		<!-- <PackageReference Include="BouncyCastle.NetCore" Version="2.2.1" /> -->
		<!-- <PackageReference Include="BouncyCastle.Cryptography" Version="2.5.0-beta.79" /> -->
		<PackageReference Include="Serenity.Web" Version="3.14.5" />
		<PackageReference Include="SimpleRedis" Version="1.1.9" />
		<PackageReference Include="SimpleTool" Version="1.0.6" />
		<PackageReference Include="SqlSugarCore" Version="*********" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.Encoding.CodePages" Version="9.0.0-preview.1.24080.9" />
		<PackageReference Include="UAParser" Version="3.1.47" />
		<PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />
		<PackageReference Include="SharpZipLib" Version="1.4.2" />
    	<PackageReference Include="System.Text.Json" Version="9.0.2" />
		<PackageReference Include="NPinyinPro" Version="0.3.3" />
		<PackageReference Include="SimpleMQTT" Version="1.0.6" />
		<PackageReference Include="Microsoft.International.Converters.PinYinConverter" Version="1.0.0" />
	</ItemGroup>
	<ItemGroup>
		<!-- <ProjectReference Include="..\PandaServer.System\PandaServer.System.csproj"/> -->
		<None Update="Core.Development.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Core.Production.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	<ItemGroup>
		<Reference Include="FubeiOpenApi">
			<HintPath>..\DLL\FubeiOpenApi.dll</HintPath>
		</Reference>
		<Reference Include="Spire.Barcode">
		<HintPath>..\DLL\netstandard2.0\Spire.Barcode.dll</HintPath>
		</Reference>
		<Reference Include="Spire.Doc">
		<HintPath>..\DLL\netstandard2.0\Spire.Doc.dll</HintPath>
		</Reference>
		<Reference Include="Spire.Pdf">
		<HintPath>..\DLL\netstandard2.0\Spire.Pdf.dll</HintPath>
		</Reference>
		<Reference Include="Spire.XLS">
		<HintPath>..\DLL\netstandard2.0\Spire.XLS.dll</HintPath>
		</Reference>
	</ItemGroup>
	<ItemGroup>
		<Folder Include="Utils\Video\" />
	</ItemGroup>
</Project>