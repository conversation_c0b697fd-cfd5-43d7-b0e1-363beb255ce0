<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PandaServer.Core</name>
    </assembly>
    <members>
        <member name="T:PandaServer.Core.BatchEditAttribute">
            <summary>
                批量更新
            </summary>
        </member>
        <member name="T:PandaServer.Core.CodeGenAttribute">
            <summary>
                代码生成
            </summary>
        </member>
        <member name="T:PandaServer.Core.IgnoreInitTableAttribute">
            <summary>
                忽略初始化表
            </summary>
        </member>
        <member name="T:PandaServer.Core.IgnoreSeedDataAddAttribute">
            <summary>
                种子数据忽略新增
            </summary>
        </member>
        <member name="T:PandaServer.Core.IgnoreSeedDataUpdateAttribute">
            <summary>
                种子数据忽略修改
            </summary>
        </member>
        <member name="T:PandaServer.Core.RolePermissionAttribute">
            <summary>
                需要角色授权权限
            </summary>
        </member>
        <member name="T:PandaServer.Core.IgnoreRolePermissionAttribute">
            <summary>
                忽略角色授权权限
            </summary>
        </member>
        <member name="T:PandaServer.Core.SuperAdminAttribute">
            <summary>
                管理员才能访问特性
            </summary>
        </member>
        <member name="T:PandaServer.Core.IgnoreSuperAdminAttribute">
            <summary>
                忽略超级管理员才能访问特性
            </summary>
        </member>
        <member name="T:PandaServer.Core.IdNotNull">
            <summary>
                验证Id不能为 空
            </summary>
        </member>
        <member name="T:PandaServer.Core.BaseFileInput">
            <summary>
                文件上传输入参数
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseFileInput.File">
            <summary>
                文件
            </summary>
        </member>
        <member name="T:PandaServer.Core.BaseIdInput">
            <summary>
                主键Id输入参数
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseIdInput.Id">
            <summary>
                主键Id
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseIdInput.SysId">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Core.BaseIdInput.OldId">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Core.BaseIdInput.ParentId">
            <summary>
            
            </summary>
            <value></value>
        </member>
        <member name="P:PandaServer.Core.BaseIdInput.TenantId">
            <summary>
                需要用到 租户的 编号
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseIdInput.CreateTime">
            <summary>
                分表的表 传入这个创建时间  SplitTable 开始时间就用这个
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseIdInput.xm">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseIdInput.sfzmhm">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseIdInput.code">
            <summary>
            
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseIntIdInPut.Id">
            <summary>
                主键Id
            </summary>
        </member>
        <member name="T:PandaServer.Core.BasePageInput">
            <summary>
                全局分页查询输入参数
            </summary>
        </member>
        <member name="P:PandaServer.Core.BasePageInput.Current">
            <summary>
                当前页码
            </summary>
        </member>
        <member name="P:PandaServer.Core.BasePageInput.Size">
            <summary>
                每页条数
            </summary>
        </member>
        <member name="P:PandaServer.Core.BasePageInput.SortField">
            <summary>
                排序字段
            </summary>
        </member>
        <member name="P:PandaServer.Core.BasePageInput.SortOrder">
            <summary>
                排序方式，升序：ascend；降序：descend"
            </summary>
        </member>
        <member name="P:PandaServer.Core.BasePageInput.SearchKey">
            <summary>
                关键字
            </summary>
        </member>
        <member name="P:PandaServer.Core.BasePageInput.KeyWord">
            <summary>
                关键字
            </summary>
        </member>
        <member name="P:PandaServer.Core.BasePageInput.DesignId">
            <summary>
                设计的 报表的 DesignId
            </summary>
        </member>
        <member name="P:PandaServer.Core.BasePageInput.TableId">
            <summary>
                报表的 TableId
            </summary>
        </member>
        <member name="P:PandaServer.Core.BasePageInput.TenantId">
            <summary>
            
            </summary>
        </member>
        <member name="T:PandaServer.Core.BaseUrlInPut">
            <summary>
                主要是用于 前台 提交 更新页面 和 会话的时候用的 InPut
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseUrlInPut.Cookie">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseUrlInPut.Url">
            <summary>
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseUrlInPut.Id">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="T:PandaServer.Core.BaseSelectorOutput">
            <summary>
                基础选择器输出
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseSelectorOutput.Value">
            <summary>
                值
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseSelectorOutput.Label">
            <summary>
                显示文本
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseSelectorOutput.Text">
            <summary>
                显示文本
            </summary>
        </member>
        <member name="T:PandaServer.Core.GzipCompressionComponent">
            <summary>
                Gzip压缩组件
            </summary>
        </member>
        <member name="T:PandaServer.Core.LoggingConsoleComponent">
            <summary>
                日志写入文件的组件
            </summary>
        </member>
        <member name="T:PandaServer.Core.LoggingFileComponent">
            <summary>
                日志写入文件的组件
            </summary>
        </member>
        <member name="M:PandaServer.Core.LoggingFileComponent.SetLogOptions(Furion.Logging.FileLoggerOptions,System.Nullable{Microsoft.Extensions.Logging.LogLevel})">
            <summary>
                日志格式化
            </summary>
            <param name="options"></param>
            <param name="logLevel"></param>
        </member>
        <member name="T:PandaServer.Core.ClaimConst">
            <summary>
                授权用户常量
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.UserId">
            <summary>
                用户Id
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.StudentId">
            <summary>
                学员Id
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.FieldId">
            <summary>
                关联考场的 Id
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.UserSysId">
            <summary>
            用户的 系统的 Id
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.Phone">
            <summary>
                用户 电话
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.Account">
            <summary>
                账号
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.RealName">
            <summary>
                真实姓名
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.CompanyName">
            <summary>
                公司名称  UserEntity  标记的 公司的名称
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.CompanyId">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.NickName">
            <summary>
                昵称
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.TenantId">
            <summary>
                租户的编号
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.CityId">
            <summary>
                租户所在的城市
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.IsSuperAdmin">
            <summary>
                账号类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.IsTenantAdmin">
            <summary>
                是否是租户的管理员
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.TenantName">
            <summary>
                租户的 公司名称
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.TenantType">
            <summary>
                租户的 公司类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.IdCard">
            <summary>
                租户的 公司类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.PlusEndTime">
            <summary>
                Plus 服务的结束时间
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.ServiceEndTime">
            <summary>
                系统服务的结束时间
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.OpenId">
            <summary>
                微信用户 OpenId
            </summary>
        </member>
        <member name="F:PandaServer.Core.ClaimConst.UnionId">
            <summary>
                微信用户 UnionId
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.Zero">
            <summary>
                0
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.Add">
            <summary>
                添加操作
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.Edit">
            <summary>
                编辑操作
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.Enable">
            <summary>
                启用操作
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.Disable">
            <summary>
                禁用操作
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.ResestPwd">
            <summary>
                重置密码操作
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.GrantRole">
            <summary>
                用户授权操作
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.SysOrg">
            <summary>
                组织
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.BizOrg">
            <summary>
                机构
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.SysPos">
            <summary>
                职位
            </summary>
        </member>
        <member name="F:PandaServer.Core.PandaServerConst.BizPos">
            <summary>
                岗位
            </summary>
        </member>
        <member name="T:PandaServer.Core.PermissionConst">
            <summary>
            系统权限常量
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_MODIFY_AUDIT">
            <summary>
            审批流程-学员信息-修改审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.FINANCE_EXPENDITURE_EDIT">
            <summary>
            财务支出-支出编辑
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.FINANCE_INCOME_EDIT">
            <summary>
            财务入账-收入编辑
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.FINANCE_EXPENDITURE_DELETE">
            <summary>
            财务支出-支出删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.FINANCE_INCOME_DELETE">
            <summary>
            财务入账-收入删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.FINANCE_REFUND_AUDIT">
            <summary>
            审批流程-学员财务-退款审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_STATUS_CHANGE_AUDIT">
            <summary>
            审批流程-学员财务-退学审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_DISCOUNT_AUDIT">
            <summary>
            审批流程-学员财务-优惠审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_QUIT_AUDIT">
            <summary>
            审批流程-学员财务-退学审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_REFUND_AUDIT">
            <summary>
            审批流程-学员财务-退款审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_CAR_ASSIGNMENT_AUDIT">
            <summary>
            审批流程-学员分车-分配审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_CAR_ASSIGNMENT_MODIFY_AUDIT">
            <summary>
            审批流程-学员分车-修改审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.ACCOUNT_INFO_CHANGE_AUDIT">
            <summary>
            系统权限-账号信息-编辑
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.ACCOUNT_ROLE_CHANGE_AUDIT">
            <summary>
            系统权限-账号权限-编辑
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.ACCOUNT_PRICE_CHANGE_AUDIT">
            <summary>
            系统权限-账号价格-编辑
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.ACCOUNT_CAR_CHANGE_AUDIT">
            <summary>
            系统权限-账号关联车辆-编辑
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.FINANCE_EXAM_REFUND_AUDIT">
            <summary>
            审批流程-学员财务-考场退款审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.SYSTEM_USER_CATEGORY_EDIT">
            <summary>
            系统权限-用户分类编辑
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_MODIFY_USERSALE3">
            <summary>
            修改协单人
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_MODIFY_USERSALE2">
            <summary>
            修改责任人
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_MODIFY_USERSALE1">
            <summary>
            修改推荐人
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.FINANCE_PAY_VIEW">
            <summary>
            查看收入
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_COST">
            <summary>
            学员管理-学员搜索-学员财务-挂账
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_EXPENSE">
            <summary>
            学员管理-学员搜索-学员财务-支出
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.FINANCE_COST_VIEW">
            <summary>
            查看支出
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_EXAM_DELETE">
            <summary>
            删除成绩
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_PAY">
            <summary>
            学员管理-学员搜索-学员财务-缴费
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_CREATE">
            <summary>
            学员管理-学员搜索-学员财务-报名
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.CASH_COUPON_AUDIT">
            <summary>
            返现审核-考试预约-模拟返现-考场财务-返现审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.USER_ADD">
            <summary>
            员工添加-学员管理-学员搜索-学员财务-员工添加
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_BATCH_COST_AND_PAY">
            <summary>
            批量支出挂账并缴费
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_BATCH_COST">
            <summary>
            批量支出挂账
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_BATCH_COST_BALANCE">
            <summary>
            批量已挂账项目平账
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_BATCH_INCOME_AND_PAY">
            <summary>
            批量收入挂账并缴费
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_BATCH_INCOME">
            <summary>
            批量收入挂账
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_BATCH_SET_TUITION_AND_PAY">
            <summary>
            批量设置学费并缴费
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.STUDENT_BATCH_SET_TUITION">
            <summary>
            批量设置学费
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.EXAM_APPOINTMENT_EXAM_FINANCE_EXPORT">
            <summary>
            考场财务导出权限
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.EXAM_APPOINTMENT_SIMULATE_CASHBACK_EXAM_FINANCE_EXPORT">
            <summary>
            模拟返现导出权限
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.EXAM_APPOINTMENT_EXAM_FINANCE_STUDY_EXPORT">
            <summary>
            消券列表导出权限
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.EXAM_SITE_OPEN_GATE">
            <summary>
            考试开闸
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.ACCOUNT_REVIEW">
            <summary>
            账号审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_VEHICLE_MONITOR">
            <summary>
            车辆监控报警
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_SCAN_MANAGEMENT">
            <summary>
            扫码管理
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_ACCOUNT_RECHARGE">
            <summary>
            账户充值
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_ENROLLMENT_STATISTICS">
            <summary>
            招生统计
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_FINANCIAL_STATISTICS">
            <summary>
            财务统计
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_EXAM_STATISTICS">
            <summary>
            考试统计
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_DAILY_MESSAGE_PUSH">
            <summary>
            微信日报消息推送
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_FACE_RECOGNITION">
            <summary>
            人脸识别
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_SITE_REGISTRATION">
            <summary>
            场地登记
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_NEW_ACCOUNT_AUDIT">
            <summary>
            新增账户审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_EXAM_LIST">
            <summary>
            考试名单
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:PandaServer.Core.PermissionConst.WECHAT_EXAM_FINANCE" -->
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_ONSITE_REFUND">
            <summary>
            现场退费
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_CASHBACK_MANAGEMENT">
            <summary>
            返现管理
            </summary>
        </member>
        <member name="F:PandaServer.Core.PermissionConst.WECHAT_USER_POINTS_CONSUMPTION">
            <summary>
            用户积分消费
            </summary>
        </member>
        <member name="T:PandaServer.Core.MqttMessage">
            <summary>
                mqtt消息
            </summary>
        </member>
        <member name="P:PandaServer.Core.MqttMessage.MsgType">
            <summary>
                消息分类
            </summary>
        </member>
        <member name="P:PandaServer.Core.MqttMessage.Data">
            <summary>
                消息内容
            </summary>
        </member>
        <member name="P:PandaServer.Core.MqttMessage.DetTime">
            <summary>
                时间
            </summary>
        </member>
        <member name="T:PandaServer.Core.TokenInfo">
            <summary>
                Token信息
            </summary>
        </member>
        <member name="P:PandaServer.Core.TokenInfo.ClientIds">
            <summary>
                MQTT客户端ID列表
            </summary>
        </member>
        <member name="P:PandaServer.Core.TokenInfo.Device">
            <summary>
                设备
            </summary>
        </member>
        <member name="P:PandaServer.Core.TokenInfo.LoginClientType">
            <summary>
                登录端
            </summary>
        </member>
        <member name="P:PandaServer.Core.TokenInfo.Expire">
            <summary>
                过期时间
            </summary>
        </member>
        <member name="P:PandaServer.Core.TokenInfo.Token">
            <summary>
                Token
            </summary>
        </member>
        <member name="P:PandaServer.Core.TokenInfo.TokenTimeout">
            <summary>
                超时时间
            </summary>
        </member>
        <member name="P:PandaServer.Core.TokenInfo.TokenRemain">
            <summary>
                token剩余有效期
            </summary>
        </member>
        <member name="P:PandaServer.Core.TokenInfo.TokenRemainPercent">
            <summary>
                token剩余有效期百分比
            </summary>
        </member>
        <member name="T:PandaServer.Core.AuditEnum">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.AuditEnum.WaitAudit">
            <summary>
                等待审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.AuditEnum.AuditPass">
            <summary>
                审核通过
            </summary>
        </member>
        <member name="F:PandaServer.Core.AuditEnum.AuditFail">
            <summary>
                审核失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.AuditEnum.NoAudit">
            <summary>
                初始状态
            </summary>
        </member>
        <member name="T:PandaServer.Core.AdminType">
            <summary>
                账号类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.AdminType.SuperAdmin">
            <summary>
                超级管理员
            </summary>
        </member>
        <member name="F:PandaServer.Core.AdminType.Admin">
            <summary>
                管理员
            </summary>
        </member>
        <member name="F:PandaServer.Core.AdminType.None">
            <summary>
                普通账号
            </summary>
        </member>
        <member name="T:PandaServer.Core.AuthDeviceTypeEumu">
            <summary>
                登录设备类型枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.AuthDeviceTypeEumu.PC">
            <summary>
                PC端
            </summary>
        </member>
        <member name="F:PandaServer.Core.AuthDeviceTypeEumu.APP">
            <summary>
                移动端
            </summary>
        </member>
        <member name="F:PandaServer.Core.AuthDeviceTypeEumu.MINI">
            <summary>
                小程序
            </summary>
        </member>
        <member name="F:PandaServer.Core.AuthDeviceTypeEumu.DEVICE">
            <summary>
                设备自己登录
            </summary>
        </member>
        <member name="T:PandaServer.Core.LoginClientTypeEnum">
            <summary>
                登录端类型枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginClientTypeEnum.B">
            <summary>
                B端
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginClientTypeEnum.C">
            <summary>
                C端 非 Web 端
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginClientTypeEnum.D">
            <summary>
                D端 非 Web 端 非标准的登录
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginClientTypeEnum.E">
            <summary>
                空用户的登录 主要用户设备自动登录 
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginClientTypeEnum.W">
            <summary>
                微信快速登录
            </summary>
        </member>
        <member name="T:PandaServer.Core.LoginType">
            <summary>
                登陆类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginType.LOGIN">
            <summary>
                登陆
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginType.LOGOUT">
            <summary>
                登出
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginType.REGISTER">
            <summary>
                注册
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginType.CHANGEPASSWORD">
            <summary>
                改密
            </summary>
        </member>
        <member name="F:PandaServer.Core.LoginType.AUTHORIZEDLOGIN">
            <summary>
                三方授权登陆
            </summary>
        </member>
        <member name="T:PandaServer.Core.MenuOpenType">
            <summary>
                系统菜单类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.MenuOpenType.NONE">
            <summary>
                无
            </summary>
        </member>
        <member name="F:PandaServer.Core.MenuOpenType.COMPONENT">
            <summary>
                组件
            </summary>
        </member>
        <member name="F:PandaServer.Core.MenuOpenType.INNER">
            <summary>
                内链
            </summary>
        </member>
        <member name="F:PandaServer.Core.MenuOpenType.OUTER">
            <summary>
                外链
            </summary>
        </member>
        <member name="T:PandaServer.Core.MenuType">
            <summary>
                系统菜单类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.MenuType.DIR">
            <summary>
                目录
            </summary>
        </member>
        <member name="F:PandaServer.Core.MenuType.MENU">
            <summary>
                菜单
            </summary>
        </member>
        <member name="F:PandaServer.Core.MenuType.BTN">
            <summary>
                按钮
            </summary>
        </member>
        <member name="T:PandaServer.Core.MenuWeight">
            <summary>
                菜单权重
            </summary>
        </member>
        <member name="F:PandaServer.Core.MenuWeight.SUPER_ADMIN_WEIGHT">
            <summary>
                系统权重
            </summary>
        </member>
        <member name="F:PandaServer.Core.MenuWeight.DEFAULT_WEIGHT">
            <summary>
                业务权重
            </summary>
        </member>
        <member name="T:PandaServer.Core.RoleType">
            <summary>
                账号类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.RoleType.AdminRole">
            <summary>
                租户管理员角色
            </summary>
        </member>
        <member name="F:PandaServer.Core.RoleType.NormalRole">
            <summary>
                租户普通角色
            </summary>
        </member>
        <member name="T:PandaServer.Core.CacheType">
            <summary>
                缓存类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.CacheType.MemoryCache">
            <summary>
                内存缓存
            </summary>
        </member>
        <member name="F:PandaServer.Core.CacheType.RedisCache">
            <summary>
                Redis缓存
            </summary>
        </member>
        <member name="T:PandaServer.Core.CarStatusEnum">
            <summary>
                车辆状态的枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarStatusEnum.Normal">
            <summary>
                正常
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarStatusEnum.Abnormal">
            <summary>
                异常
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarStatusEnum.OutOfControl">
            <summary>
                失联失控
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarStatusEnum.WaitingForScrap">
            <summary>
                等待报废
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarStatusEnum.Scrapped">
            <summary>
                已经报废
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarStatusEnum.TransferredOut">
            <summary>
                车辆转出
            </summary>
        </member>
        <member name="T:PandaServer.Core.CarImageEnum">
            <summary>
                车辆照片 枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image0">
            <summary>
                车身照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image1">
            <summary>
                车身照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image2">
            <summary>
                车身照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image7">
            <summary>
                行驶证照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image71">
            <summary>
                行驶证照片 副本
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image72">
            <summary>
                行驶证照片 副本背面
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image8">
            <summary>
                登记证书照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image9">
            <summary>
                发票照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image10">
            <summary>
                保险照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image11">
            <summary>
                保险照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image12">
            <summary>
                保险照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image13">
            <summary>
                保险照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.CarImageEnum.image14">
            <summary>
                保险照片
            </summary>
        </member>
        <member name="T:PandaServer.Core.StudyDeviceStatusEnum">
            <summary>
                计时 设别状态  枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.StudyDeviceStatusEnum.Normal">
            <summary>
                正常状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.StudyDeviceStatusEnum.PowerError">
            <summary>
                无法开机
            </summary>
        </member>
        <member name="F:PandaServer.Core.StudyDeviceStatusEnum.SoftError">
            <summary>
                软件错误
            </summary>
        </member>
        <member name="F:PandaServer.Core.StudyDeviceStatusEnum.NoStudy">
            <summary>
                软件错误
            </summary>
        </member>
        <member name="F:PandaServer.Core.StudyDeviceStatusEnum.CameraError">
            <summary>
                摄像头损坏
            </summary>
        </member>
        <member name="F:PandaServer.Core.StudyDeviceStatusEnum.SimError">
            <summary>
                无法上网
            </summary>
        </member>
        <member name="F:PandaServer.Core.StudyDeviceStatusEnum.UnableToConfirm">
            <summary>
                无法确认
            </summary>
        </member>
        <member name="F:PandaServer.Core.StudyDeviceStatusEnum.Other">
            <summary>
                其他问题
            </summary>
        </member>
        <member name="T:PandaServer.Core.ChildStudentStatusEnum">
            <summary>
                学员状态的枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.ChildStudentStatusEnum.OnStudy">
            <summary>
                在培
            </summary>
        </member>
        <member name="F:PandaServer.Core.ChildStudentStatusEnum.Graduate">
            <summary>
                毕业
            </summary>
        </member>
        <member name="F:PandaServer.Core.ChildStudentStatusEnum.Quit">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.StatusEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.IsEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.XbEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.NeedEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.OperateStatusEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.UploadFileType">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.PlatformEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.ApplyStatusEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.AuditStatusEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.PayStatusEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.YesOrNot">
            <summary>
                是否枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.YesOrNot.Y">
            <summary>
                是
            </summary>
        </member>
        <member name="F:PandaServer.Core.YesOrNot.N">
            <summary>
                否
            </summary>
        </member>
        <member name="T:PandaServer.Core.AppTypeEnum">
            <summary>
                各个租户 自己使用的 App  的相关配置
            </summary>
        </member>
        <member name="T:PandaServer.Core.CommonStatus">
            <summary>
                公共状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.CommonStatus.ENABLE">
            <summary>
                正常
            </summary>
        </member>
        <member name="F:PandaServer.Core.CommonStatus.DISABLE">
            <summary>
                停用
            </summary>
        </member>
        <member name="F:PandaServer.Core.CommonStatus.DELETED">
            <summary>
                删除
            </summary>
        </member>
        <member name="T:PandaServer.Core.DataOpType">
            <summary>
                数据操作类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.OTHER">
            <summary>
                其它
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.ADD">
            <summary>
                增加
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.DELETE">
            <summary>
                删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.EDIT">
            <summary>
                编辑
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.UPDATE">
            <summary>
                更新
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.QUERY">
            <summary>
                查询
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.DETAIL">
            <summary>
                详情
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.TREE">
            <summary>
                树
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.IMPORT">
            <summary>
                导入
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.EXPORT">
            <summary>
                导出
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.GRANT">
            <summary>
                授权
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.FORCE">
            <summary>
                强退
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.CLEAN">
            <summary>
                清空
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataOpType.CHANGE_STATUS">
            <summary>
                修改状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataScopeType.ALL">
            <summary>
                全部数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataScopeType.DEPT_WITH_CHILD">
            <summary>
                本部门及以下数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataScopeType.DEPT">
            <summary>
                本部门数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataScopeType.SELF">
            <summary>
                仅本人数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.DataScopeType.DEFINE">
            <summary>
                自定义数据
            </summary>
        </member>
        <member name="T:PandaServer.Core.Dict">
            <summary>
                一些 字典类型
            </summary>
        </member>
        <member name="P:PandaServer.Core.Dict.sfzmmc">
            <summary>
                证件类型
            </summary>
        </member>
        <member name="P:PandaServer.Core.Dict.CarType">
            <summary>
                准驾车型
            </summary>
        </member>
        <member name="T:PandaServer.Core.DocumentType">
            <summary>
                文档类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.DocumentType.Folder">
            <summary>
                文件夹
            </summary>
        </member>
        <member name="F:PandaServer.Core.DocumentType.File">
            <summary>
                文件
            </summary>
        </member>
        <member name="T:PandaServer.Core.ErrorCode">
            <summary>
                系统错误码
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.A500">
            <summary>
                系统错误
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB2001">
            <summary>
                名字不能重复
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB1002">
            <summary>
                添加数据失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB1003">
            <summary>
                更新数据失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB1004">
            <summary>
                数据转换失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB1005">
            <summary>
                数据删除失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB10050">
            <summary>
                当前数据有关联数据，无法删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB10051">
            <summary>
                非数据创建人员或管理员无法删除该数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB10052">
            <summary>
                非数据创建人员或管理员无法修改该数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB10053">
            <summary>
                您没有修改该数据的权限
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB10054">
            <summary>
                您没有删除该数据的权限
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB1006">
            <summary>
                数据重复,添加数据失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D0009">
            <summary>
                请完整填写各项
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1000">
            <summary>
                用户名或密码不正确
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1001">
            <summary>
                非法操作！禁止删除自己
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D404">
            <summary>
                记录不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1003">
            <summary>
                系统暂未开通此功能
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D10030">
            <summary>
                账号已存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D10031">
            <summary>
                账号不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1004">
            <summary>
                旧密码不匹配
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1005">
            <summary>
                测试数据禁止更改admin密码
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1006">
            <summary>
                数据已存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1007">
            <summary>
                数据不存在或含有关联引用，禁止删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1008">
            <summary>
                禁止为管理员分配角色
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1009">
            <summary>
                重复数据或记录含有不存在数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1010">
            <summary>
                禁止为超级管理员角色分配权限
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1011">
            <summary>
                非法数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1012">
            <summary>
                Id不能为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1013">
            <summary>
                没有权限操作该数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1014">
            <summary>
                禁止删除超级管理员
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1015">
            <summary>
                禁止修改超级管理员状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1016">
            <summary>
                没有权限
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1017">
            <summary>
                账号已冻结
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D10171">
            <summary>
                因未允许登录时间无法登录
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D10172">
            <summary>
                因锁定时间导致无法登录
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D10173">
            <summary>
                因账号无效状态无法登录
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1018">
            <summary>
                账号不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1019">
            <summary>
                账号的登录权限未初始化，请先联系管理员初始化登录权限
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1020">
            <summary>
                所属公司不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1021">
            <summary>
                IP限制
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1022">
            <summary>
                登录会话已经超时
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D2000">
            <summary>
                父机构不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D2001">
            <summary>
                当前机构Id不能与父机构Id相同
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D2002">
            <summary>
                已有相同组织机构,编码或名称相同
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D2003">
            <summary>
                没有权限操作机构
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D2004">
            <summary>
                该机构下有员工禁止删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D2005">
            <summary>
                附属机构下有员工禁止删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D2006">
            <summary>
                只能增加下级机构
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D3000">
            <summary>
                字典类型不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D3001">
            <summary>
                字典类型已存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D3002">
            <summary>
                字典类型下面有字典值禁止删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D3003">
            <summary>
                字典值已存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D3004">
            <summary>
                字典值不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D3005">
            <summary>
                字典状态错误
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D4000">
            <summary>
                菜单已存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D4001">
            <summary>
                路由地址为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D4002">
            <summary>
                打开方式为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D4003">
            <summary>
                权限标识格式为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D4004">
            <summary>
                权限标识格式错误
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D4005">
            <summary>
                权限不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D4006">
            <summary>
                父级菜单不能为当前节点，请重新选择父级菜单
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D4007">
            <summary>
                不能移动根节点
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D5000">
            <summary>
                已存在同名或同编码应用
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D5001">
            <summary>
                默认激活系统只能有一个
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D5002">
            <summary>
                该应用下有菜单禁止删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D5003">
            <summary>
                已存在同名或同编码应用
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D6000">
            <summary>
                已存在同名或同编码职位
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D6001">
            <summary>
                该职位下有员工禁止删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D7000">
            <summary>
                通知公告状态错误
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D7001">
            <summary>
                通知公告删除失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D7002">
            <summary>
                通知公告编辑失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D8000">
            <summary>
                文件不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D9000">
            <summary>
                已存在同名或同编码参数配置
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D9001">
            <summary>
                禁止删除系统参数
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1100">
            <summary>
                已存在同名任务调度
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1101">
            <summary>
                任务调度不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1200">
            <summary>
                演示环境禁止修改数据
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1300">
            <summary>
                已存在同名或同主机租户
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1400">
            <summary>
                该表代码模板已经生成过
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1501">
            <summary>
                该类型不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1502">
            <summary>
                该字段不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1503">
            <summary>
                该类型不是枚举类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1504">
            <summary>
                该实体不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.D1505">
            <summary>
                父菜单不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.XG1000">
            <summary>
                已存在同名或同编码项目
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.XG1001">
            <summary>
                已存在相同证件号码人员
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.XG1002">
            <summary>
                检测数据不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB1000">
            <summary>
                请添加数据列
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DB1001">
            <summary>
                数据表不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.F1000">
            <summary>
                表单不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CP0001">
            <summary>
                新密码长度不能少于 6 位
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CP0002">
            <summary>
                两次输入的新密码不一致
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CP0003">
            <summary>
                原始密码输入错误
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CP0004">
            <summary>
                该账号绑定的手机号码和您登录的手机号码不一致
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CP0005">
            <summary>
                密码必须要大于 6 位
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CP0006">
            <summary>
                手机号码无法匹配相关账号
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.AT0001">
            <summary>
                账号规则不能为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.AT0002">
            <summary>
                账号规则不能重复
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.AT0003">
            <summary>
                账号规则长度必须要大于 2 位
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0001">
            <summary>
                微信配置的 AppId 已经被占用
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0002">
            <summary>
                平台正在初始化，请重新打开
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0003">
            <summary>
                读取配置失败请联系工作人员
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0004">
            <summary>
                当前微信还未登录,请重新进入
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0005">
            <summary>
                当前用户 OpenId 不能为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0006">
            <summary>
                当前 appId 不能为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0007">
            <summary>
                当前 appId 还未在后台配置
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0008">
            <summary>
                当前微信还未完成登录!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0009">
            <summary>
                手机号码无效，请联系驾校工作人员!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0010">
            <summary>
                当前用户数据库还未初始化!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0011">
            <summary>
                Hnjx 教练数据还未初始化，请先登录!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0012">
            <summary>
                当前公司还未绑定小程序，请先申请小程序进行绑定!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WX0013">
            <summary>
                当前小程序的 appId 还为配置到相关的公司账户，请联系技术人员!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SMS00001">
            <summary>
                同一个微信 120 秒之内只能申请一次验证码!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SMS00002">
            <summary>
                同一个微信 24 小时之内只能申请 10 次 验证码!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SMS00003">
            <summary>
                当前验证码已经失效!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SMS00004">
            <summary>
                验证码错误，请重新输入!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SMS00005">
            <summary>
                验证码输入次数过多，请重新申请!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.EF0001">
            <summary>
                考场名称不能为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.EF0002">
            <summary>
                考场昵称不能为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.EF0003">
            <summary>
                考场昵称长度不能超过10位
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.EF0004">
            <summary>
                考场昵称不能为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.EF0005">
            <summary>
                考场昵称长度不能超过10位
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.EF0006">
            <summary>
                考场名称已经被占用
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.EF0007">
            <summary>
                考场地址不能为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.EF0008">
            <summary>
                考场定位不能为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.WXP0001">
            <summary>
                当前支付账户配置为空
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HTTP0001">
            <summary>
                参数错误
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.JX0001">
            <summary>
                请选择所属驾校
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.JX0002">
            <summary>
                当前驾校名称未注册，请联系管理员
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.JX0003">
            <summary>
                该商品的价格未配置
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.JX0004">
            <summary>
                请保存驾校的相关系统配置!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MENU0001">
            <summary>
                无相关权限
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.ROLE0001">
            <summary>
                权限名称重复
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.ASS001">
            <summary>
                请完整的给信誉考核的每一项打分!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.ASS002">
            <summary>
                本年度的的考核题目还未更新!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.ASS003">
            <summary>
                请勿重复申报!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.JXF001">
            <summary>
                场地名称重复，无法添加!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.JXF002">
            <summary>
                场地名称不能为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CT0001">
            <summary>
                费用类型名称不能为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CT0002">
            <summary>
                费用类型名称不能重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CL0001">
            <summary>
                班别类型名称不能为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CL0002">
            <summary>
                班别类型名称不能重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CL0003">
            <summary>
                车型为空,无法读取配置数据!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CL0004">
            <summary>
                当前车型未配置相关的价格!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CL0005">
            <summary>
                学费的费用类型必须要选择!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CL0006">
            <summary>
                报名的金额必须要大于等于 0!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CL0007">
            <summary>
                有重复的配置，添加失败!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0001">
            <summary>
                身份证号码重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0002">
            <summary>
                请先安装并打开报名插件客户端!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0003">
            <summary>
                当前电脑还未注册!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0004">
            <summary>
                当前电脑注册的报名店面和选择的店面不一致!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0005">
            <summary>
                请选择正确的训练场地!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0006">
            <summary>
                请选择正确的准驾车型!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0007">
            <summary>
                请选择正确的报名班型!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0008">
            <summary>
                推荐人员不能为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0009">
            <summary>
                系统配置的价格出错，请联系管理员!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0010">
            <summary>
                姓名和证件号验证失败!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0011">
            <summary>
                姓名和证件号验证失败!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0012">
            <summary>
                请先选择学员!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0013">
            <summary>
                身份证电子照片无法上传!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0014">
            <summary>
                请填写邮政编码!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.STU0015">
            <summary>
                请填写正确的身份证号码!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0001">
            <summary>
                请选择正确的缴费门店!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0002">
            <summary>
                当前缴费店面没有设置相应的支付账户!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0003">
            <summary>
                无法检索相关的学员信息!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0004">
            <summary>
                无法找到相关培训场地信息!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0005">
            <summary>
                三方支付账户获取失败!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0006">
            <summary>
                当前系统暂未支持该支付通道!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0007">
            <summary>
                添加订单失败!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0008">
            <summary>
                支付账户参数获取失败，请重新扫码!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0009">
            <summary>
                支付账户的信息获取失败!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0010">
            <summary>
                sign 验证失败!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0011">
            <summary>
                当前支付状态异常!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0012">
            <summary>
                支付失败，请稍后重试!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0013">
            <summary>
                请选择正确的支付方式!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0014">
            <summary>
                请输入正确的缴费金额!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0015">
            <summary>
                请选择正确的缴费日期的区间!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0016">
            <summary>
                缴费日期区间不能跨月!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0017">
            <summary>
                还未完成支付!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0018">
            <summary>
                当前优惠券未关联订单!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0019">
            <summary>
                订单已经支付成功，相关的优惠无法取消!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0020">
            <summary>
                订单已经提交取消申请，请重试!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0021">
            <summary>
                当前订单状态未知!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0022">
            <summary>
                有缴费信息无法删除应缴信息!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0023">
            <summary>
                有缴费记录，应缴记录无法删除!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAY0024">
            <summary>
                当前商品未定价，无法进行购买!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CSJX0001">
            <summary>
                当前的姓名和证件号未检索到任何信息，请先联系驾校录入相关的资料!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CSJX0002">
            <summary>
                当前的姓名和证件号检索到多条记录!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CSJX0003">
            <summary>
                当前微信已经绑定了其他学员信息，无法再次登录验证!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CSJX0004">
            <summary>
                当前报名场地未验收，无法进行认证!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.AI0001">
            <summary>
                当前证件照片和您的提交的身份证信息不匹配!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DC0001">
            <summary>
                当前 Mac 地址已经被录入，请勿重复录入!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DC0002">
            <summary>
                电脑 的名称不能为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DC0003">
            <summary>
                电脑 Mac 地址不能为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DC0004">
            <summary>
                当前 Mac 地址为绑定任何报名点!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.AU0001">
            <summary>
                当前审核状况无法审核!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MIMG001">
            <summary>
                系统未检索到相关的身份证电子照片!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MIMG002">
            <summary>
                系统未检索到相关的视频!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MIMG003">
            <summary>
                视频和您上传的证件照人脸对比失败!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MIMG004">
            <summary>
                认证绑定失败，请重新进入小程序!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MIMG005">
            <summary>
                当前微信暂未认证任何学员信息!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MIMG006">
            <summary>
                当前微信绑定的学员信息为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MIMG007">
            <summary>
                未找到相关报名点数据!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MIMG008">
            <summary>
                当前距离报名点的距离超过限制，请移步至相应报名点以后重新录制视频!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.MIMG009">
            <summary>
                绑定认证失败，请稍后重试!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.DTP0001">
            <summary>
                该报名点当前无效!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SL0001">
            <summary>
                活动名称重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SL0002">
            <summary>
                活动名称不能为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SL0003">
            <summary>
                当前两个活动无法叠加!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0001">
            <summary>
                报班的名称不能为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0002">
            <summary>
                报班的名称不能重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0003">
            <summary>
                同一个身份证培训只能录一次!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0004">
            <summary>
                同一个身份证 7 天只能录一次!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0005">
            <summary>
                已经审核通过的数据不能进行修改操作!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0006">
            <summary>
                审核不合格需要填写不合格的备注!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0007">
            <summary>
                非等待审核状态的教练无法再次审核!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0008">
            <summary>
                驾校全称重复，请检查全称!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0009">
            <summary>
                驾校简称重复，请检查全称!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0010">
            <summary>
                你没有添加教练的权限!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0011">
            <summary>
                身份证号码重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0012">
            <summary>
                审核不合格需要填写不合格的备注!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0013">
            <summary>
                当前核验状态无法进行提交操作，如有疑问，请联系驾协的工作人员!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0014">
            <summary>
                当前账户未绑定单位信息，无法进行相关操作!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0015">
            <summary>
                非理论教练必须要选择准教车型!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0016">
            <summary>
                实操教练必须要小于 60 岁!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNC0017">
            <summary>
                实操教练驾驶证必须要满 5 年!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNW0001">
            <summary>
                请填写工作单位!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNW0002">
            <summary>
                请输入准驾车型!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNW0003">
            <summary>
                请选择正确的工作开始日期!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNW0004">
            <summary>
                请选择正确的工作结束日期!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.UPI0001">
            <summary>
                该类型照片已经存在，无法覆盖，请先删除再上传!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.UPI0002">
            <summary>
                上传图片为空，请重新上传!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.UPI0003">
            <summary>
                请上传身份证的正面!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.UPI0004">
            <summary>
                请上传身份证的反面（国徽面）!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.UPI0005">
            <summary>
                上传的身份证号码和系统保存的号码不一致!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.UPI0006">
            <summary>
                上传的证件的名字和系统保存的名字不一致!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SRG0001">
            <summary>
                名册名称不能重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SRG0002">
            <summary>
                名册名称不能重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CNG0001">
            <summary>
                如果介绍人是必选，显示介绍人必须打钩!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SR0001">
            <summary>
                学员已经在名单中，请不要重复添加!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SR0002">
            <summary>
                学员不在名单中，无法删除!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.SR0003">
            <summary>
                学员未验证，无法添加到名册!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CST0001">
            <summary>
                费用类型错误，请设置相关的费用类型!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CST0002">
            <summary>
                费用类型名称不能为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CST0003">
            <summary>
                费用类型名称不能重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CST0004">
            <summary>
                请选择准驾车型!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CST0005">
            <summary>
                请选择准驾车型!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CST0006">
            <summary>
                培训报名点 和 培训场地 两个必选一个!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.USER0001">
            <summary>
                当前账户没有配置报名点!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.USER0002">
            <summary>
                当前账户没有配置培训场地!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.USER0003">
            <summary>
                身份证号码重复，如需注册请先删除以前的账号!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.USER0004">
            <summary>
                电话号码重复，如需注册请先删除以前的账号!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.USER0005">
            <summary>
                当前微信无法重复注册账号，如需注册请先删除以前的账号!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.USER0006">
            <summary>
                您有一条待审核的记录，无法重新注册账户!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.USER0007">
            <summary>
                当前电话关联多条账户，请选择要登录的账户!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.IDCARD0001">
            <summary>
                身份证号码错误!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAYBACK0001">
            <summary>
                没有找到相关的订单数据!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAYBACK0002">
            <summary>
                没有找到三方交易的订单数据!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAYBACK0003">
            <summary>
                三方订单数据库异常，请联系技术人员!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PAYBACK0004">
            <summary>
                该订单的支付方式不支持在线退款，请联系财务人员进行手工退款!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.FIELD0001">
            <summary>
                当前的训练场还有关联的报名场地，无法删除，请先删除相关联的报名场地!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.FIELD0002">
            <summary>
                请先设置训练场的定位才能设置关联的训练场的定位!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.FIELD0003">
            <summary>
                报名场地和所属的训练场的距离不能超过 5 公里!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.TEMP0001">
            <summary>
                请选择费用类型!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.TEMP0002">
            <summary>
                请填写正确的金额!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.TEMP0003">
            <summary>
                请选择您要复制的源模板!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.TEMP0004">
            <summary>
                目标模板为空，请刷新页面重试!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.TEMP0005">
            <summary>
                源模板不能和目标模板一致!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.TEMP0006">
            <summary>
                目标模板不为空无法复制!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.TEMP0007">
            <summary>
                目标模板不为空无法复制!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.COUPON0001">
            <summary>
                系统暂时不支持当前的优惠项目!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.COUPON0002">
            <summary>
                当前优惠券暂时无法使用!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.COUPON0003">
            <summary>
                当前优惠券已经过期!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.COUPON0004">
            <summary>
                无法获取学员编号!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.COUPON0005">
            <summary>
                当前用户无法使用该券!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.COUPON0006">
            <summary>
                当前券已经被其他订单锁定，无法使用!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.IMG000001">
            <summary>
                请先上传身份证的正面和身份证的反面!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.IMG000002">
            <summary>
                请拍照上传身份证的正面（人像面）!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.IMG000003">
            <summary>
                请拍照上传身份证的正面（国徽面）!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.IMG000004">
            <summary>
                系统只接受普通身份证拍照，也请不要翻拍!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.IMG000005">
            <summary>
                合同照片暂时只支持 10 页!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.IMG000006">
            <summary>
                请先上传合同拍照图片!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.PHONE0001">
            <summary>
                当前手机号码验证已经超时，请重新获取手机号码!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00001">
            <summary>
                您的信息还未审核，请等待工作人员审核资料!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00002">
            <summary>
                您的资料异常，请联系申报驾校修改更新资料!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00003">
            <summary>
                当前状态无法修改和上传任何资料!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00004">
            <summary>
                业务状态已经办结，无法修改和上传任何资料!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00005">
            <summary>
                无法重复分配教练证号!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00006">
            <summary>
                请重新设置最大值，当前最大值有重复!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00007">
            <summary>
                当前身份证号码无法查找到任务相关资料!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00008">
            <summary>
                名字核对失败，请检查名字!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00009">
            <summary>
                您的资料还未提交，等待驾校工作人员提交!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00010">
            <summary>
                当前还未分班无法登录，请等待系统分班!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00011">
            <summary>
                当前教练有缴费记录，无法重复缴费!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00012">
            <summary>
                系统未查询到缴费信息，请联系驾校工作人员!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00013">
            <summary>
                当前状态无法查看相关信息，请联系驾校工作人员!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00014">
            <summary>
                生成邀请码失败，请重试!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00015">
            <summary>
                请填写注册码!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00016">
            <summary>
                请填写有效注册码!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00017">
            <summary>
                当前注册码已经被使用!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.HNJX00018">
            <summary>
                当前注册码已经过期!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CON00001">
            <summary>
                请先上传的模板文件!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.CON00002">
            <summary>
                模板文件只支持 doc 或者 docx 格式的 Word 文件!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.EXL0001">
            <summary>
                名称重复，请重新设置!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.FACE0001">
            <summary>
                图像数据为空!
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCode.FACE0002">
            <summary>
                机器识别码为空!
            </summary>
        </member>
        <member name="T:PandaServer.Core.ErrorCodeEnum">
            <summary>
                通用错误码
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCodeEnum.A0000">
            <summary>
                系统异常
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCodeEnum.A0001">
            <summary>
                数据不存在
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCodeEnum.A0002">
            <summary>
                删除失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCodeEnum.A0003">
            <summary>
                操作失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.ErrorCodeEnum.A0004">
            <summary>
                没有权限
            </summary>
        </member>
        <member name="T:PandaServer.Core.FileExtensionEnum">
            <summary>
                文件扩展枚举
            </summary>
        </member>
        <member name="T:PandaServer.Core.FileLocation">
            <summary>
                文件存储位置
            </summary>
        </member>
        <member name="F:PandaServer.Core.FileLocation.ALIYUN">
            <summary>
                阿里云
            </summary>
        </member>
        <member name="F:PandaServer.Core.FileLocation.TENCENT">
            <summary>
                腾讯云
            </summary>
        </member>
        <member name="F:PandaServer.Core.FileLocation.MINIO">
            <summary>
                minio服务器
            </summary>
        </member>
        <member name="F:PandaServer.Core.FileLocation.LOCAL">
            <summary>
                本地
            </summary>
        </member>
        <member name="F:PandaServer.Core.FilterType.User">
            <summary>
                用户
            </summary>
        </member>
        <member name="F:PandaServer.Core.FilterType.Org">
            <summary>
                组织
            </summary>
        </member>
        <member name="T:PandaServer.Core.HnjxApplyTypeEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.HnjxQuestionTypeEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.HnjxCheckStatusEnum">
            <summary>
                档案审核的状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCheckStatusEnum.WaitSubmit">
            <summary>
                等待提交
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCheckStatusEnum.Wait">
            <summary>
                等待审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCheckStatusEnum.Qualified">
            <summary>
                合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCheckStatusEnum.Unqualified">
            <summary>
                不合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image0">
            <summary>
                寸照
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image100">
            <summary>
                其他
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image101">
            <summary>
                其他
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image10">
            <summary>
                聘用表
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image11">
            <summary>
                驾驶经历申明
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image12">
            <summary>
                身份证正面
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image13">
            <summary>
                身份证反面
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image19">
            <summary>
                驾驶证正本
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image20">
            <summary>
                驾驶证副本
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image30">
            <summary>
                原执教卡
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image50">
            <summary>
                安全驾驶记录情况一
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image51">
            <summary>
                安全驾驶记录情况二
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image52">
            <summary>
                安全驾驶记录情况三
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image61">
            <summary>
                变更服务单位申请表
            </summary>
        </member>
        <member name="F:PandaServer.Core.HnjxCoachImageIdEnum.image62">
            <summary>
                变更服务单位申请表
            </summary>
        </member>
        <member name="T:PandaServer.Core.HttpStatusCode">
            <summary>
                HTTP状态码
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.Continue">
            <summary>
                客户端可能继续其请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.SwitchingProtocols">
            <summary>
                正在更改协议版本或协议
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.OK">
            <summary>
                请求成功，且请求的信息包含在响应中
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.Created">
            <summary>
                请求导致在响应被发送前创建新资源
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.Accepted">
            <summary>
                请求已被接受做进一步处理
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.NonAuthoritativeInformation">
            <summary>
                返回的元信息来自缓存副本而不是原始服务器，因此可能不正确
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.NoContent">
            <summary>
                已成功处理请求并且响应已被设定为无内容
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.ResetContent">
            <summary>
                客户端应重置（或重新加载）当前资源
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.PartialContent">
            <summary>
                响应是包括字节范围的 GET请求所请求的部分响应
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.MultipleChoices">
            <summary>
                请求的信息有多种表示形式，默认操作是将此状态视为重定向
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.MovedPermanently">
            <summary>
                请求的信息已移到 Location头中指定的 URI 处
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.Found">
            <summary>
                请求的信息位于 Location 头中指定的 URI 处
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.SeeOther">
            <summary>
                将客户端自动重定向到 Location 头中指定的 URI
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.NotModified">
            <summary>
                客户端的缓存副本是最新的
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.UseProxy">
            <summary>
                请求应使用位于 Location 头中指定的 URI 的代理服务器
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.BadRequest">
            <summary>
                服务器未能识别请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.Unauthorized">
            <summary>
                请求的资源要求身份验证
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.PaymentRequired">
            <summary>
                需要付费
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.Forbidden">
            <summary>
                服务器拒绝满足请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.NotFound">
            <summary>
                请求的资源不在服务器上
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.MethodNotAllowed">
            <summary>
                请求的资源上不允许请求方法（POST或 GET）
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.NotAcceptable">
            <summary>
                客户端已用 Accept 头指示将不接受资源的任何可用表示形式
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.ProxyAuthenticationRequired">
            <summary>
                请求的代理要求身份验证
                Proxy-authenticate 头包含如何执行身份验证的详细信息
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.RequestTimeout">
            <summary>
                客户端没有在服务器期望请求的时间内发送请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.Conflict">
            <summary>
                由于服务器上的冲突而未能执行请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.Gone">
            <summary>
                请求的资源不再可用
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.LengthRequired">
            <summary>
                缺少必需的 Content-length
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.PreconditionFailed">
            <summary>
                为此请求设置的条件失败，且无法执行此请求
                条件是用条件请求标头（如 If-Match、If-None-Match 或 If-Unmodified-Since）设置的。
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.RequestEntityTooLarge">
            <summary>
                请求太大，服务器无法处理
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.RequestUriTooLong">
            <summary>
                URI 太长
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.UnsupportedMediaType">
            <summary>
                请求是不支持的类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.RequestedRangeNotSatisfiable">
            <summary>
                无法返回从资源请求的数据范围，因为范围的开头在资源的开头之前，或因为范围的结尾在资源的结尾之后
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.ExpectationFailed">
            <summary>
                服务器未能符合Expect头中给定的预期值
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.UpgradeRequired">
            <summary>
                服务器拒绝处理客户端使用当前协议发送的请求，但是可以接受其使用升级后的协议发送的请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.InternalServerError">
            <summary>
                服务器上发生了一般错误
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.NotImplemented">
            <summary>
                服务器不支持请求的函数
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.BadGateway">
            <summary>
                中间代理服务器从另一代理或原始服务器接收到错误响应
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.ServiceUnavailable">
            <summary>
                服务器暂时不可用，通常是由于过多加载或维护
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.GatewayTimeout">
            <summary>
                中间代理服务器在等待来自另一个代理或原始服务器的响应时已超时
            </summary>
        </member>
        <member name="F:PandaServer.Core.HttpStatusCode.HttpVersionNotSupported">
            <summary>
                服务器不支持请求的HTTP版本
            </summary>
        </member>
        <member name="T:PandaServer.Core.JxStudentStatusEnum">
            <summary>
                学员 状态的 枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.WaitStudy">
            <summary>
                待培
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.OnStudy">
            <summary>
                在培
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.Graduate">
            <summary>
                毕业
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.Quit">
            <summary>
                退学
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.Out">
            <summary>
                转出
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.BeGraduate">
            <summary>
                结业
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.OverFive">
            <summary>
                考爆
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.Expired">
            <summary>
                过期
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.EmptyNumber">
            <summary>
                空号
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.Refund">
            <summary>
                退款
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.LogOut">
            <summary>
                注销
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.ApplyQuit">
            <summary>
                申退
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentStatusEnum.RunOut">
            <summary>
                跑单
            </summary>
        </member>
        <member name="T:PandaServer.Core.JxExamResultEnum">
            <summary>
                学员 考试成绩 的枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamResultEnum.NoExam">
            <summary>
                未考
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamResultEnum.Pass">
            <summary>
                合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamResultEnum.NoPass">
            <summary>
                不合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamResultEnum.Absent">
            <summary>
                缺考
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamResultEnum.Error">
            <summary>
                仪器故障
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamResultEnum.Cancel">
            <summary>
                取消
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamResultEnum.WaitExam">
            <summary>
                待考
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamKeMuEnum.KeMu1">
            <summary>
                科目一
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamKeMuEnum.KeMu2">
            <summary>
                科目二
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamKeMuEnum.KeMu3">
            <summary>
                科目三
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamKeMuEnum.KeMu4">
            <summary>
                科目四
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamKeMuEnum.KeMu9">
            <summary>
                科目二三
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamKeMuEnum.KeMu20">
            <summary>
                科目二
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxExamKeMuEnum.KeMu30">
            <summary>
                科目三
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.BaoMingJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.KeYiHeGeLiJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.KeTwoHeGeLiJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.KeSanHeGeLiJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.KeSiHeGeLiJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.XueFeiJiaoQingJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.ShouLiJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.KeTwoWuCiBuHeGeJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.KeTwoBuHeGeJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.KeSanWuCiBuHeGeJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.KeSanBuHeGeJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxWagesKeMuEnum.ZongHeJieSuan">
            <summary>
            
            </summary>
        </member>
        <member name="T:PandaServer.Core.JxWagesType">
            <summary>
            工资的明细里面的结算类型
            </summary>
        </member>
        <member name="T:PandaServer.Core.JxStudentImageEnum">
            <summary>
                照片 类型的 枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image0">
            <summary>
                寸照
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image1">
            <summary>
                身份证电子照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image2">
            <summary>
                现场拍照
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image8">
            <summary>
                数码照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image4">
            <summary>
                身份证明正反面
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image14">
            <summary>
                身份证复印件
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image40">
            <summary>
                身份证正面
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image41">
            <summary>
                身份证反面
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image6">
            <summary>
                驾驶证申请表
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image7">
            <summary>
                体检表
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image5">
            <summary>
                暂住证
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image9">
            <summary>
                申请大中型客货车驾驶证业务告知书
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image10">
            <summary>
                体检头像
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image11">
            <summary>
                体检上肢
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image20">
            <summary>
                原驾驶证照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image21">
            <summary>
                其他资料《一》
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image22">
            <summary>
                其他资料《一》
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image23">
            <summary>
                其他资料《三》
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image110">
            <summary>
                人脸库预留照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image200">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image201">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image202">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image203">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image204">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image205">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image206">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image207">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image208">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image209">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image210">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image220">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image300">
            <summary>
                保险理赔授权书
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image400">
            <summary>
                学生证
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentImageEnum.image500">
            <summary>
                人脸识别
            </summary>
        </member>
        <member name="T:PandaServer.Core.JxStudentDocSatusEnum">
            <summary>
                学员审核的状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentDocSatusEnum.Wait">
            <summary>
                等待审核
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentDocSatusEnum.Success">
            <summary>
                审核通过
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxStudentDocSatusEnum.Failure">
            <summary>
                审核失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image0">
            <summary>
                营业执照
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image1">
            <summary>
                道路运输许可证
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image11">
            <summary>
                法人身份证正面
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image12">
            <summary>
                法人身份证反面
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image21">
            <summary>
                负责人身份证正面
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image22">
            <summary>
                负责人身份证反面
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image31">
            <summary>
                联络人身份证正面
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image32">
            <summary>
                联络人身份证反面
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image50">
            <summary>
                门头照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image51">
            <summary>
                场地照片
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image100">
            <summary>
                公章签章
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxCompanyImageEnum.image101">
            <summary>
                法人签字
            </summary>
        </member>
        <member name="T:PandaServer.Core.JxAddCostTypeScenes">
            <summary>
                挂账 场景的枚举
            </summary>
        </member>
        <member name="T:PandaServer.Core.NoticeStatus">
            <summary>
                通知公告状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.NoticeStatus.DRAFT">
            <summary>
                草稿
            </summary>
        </member>
        <member name="F:PandaServer.Core.NoticeStatus.PUBLIC">
            <summary>
                发布
            </summary>
        </member>
        <member name="F:PandaServer.Core.NoticeStatus.CANCEL">
            <summary>
                撤回
            </summary>
        </member>
        <member name="F:PandaServer.Core.NoticeStatus.DELETED">
            <summary>
                删除
            </summary>
        </member>
        <member name="F:PandaServer.Core.NoticeType.NOTICE">
            <summary>
                通知
            </summary>
        </member>
        <member name="F:PandaServer.Core.NoticeType.ANNOUNCEMENT">
            <summary>
                公告
            </summary>
        </member>
        <member name="T:PandaServer.Core.NoticeUserStatus">
            <summary>
                通知公告用户状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.NoticeUserStatus.UNREAD">
            <summary>
                未读
            </summary>
        </member>
        <member name="F:PandaServer.Core.NoticeUserStatus.READ">
            <summary>
                已读
            </summary>
        </member>
        <member name="T:PandaServer.Core.OrderCarStatusEnum">
            <summary>
                约车的状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.OrderCarStatusEnum.WaitConfirm">
            <summary>
                等待确认
            </summary>
        </member>
        <member name="F:PandaServer.Core.OrderCarStatusEnum.Success">
            <summary>
                预约成功
            </summary>
        </member>
        <member name="F:PandaServer.Core.OrderCarStatusEnum.TrainingComplete">
            <summary>
                训练完成
            </summary>
        </member>
        <member name="F:PandaServer.Core.OrderCarStatusEnum.TrainingAbsence">
            <summary>
                学员缺训
            </summary>
        </member>
        <member name="F:PandaServer.Core.OrderCarStatusEnum.Canceled">
            <summary>
                预约取消
            </summary>
        </member>
        <member name="F:PandaServer.Core.OrderCarStatusEnum.Failed">
            <summary>
                预约失败
            </summary>
        </member>
        <member name="T:PandaServer.Core.CostTypeOuTableEnum">
            <summary>
                关联字段枚举
            </summary>
        </member>
        <member name="T:PandaServer.Core.PayMethodEnum">
            <summary>
                交易方式  交通通道的 枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.PayMethodEnum.FuBei">
            <summary>
                付呗支付
            </summary>
        </member>
        <member name="F:PandaServer.Core.PayMethodEnum.JlPay">
            <summary>
                嘉联支付
            </summary>
        </member>
        <member name="T:PandaServer.Core.PayOutTableEnum">
            <summary>
                交易的  外部关联表 枚举
            </summary>
        </member>
        <member name="T:PandaServer.Core.PayChannelEnum">
            <summary>
                交易  支付通道
            </summary>
        </member>
        <member name="F:PandaServer.Core.PayChannelEnum.NoPay">
            <summary>
                未付款
            </summary>
        </member>
        <member name="F:PandaServer.Core.PayChannelEnum.Cash">
            <summary>
                现金
            </summary>
        </member>
        <member name="F:PandaServer.Core.PayChannelEnum.WxPay">
            <summary>
                微信
            </summary>
        </member>
        <member name="F:PandaServer.Core.PayChannelEnum.AliPay">
            <summary>
                支付宝
            </summary>
        </member>
        <member name="F:PandaServer.Core.PayChannelEnum.UnionPay">
            <summary>
                银联
            </summary>
        </member>
        <member name="F:PandaServer.Core.PayChannelEnum.Unknown">
            <summary>
                未知
            </summary>
        </member>
        <member name="T:PandaServer.Core.OrderStatusEnum">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.PlanOrderStatusEnum">
            <summary>
                考场预约状态
            </summary>
        </member>
        <member name="F:PandaServer.Core.PlanOrderStatusEnum.Success">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.PlanOrderStatusEnum.Cancel">
            <summary>
            </summary>
        </member>
        <member name="F:PandaServer.Core.PlanOrderStatusEnum.Fail">
            <summary>
            </summary>
        </member>
        <member name="T:PandaServer.Core.QueryTypeEnum">
            <summary>
                查询类型的枚举
            </summary>
        </member>
        <member name="F:PandaServer.Core.QueryTypeEnum.eq">
            <summary>
                等于
            </summary>
        </member>
        <member name="F:PandaServer.Core.QueryTypeEnum.like">
            <summary>
                模糊
            </summary>
        </member>
        <member name="F:PandaServer.Core.QueryTypeEnum.gt">
            <summary>
                大于
            </summary>
        </member>
        <member name="F:PandaServer.Core.QueryTypeEnum.lt">
            <summary>
                小于
            </summary>
        </member>
        <member name="F:PandaServer.Core.QueryTypeEnum.ne">
            <summary>
                不等于
            </summary>
        </member>
        <member name="F:PandaServer.Core.QueryTypeEnum.ge">
            <summary>
                大于等于
            </summary>
        </member>
        <member name="F:PandaServer.Core.QueryTypeEnum.le">
            <summary>
                小于等于
            </summary>
        </member>
        <member name="F:PandaServer.Core.QueryTypeEnum.isNotNull">
            <summary>
                不为空
            </summary>
        </member>
        <member name="T:PandaServer.Core.RequestTypeEnum">
            <summary>
                http请求类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.RequestTypeEnum.Run">
            <summary>
                执行内部方法
            </summary>
        </member>
        <member name="F:PandaServer.Core.RequestTypeEnum.Get">
            <summary>
                GET请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.RequestTypeEnum.Post">
            <summary>
                POST请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.RequestTypeEnum.Put">
            <summary>
                PUT请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.RequestTypeEnum.Delete">
            <summary>
                DELETE请求
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Register">
            <summary>
            报名
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.FirstPayment">
            <summary>
            第一次缴费
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.SecondPayment">
            <summary>
            第二次缴费
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject1FirstExam">
            <summary>
            科目一初考约考成功
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject1MakeupExam">
            <summary>
            科目一补考约考成功
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject1Pass">
            <summary>
            科目一合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject1Fail">
            <summary>
            科目一不合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject2FirstExam">
            <summary>
            科目二初考约考成功
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject2MakeupExam">
            <summary>
            科目二补考约考成功
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject2Pass">
            <summary>
            科目二合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject2Fail">
            <summary>
            科目二不合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject3FirstExam">
            <summary>
            科目三初考约考成功
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject3MakeupExam">
            <summary>
            科目三补考约考成功
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject3Pass">
            <summary>
            科目三合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.Enum.SMSSceneEnum.Subject3Fail">
            <summary>
            科目三不合格
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffTypeEnum.Coach">
            <summary>
                正常
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffTypeEnum.ContactPerson">
            <summary>
                联络员
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffTypeEnum.Office">
            <summary>
                办公室文员
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffTypeEnum.Manager">
            <summary>
                管理人员
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffTypeEnum.Others">
            <summary>
                其他人员
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image0">
            <summary>
                寸照
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image10">
            <summary>
                聘用表
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image11">
            <summary>
                驾驶经历申明
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image12">
            <summary>
                身份证正面
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image13">
            <summary>
                身份证反面
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image19">
            <summary>
                驾驶证正本
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image20">
            <summary>
                驾驶证副本
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image30">
            <summary>
                执教卡
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image50">
            <summary>
                安全驾驶记录情况一
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image51">
            <summary>
                安全驾驶记录情况二
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image52">
            <summary>
                安全驾驶记录情况三
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image62">
            <summary>
                变更服务单位申请表
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image70">
            <summary>
                服务监督卡
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image100">
            <summary>
                其他
            </summary>
        </member>
        <member name="F:PandaServer.Core.StaffImageEnum.image101">
            <summary>
                其他
            </summary>
        </member>
        <member name="F:PandaServer.Core.EventTypeEnum.CoachLogOnFailure">
            <summary>
                教练登签
            </summary>
        </member>
        <member name="F:PandaServer.Core.EventTypeEnum.CoachLogOnSuccess">
            <summary>
                教练登签
            </summary>
        </member>
        <member name="F:PandaServer.Core.EventTypeEnum.StudentLogOnFailure">
            <summary>
                学员登签失败
            </summary>
        </member>
        <member name="F:PandaServer.Core.EventTypeEnum.StudentLogOnSuccess">
            <summary>
                学员登签成功
            </summary>
        </member>
        <member name="F:PandaServer.Core.EventTypeEnum.DailyTrainSuccess">
            <summary>
                日常训练
            </summary>
        </member>
        <member name="F:PandaServer.Core.EventTypeEnum.CoachLogOut">
            <summary>
                教练签退
            </summary>
        </member>
        <member name="F:PandaServer.Core.EventTypeEnum.StudentLogOut">
            <summary>
                学员签退
            </summary>
        </member>
        <member name="F:PandaServer.Core.EventTypeEnum.HeartMove">
            <summary>
                心跳回报
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDeviceTypeEnum.CarStudyDevice">
            <summary>
                车载计时 设备
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDeviceTypeEnum.DoorDevice">
            <summary>
                闸机 设备
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDeviceTypeEnum.ExamSiteDoorDevice">
            <summary>
                考场身份闸机 设备
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDeviceTypeEnum.ExamSiteCashierDevice">
            <summary>
                考场销售设备
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDeviceTypeEnum.ExamSiteCarCamera">
            <summary>
                考场车辆摄像头
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDeviceTypeEnum.ExamSiteStudyDoorDevice">
            <summary>
                考场训练闸机 设备
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDeviceTypeEnum.ExamSiteBuyStudyDoorDevice">
            <summary>
                考场训练闸机 设备
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDeviceTypeEnum.ExamSiteSelfCommentDevice">
            <summary>
                长沙自助点评设备
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDoorDirectionEnum.NoDirection">
            <summary>
                无方向
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDoorDirectionEnum.Entrance">
            <summary>
                无方向
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDoorDirectionEnum.Exit">
            <summary>
                无方向
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDoorBrandModelEnum.S8000">
            <summary>
                无方向
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDoorBrandModelEnum.FuLiTuo">
            <summary>
                富利拓闸机
            </summary>
        </member>
        <member name="F:PandaServer.Core.JxDoorBrandModelEnum.LuoLa">
            <summary>
                罗拉闸机
            </summary>
        </member>
        <member name="T:PandaServer.Core.TenantTypeEnum">
            <summary>
                公司类型
            </summary>
        </member>
        <member name="T:PandaServer.Core.Extend.Extensions">
            <summary>
                string扩展类
            </summary>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.CastSuper``1(System.Collections.IEnumerable)">
            <summary>
                强制转换类型
            </summary>
            <typeparam name="TResult"></typeparam>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToGuid(System.Object)">
            <summary>
                将string转换为Guid，若转换失败，则返回Guid.Empty。不抛出异常。
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToList``1(System.Object)">
            <summary>
                将string转换为Guid，若转换失败，则返回Guid.Empty。不抛出异常。
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.SafeValue``1(System.Nullable{``0})">
            <summary>
                安全返回值
            </summary>
            <param name="value">可空值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToUrlParams(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
                字典类型转URL参数字符串
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToImage(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
                IFormFile 转成 图片
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToBytes(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
                IFormFile 转成 图片
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToBytes(SixLabors.ImageSharp.Image)">
            <summary>
                Image 转成 数组
            </summary>
            <param name="image"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToImage(System.String)">
            <summary>
                Base64 转  Image
            </summary>
            <param name="img"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToInt(System.Object)">
            <summary>
                转换为整型
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToLong(System.Object)">
            <summary>
                将object转换为long，若转换失败，则返回0。不抛出异常。
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToIntOrNull(System.Object)">
            <summary>
                转换为可空整型
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDouble(System.Object)">
            <summary>
                转换为双精度浮点数
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDouble(System.Object,System.Int32)">
            <summary>
                转换为双精度浮点数,并按指定的小数位4舍5入
            </summary>
            <param name="data">数据</param>
            <param name="digits">小数位数</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDoubleOrNull(System.Object)">
            <summary>
                转换为可空双精度浮点数
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDecimal(System.Object)">
            <summary>
                转换为高精度浮点数
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDecimal(System.Object,System.Int32)">
            <summary>
                转换为高精度浮点数,并按指定的小数位4舍5入
            </summary>
            <param name="data">数据</param>
            <param name="digits">小数位数</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDecimalOrNull(System.Object)">
            <summary>
                转换为可空高精度浮点数
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDecimalOrNull(System.Object,System.Int32)">
            <summary>
                转换为可空高精度浮点数,并按指定的小数位4舍5入
            </summary>
            <param name="data">数据</param>
            <param name="digits">小数位数</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDate(System.Object)">
            <summary>
                转换为日期
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDateOrNull(System.Object)">
            <summary>
                转换为可空日期
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToBool(System.Object)">
            <summary>
                转换为布尔值
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetBool(System.Object)">
            <summary>
                获取布尔值
            </summary>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToBoolOrNull(System.Object)">
            <summary>
                转换为可空布尔值
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.IsEmpty(System.String)">
            <summary>
                是否为空
            </summary>
            <param name="value">值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.IsEmpty(System.Object)">
            <summary>
                是否为空
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.IsEmpty(System.Nullable{System.Guid})">
            <summary>
                是否为空
            </summary>
            <param name="value">值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.IsEmpty(System.Guid)">
            <summary>
                是否为空
            </summary>
            <param name="value">值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToLong(System.Object)">
            <summary>
                将object转换为long，若转换失败，则返回0。不抛出异常。
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToLong(System.String,System.Int64)">
            <summary>
                将object转换为long，若转换失败，则返回指定值。不抛出异常。
            </summary>
            <param name="str"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToInt(System.Object)">
            <summary>
                将object转换为int，若转换失败，则返回0。不抛出异常。
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToIntArray(System.Object[])">
            <summary>
                转换成  整形数组
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToInt(System.Object,System.Int32)">
            <summary>
                将object转换为int，若转换失败，则返回指定值。不抛出异常。
                null返回默认值
            </summary>
            <param name="str"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToShort(System.Object)">
            <summary>
                将object转换为short，若转换失败，则返回0。不抛出异常。
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToShort(System.Object,System.Int16)">
            <summary>
                将object转换为short，若转换失败，则返回指定值。不抛出异常。
            </summary>
            <param name="str"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToDecimal(System.Object,System.Decimal)">
            <summary>
                将object转换为demical，若转换失败，则返回指定值。不抛出异常。
            </summary>
            <param name="str"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.IsDecimal(System.Object)">
            <summary>
                判断是否是  Decimal 类型
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToDecimal(System.Object)">
            <summary>
                将object转换为demical，若转换失败，则返回0。不抛出异常。
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToBool(System.Object)">
            <summary>
                将object转换为bool，若转换失败，则返回false。不抛出异常。
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToBool(System.Object,System.Boolean)">
            <summary>
                将object转换为bool，若转换失败，则返回指定值。不抛出异常。
            </summary>
            <param name="str"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToFloat(System.Object)">
            <summary>
                将object转换为float，若转换失败，则返回0。不抛出异常。
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToFloat(System.Object,System.Single)">
            <summary>
                将object转换为float，若转换失败，则返回指定值。不抛出异常。
            </summary>
            <param name="str"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToDateTime(System.Object)">
            <summary>
                将string转换为DateTime，若转换失败，则返回日期最小值。不抛出异常。
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToDateTime(System.String,System.Nullable{System.DateTime})">
            <summary>
                将string转换为DateTime，若转换失败，则返回默认值。
            </summary>
            <param name="str"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToString(System.Object)">
            <summary>
                将object转换为string，若转换失败，则返回""。不抛出异常。
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToDouble(System.Object)">
            <summary>
                将object转换为double，若转换失败，则返回0。不抛出异常。
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ParseToDouble(System.Object,System.Double)">
            <summary>
                将object转换为double，若转换失败，则返回指定值。不抛出异常。
            </summary>
            <param name="str"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetValueOrDefault(System.String,System.String)">
            <summary>
            获取可空字符串的值，如果为null则返回默认值
            </summary>
            <param name="str">可空字符串</param>
            <param name="defaultValue">默认值（可选）</param>
            <returns>处理后的字符串</returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetValueOrDefault(System.String,System.String[],System.String)">
            <summary>
            安全地获取字符串的值，支持自定义空值判断条件
            </summary>
            <param name="str">源字符串</param>
            <param name="emptyValues">被视为空的值（可选）</param>
            <param name="defaultValue">默认值（可选）</param>
            <returns>处理后的字符串</returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetValueOrDefault(System.String,System.Func{System.String,System.String},System.String)">
            <summary>
            安全地获取字符串的值，支持自定义处理函数
            </summary>
            <param name="str">源字符串</param>
            <param name="processFunc">自定义处理函数</param>
            <param name="defaultValue">默认值（可选）</param>
            <returns>处理后的字符串</returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetValueOrDefault(System.String,System.Int32,System.String)">
            <summary>
            获取指定长度的字符串，超出部分将被截断
            </summary>
            <param name="str">源字符串</param>
            <param name="maxLength">最大长度</param>
            <param name="defaultValue">默认值（可选）</param>
            <returns>处理后的字符串</returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetValueOrDefault``1(System.String,``0)">
            <summary>
            将字符串转换为指定类型的值，转换失败时返回默认值
            </summary>
            <typeparam name="T">目标类型</typeparam>
            <param name="str">源字符串</param>
            <param name="defaultValue">默认值</param>
            <returns>转换后的值</returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDateTimeString(System.DateTime,System.Boolean)">
            <summary>
                获取格式化字符串，带时分秒，格式："yyyy-MM-dd HH:mm:ss"
            </summary>
            <param name="dateTime">日期</param>
            <param name="isRemoveSecond">是否移除秒</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDateTimeString(System.Nullable{System.DateTime},System.Boolean)">
            <summary>
                获取格式化字符串，带时分秒，格式："yyyy-MM-dd HH:mm:ss"
            </summary>
            <param name="dateTime">日期</param>
            <param name="isRemoveSecond">是否移除秒</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDateString(System.DateTime)">
            <summary>
                获取格式化字符串，不带时分秒，格式："yyyy-MM-dd"
            </summary>
            <param name="dateTime">日期</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDateString">
            <summary>
                获取格式化字符串，不带时分秒，格式："yyyy-MM-dd"
            </summary>
            <param name="dateTime">日期</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDateString(System.Nullable{System.DateTime})">
            <summary>
                获取格式化字符串，不带时分秒，格式："yyyy-MM-dd"
            </summary>
            <param name="dateTime">日期</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToTimeString(System.DateTime)">
            <summary>
                获取格式化字符串，不带年月日，格式："HH:mm:ss"
            </summary>
            <param name="dateTime">日期</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToTimeString(System.Nullable{System.DateTime})">
            <summary>
                获取格式化字符串，不带年月日，格式："HH:mm:ss"
            </summary>
            <param name="dateTime">日期</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToMillisecondString(System.DateTime)">
            <summary>
                获取格式化字符串，带毫秒，格式："yyyy-MM-dd HH:mm:ss.fff"
            </summary>
            <param name="dateTime">日期</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToMillisecondString(System.Nullable{System.DateTime})">
            <summary>
                获取格式化字符串，带毫秒，格式："yyyy-MM-dd HH:mm:ss.fff"
            </summary>
            <param name="dateTime">日期</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToChineseDateString(System.DateTime)">
            <summary>
                获取格式化字符串，不带时分秒，格式："yyyy年MM月dd日"
            </summary>
            <param name="dateTime">日期</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToChineseDateString(System.Nullable{System.DateTime})">
            <summary>
                获取格式化字符串，不带时分秒，格式："yyyy年MM月dd日"
            </summary>
            <param name="dateTime">日期</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToChineseDateTimeString(System.DateTime,System.Boolean)">
            <summary>
                获取格式化字符串，带时分秒，格式："yyyy年MM月dd日 HH时mm分"
            </summary>
            <param name="dateTime">日期</param>
            <param name="isRemoveSecond">是否移除秒</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToChineseDateTimeString(System.Nullable{System.DateTime},System.Boolean)">
            <summary>
                获取格式化字符串，带时分秒，格式："yyyy年MM月dd日 HH时mm分"
            </summary>
            <param name="dateTime">日期</param>
            <param name="isRemoveSecond">是否移除秒</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.FormatTime(System.Int64)">
            <summary>
                毫秒转天时分秒
            </summary>
            <param name="ms"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetDescription(System.Enum)">
            <summary>
                获取枚举值对应的描述
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetEnumDescription(System.Enum)">
            <summary>
                获取枚举值对应的描述
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.EnumToDictionary(System.Type)">
            <summary>
                转成dictionary类型
            </summary>
            <param name="enumType"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.EnumToDictionaryString(System.Type)">
            <summary>
                枚举成员转成键值对Json字符串
            </summary>
            <param name="enumType"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.ToDescription(System.Enum)">
            <summary>
                枚举
            </summary>
            <param name="enumType"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.Description(System.Boolean)">
            <summary>
                获取描述
            </summary>
            <param name="value">布尔值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.Description(System.Nullable{System.Boolean})">
            <summary>
                获取描述
            </summary>
            <param name="value">布尔值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.Format(System.Int32,System.String)">
            <summary>
                获取格式化字符串
            </summary>
            <param name="number">数值</param>
            <param name="defaultValue">空值显示的默认文本</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.Format(System.Nullable{System.Int32},System.String)">
            <summary>
                获取格式化字符串
            </summary>
            <param name="number">数值</param>
            <param name="defaultValue">空值显示的默认文本</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.Format(System.Decimal,System.String)">
            <summary>
                获取格式化字符串
            </summary>
            <param name="number">数值</param>
            <param name="defaultValue">空值显示的默认文本</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.Format(System.Nullable{System.Decimal},System.String)">
            <summary>
                获取格式化字符串
            </summary>
            <param name="number">数值</param>
            <param name="defaultValue">空值显示的默认文本</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.Format(System.Double,System.String)">
            <summary>
                获取格式化字符串
            </summary>
            <param name="number">数值</param>
            <param name="defaultValue">空值显示的默认文本</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.Format(System.Nullable{System.Double},System.String)">
            <summary>
                获取格式化字符串
            </summary>
            <param name="number">数值</param>
            <param name="defaultValue">空值显示的默认文本</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.FormatRmb(System.Decimal)">
            <summary>
                获取格式化字符串,带￥
            </summary>
            <param name="number">数值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.FormatRmb(System.Nullable{System.Decimal})">
            <summary>
                获取格式化字符串,带￥
            </summary>
            <param name="number">数值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.FormatPercent(System.Decimal)">
            <summary>
                获取格式化字符串,带%
            </summary>
            <param name="number">数值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.FormatPercent(System.Nullable{System.Decimal})">
            <summary>
                获取格式化字符串,带%
            </summary>
            <param name="number">数值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.FormatPercent(System.Double)">
            <summary>
                获取格式化字符串,带%
            </summary>
            <param name="number">数值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.FormatPercent(System.Nullable{System.Double})">
            <summary>
                获取格式化字符串,带%
            </summary>
            <param name="number">数值</param>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetPage``1(System.Collections.Generic.List{``0},System.Int32,System.Int32,System.Int32@)">
            <summary>
                获取表里某页的数据
            </summary>
            <param name="data">表数据</param>
            <param name="pageIndex">当前页</param>
            <param name="pageSize">分页大小</param>
            <param name="allPage">返回总页数</param>
            <returns>返回当页表数据</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:PandaServer.Core.Extend.Extensions.IListToList``1(System.Collections.IList)" -->
        <member name="M:PandaServer.Core.Extend.Extensions.removeNull(System.Collections.Generic.List{System.String})">
            <summary>
                去除空元素
            </summary>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.MapTo``1(System.Object)">
            <summary>
                类型映射
            </summary>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.MapToList``1(System.Collections.IEnumerable)">
            <summary>
                集合列表类型映射
            </summary>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.MapToList``2(System.Collections.Generic.IEnumerable{``0})">
            <summary>
                集合列表类型映射
            </summary>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.MapTo``2(``0,``1)">
            <summary>
                类型映射
            </summary>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.Substring(System.String,System.String,System.Boolean)">
            <summary>
                从分隔符开始向尾部截取字符串
            </summary>
            <param name="this">源字符串</param>
            <param name="separator">分隔符</param>
            <param name="lastIndexOf">true：从最后一个匹配的分隔符开始截取，false：从第一个匹配的分隔符开始截取，默认：true</param>
            <returns>string</returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetPage(System.Data.DataTable,System.Int32,System.Int32,System.Int32@)">
            <summary>
                获取表里某页的数据
            </summary>
            <param name="data">表数据</param>
            <param name="pageIndex">当前页</param>
            <param name="pageSize">分页大小</param>
            <param name="allPage">返回总页数</param>
            <returns>返回当页表数据</returns>
        </member>
        <member name="M:PandaServer.Core.Extend.Extensions.GetDataFilter(System.Data.DataTable,System.String)">
            <summary>
                根据字段过滤表的内容
            </summary>
            <param name="data">表数据</param>
            <param name="condition">条件</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Extend.ExtLinq.GenerateLambda(System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression)">
            <summary>
                创建完整的lambda
            </summary>
        </member>
        <member name="M:PandaServer.Core.Extend.ExtLinq.ParameterRebinder.#ctor(System.Collections.Generic.Dictionary{System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.ParameterExpression})">
            <summary>
                Initializes a new instance of the <see cref="T:PandaServer.Core.Extend.ExtLinq.ParameterRebinder" /> class.
            </summary>
            <param name="map">The map.</param>
        </member>
        <member name="M:PandaServer.Core.Extend.ExtLinq.ParameterRebinder.ReplaceParameters(System.Collections.Generic.Dictionary{System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.Expression)">
            <summary>
                Replaces the parameters.
            </summary>
            <param name="map">The map.</param>
            <param name="exp">The exp.</param>
            <returns>Expression</returns>
        </member>
        <member name="T:PandaServer.Core.Extension.LinqExtension">
            <summary>
                Linq扩展
            </summary>
        </member>
        <member name="M:PandaServer.Core.Extension.LinqExtension.ContainsAll``1(System.Collections.Generic.List{``0},System.Collections.Generic.List{``0})">
            <summary>
                是否都包含
            </summary>
            <typeparam name="T"></typeparam>
            <param name="first">第一个列表</param>
            <param name="secend">第二个列表</param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.ObjectExtension">
            <summary>
                object拓展
            </summary>
        </member>
        <member name="M:PandaServer.Core.ObjectExtension.ToObject(System.String)">
            <summary>
                json字符串序列化
            </summary>
            <param name="json"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.ObjectExtension.ToObject``1(System.String)">
            <summary>
                json字符串序列化
            </summary>
            <typeparam name="T"></typeparam>
            <param name="json"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.ObjectExtension.ToJObject(System.String)">
            <summary>
                json字符串序列化
            </summary>
            <param name="json"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.BaseOptions">
            <summary>
                默认业务配置
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseOptions.InitTable">
            <summary>
                初始化表
            </summary>
        </member>
        <member name="P:PandaServer.Core.BaseOptions.InitSeedData">
            <summary>
                初始化数据
            </summary>
        </member>
        <member name="T:PandaServer.Core.LoggingSetting">
            <summary>
                日志配置
            </summary>
        </member>
        <member name="P:PandaServer.Core.LoggingSetting.Monitor">
            <summary>
                LoggingMonitor配置
            </summary>
        </member>
        <member name="P:PandaServer.Core.LoggingSetting.MessageFormat">
            <summary>
                是否日志消息格式化
            </summary>
        </member>
        <member name="P:PandaServer.Core.LoggingSetting.LogLevel">
            <summary>
                日志等级
            </summary>
        </member>
        <member name="T:PandaServer.Core.LoggingSetting.MonitorOptions">
            <summary>
                LoggingMonitor配置
            </summary>
        </member>
        <member name="P:PandaServer.Core.LoggingSetting.MonitorOptions.Write">
            <summary>
                写入文件
            </summary>
        </member>
        <member name="P:PandaServer.Core.LoggingSetting.MonitorOptions.Console">
            <summary>
                写入控制台
            </summary>
        </member>
        <member name="T:PandaServer.Core.LoggingSetting.LogLevelOptions">
            <summary>
                日志等级
            </summary>
        </member>
        <member name="P:PandaServer.Core.LoggingSetting.LogLevelOptions.Default">
            <summary>
                默认日志等级
            </summary>
        </member>
        <member name="P:PandaServer.Core.LoggingSetting.LogLevelOptions.MaxLevel">
            <summary>
                最大日志等级
            </summary>
        </member>
        <member name="T:PandaServer.Core.Startup">
            <summary>
                AppStartup启动类
            </summary>
        </member>
        <member name="T:PandaServer.Core.PandaServerResult`1">
            <summary>
                全局返回结果
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:PandaServer.Core.PandaServerResult`1.Code">
            <summary>
                状态码
            </summary>
        </member>
        <member name="P:PandaServer.Core.PandaServerResult`1.Success">
            <summary>
                是否成功
            </summary>
        </member>
        <member name="P:PandaServer.Core.PandaServerResult`1.Message">
            <summary>
                错误信息
            </summary>
        </member>
        <member name="P:PandaServer.Core.PandaServerResult`1.Data">
            <summary>
                数据
            </summary>
        </member>
        <member name="P:PandaServer.Core.PandaServerResult`1.Extras">
            <summary>
                附加数据
            </summary>
        </member>
        <member name="P:PandaServer.Core.PandaServerResult`1.Time">
            <summary>
                时间
            </summary>
        </member>
        <member name="T:PandaServer.Core.PandaServerResultProvider">
            <summary>
                规范化RESTful风格返回值
            </summary>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.OnException(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext,Furion.FriendlyException.ExceptionMetadata)">
            <summary>
                异常返回
            </summary>
            <param name="context"></param>
            <param name="metadata"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.OnSucceeded(Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext,System.Object)">
            <summary>
                成功返回
            </summary>
            <param name="context"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.OnValidateFailed(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Furion.DataValidation.ValidationMetadata)">
            <summary>
                验证失败返回
            </summary>
            <param name="context"></param>
            <param name="metadata"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.OnResponseStatusCodes(Microsoft.AspNetCore.Http.HttpContext,System.Int32,Furion.UnifyResult.UnifyResultSettingsOptions)">
            <summary>
                状态码响应拦截
            </summary>
            <param name="context"></param>
            <param name="statusCode"></param>
            <param name="unifyResultSettings"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.RESTfulResult(System.Int32,System.String)">
            <summary>
            简化版的 RESTful 风格结果集
            </summary>
            <param name="code">状态码</param>
            <param name="message">消息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.RESTfulResult(System.Int32,System.Boolean,System.String)">
            <summary>
            简化版的 RESTful 风格结果集
            </summary>
            <param name="code">状态码</param>
            <param name="succeeded"></param>
            <param name="message">消息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.GetStatusCodeMessage(System.Int32)">
            <summary>
            获取状态码对应的默认消息
            </summary>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.RESTfulResult(System.Int32,System.Boolean,System.Object,System.Object)">
            <summary>
                返回 RESTful 风格结果集
            </summary>
            <param name="statusCode">状态码</param>
            <param name="succeeded">是否成功</param>
            <param name="data">数据</param>
            <param name="errors">错误信息</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.GetErrorMessage(System.Int32)">
            <summary>
            获取错误信息
            </summary>
        </member>
        <member name="M:PandaServer.Core.PandaServerResultProvider.OnAuthorizeException(Microsoft.AspNetCore.Http.DefaultHttpContext,Furion.FriendlyException.ExceptionMetadata)">
            <summary>
            JWT 授权异常返回值
            </summary>
            <param name="context"></param>
            <param name="metadata"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.CaptchaInfo">
            <summary>
                验证码信息
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.CaptchaInfo.Code">
            <summary>
                验证码
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.CaptchaInfo.Image">
            <summary>
                验证码数据流
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.CaptchaInfo.Base64Str">
            <summary>
                base64
            </summary>
        </member>
        <member name="T:PandaServer.Core.Utils.CaptchaType">
            <summary>
                验证码类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.CaptchaType.NUM">
            <summary>
                纯数字验证码
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.CaptchaType.CHAR">
            <summary>
                数字加字母验证码
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.CaptchaType.ARITH">
            <summary>
                数字运算验证码
            </summary>
        </member>
        <member name="T:PandaServer.Core.Utils.CaptchaUtil">
            <summary>
                生成验证码功能
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.CaptchaUtil.CreateCaptcha(PandaServer.Core.Utils.CaptchaType,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
                获取验证码
            </summary>
            <param name="length">验证码数</param>
            <param name="width">长度</param>
            <param name="heigh">高度</param>
            <param name="fontSize">字体大小</param>
            <param name="type">类型 0：数字 1：字符 2：计算</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.CaptchaUtil.CreateNumCode(System.Int32)">
            <summary>
                获取数字验证码
            </summary>
            <param name="n">验证码数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.CaptchaUtil.CreateCharCode(System.Int32)">
            <summary>
                获取字符验证码
            </summary>
            <param name="n">验证码数</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.CaptchaUtil.CreateArithCode(System.String@)">
            <summary>
                获取运算符验证码
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.CommonUtils">
            <summary>
                公共功能
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.CommonUtils.GetSingleId">
            <summary>
                获取唯一Id
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.CryptogramUtil">
            <summary>
                加解密功能
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.CryptogramUtil.Sm2Decrypt(System.String)">
            <summary>
                SM2解密
            </summary>
            <param name="str">密文</param>
            <returns>明文</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.CryptogramUtil.Sm2Encrypt(System.String)">
            <summary>
                SM2加密
            </summary>
            <param name="str">明文</param>
            <returns>密文</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.CryptogramUtil.Sm4Decrypt(System.String)">
            <summary>
                SM4解密
            </summary>
            <param name="str">密文</param>
            <returns>明文</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.CryptogramUtil.Sm4Encrypt(System.String)">
            <summary>
                SM4加密
            </summary>
            <param name="str">明文</param>
            <returns>密文</returns>
        </member>
        <member name="T:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil">
             * need lib:
             * BouncyCastle.Crypto.dll（http://www.bouncycastle.org/csharp/index.html） 
              
             * 用BC的注意点：
             * 这个版本的BC对SM3withSM2的结果为asn1格式的r和s，如果需要直接拼接的r||s需要自己转换。下面rsAsn1ToPlainByteArray、rsPlainByteArrayToAsn1就在干这事。
             * 这个版本的BC对SM2的结果为C1||C2||C3，据说为旧标准，新标准为C1||C3||C2，用新标准的需要自己转换。下面（被注释掉的）changeC1C2C3ToC1C3C2、changeC1C3C2ToC1C2C3就在干这事。java版的高版本有加上C1C3C2，csharp版没准以后也会加，但目前还没有，java版的目前可以初始化时" SM2Engine sm2Engine = new SM2Engine(SM2Engine.Mode.C1C3C2);"。
             * 
             * 按要求国密算法仅允许使用加密机，本demo国密算法仅供学习使用，请不要用于生产用途。
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.SignSm3WithSm2(System.Byte[],System.Byte[],Org.BouncyCastle.Crypto.AsymmetricKeyParameter)">
            
             @param msg
             @param userId
             @param privateKey
             @return r||s，直接拼接byte数组的rs
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.SignSm3WithSm2Asn1Rs(System.Byte[],System.Byte[],Org.BouncyCastle.Crypto.AsymmetricKeyParameter)">
            @param msg
            @param userId
            @param privateKey
            @return rs in <b>asn1 format</b>
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.VerifySm3WithSm2(System.Byte[],System.Byte[],System.Byte[],Org.BouncyCastle.Crypto.AsymmetricKeyParameter)">
            
             @param msg
             @param userId
             @param rs r||s，直接拼接byte数组的rs
             @param publicKey
             @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.VerifySm3WithSm2Asn1Rs(System.Byte[],System.Byte[],System.Byte[],Org.BouncyCastle.Crypto.AsymmetricKeyParameter)">
            
             @param msg
             @param userId
             @param rs in <b>asn1 format</b>
             @param publicKey
             @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.ChangeC1C2C3ToC1C3C2(System.Byte[])">
            bc加解密使用旧标c1||c2||c3，此方法在加密后调用，将结果转化为c1||c3||c2
            @param c1c2c3
            @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.ChangeC1C3C2ToC1C2C3(System.Byte[])">
            bc加解密使用旧标c1||c3||c2，此方法在解密前调用，将密文转化为c1||c2||c3再去解密
            @param c1c3c2
            @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.Sm2Decrypt(System.Byte[],Org.BouncyCastle.Crypto.AsymmetricKeyParameter)">
            c1||c3||c2
            @param data
            @param key
            @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.Sm2Encrypt(System.Byte[],Org.BouncyCastle.Crypto.AsymmetricKeyParameter)">
            c1||c3||c2
            @param data
            @param key
            @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.Sm2EncryptOld(System.Byte[],Org.BouncyCastle.Crypto.AsymmetricKeyParameter)">
            c1||c2||c3
            @param data
            @param key
            @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.Sm2DecryptOld(System.Byte[],Org.BouncyCastle.Crypto.AsymmetricKeyParameter)">
            c1||c2||c3
            @param data
            @param key
            @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.Sm3(System.Byte[])">
            @param bytes
            @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.RsAsn1ToPlainByteArray(System.Byte[])">
            BC的SM3withSM2签名得到的结果的rs是asn1格式的，这个方法转化成直接拼接r||s
            @param rsDer rs in asn1 format
            @return sign result in plain byte array
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.RsPlainByteArrayToAsn1(System.Byte[])">
            BC的SM3withSM2验签需要的rs是asn1格式的，这个方法将直接拼接r||s的字节数组转化成asn1格式
            @param sign in plain byte array
            @return rs result in asn1 format
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.Join(System.Byte[][])">
             字节数组拼接
            
             @param params
             @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.KDF(System.Byte[],System.Int32)">
             密钥派生函数
            
             @param Z
             @param klen
                        生成klen字节数长度的密钥
             @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.readSm2File(System.Byte[],System.String)">
            cfca官网CSP沙箱导出的sm2文件
            @param pem 二进制原文
            @param pwd 密码
            @return
        </member>
        <member name="M:PandaServer.Core.Utils.Cryptogram.Sm.GmUtil.ReadSm2X509Cert(System.Byte[])">
            
             @param cert
             @return
        </member>
        <member name="T:PandaServer.Core.Utils.SM2CryptoUtil">
            <summary>
                SM2工具类
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.SM2CryptoUtil.GetKey">
            <summary>
                获取公钥私钥
            </summary>
            <returns></returns>
        </member>
        <member name="P:PandaServer.Core.Utils.SM2CryptoUtil.SM2Model.PublicKey">
            <summary>
                公钥
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.SM2CryptoUtil.SM2Model.PrivateKey">
            <summary>
                私钥
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.SM2CryptoUtil.Encrypt(System.String,System.String)">
            <summary>
                加密
            </summary>
            <param name="publickey">公钥</param>
            <param name="sourceData">需要加密的值</param>
            <returns>加密结果</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM2CryptoUtil.Encrypt(System.Byte[],System.Byte[])">
            <summary>
                加密
            </summary>
            <param name="publicKey">公钥</param>
            <param name="data">需要加密的值</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM2CryptoUtil.Decrypt(System.String,System.String)">
            <summary>
            </summary>
            <param name="privateKey"></param>
            <param name="encryptedData"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM2CryptoUtil.Decrypt(System.Byte[],System.Byte[])">
            <summary>
                解密
            </summary>
            <param name="privateKey"></param>
            <param name="encryptedData"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.SM4CryptoUtil">
            <summary>
                SM4工具类
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.SM4CryptoUtil.CK">
            <summary>
                固定参数CK
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.SM4CryptoUtil.FK">
            <summary>
                系统参数FK
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.SM4CryptoUtil.SboxTable">
            <summary>
                S盒
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.GetULongByBe(System.Byte[],System.Int32)">
            <summary>
                加密 非线性τ函数B=τ(A)
            </summary>
            <param name="b"></param>
            <param name="i"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.PutULongToBe(System.Int64,System.Byte[],System.Int32)">
            <summary>
                解密 非线性τ函数B=τ(A)
            </summary>
            <param name="n"></param>
            <param name="b"></param>
            <param name="i"></param>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Rotl(System.Int64,System.Int32)">
            <summary>
                循环移位,为32位的x循环左移n位
            </summary>
            <param name="x"></param>
            <param name="n"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Swap(System.Int64[],System.Int32)">
            <summary>
                将密钥逆序
            </summary>
            <param name="sk"></param>
            <param name="i"></param>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Sm4Sbox(System.Byte)">
            <summary>
                Sm4的S盒取值
            </summary>
            <param name="inch"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Sm4Lt(System.Int64)">
            <summary>
                线性变换 L
            </summary>
            <param name="ka"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Sm4F(System.Int64,System.Int64,System.Int64,System.Int64,System.Int64)">
            <summary>
                轮函数 F
            </summary>
            <param name="x0"></param>
            <param name="x1"></param>
            <param name="x2"></param>
            <param name="x3"></param>
            <param name="rk"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Sm4CalciRk(System.Int64)">
            <summary>
                轮密钥rk
            </summary>
            <param name="ka"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.SetKey(System.Int64[],System.Byte[])">
            <summary>
                加密密钥
            </summary>
            <param name="SK"></param>
            <param name="key"></param>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Sm4OneRound(System.Int64[],System.Byte[],System.Byte[])">
            <summary>
                解密函数
            </summary>
            <param name="sk">轮密钥</param>
            <param name="input">输入分组的密文</param>
            <param name="output">输出的对应的分组明文</param>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Padding(System.Byte[],System.Int32)">
            <summary>
                补足 16 进制字符串的 0 字符，返回不带 0x 的16进制字符串
            </summary>
            <param name="input"></param>
            <param name="mode">1表示加密，0表示解密</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.SetKeyEnc(PandaServer.Core.Utils.Sm4Context,System.Byte[])">
            <summary>
                设置加密的key
            </summary>
            <param name="ctx"></param>
            <param name="key"></param>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Sm4SetKeyDec(PandaServer.Core.Utils.Sm4Context,System.Byte[])">
            <summary>
                设置解密的key
            </summary>
            <param name="ctx"></param>
            <param name="key"></param>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Sm4CryptEcb(PandaServer.Core.Utils.Sm4Context,System.Byte[])">
            <summary>
                ECB
            </summary>
            <param name="ctx"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4CryptoUtil.Sm4CryptCbc(PandaServer.Core.Utils.Sm4Context,System.Byte[],System.Byte[])">
            <summary>
                CBC
            </summary>
            <param name="ctx"></param>
            <param name="iv"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.Sm4Context">
            <summary>
                SM4处理中心
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.Sm4Context.IsPadding">
            <summary>
                是否补足16进制字符串
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.Sm4Context.Key">
            <summary>
                密钥
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.Sm4Context.Mode">
            <summary>
                1表示加密，0表示解密
            </summary>
        </member>
        <member name="T:PandaServer.Core.Utils.SM2Util">
            <summary>
                SM2加密解密
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.SM2Util.PublicKey">
            <summary>
                公钥
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.SM2Util.PrivateKey">
            <summary>
                私钥
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.SM2Util.Encrypt(System.String)">
            <summary>
                公钥加密明文
            </summary>
            <param name="plainText">明文</param>
            <returns>密文</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM2Util.Decrypt(System.String)">
            <summary>
                私钥解密密文
            </summary>
            <param name="cipherText">密文</param>
            <returns>明文</returns>
        </member>
        <member name="T:PandaServer.Core.Utils.SM4Util">
            <summary>
                Sm4算法
                对标国际DES算法
            </summary>
        </member>
        <member name="T:PandaServer.Core.Utils.SM4Util.Sm4CryptoEnum">
            <summary>
                加密类型
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.SM4Util.Sm4CryptoEnum.ECB">
            <summary>
                ECB(电码本模式)
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.SM4Util.Sm4CryptoEnum.CBC">
            <summary>
                CBC(密码分组链接模式)
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.SM4Util.Data">
            <summary>
                数据
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.SM4Util.Key">
            <summary>
                秘钥
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.SM4Util.Iv">
            <summary>
                向量
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.SM4Util.HexString">
            <summary>
                明文是否是十六进制
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.SM4Util.CryptoMode">
            <summary>
                加密模式(默认ECB)
                统一改为ECB模式
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4Util.EncryptECB(PandaServer.Core.Utils.SM4Util)">
            <summary>
                ECB加密
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4Util.EncryptCBC(PandaServer.Core.Utils.SM4Util)">
            <summary>
                CBC加密
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4Util.Decrypt(PandaServer.Core.Utils.SM4Util)">
            <summary>
                解密
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4Util.DecryptECB(PandaServer.Core.Utils.SM4Util)">
            <summary>
                ECB解密
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.SM4Util.DecryptCBC(PandaServer.Core.Utils.SM4Util)">
            <summary>
                CBC解密
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Excel.ExcelData.GetTableByFile(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
                通过上传文件指直接转成 DataTable
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Excel.ExcelData.GetValue(System.Data.DataRow,System.String)">
            <summary>
            
            </summary>
            <param name="item"></param>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Excel.ExcelData.GetValue(System.Data.DataRow,System.String[])">
            <summary>
            
            </summary>
            <param name="item"></param>
            <param name="names"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Excel.ExcelData.GetTableByFile(System.Byte[])">
            <summary>
            </summary>
            <param name="fileBytes"></param>
            <returns></returns>
            <exception cref="T:Furion.FriendlyException.AppFriendlyException"></exception>
        </member>
        <member name="T:PandaServer.Core.Utils.GeoHelper">
            <summary>
                相关的地理转换的
            </summary>
        </member>
        <member name="F:PandaServer.Core.Utils.GeoHelper.PI">
            <summary>
                圆周率 较 Math.PI 精度更大
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.OutOfChina(System.Double,System.Double)">
            <summary>
                地理位置是否位于中国以外
            </summary>
            <param name="wgLat">WGS-84坐标纬度</param>
            <param name="wgLng">WGS-84坐标经度</param>
            <returns>
                true：国外
                false：国内
            </returns>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.WGS84ToGCJ02(System.Double,System.Double,System.Double@,System.Double@)">
            <summary>
                WGS-84坐标系转火星坐标系 (GCJ-02)
            </summary>
            <param name="wgLat">WGS-84坐标纬度</param>
            <param name="wgLng">WGS-84坐标经度</param>
            <param name="mgLat">输出：GCJ-02坐标纬度</param>
            <param name="mgLng">输出：GCJ-02坐标经度</param>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.GCJ02ToWGS84(System.Double,System.Double,System.Double@,System.Double@)">
            <summary>
                火星坐标系 (GCJ-02)转WGS-84坐标系
            </summary>
            <param name="mgLat">GCJ-02坐标纬度</param>
            <param name="mgLng">GCJ-02坐标经度</param>
            <param name="wgLat">输出：WGS-84坐标纬度</param>
            <param name="wgLng">输出：WGS-84坐标经度</param>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.GCJ02ToWGS84Exact(System.Double,System.Double,System.Double@,System.Double@)">
            <summary>
                火星坐标系 (GCJ-02)转WGS-84坐标系(精度更高)
            </summary>
            <param name="mgLat">GCJ-02坐标纬度</param>
            <param name="mgLng">GCJ-02坐标经度</param>
            <param name="wgLat">输出：WGS-84坐标纬度</param>
            <param name="wgLon">输出：WGS-84坐标经度</param>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.BD09ToGCJ02(System.Double,System.Double,System.Double@,System.Double@)">
            <summary>
                百度坐标系 (BD-09)转火星坐标系 (GCJ-02)
            </summary>
            <param name="bdLat">百度坐标系纬度</param>
            <param name="bdLon">百度坐标系经度</param>
            <param name="mgLat">输出：GCJ-02坐标纬度</param>
            <param name="mgLon">输出：GCJ-02坐标经度</param>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.GCJ02ToBD09(System.Double,System.Double,System.Double@,System.Double@)">
            <summary>
                火星坐标系 (GCJ-02)转百度坐标系 (BD-09)
            </summary>
            <param name="mgLat">GCJ-02坐标纬度</param>
            <param name="mgLon">GCJ-02坐标经度</param>
            <param name="bdLat">输出：百度坐标系纬度</param>
            <param name="bdLon">输出：百度坐标系经度</param>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.WGS84ToBD09(System.Double,System.Double,System.Double@,System.Double@)">
            <summary>
                WGS-84坐标系转百度坐标系 (BD-09)
            </summary>
            <param name="wgLat">WGS-84坐标纬度</param>
            <param name="wgLon">WGS-84坐标经度</param>
            <param name="bdLat">输出：百度坐标系纬度</param>
            <param name="bdLon">输出：百度坐标系经度</param>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.BD09ToWGS84(System.Double,System.Double,System.Double@,System.Double@)">
            <summary>
                百度坐标系 (BD-09)转WGS-84坐标系
            </summary>
            <param name="bdLat">百度坐标系纬度</param>
            <param name="bdLon">百度坐标系经度</param>
            <param name="wgLat">输出：WGS-84坐标纬度</param>
            <param name="wgLon">输出：WGS-84坐标经度</param>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.Distance(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            </summary>
            <param name="LatA"></param>
            <param name="LonA"></param>
            <param name="LatB"></param>
            <param name="LonB"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.Delta(System.Double,System.Double,System.Double@,System.Double@)">
            <summary>
            </summary>
            <param name="Lat"></param>
            <param name="Lon"></param>
            <param name="dLat"></param>
            <param name="dLon"></param>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.TransformLat(System.Double,System.Double)">
            <summary>
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.GeoHelper.TransformLon(System.Double,System.Double)">
            <summary>
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.HttpNewUtil">
            <summary>
                HTTP网络工具
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.HttpNewUtil.Ip">
            <summary>
                客户端IP地址
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.HttpNewUtil.Url">
            <summary>
                请求Url
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.GetHeader(System.String)">
            <summary>
                直接返回 Header 的值
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.UserAgent">
            <summary>
                请求UserAgent信息
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.GetWebClientIp">
            <summary>
                得到客户端IP地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.GetLanIp">
            <summary>
                得到局域网IP地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.GetWebRemoteIp">
            <summary>
                得到远程Ip地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.GetOsVersion">
            <summary>
                得到操作系统版本
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.WanInfo">
            <summary>
                公网信息
                慎用，如果不是直接请求接口，而是通过代理转发，拿到的是服务器的公网信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.WanInfo(System.String)">
            <summary>
                根据IP地址获取公网信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.UserAgentInfo">
            <summary>
                UserAgent信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.EncodeRemotePath(System.String)">
            <summary>
                远程路径Encode处理,会保证开头是/，结尾也是/
            </summary>
            <param name="remotePath"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.StandardizationRemotePath(System.String)">
            <summary>
                标准化远程目录路径,会保证开头是/，结尾也是/ ,如果命名不规范，存在保留字符，会返回空字符
            </summary>
            <param name="remotePath">要标准化的远程路径</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.GetBody``1">
            <summary>
                获取 Request  Body 中的 内容 转换成  T 对象
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpNewUtil.GetBody">
            <summary>
                获取 Request  Body 中的 文本内容
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.WhoisIpInfoModel">
            <summary>
                万网Ip信息Model类
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.WhoisIpInfoModel.Ip">
            <summary>
                Ip地址
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.WhoisIpInfoModel.Pro">
            <summary>
                省份
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.WhoisIpInfoModel.ProCode">
            <summary>
                省份邮政编码
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.WhoisIpInfoModel.City">
            <summary>
                城市
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.WhoisIpInfoModel.CityCode">
            <summary>
                城市邮政编码
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.WhoisIpInfoModel.Address">
            <summary>
                地理信息
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.WhoisIpInfoModel.Operator">
            <summary>
                运营商
            </summary>
        </member>
        <member name="T:PandaServer.Core.Utils.UserAgentInfoModel">
            <summary>
                UserAgent 信息Model类
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.UserAgentInfoModel.PhoneModel">
            <summary>
                手机型号
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.UserAgentInfoModel.OS">
            <summary>
                操作系统（版本）
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.UserAgentInfoModel.Browser">
            <summary>
                浏览器（版本）
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpWebClient.GetAsync(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Int32)">
            <summary>
                Get异步请求
            </summary>
            <param name="url"></param>
            <param name="dicHeaders"></param>
            <param name="timeoutSecond"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpWebClient.GetDataAsync(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Int32)">
            <summary>
                Get 二进制 文件 异步请求
            </summary>
            <param name="url"></param>
            <param name="dicHeaders"></param>
            <param name="timeoutSecond"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpWebClient.PostAsync(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Int32)">
            <summary>
            </summary>
            <param name="url"></param>
            <param name="requestString"></param>
            <param name="dicHeaders"></param>
            <param name="timeoutSecond"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpWebClient.PutAsync(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Int32)">
            <summary>
            </summary>
            <param name="url"></param>
            <param name="requestString"></param>
            <param name="dicHeaders"></param>
            <param name="timeoutSecond"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpWebClient.PatchAsync(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Int32)">
            <summary>
                Patch异步请求
            </summary>
            <param name="url"></param>
            <param name="requestString"></param>
            <param name="dicHeaders"></param>
            <param name="timeoutSecond"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpWebClient.DeleteAsync(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Int32)">
            <summary>
            </summary>
            <param name="url"></param>
            <param name="dicHeaders"></param>
            <param name="timeoutSecond"></param>
            <returns></returns>
            <exception cref="T:PandaServer.Core.Utils.CustomerHttpException"></exception>
        </member>
        <member name="M:PandaServer.Core.Utils.HttpWebClient.ExecuteAsync(System.String,System.Net.Http.HttpMethod,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Int32,System.String)">
            <summary>
                异步请求（通用）
            </summary>
            <param name="url"></param>
            <param name="method"></param>
            <param name="requestString"></param>
            <param name="dicHeaders"></param>
            <param name="timeoutSecond"></param>
            <param name="accept"></param>
            <returns></returns>
            <exception cref="T:PandaServer.Core.Utils.CustomerHttpException"></exception>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.ResolveUrl(System.String)">
            <summary>
                解析相对Url
            </summary>
            <param name="relativeUrl">相对Url</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.NoHtml(System.String)">
            <summary>
                去除HTML标记
            </summary>
            <param name="NoHTML">包括HTML的源码 </param>
            <returns>已经去除后的文字</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.Formatstr(System.String)">
            <summary>
                格式化文本（防止SQL注入）
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.HtmlEncode(System.String)">
            <summary>
                对html字符串进行编码
            </summary>
            <param name="html">html字符串</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.HtmlDecode(System.String)">
            <summary>
                对html字符串进行解码
            </summary>
            <param name="html">html字符串</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.UrlEncode(System.String,System.Boolean)">
            <summary>
                对Url进行编码
            </summary>
            <param name="url">url</param>
            <param name="isUpper">编码字符是否转成大写,范例,"http://"转成"http%3A%2F%2F"</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.UrlEncode(System.String,System.Text.Encoding,System.Boolean)">
            <summary>
                对Url进行编码
            </summary>
            <param name="url">url</param>
            <param name="encoding">字符编码</param>
            <param name="isUpper">编码字符是否转成大写,范例,"http://"转成"http%3A%2F%2F"</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.GetUpperEncode(System.String)">
            <summary>
                获取大写编码字符串
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.UrlDecode(System.String)">
            <summary>
                对Url进行解码,对于javascript的encodeURIComponent函数编码参数,应使用utf-8字符编码来解码
            </summary>
            <param name="url">url</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.UrlDecode(System.String,System.Text.Encoding)">
            <summary>
                对Url进行解码,对于javascript的encodeURIComponent函数编码参数,应使用utf-8字符编码来解码
            </summary>
            <param name="url">url</param>
            <param name="encoding">字符编码,对于javascript的encodeURIComponent函数编码参数,应使用utf-8字符编码来解码</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.WriteSession(System.String,System.String)">
            <summary>
                写Session
            </summary>
            <param name="key">Session的键名</param>
            <param name="value">Session的键值</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.GetSession(System.String)">
            <summary>
                读取Session的值
            </summary>
            <param name="key">Session的键名</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.RemoveSession(System.String)">
            <summary>
                删除指定Session
            </summary>
            <param name="key">Session的键名</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.WriteCookie(System.String,System.String,Microsoft.AspNetCore.Http.CookieOptions)">
            <summary>
                写cookie值
            </summary>
            <param name="strName">名称</param>
            <param name="strValue">值</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.WriteCookie(System.String,System.String,System.Int32)">
            <summary>
                写cookie值
            </summary>
            <param name="strName">名称</param>
            <param name="strValue">值</param>
            <param name="strValue">过期时间(分钟)</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.GetCookie(System.String)">
            <summary>
                读cookie值
            </summary>
            <param name="strName">名称</param>
            <returns>cookie值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.RemoveCookie(System.String)">
            <summary>
                删除Cookie对象
            </summary>
            <param name="CookiesName">Cookie对象名称</param>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.GetIpNum(System.String)">
            <summary>
                把IP地址转换为Long型数字
            </summary>
            <param name="ipAddress">IP地址字符串</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.WebHelper.Resove(System.String,System.String,System.String)">
            <summary>
                Get part Content from HTML by apply prefix part and subfix part
            </summary>
            <param name="html">souce html</param>
            <param name="prefix">prefix</param>
            <param name="subfix">subfix</param>
            <returns>part content</returns>
        </member>
        <member name="T:PandaServer.Core.Utils.AvatarUtil">
            <summary>
                头像功能
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.AvatarUtil.GetNameColor(System.String)">
            <summary>
                获取姓名对应的颜色值
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.AvatarUtil.GetNameImage(System.String,System.Int32,System.Int32)">
            <summary>
                获取姓名对应的图片
            </summary>
            <param name="name"></param>
            <param name="width"></param>
            <param name="height"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.AvatarUtil.GetNameImageBase64(System.String,System.Int32,System.Int32)">
            <summary>
                获取图片base64格式
            </summary>
            <param name="name">名称</param>
            <param name="width">宽</param>
            <param name="height">高</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.AvatarUtil.IsChinese(System.String)">
            <summary>
                用 正则表达式 判断字符是不是汉字
            </summary>
            <param name="text">待判断字符或字符串</param>
            <returns>真：是汉字；假：不是</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.ImgToBase64String(SkiaSharp.SKImage)">
            <summary>
                图片转换成base64
            </summary>
            <param name="img"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.ImgToBase64String(SkiaSharp.SKBitmap)">
            <summary>
                图片转换成base64
            </summary>
            <param name="bmp"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.GetBitmapFromBase64(System.String)">
            <summary>
                base64转bitmap
            </summary>
            <param name="base64string"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.GetSKBitmapFromBase64(System.String)">
            <summary>
                base64转bitmap
            </summary>
            <param name="base64string"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.ToImageBase64(System.String)">
            <summary>
                base64转image格式
            </summary>
            <param name="base64string"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.ResizeImage(System.Drawing.Image,System.Drawing.Size)">
            <summary>
                重新修改尺寸
            </summary>
            <param name="imgToResize">图片</param>
            <param name="size">尺寸</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.ResizeImage(System.Drawing.Bitmap,System.Int32,System.Int32)">
            <summary>
                Resize图片
            </summary>
            <param name="bmp">原始Bitmap </param>
            <param name="newW">新的宽度</param>
            <param name="newH">新的高度</param>
            <returns>处理以后的图片</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.ResizeImage(SkiaSharp.SKBitmap,System.Int32,System.Int32)">
            <summary>
                Resize图片
            </summary>
            <param name="bmp">原始Bitmap </param>
            <param name="newW">新的宽度</param>
            <param name="newH">新的高度</param>
            <returns>处理以后的图片</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.GetPicThumbnail(System.Drawing.Bitmap,System.Int32,System.Int32)">
            <summary>
                获取缩略图
            </summary>
            <param name="bmp"></param>
            <param name="w">宽</param>
            <param name="h">高</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.GetPicThumbnail(SkiaSharp.SKBitmap,System.Int32,System.Int32)">
            <summary>
                获取缩略图
            </summary>
            <param name="bmp"></param>
            <param name="w">宽</param>
            <param name="h">高</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.AddTextToImage(SkiaSharp.SKBitmap,System.String,System.String,SkiaSharp.SKColor,System.Int32,System.Boolean)">
            <summary>
            Adds text to an image at the top or bottom with auto-sizing based on image width
            </summary>
            <param name="image">Source image</param>
            <param name="text">Text to add</param>
            <param name="fontFamily">Font family name</param>
            <param name="textColor">Color of the text</param>
            <param name="padding">Padding between text and image edge</param>
            <param name="position">Position of text - true for top, false for bottom</param>
            <returns>New image with text added</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ImageUtil.CalculateOptimalFontSize(System.String,System.Int32,System.String)">
            <summary>
            Calculate the optimal font size to fit text within a given width
            </summary>
            <param name="text">Text to measure</param>
            <param name="maxWidth">Maximum width available</param>
            <param name="fontFamily">Font family name</param>
            <returns>Optimal font size</returns>
        </member>
        <member name="T:PandaServer.Core.Utils.SealUtil">
            <summary>
            印章工具类
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.SealUtil.CreateSeal(System.String,System.String)">
            <summary>
            创建印章图片
            </summary>
            <param name="outerCircleText">外圈文字</param>
            <param name="interiorText">内部文字</param>
            <returns>生成的印章图片数据</returns>
        </member>
        <member name="T:PandaServer.Core.Utils.Pdf.PdfTextWriter">
            <summary>
            PDF 文本写入工具类
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.GetDefaultFontPath">
            <summary>
            获取默认字体路径
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.IsFontFileExists">
            <summary>
            检查字体文件是否存在
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.GetFont(System.Single)">
            <summary>
            获取字体实例
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.DrawText(Spire.Pdf.PdfPageBase,System.String,System.Single,System.Single,System.Single,Spire.Pdf.Graphics.PdfBrush,Spire.Pdf.Graphics.PdfFontStyle)">
            <summary>
            在PDF页面上绘制文本
            </summary>
            <param name="page">PDF页面</param>
            <param name="text">要绘制的文本</param>
            <param name="x">X坐标</param>
            <param name="y">Y坐标</param>
            <param name="fontSize">字体大小</param>
            <param name="color">文本颜色</param>
            <param name="style">字体样式</param>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.DrawText(Spire.Pdf.PdfPageBase,System.String,System.Single,System.Single,System.Single,System.Single,Spire.Pdf.Graphics.PdfStringFormat,Spire.Pdf.Graphics.PdfBrush,Spire.Pdf.Graphics.PdfFontStyle)">
            <summary>
            在PDF页面上绘制文本（支持对齐方式）
            </summary>
            <param name="page">PDF页面</param>
            <param name="text">要绘制的文本</param>
            <param name="x">X坐标</param>
            <param name="y">Y坐标</param>
            <param name="width">文本区域宽度</param>
            <param name="fontSize">字体大小</param>
            <param name="alignment">对齐方式</param>
            <param name="color">文本颜色</param>
            <param name="style">字体样式</param>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.DrawMultilineText(Spire.Pdf.PdfPageBase,System.String,System.Single,System.Single,System.Single,System.Single,System.Single,Spire.Pdf.Graphics.PdfBrush,Spire.Pdf.Graphics.PdfFontStyle)">
            <summary>
            绘制多行文本
            </summary>
            <param name="page">PDF页面</param>
            <param name="text">要绘制的文本</param>
            <param name="x">X坐标</param>
            <param name="y">Y坐标</param>
            <param name="width">文本区域宽度</param>
            <param name="height">文本区域高度</param>
            <param name="fontSize">字体大小</param>
            <param name="color">文本颜色</param>
            <param name="style">字体样式</param>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.DrawRotatedText(Spire.Pdf.PdfPageBase,System.String,System.Single,System.Single,System.Single,System.Single,Spire.Pdf.Graphics.PdfBrush,Spire.Pdf.Graphics.PdfFontStyle)">
            <summary>
            绘制旋转文本
            </summary>
            <param name="page">PDF页面</param>
            <param name="text">要绘制的文本</param>
            <param name="x">X坐标</param>
            <param name="y">Y坐标</param>
            <param name="fontSize">字体大小</param>
            <param name="angle">旋转角度（度）</param>
            <param name="color">文本颜色</param>
            <param name="style">字体样式</param>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.GetTextSize(System.String,System.Single,Spire.Pdf.Graphics.PdfFontStyle)">
            <summary>
            获取文本尺寸
            </summary>
            <param name="text">文本内容</param>
            <param name="fontSize">字体大小</param>
            <param name="style">字体样式</param>
            <returns>文本尺寸</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.ConvertPixelToPoint(System.Single,System.Single)">
            <summary>
            像素坐标转换为PDF坐标（point）
            </summary>
            <param name="pixelValue">像素值</param>
            <param name="dpi">DPI值，默认96</param>
            <returns>PDF坐标值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.ConvertMmToPoint(System.Single)">
            <summary>
            毫米坐标转换为PDF坐标（point）
            </summary>
            <param name="mmValue">毫米值</param>
            <returns>PDF坐标值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.ConvertInchToPoint(System.Single)">
            <summary>
            英寸坐标转换为PDF坐标（point）
            </summary>
            <param name="inchValue">英寸值</param>
            <returns>PDF坐标值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.DrawTextWithRatio(Spire.Pdf.PdfPageBase,System.String,System.Single,System.Single,System.Single,Spire.Pdf.Graphics.PdfBrush,Spire.Pdf.Graphics.PdfFontStyle)">
            <summary>
            在PDF页面上绘制文本（使用比例坐标）
            </summary>
            <param name="page">PDF页面</param>
            <param name="text">要绘制的文本</param>
            <param name="ratioX">X坐标比例（0-1之间）</param>
            <param name="ratioY">Y坐标比例（0-1之间）</param>
            <param name="fontSize">字体大小</param>
            <param name="color">文本颜色</param>
            <param name="style">字体样式</param>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.DrawTextWithRatio(Spire.Pdf.PdfPageBase,System.String,System.Single,System.Single,System.Single,System.Single,Spire.Pdf.Graphics.PdfStringFormat,Spire.Pdf.Graphics.PdfBrush,Spire.Pdf.Graphics.PdfFontStyle)">
            <summary>
            在PDF页面上绘制文本（使用比例坐标，支持对齐方式）
            </summary>
            <param name="page">PDF页面</param>
            <param name="text">要绘制的文本</param>
            <param name="ratioX">X坐标比例（0-1之间）</param>
            <param name="ratioY">Y坐标比例（0-1之间）</param>
            <param name="ratioWidth">文本区域宽度比例（0-1之间）</param>
            <param name="fontSize">字体大小</param>
            <param name="alignment">对齐方式</param>
            <param name="color">文本颜色</param>
            <param name="style">字体样式</param>
        </member>
        <member name="M:PandaServer.Core.Utils.Pdf.PdfTextWriter.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:PandaServer.Core.Utils.PwdUtil">
            <summary>
                密码相关通用类
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.PwdUtil.Similarity(System.String,System.String)">
            <summary>
                密码相似度
            </summary>
            <param name="oldPassword"></param>
            <param name="newPassword"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.PwdUtil.LevenshteinDistance(System.String,System.String)">
            <summary>
                计算莱文斯坦距离算法
            </summary>
            <param name="s1"></param>
            <param name="s2"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.DESEncrypt">
            <summary>
                DES加密、解密帮助类
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.DESEncrypt.Sha1(System.String)">
            <summary>
                基于Sha1的自定义加密字符串方法：输入一个字符串，返回一个由40个字符组成的十六进制的哈希散列（字符串）。
            </summary>
            <param name="str">要加密的字符串</param>
            <returns>加密后的十六进制的哈希散列（字符串）</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DESEncrypt.Encrypt(System.String)">
            <summary>
                加密
            </summary>
            <param name="Text"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DESEncrypt.Encrypt(System.String,System.String)">
            <summary>
                加密数据，用Web.Security的Hash方式加密
            </summary>
            <param name="Text"></param>
            <param name="sKey"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DESEncrypt.Encrypt2(System.String)">
            <summary>
                加密数据, 用Security.MD5而非Web.Security的Hash方式加密
            </summary>
            <param name="Text"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DESEncrypt.Encrypt2(System.String,System.String)">
            <summary>
                加密数据, 用Security.MD5而非Web.Security的Hash方式加密
            </summary>
            <param name="Text"></param>
            <param name="sKey"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DESEncrypt.Decrypt(System.String)">
            <summary>
                解密
            </summary>
            <param name="Text"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DESEncrypt.Decrypt(System.String,System.String)">
            <summary>
                解密数据，用Web.Security的Hash方式加密
            </summary>
            <param name="Text"></param>
            <param name="sKey"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DESEncrypt.Decrypt2(System.String)">
            <summary>
                解密数据,用Security.MD5而非Web.Security的Hash方式加密
            </summary>
            <param name="Text"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DESEncrypt.Decrypt2(System.String,System.String)">
            <summary>
                解密数据,用Security.MD5而非Web.Security的Hash方式加密
            </summary>
            <param name="Text"></param>
            <param name="sKey"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.Md5">
            <summary>
                MD5加密
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.Md5.md5(System.String,System.Int32)">
            <summary>
                MD5加密
            </summary>
            <param name="str">加密字符</param>
            <param name="code">加密位数16/32</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Md5.MD5(System.String)">
            <summary>
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Md5.MD5Lower16(System.String)">
            <summary>
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Md5.MD5Lower32(System.String)">
            <summary>
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Md5.SHA1(System.String)">
            <summary>
                32位小写
            </summary>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:PandaServer.Core.Utils.RSAHelper.PrivateKeyEncrypt(System.String,System.String,System.Boolean)" -->
        <!-- Badly formed XML comment ignored for member "M:PandaServer.Core.Utils.RSAHelper.PrivateKeyEncrypt(System.String,System.Byte[],System.Boolean)" -->
        <!-- Badly formed XML comment ignored for member "M:PandaServer.Core.Utils.RSAHelper.PublicKeyDecrypt(System.String,System.String,System.Boolean)" -->
        <!-- Badly formed XML comment ignored for member "M:PandaServer.Core.Utils.RSAHelper.XmlPublicKeyToPem(System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:PandaServer.Core.Utils.RSAHelper.PemPublicKeyToXml(System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:PandaServer.Core.Utils.RSAHelper.XmlPrivateKeyToPem(System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:PandaServer.Core.Utils.RSAHelper.PemPrivateKeyToXml(System.String)" -->
        <member name="T:PandaServer.Core.Utils.DateHelper">
            <summary>
             关于时间方面的方法
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.DateHelper.Week(System.DateTime)">
            <summary>
            
            </summary>
            <param name="dateTime"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DateHelper.Week(System.String)">
            <summary>
            
            </summary>
            <param name="weekName"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.DateHelper.Week(System.DayOfWeek)">
            <summary>
            
            </summary>
            <param name="weekName"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.IdCardHelper.GetBirthDay(System.String)">
            <summary>
                身份证号码 获取 他的生日
            </summary>
            <param name="IdCardNo"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.IdCardHelper.GetSex(System.String)">
            <summary>
                身份证号码 获取 他的性别
            </summary>
            <param name="IdCardNo"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.IdCardHelper.ClearMiddleString(System.String)">
            <summary>
            脱敏
            </summary>
            <param name="InputString"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.IdCardHelper.IsValidIdCard(System.String)">
            <summary>
            检查 是否有效的 身份证方法
            </summary>
            <param name="idCard"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.PingYinHelper.ConvertToAllSpell(System.String)">
            <summary>
                汉字转全拼
            </summary>
            <param name="strChinese"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.PingYinHelper.GetFirstSpell(System.String)">
            <summary>
                汉字转首字母
            </summary>
            <param name="strChinese"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.Core.Utils.Text.StringMatcherUtil">
            <summary>
            字符串匹配工具类
            </summary>
        </member>
        <member name="M:PandaServer.Core.Utils.Text.StringMatcherUtil.HasMatchingStrings(System.String,System.String,System.Char,System.Boolean)">
            <summary>
            检查两组字符串之间是否存在包含关系
            </summary>
            <param name="sourceText">源字符串，多个值用分隔符分隔</param>
            <param name="targetText">目标字符串，多个值用分隔符分隔</param>
            <param name="separator">分隔符，默认为分号</param>
            <param name="ignoreCase">是否忽略大小写，默认为true</param>
            <returns>如果存在包含关系则返回true，否则返回false</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Text.StringMatcherUtil.ParseTextToList(System.String,System.Char)">
            <summary>
            将分隔符分隔的文本转换为列表
            </summary>
            <param name="text">要解析的文本</param>
            <param name="separator">分隔符</param>
            <returns>解析后的字符串列表</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Text.StringMatcherUtil.CheckForMatches(System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.StringComparison)">
            <summary>
            检查两个字符串列表之间是否存在匹配
            </summary>
            <param name="sourceItems">源字符串列表</param>
            <param name="targetItems">目标字符串列表</param>
            <param name="comparisonType">字符串比较方式</param>
            <returns>如果存在匹配则返回true，否则返回false</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.Text.StringMatcherUtil.IsSameMaskValue(System.String,System.String)">
            <summary>
            比较两个字符串是否相等（忽略脱敏字符'*'）
            </summary>
            <param name="value1">第一个字符串</param>
            <param name="value2">第二个字符串</param>
            <returns>
            true: 所有非脱敏字符相等
            false: 长度不等或存在不相等的非脱敏字符
            </returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.GetCustomValue(System.String,System.String)">
            <summary>
                获取默认值
            </summary>
            <param name="value"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.GetSubString(System.String,System.Int32,System.Boolean)">
            <summary>
                截取指定长度的字符串
            </summary>
            <param name="value"></param>
            <param name="length"></param>
            <param name="ellipsis"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.SplitToArray``1(System.String,System.Char)">
            <summary>
                字符串转指定类型数组
            </summary>
            <param name="value"></param>
            <param name="split"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.IsArrayIntersection``1(System.Collections.Generic.List{``0},System.Collections.Generic.List{``0})">
            <summary>
                判断是否有交集
            </summary>
            <typeparam name="T"></typeparam>
            <param name="list1"></param>
            <param name="list2"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.SetSensitiveName(System.String)">
            <summary>
                姓名敏感处理
            </summary>
            <param name="fullName">姓名</param>
            <returns>脱敏后的姓名</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.SetSensitiveIdCardNo(System.String)">
            <summary>
                身份证脱敏
            </summary>
            <param name="idCardNo">身份证号</param>
            <returns>脱敏后的身份证号</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.GetBirthdayByIdCardNo(System.String)">
            <summary>
                通过 身份证 号码 获取 生日
            </summary>
            <param name="idCardNo"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.MakeSignWord(System.String)">
            <summary>
                生成签字 后面的 背景字
            </summary>
            <param name="word"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.GuId">
            <summary>
                表示全局唯一标识符 (GUID)。
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.CreateNo">
            <summary>
                自动生成编号  201008251145409865
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.CreateRandomNo(System.Int32)">
            <summary>
                生成随机数字字符串
            </summary>
            <param name="Length">字符串的长度</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.TextHelper.CreateRandomWord(System.Int32)">
            <summary>
                生成随机数字字符串
            </summary>
            <param name="Length">字符串的长度</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsTel(System.String)">
            <summary>
                验证中国电话格式是否有效，格式010-85849685
            </summary>
            <param name="str">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsPhone(System.String)">
            <summary>
                验证输入字符串为电话号码
            </summary>
            <param name="str">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsFax(System.String)">
            <summary>
                验证是否是有效传真号码
            </summary>
            <param name="str">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsMobileNumber(System.String)">
            <summary>
                验证手机号是否合法 号段为13,14,15,16,17,18,19  0，86开头将自动识别
            </summary>
            <param name="str">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsEmail(System.String)">
            <summary>
                验证是否是有效邮箱地址
            </summary>
            <param name="str">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsOnlyChinese(System.String)">
            <summary>
                验证是否只含有汉字
            </summary>
            <param name="strln">输入字符</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsBadString(System.String)">
            <summary>
                是否有多余的字符 防止SQL注入
            </summary>
            <param name="str">输入字符</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsNzx(System.String)">
            <summary>
                是否由数字、26个英文字母或者下划线組成的字串
            </summary>
            <param name="str">输入字符</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsSzzmChinese(System.String)">
            <summary>
                由数字、26个英文字母、汉字組成的字串
            </summary>
            <param name="str">输入字符</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsSzzm(System.String)">
            <summary>
                是否由数字、26个英文字母組成的字串
            </summary>
            <param name="str">输入字符</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsPostCode(System.String)">
            <summary>
                验证输入字符串为邮政编码
            </summary>
            <param name="str">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.CheckLength(System.String,System.Int32)">
            <summary>
                检查对象的输入长度
            </summary>
            <param name="str">输入字符</param>
            <param name="length">指定的长度</param>
            <returns>false 太长，true -太短</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsDateTime(System.String)">
            <summary>
                判断用户输入是否为日期
            </summary>
            <param name="str">输入字符</param>
            <returns>返回一个bool类型的值</returns>
            <remarks>
                可判断格式如下（其中-可替换为/，不影响验证)
                YYYY | YYYY-MM | YYYY-MM-DD | YYYY-MM-DD HH:MM:SS | YYYY-MM-DD HH:MM:SS.FFF
            </remarks>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsNumber(System.String)">
            <summary>
                验证输入字符串为带小数点正数
            </summary>
            <param name="str">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsNumberic(System.String)">
            <summary>
                验证输入字符串为带小数点正负数
            </summary>
            <param name="str">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsIDCard(System.String)">
            <summary>
                验证身份证是否有效
            </summary>
            <param name="cardNumber">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsIDCard18(System.String)">
            <summary>
                验证输入字符串为18位的身份证号码
            </summary>
            <param name="cardNumber">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ValidatorHelper.IsIDCard15(System.String)">
            <summary>
                验证输入字符串为15位的身份证号码
            </summary>
            <param name="cardNumber">输入字符</param>
            <returns>返回一个bool类型的值</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.ZipFile(System.String,System.String,System.Int32,System.Int32)">
            <summary>
                压缩单个文件
            </summary>
            <param name="fileToZip">要压缩的文件</param>
            <param name="zipedFile">压缩后的文件</param>
            <param name="compressionLevel">压缩等级</param>
            <param name="blockSize">每次写入大小</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.ZipFile(System.String,System.String)">
            <summary>
                压缩单个文件
            </summary>
            <param name="fileToZip">要进行压缩的文件名</param>
            <param name="zipedFile">压缩后生成的压缩文件名</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.ZipFile(System.Collections.Generic.List{System.String},System.String)">
            <summary>
                压缩多个文件到指定路径
            </summary>
            <param name="sourceFileNames">压缩到哪个路径</param>
            <param name="zipFileName">压缩文件名称</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.ZipFileDirectory(System.String,System.String)">
            <summary>
                压缩多层目录
            </summary>
            <param name="strDirectory">待压缩目录</param>
            <param name="zipedFile">压缩后生成的压缩文件名，绝对路径</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.ZipFileDirectory(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
                压缩多层目录
            </summary>
            <param name="strDirectory">待压缩目录</param>
            <param name="zipedFile">压缩后生成的压缩文件名，绝对路径</param>
            <param name="files">指定要压缩的文件列表(完全路径)</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.ZipSetp(System.String,ICSharpCode.SharpZipLib.Zip.ZipOutputStream,System.String,System.Collections.Generic.List{System.String})">
            <summary>
                递归遍历目录
            </summary>
            <param name="strDirectory">需遍历的目录</param>
            <param name="s">压缩输出流对象</param>
            <param name="parentPath">The parent path.</param>
            <param name="files">需要压缩的文件</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.UnZip(System.String,System.String,System.Boolean,System.String)">
            <summary>
                解压缩一个 zip 文件。
            </summary>
            <param name="zipedFile">压缩文件</param>
            <param name="strDirectory">解压目录</param>
            <param name="password">zip 文件的密码。</param>
            <param name="overWrite">是否覆盖已存在的文件。</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.UnZip(System.String,System.String,System.Boolean)">
            <summary>
                解压缩一个 zip 文件。
            </summary>
            <param name="zipedFile">压缩文件</param>
            <param name="strDirectory">解压目录</param>
            <param name="overWrite">是否覆盖已存在的文件。</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.UnZip(System.String,System.String)">
            <summary>
                解压缩一个 zip 文件。
                覆盖已存在的文件。
            </summary>
            <param name="zipedFile">压缩文件</param>
            <param name="strDirectory">解压目录</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.GetFiles(System.String,System.Collections.Generic.List{System.String})">
            <summary>
                获取压缩文件中指定类型的文件
            </summary>
            <param name="zipedFile">压缩文件</param>
            <param name="fileExtension">文件类型(.txt|.exe)</param>
            <returns>文件名称列表(包含子目录)</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.GetFiles(System.String)">
            <summary>
                获取压缩文件中的所有文件
            </summary>
            <param name="zipedFile">压缩文件</param>
            <returns>文件名称列表(包含子目录)</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.ZipFiles(System.String,System.Collections.Generic.List{PandaServer.Core.Utils.FileItem},System.String@,System.Boolean)">
            <summary>
                打包线上线下文件
            </summary>
            <param name="zipName">压缩文件名称</param>
            <param name="fileList">文件列表</param>
            <param name="error">保存路径</param>
            <param name="isLocal">是否本地</param>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.ZipFilesToStream(System.Collections.Generic.List{PandaServer.Core.Utils.FileItem},System.Boolean)">
            <summary>
                打包文件到内存流
            </summary>
            <param name="fileList">文件列表</param>
            <param name="isLocal">是否本地文件</param>
            <returns>包含压缩文件的内存流</returns>
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.CompressDirectory(System.String,System.Boolean)">
            压缩文件夹
            要打包的文件夹
            是否删除原文件夹
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.GetAllFies(System.String)">
            获取所有文件
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.GetAllDirsFiles(System.IO.DirectoryInfo[],System.Collections.Generic.Dictionary{System.String,System.DateTime})">
            获取一个文件夹下的所有文件夹里的文件
        </member>
        <member name="M:PandaServer.Core.Utils.ZipUtils.GetAllDirFiles(System.IO.DirectoryInfo,System.Collections.Generic.Dictionary{System.String,System.DateTime})">
            获取一个文件夹下的文件
            
            目录名称
            文件列表HastTable
        </member>
        <member name="T:PandaServer.Core.Utils.FileItem">
            <summary>
                文件对象
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.FileItem.FileName">
            <summary>
                文件名称
            </summary>
        </member>
        <member name="P:PandaServer.Core.Utils.FileItem.FilePath">
            <summary>
                文件路径
            </summary>
        </member>
    </members>
</doc>
