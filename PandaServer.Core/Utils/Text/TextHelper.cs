using System.Reflection;
using Furion.DistributedIDGenerator;
using PandaServer.Core.Extend;
using SkiaSharp;

namespace PandaServer.Core.Utils;

public class TextHelper
{
    /// <summary>
    ///     获取默认值
    /// </summary>
    /// <param name="value"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static string GetCustomValue(string value, string defaultValue)
    {
        if (string.IsNullOrEmpty(value))
            return defaultValue;
        return value;
    }


    /// <summary>
    ///     截取指定长度的字符串
    /// </summary>
    /// <param name="value"></param>
    /// <param name="length"></param>
    /// <param name="ellipsis"></param>
    /// <returns></returns>
    public static string GetSubString(string value, int length, bool ellipsis = false)
    {
        if (string.IsNullOrEmpty(value)) return value;
        if (value.Length > length)
        {
            value = value.Substring(0, length);
            if (ellipsis) value += "...";
        }

        return value;
    }

    /// <summary>
    ///     字符串转指定类型数组
    /// </summary>
    /// <param name="value"></param>
    /// <param name="split"></param>
    /// <returns></returns>
    public static T[] SplitToArray<T>(string value, char split)
    {
        var arr = value.Split(new[] { split.ToString() }, StringSplitOptions.RemoveEmptyEntries).CastSuper<T>()
            .ToArray();
        return arr;
    }


    /// <summary>
    ///     判断是否有交集
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="list1"></param>
    /// <param name="list2"></param>
    /// <returns></returns>
    public static bool IsArrayIntersection<T>(List<T> list1, List<T> list2)
    {
        var t = list1.Distinct().ToList();

        var exceptArr = t.Except(list2).ToList();

        if (exceptArr.Count < t.Count)
            return true;
        return false;
    }

    /// <summary>
    ///     姓名敏感处理
    /// </summary>
    /// <param name="fullName">姓名</param>
    /// <returns>脱敏后的姓名</returns>
    public static string SetSensitiveName(string fullName)
    {
        if (string.IsNullOrEmpty(fullName)) return string.Empty;

        var familyName = fullName.Substring(0, 1);
        var end = fullName.Substring(fullName.Length - 1, 1);
        var name = string.Empty;
        //长度为2
        if (fullName.Length <= 2) name = familyName + "*";
        //长度大于2
        else if (fullName.Length >= 3) name = familyName.PadRight(fullName.Length - 1, '*') + end;
        return name;
    }

    /// <summary>
    ///     身份证脱敏
    /// </summary>
    /// <param name="idCardNo">身份证号</param>
    /// <returns>脱敏后的身份证号</returns>
    public static string SetSensitiveIdCardNo(string idCardNo)
    {
        if (string.IsNullOrEmpty(idCardNo)
            || (idCardNo.Length != 15 && idCardNo.Length != 18)) return idCardNo;

        var begin = idCardNo.Substring(0, 6);
        var middle = idCardNo.Substring(6, 8);
        var end = idCardNo.Substring(14, idCardNo.Length - 14);

        var card = string.Empty;
        card = begin + "********" + end;
        return card;
    }


    /// <summary>
    ///     通过 身份证 号码 获取 生日
    /// </summary>
    /// <param name="idCardNo"></param>
    /// <returns></returns>
    public static DateTime GetBirthdayByIdCardNo(string idCardNo)
    {
        try
        {
            var birthYear = idCardNo.Substring(6, 4);
            var birthMonth = idCardNo.Substring(10, 2);
            var birthDay = idCardNo.Substring(12, 2);

            var birthDate = DateTime.Parse($"{birthYear}-{birthMonth}-{birthDay}");

            return birthDate;
        }
        catch
        {
            return Convert.ToDateTime("1900-01-01");
        }
    }


    /// <summary>
    ///     生成签字 后面的 背景字
    /// </summary>
    /// <param name="word"></param>
    /// <returns></returns>
    public static SKBitmap MakeSignWord(string word)
    {
        var width = 200; // 图片宽度
        var height = 200; // 图片高度

        var lightRed = new SKColor(255, 102, 102); // 枣红色
        using (var bitmap = new SKBitmap(width, height))
        {
            using (var canvas = new SKCanvas(bitmap))
            {
                // 填充背景色
                canvas.Clear(SKColors.White);

                // 绘制田字格
                using (var gridPaint = new SKPaint())
                {
                    gridPaint.Color = lightRed;
                    gridPaint.StrokeWidth = 1;

                    for (var x = width / 2; x < width; x += width / 2) canvas.DrawLine(x, 0, x, height, gridPaint);

                    for (var y = height / 2; y < height; y += height / 2) canvas.DrawLine(0, y, width, y, gridPaint);
                }

                using (var gridPaint = new SKPaint())
                {
                    gridPaint.Color = lightRed;
                    gridPaint.StrokeWidth = 1;
                    gridPaint.Style = SKPaintStyle.Stroke;


                    // 设置虚线效果
                    gridPaint.PathEffect = SKPathEffect.CreateDash(new float[] { 10, 10 }, 0);

                    // 从左上角到右下角的虚线
                    canvas.DrawLine(0, 0, width, height, gridPaint);

                    // 从右上角到左下角的虚线
                    canvas.DrawLine(width, 0, 0, height, gridPaint);
                }

                var fileInfo = new FileInfo(Assembly.GetEntryAssembly().Location);
                // 加载自定义字体文件
                var typeface = SKTypeface.FromFile(fileInfo.DirectoryName + "/font/ChillKai.ttf");

                // 绘制字符
                using (var paint = new SKPaint())
                {
                    paint.Color = SKColors.LightGray; // 字的颜色，淡灰色
                    paint.IsAntialias = true;

                    // 使用新的字体API
                    paint.Typeface = typeface;
                    paint.TextSize = 200;

                    float textWidth = paint.MeasureText(word);
                    var x = (width - textWidth) / 2;
                    var y = (height - paint.TextSize) / 2 + paint.TextSize * 0.8f; // 调整垂直位置

                    canvas.DrawText(word, x, y, paint);
                }
            }

            return bitmap.Copy();
        }
    }

    #region 自动生成编号

    /// <summary>
    ///     表示全局唯一标识符 (GUID)。
    /// </summary>
    /// <returns></returns>
    public static string GuId()
    {
        return IDGen.NextID().ToString();
    }

    /// <summary>
    ///     自动生成编号  201008251145409865
    /// </summary>
    /// <returns></returns>
    public static string CreateNo()
    {
        var random = new Random();
        var strRandom = random.Next(1000, 10000).ToString(); //生成编号 
        var code = DateTime.Now.ToString("yyyyMMddHHmmss") + strRandom; //形如
        return code;
    }


    /// <summary>
    ///     生成随机数字字符串
    /// </summary>
    /// <param name="Length">字符串的长度</param>
    /// <returns></returns>
    public static string CreateRandomNo(int Length)
    {
        var random = new Random();

        var code = "";
        for (var i = 0; i < Length; i++) code += random.Next(0, 9).ToString();

        return code;
    }


    /// <summary>
    ///     生成随机数字字符串
    /// </summary>
    /// <param name="Length">字符串的长度</param>
    /// <returns></returns>
    public static string CreateRandomWord(int Length)
    {
        var words = "!#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[]^_`abcdefghijklmnopqrstuvwxyz{|}~"
            .ToCharArray();
        //char[] words = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".ToCharArray();
        var random = new Random();

        var code = "";
        for (var i = 0; i < Length; i++)
            code += words[random.Next(0, words.Length)].ToString();

        return code;
    }

    #endregion

}