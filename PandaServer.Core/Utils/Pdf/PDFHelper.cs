using System;
using SkiaSharp;
using Spire.Pdf;

namespace PandaServer.Core.Utils.Pdf;

public class PDFHelper
{
    public static byte[] ConvertDocumentToImage(PdfDocument pdfDocument)
    {
        // 遍历所有页面，将每页转换为图片
        var pageImages = new List<SKBitmap>();
        int totalHeight = 0;
        int maxWidth = 0;

        for (int i = 0; i < pdfDocument.Pages.Count; i++)
        {
            using var pageStream = pdfDocument.SaveAsImage(i);
            using var skImage = SKImage.FromEncodedData(pageStream);
            var bitmap = SKBitmap.FromImage(skImage);

            pageImages.Add(bitmap);
            totalHeight += bitmap.Height;
            maxWidth = Math.Max(maxWidth, bitmap.Width);
        }

        // 创建拼接后的大图
        using var finalBitmap = new SKBitmap(maxWidth, totalHeight);
        using var canvas = new SKCanvas(finalBitmap);

        // 设置白色背景
        canvas.Clear(SKColors.White);

        int currentY = 0;
        foreach (var pageImage in pageImages)
        {
            canvas.DrawBitmap(pageImage, 0, currentY);
            currentY += pageImage.Height;
            pageImage.Dispose();
        }

        // 将拼接后的图片保存到内存流
        using var ms = new MemoryStream();
        using var image = SKImage.FromBitmap(finalBitmap);
        using var data = image.Encode(SKEncodedImageFormat.Png, 100);
        data.SaveTo(ms);

        return ms.ToArray();
    }
}
