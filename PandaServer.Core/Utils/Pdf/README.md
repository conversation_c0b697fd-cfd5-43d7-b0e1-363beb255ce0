# PDF 文本写入工具使用说明

## 概述

`PdfTextWriter` 是一个用于在 PDF 文档中写入文本的工具类，基于 Spire.PDF 库开发。该工具类解决了常见的字体初始化问题，并提供了丰富的文本绘制功能。

## 主要修复问题

### 1. 字体初始化问题
**原问题：**
```csharp
// 错误的初始化方式
var font = new PdfTrueTypeFont(fontPath, fontSize, PdfFontStyle.Regular);
```

**修复后：**
```csharp
// 正确的初始化方式
using var fontStream = File.OpenRead(fontPath);
var font = new PdfTrueTypeFont(fontStream, fontSize, PdfFontStyle.Regular);
```

### 2. 字体文件路径问题
- 使用 `Path.Combine()` 确保跨平台兼容性
- 添加字体文件存在性检查
- 提供默认字体路径获取方法

### 3. 坐标单位转换
- 提供像素到 PDF 坐标的转换
- 提供毫米到 PDF 坐标的转换
- 提供英寸到 PDF 坐标的转换
- **新增：比例坐标支持**

## 基本使用方法

### 1. 简单文本绘制
```csharp
using var pdfWriter = new PdfTextWriter();

// 检查字体文件是否存在
if (!pdfWriter.IsFontFileExists())
{
    throw new Exception("字体文件不存在");
}

// 绘制文本
pdfWriter.DrawText(page, "Hello World", 100, 100, 12);
```

### 2. 比例坐标绘制（推荐用于合同模板）
```csharp
using var pdfWriter = new PdfTextWriter();

// 使用比例坐标绘制文本（坐标值在0-1之间）
pdfWriter.DrawTextWithRatio(page, "姓名", 0.1f, 0.2f, 12);  // 页面10%宽度，20%高度位置
pdfWriter.DrawTextWithRatio(page, "身份证号", 0.1f, 0.3f, 12);  // 页面10%宽度，30%高度位置
pdfWriter.DrawTextWithRatio(page, "中心文本", 0.5f, 0.5f, 16);  // 页面中心位置
```

### 3. 在合同模板中使用
```csharp
public async Task<PdfDocument> MakeContract(JxContractTemplateEntity template, JxStudentOutPut student)
{
    var doc = new PdfDocument();
    // ... 加载 PDF 模板
    
    using var pdfWriter = new PdfTextWriter();
    
    foreach (var control in controls)
    {
        var page = doc.Pages[control.PageNo - 1];
        var fieldValue = await GetFieldValue(control.Field, student, jxCompany);
        
        if (!string.IsNullOrEmpty(fieldValue))
        {
            // 使用比例坐标方法，control.X 和 control.Y 是0-1之间的比例值
            pdfWriter.DrawTextWithRatio(page, fieldValue, control.X, control.Y, control.FontSize);
        }
    }
    
    return doc;
}
```

## 高级功能

### 1. 文本对齐
```csharp
var format = new PdfStringFormat(PdfTextAlignment.Center, PdfVerticalAlignment.Middle);
pdfWriter.DrawText(page, "居中文本", x, y, width, fontSize, format);
```

### 2. 比例坐标对齐
```csharp
var format = new PdfStringFormat(PdfTextAlignment.Center, PdfVerticalAlignment.Middle);
pdfWriter.DrawTextWithRatio(page, "居中文本", 0.5f, 0.5f, 0.8f, fontSize, format);
```

### 3. 多行文本
```csharp
pdfWriter.DrawMultilineText(page, "多行文本内容", x, y, width, height, fontSize);
```

### 4. 旋转文本
```csharp
pdfWriter.DrawRotatedText(page, "旋转文本", x, y, fontSize, 45); // 45度旋转
```

### 5. 坐标转换
```csharp
// 像素转 PDF 坐标
float x = PdfTextWriter.ConvertPixelToPoint(100); // 100像素

// 毫米转 PDF 坐标
float y = PdfTextWriter.ConvertMmToPoint(25.4f); // 25.4毫米

// 英寸转 PDF 坐标
float z = PdfTextWriter.ConvertInchToPoint(1.0f); // 1英寸
```

## 比例坐标系统

### 优势
1. **跨页面兼容性：** 比例坐标在不同尺寸的页面上都能保持相对位置
2. **易于调整：** 坐标值在0-1之间，便于理解和调整
3. **响应式布局：** 自动适应不同页面尺寸

### 使用场景
- **合同模板：** 在不同尺寸的合同模板上保持文本位置一致
- **表单填写：** 在标准化的表单上填写信息
- **水印添加：** 在页面特定比例位置添加水印

### 坐标说明
- **X坐标：** 0.0 = 页面左边缘，1.0 = 页面右边缘
- **Y坐标：** 0.0 = 页面顶部，1.0 = 页面底部
- **常用位置：**
  - 左上角：(0.1, 0.1)
  - 右上角：(0.9, 0.1)
  - 左下角：(0.1, 0.9)
  - 右下角：(0.9, 0.9)
  - 中心：(0.5, 0.5)

## 字体文件要求

### 支持的字体格式
- TrueType (.ttf)
- OpenType (.otf)

### 默认字体路径
- 应用程序目录下的 `font/simsun.ttf`
- 可通过构造函数指定自定义字体路径

### 字体文件检查
```csharp
var pdfWriter = new PdfTextWriter();
if (!pdfWriter.IsFontFileExists())
{
    // 处理字体文件不存在的情况
}
```

## 测试方法

### 1. 通过 API 测试
```http
GET /Jx/Student/Contract/JxContractTemplate/testPdfTextWriting
```

### 2. 代码测试
```csharp
var result = await _jxContractTemplateService.TestPdfTextWriting();
Console.WriteLine(result);
```

## 常见问题解决

### 1. 字体文件不存在
**错误信息：** "字体文件不存在"
**解决方案：** 确保 `font/simsun.ttf` 文件存在于应用程序目录中

### 2. 文本不显示
**可能原因：**
- 坐标超出页面范围
- 字体大小过小
- 文本颜色与背景色相同

**解决方案：**
- 检查坐标值是否合理
- 调整字体大小
- 指定明确的文本颜色

### 3. 中文显示乱码
**解决方案：** 确保使用支持中文的字体文件（如 simsun.ttf）

### 4. 比例坐标不准确
**可能原因：**
- 比例值超出0-1范围
- 页面尺寸获取失败

**解决方案：**
- 确保比例值在0-1之间
- 检查页面是否正确加载

## 性能优化建议

1. **字体缓存：** 对于大量文本绘制，考虑实现字体缓存机制
2. **批量操作：** 尽量批量处理文本绘制操作
3. **比例坐标：** 优先使用比例坐标，提高跨页面兼容性

## 注意事项

1. **线程安全：** Spire.PDF 对象不能跨线程共享
2. **异步操作：** 在异步方法中使用时注意资源管理
3. **内存管理：** 大量 PDF 操作时注意内存使用情况
4. **字体版权：** 确保使用的字体文件具有合法的使用权限 