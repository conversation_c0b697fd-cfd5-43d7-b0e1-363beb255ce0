using System.Drawing;
using System.Reflection;
using Spire.Pdf;
using Spire.Pdf.Graphics;

namespace PandaServer.Core.Utils.Pdf;

/// <summary>
/// PDF 文本写入工具类
/// </summary>
public class PdfTextWriter : IDisposable
{
    private readonly string _fontPath;

    public PdfTextWriter(string fontPath = null)
    {
        _fontPath = fontPath ?? GetDefaultFontPath();
    }

    /// <summary>
    /// 获取默认字体路径
    /// </summary>
    private static string GetDefaultFontPath()
    {
        var assembly = Assembly.GetEntryAssembly();
        if (assembly == null)
            throw Oops.Bah("系统当前无法获取相关服务器信息，生成 PDF 失败");

        var fileInfo = new FileInfo(assembly.Location);
        var fontDir = Path.Combine(fileInfo.DirectoryName, "font");

        // 尝试多个中文字体文件，按优先级排序
        var fontFiles = new[]
        {
            "msyh.ttf",        // 微软雅黑
            "simsun.ttf",      // 宋体
            "FangSong.ttf",    // 仿宋
            "STSong-Light.ttf", // 华文宋体
            "song.ttf"         // 宋体
        };

        foreach (var fontFile in fontFiles)
        {
            var fontPath = Path.Combine(fontDir, fontFile);
            if (File.Exists(fontPath))
            {
                // Console.WriteLine($"使用字体文件: {fontPath}");
                return fontPath;
            }
        }

        // 如果都找不到，返回默认的 simsun.ttf
        var defaultPath = Path.Combine(fontDir, "simsun.ttf");
        Console.WriteLine($"未找到中文字体文件，使用默认路径: {defaultPath}");
        return defaultPath;
    }

    /// <summary>
    /// 检查字体文件是否存在
    /// </summary>
    public bool IsFontFileExists()
    {
        var exists = File.Exists(_fontPath);
        return exists;
    }

    /// <summary>
    /// 获取字体实例
    /// </summary>
    private PdfTrueTypeFont GetFont(float fontSize)
    {
        if (!File.Exists(_fontPath))
        {
            Console.WriteLine($"字体文件不存在: {_fontPath}");

            // 尝试重新获取字体路径
            var newFontPath = GetDefaultFontPath();
            if (File.Exists(newFontPath))
            {
                return new PdfTrueTypeFont(newFontPath, fontSize);
            }

            throw new FileNotFoundException($"无法找到可用的中文字体文件");
        }

        try
        {
            var font = new PdfTrueTypeFont(_fontPath, fontSize);
            return font;
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"字体创建失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 在PDF页面上绘制文本
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="text">要绘制的文本</param>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="fontSize">字体大小</param>
    /// <param name="color">文本颜色</param>
    /// <param name="style">字体样式</param>
    public void DrawText(PdfPageBase page, string text, float x, float y, float fontSize, PdfBrush color = null, PdfFontStyle style = PdfFontStyle.Regular)
    {
        if (string.IsNullOrEmpty(text) || page == null)
            return;

        var font = GetFont(fontSize);
        var brush = color ?? PdfBrushes.Black;

        // 确保字体正确加载
        if (font == null)
        {
            return;
        }

        page.Canvas.DrawString(text, font, brush, x, y);
    }

    /// <summary>
    /// 在PDF页面上绘制文本（支持对齐方式）
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="text">要绘制的文本</param>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="width">文本区域宽度</param>
    /// <param name="fontSize">字体大小</param>
    /// <param name="alignment">对齐方式</param>
    /// <param name="color">文本颜色</param>
    /// <param name="style">字体样式</param>
    public void DrawText(PdfPageBase page, string text, float x, float y, float width, float fontSize, PdfStringFormat alignment = null, PdfBrush color = null, PdfFontStyle style = PdfFontStyle.Regular)
    {
        if (string.IsNullOrEmpty(text) || page == null)
            return;

        using var font = GetFont(fontSize);
        var brush = color ?? PdfBrushes.Black;
        var format = alignment ?? new PdfStringFormat(PdfTextAlignment.Left, PdfVerticalAlignment.Top);

        var rect = new RectangleF(x, y, width, fontSize + 2);
        page.Canvas.DrawString(text, font, brush, rect, format);
    }

    /// <summary>
    /// 绘制多行文本
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="text">要绘制的文本</param>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="width">文本区域宽度</param>
    /// <param name="height">文本区域高度</param>
    /// <param name="fontSize">字体大小</param>
    /// <param name="color">文本颜色</param>
    /// <param name="style">字体样式</param>
    public void DrawMultilineText(PdfPageBase page, string text, float x, float y, float width, float height,
        float fontSize, PdfBrush color = null, PdfFontStyle style = PdfFontStyle.Regular)
    {
        if (string.IsNullOrEmpty(text) || page == null)
            return;

        using var font = GetFont(fontSize);
        var brush = color ?? PdfBrushes.Black;
        var format = new PdfStringFormat(PdfTextAlignment.Left, PdfVerticalAlignment.Top);
        format.LineSpacing = fontSize * 1.2f; // 行间距

        var rect = new RectangleF(x, y, width, height);
        page.Canvas.DrawString(text, font, brush, rect, format);
    }

    /// <summary>
    /// 绘制旋转文本
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="text">要绘制的文本</param>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="fontSize">字体大小</param>
    /// <param name="angle">旋转角度（度）</param>
    /// <param name="color">文本颜色</param>
    /// <param name="style">字体样式</param>
    public void DrawRotatedText(PdfPageBase page, string text, float x, float y, float fontSize, float angle,
        PdfBrush color = null, PdfFontStyle style = PdfFontStyle.Regular)
    {
        if (string.IsNullOrEmpty(text) || page == null)
            return;

        using var font = GetFont(fontSize);

        // 保存当前状态
        page.Canvas.Save();

        // 移动到指定位置并旋转
        page.Canvas.TranslateTransform(x, y);
        page.Canvas.RotateTransform(angle);

        // 绘制文本
        page.Canvas.DrawString(text, font, PdfBrushes.Black, 0, 0);

        // 恢复状态
        page.Canvas.Restore();
    }

    /// <summary>
    /// 获取文本尺寸
    /// </summary>
    /// <param name="text">文本内容</param>
    /// <param name="fontSize">字体大小</param>
    /// <param name="style">字体样式</param>
    /// <returns>文本尺寸</returns>
    public SizeF GetTextSize(string text, float fontSize, PdfFontStyle style = PdfFontStyle.Regular)
    {
        if (string.IsNullOrEmpty(text))
            return SizeF.Empty;

        using var font = GetFont(fontSize);
        return font.MeasureString(text);
    }

    /// <summary>
    /// 像素坐标转换为PDF坐标（point）
    /// </summary>
    /// <param name="pixelValue">像素值</param>
    /// <param name="dpi">DPI值，默认96</param>
    /// <returns>PDF坐标值</returns>
    public static float ConvertPixelToPoint(float pixelValue, float dpi = 96f)
    {
        return pixelValue * 72f / dpi;
    }

    /// <summary>
    /// 毫米坐标转换为PDF坐标（point）
    /// </summary>
    /// <param name="mmValue">毫米值</param>
    /// <returns>PDF坐标值</returns>
    public static float ConvertMmToPoint(float mmValue)
    {
        return mmValue * 2.83465f; // 1mm = 2.83465pt
    }

    /// <summary>
    /// 英寸坐标转换为PDF坐标（point）
    /// </summary>
    /// <param name="inchValue">英寸值</param>
    /// <returns>PDF坐标值</returns>
    public static float ConvertInchToPoint(float inchValue)
    {
        return inchValue * 72f; // 1inch = 72pt
    }

    /// <summary>
    /// 在PDF页面上绘制文本（使用比例坐标）
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="text">要绘制的文本</param>
    /// <param name="ratioX">X坐标比例（0-1之间）</param>
    /// <param name="ratioY">Y坐标比例（0-1之间）</param>
    /// <param name="fontSize">字体大小</param>
    /// <param name="color">文本颜色</param>
    /// <param name="style">字体样式</param>
    public void DrawTextWithRatio(PdfPageBase page, string text, float ratioX, float ratioY, float fontSize,
        PdfBrush color = null, PdfFontStyle style = PdfFontStyle.Regular)
    {
        if (string.IsNullOrEmpty(text) || page == null)
            return;

        // 将比例坐标转换为实际坐标
        var actualX = ratioX * page.Size.Width;
        var actualY = ratioY * page.Size.Height;

        // 调用原有的绘制方法
        DrawText(page, text, actualX, actualY, fontSize, color, style);
    }

    /// <summary>
    /// 在PDF页面上绘制文本（使用比例坐标，支持对齐方式）
    /// </summary>
    /// <param name="page">PDF页面</param>
    /// <param name="text">要绘制的文本</param>
    /// <param name="ratioX">X坐标比例（0-1之间）</param>
    /// <param name="ratioY">Y坐标比例（0-1之间）</param>
    /// <param name="ratioWidth">文本区域宽度比例（0-1之间）</param>
    /// <param name="fontSize">字体大小</param>
    /// <param name="alignment">对齐方式</param>
    /// <param name="color">文本颜色</param>
    /// <param name="style">字体样式</param>
    public void DrawTextWithRatio(PdfPageBase page, string text, float ratioX, float ratioY, float ratioWidth, float fontSize,
        PdfStringFormat alignment = null, PdfBrush color = null, PdfFontStyle style = PdfFontStyle.Regular)
    {
        if (string.IsNullOrEmpty(text) || page == null)
            return;

        // 将比例坐标转换为实际坐标
        var actualX = ratioX * page.Size.Width;
        var actualY = ratioY * page.Size.Height;
        var actualWidth = ratioWidth * page.Size.Width;

        // 调用原有的绘制方法
        DrawText(page, text, actualX, actualY, actualWidth, fontSize, alignment, color, style);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        // 目前没有需要释放的资源
    }
}