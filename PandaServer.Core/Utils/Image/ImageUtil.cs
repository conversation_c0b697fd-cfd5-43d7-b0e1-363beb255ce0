﻿using System.Drawing;
using System.Drawing.Drawing2D;
using SkiaSharp;

namespace PandaServer.Core.Utils;

public static class ImageUtil
{
    /// <summary>
    ///     图片转换成base64
    /// </summary>
    /// <param name="img"></param>
    /// <returns></returns>
    public static string ImgToBase64String(SKImage img)
    {
        try
        {
            var p = img.Encode(SKEncodedImageFormat.Png, 100);
            var arr = p.ToArray();
            return Convert.ToBase64String(arr);
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    ///     图片转换成base64
    /// </summary>
    /// <param name="bmp"></param>
    /// <returns></returns>
    public static string ImgToBase64String(this SKBitmap bmp)
    {
        try
        {
            var img = SKImage.FromBitmap(bmp);
            var p = img.Encode(SKEncodedImageFormat.Png, 100);
            var arr = p.<PERSON>();
            return Convert.ToBase64String(arr);
        }
        catch
        {
            return string.Empty;
        }
    }


    /// <summary>
    ///     base64转bitmap
    /// </summary>
    /// <param name="base64string"></param>
    /// <returns></returns>
    public static Bitmap GetBitmapFromBase64(this string base64string)
    {
        var b = Convert.FromBase64String(base64string);
        var ms = new MemoryStream(b);
        var bitmap = new Bitmap(ms);
        ms.Close();
        return bitmap;
    }


    /// <summary>
    ///     base64转bitmap
    /// </summary>
    /// <param name="base64string"></param>
    /// <returns></returns>
    public static SKBitmap GetSKBitmapFromBase64(this string base64string)
    {
        var b = Convert.FromBase64String(base64string);
        var bitmap = SKBitmap.Decode(b);
        return bitmap;
    }

    /// <summary>
    ///     base64转image格式
    /// </summary>
    /// <param name="base64string"></param>
    /// <returns></returns>
    public static string ToImageBase64(this string base64string)
    {
        return "data:image/png;base64," + base64string;
    }


    /// <summary>
    ///     重新修改尺寸
    /// </summary>
    /// <param name="imgToResize">图片</param>
    /// <param name="size">尺寸</param>
    /// <returns></returns>
    public static Bitmap ResizeImage(Image imgToResize, Size size)
    {
        //获取图片宽度
        var sourceWidth = imgToResize.Width;
        //获取图片高度
        var sourceHeight = imgToResize.Height;

        float nPercent = 0;
        float nPercentW = 0;
        float nPercentH = 0;
        //计算宽度的缩放比例
        nPercentW = size.Width / (float)sourceWidth;
        //计算高度的缩放比例
        nPercentH = size.Height / (float)sourceHeight;

        if (nPercentH < nPercentW)
            nPercent = nPercentH;
        else
            nPercent = nPercentW;
        //期望的宽度
        var destWidth = (int)(sourceWidth * nPercent);
        //期望的高度
        var destHeight = (int)(sourceHeight * nPercent);

        var b = new Bitmap(destWidth, destHeight);
        var g = Graphics.FromImage(b);
        g.InterpolationMode = InterpolationMode.HighQualityBicubic;
        //绘制图像
        g.DrawImage(imgToResize, 0, 0, destWidth, destHeight);
        g.Dispose();
        return b;
    }

    /// <summary>
    ///     Resize图片
    /// </summary>
    /// <param name="bmp">原始Bitmap </param>
    /// <param name="newW">新的宽度</param>
    /// <param name="newH">新的高度</param>
    /// <returns>处理以后的图片</returns>
    public static Bitmap ResizeImage(this Bitmap bmp, int newW, int newH)
    {
        try
        {
            var b = new Bitmap(newW, newH);
            var g = Graphics.FromImage(b);
            // 插值算法的质量
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.DrawImage(bmp, new Rectangle(0, 0, newW, newH), new Rectangle(0, 0, bmp.Width, bmp.Height),
                GraphicsUnit.Pixel);
            g.Dispose();
            return b;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     Resize图片
    /// </summary>
    /// <param name="bmp">原始Bitmap </param>
    /// <param name="newW">新的宽度</param>
    /// <param name="newH">新的高度</param>
    /// <returns>处理以后的图片</returns>
    public static SKBitmap ResizeImage(this SKBitmap bmp, int newW, int newH)
    {
        try
        {
            var info = new SKImageInfo(newW, newH);
            var resized = bmp.Resize(info, SKFilterQuality.High);
            return resized;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     获取缩略图
    /// </summary>
    /// <param name="bmp"></param>
    /// <param name="w">宽</param>
    /// <param name="h">高</param>
    /// <returns></returns>
    public static Image GetPicThumbnail(this Bitmap bmp, int w, int h)
    {
        try
        {
            var thumbnail = bmp.GetThumbnailImage(w, h, () => false, IntPtr.Zero);
            return thumbnail;
        }
        catch (Exception)
        {
            return null;
        }
    }

    /// <summary>
    ///     获取缩略图
    /// </summary>
    /// <param name="bmp"></param>
    /// <param name="w">宽</param>
    /// <param name="h">高</param>
    /// <returns></returns>
    public static SKImage GetPicThumbnail(this SKBitmap bmp, int w, int h)
    {
        try
        {
            var resized = bmp.Resize(new SKImageInfo(w, h), SKFilterQuality.Medium);
            if (resized is null) return null;
            var image = SKImage.FromBitmap(resized);
            return image;
        }
        catch (Exception)
        {
            return null;
        }
    }

    /// <summary>
    /// Adds text to an image at the top or bottom with auto-sizing based on image width
    /// </summary>
    /// <param name="image">Source image</param>
    /// <param name="text">Text to add</param>
    /// <param name="fontFamily">Font family name</param>
    /// <param name="textColor">Color of the text</param>
    /// <param name="padding">Padding between text and image edge</param>
    /// <param name="position">Position of text - true for top, false for bottom</param>
    /// <returns>New image with text added</returns>
    public static SKBitmap AddTextToImage(this SKBitmap image, string text, string fontFamily, SKColor textColor, int padding, bool position = false)
    {
        try
        {
            if (string.IsNullOrEmpty(text))
                return image;

            // Use default font if fontFamily is empty
            string actualFontFamily = fontFamily;
            if (string.IsNullOrEmpty(fontFamily))
            {
                var fileInfo = new System.IO.FileInfo(System.Reflection.Assembly.GetEntryAssembly().Location);
                actualFontFamily = fileInfo.DirectoryName + "/font/msyh.ttf";
            }

            // Calculate optimal font size based on image width
            float fontSize = CalculateOptimalFontSize(text, image.Width, actualFontFamily);

            // Measure text size to determine if we need a wider canvas
            var typeface = SKTypeface.FromFile(actualFontFamily);

            using (SKPaint textPaint = new SKPaint())
            {
                textPaint.Typeface = typeface;
                textPaint.TextSize = fontSize;
                textPaint.Color = textColor;
                textPaint.IsAntialias = true;
                textPaint.TextAlign = SKTextAlign.Center;

                float textWidth = textPaint.MeasureText(text);
                float textHeight = textPaint.TextSize; // Use font size as approximate height

                // Determine final image dimensions
                // Only expand width if text is significantly wider than the image
                int resultWidth = image.Width;
                if (textWidth > image.Width * 1.2f) // Only widen if text is more than 20% wider than image
                {
                    resultWidth = Math.Max(image.Width, (int)Math.Ceiling(textWidth) + 2 * padding);
                }
                int resultHeight = image.Height + (int)Math.Ceiling(textHeight) + 2 * padding;

                // Create a copy of the original image with adjusted dimensions
                SKBitmap resultImage = new SKBitmap(resultWidth, resultHeight);

                using (SKCanvas canvas = new SKCanvas(resultImage))
                {
                    // Clear canvas with white background
                    canvas.Clear(SKColors.White);

                    // Calculate image position
                    int imageX = (resultWidth - image.Width) / 2;

                    if (position) // Top position
                    {
                        // Draw text then image
                        float textY = padding + textHeight * 0.8f; // Approximate text baseline
                        // Center text over the image area, not just the canvas width
                        float textX = imageX + (image.Width / 2f);
                        canvas.DrawText(text, textX, textY, textPaint);
                        canvas.DrawBitmap(image, imageX, (int)Math.Ceiling(textHeight) + padding);
                    }
                    else // Bottom position
                    {
                        // Draw image then text
                        canvas.DrawBitmap(image, imageX, 0);
                        float textY = image.Height + padding + textHeight * 0.8f; // Approximate text baseline
                        // Center text under the image area, not just the canvas width
                        float textX = imageX + (image.Width / 2f);
                        canvas.DrawText(text, textX, textY, textPaint);
                    }
                }
                return resultImage;
            }
        }
        catch (Exception)
        {
            return image;
        }
    }

    /// <summary>
    /// Calculate the optimal font size to fit text within a given width
    /// </summary>
    /// <param name="text">Text to measure</param>
    /// <param name="maxWidth">Maximum width available</param>
    /// <param name="fontFamily">Font family name</param>
    /// <returns>Optimal font size</returns>
    private static float CalculateOptimalFontSize(string text, int maxWidth, string fontFamily)
    {
        const float startSize = 72f; // Start with large font size
        const float minSize = 10f;   // Minimum acceptable font size
        float fontSize = startSize;
        float minFontSize = minSize; // Declare at method level to be accessible in return statement

        // Use default font if fontFamily is empty
        string actualFontFamily = fontFamily;
        if (string.IsNullOrEmpty(fontFamily))
        {
            var fileInfo = new System.IO.FileInfo(System.Reflection.Assembly.GetEntryAssembly().Location);
            actualFontFamily = fileInfo.DirectoryName + "/font/msyh.ttf";
        }
        var typeface = SKTypeface.FromFile(actualFontFamily);

        using (var paint = new SKPaint())
        {
            paint.Typeface = typeface;
            // For short text, we can use a larger font that doesn't necessarily fill the width
            if (text.Length < 10)
            {
                // For short text, aim for 60-70% of max width
                float targetWidthRatio = 0.65f;

                // Binary search to find optimal font size
                minFontSize = minSize; // Reassign to be clear
                float maxFontSize = startSize;

                while (maxFontSize - minFontSize > 1f)
                {
                    fontSize = (minFontSize + maxFontSize) / 2f;
                    paint.TextSize = fontSize;

                    float textWidth = paint.MeasureText(text);

                    if (textWidth > maxWidth * targetWidthRatio)
                    {
                        maxFontSize = fontSize;
                    }
                    else
                    {
                        minFontSize = fontSize;
                    }
                }
            }
            else
            {
                // For longer text, use more of the available width (90%)
                float targetWidthRatio = 0.9f;

                // Binary search to find optimal font size
                minFontSize = minSize; // Reassign to be clear
                float maxFontSize = startSize;

                while (maxFontSize - minFontSize > 1f)
                {
                    fontSize = (minFontSize + maxFontSize) / 2f;
                    paint.TextSize = fontSize;

                    float textWidth = paint.MeasureText(text);

                    if (textWidth > maxWidth * targetWidthRatio)
                    {
                        maxFontSize = fontSize;
                    }
                    else
                    {
                        minFontSize = fontSize;
                    }
                }
            }
        }

        return minFontSize; // Use the largest size that fits
    }
}