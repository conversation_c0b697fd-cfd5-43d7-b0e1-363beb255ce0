namespace PandaServer.Core.Utils;

using System;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Drawing;
using SixLabors.Fonts;
using System.IO;
using Path = System.IO.Path;

/// <summary>
/// 印章工具类
/// </summary>
public static class SealUtil
{
    /// <summary>
    /// 创建印章图片
    /// </summary>
    /// <param name="outerCircleText">外圈文字</param>
    /// <param name="interiorText">内部文字</param>
    /// <returns>生成的印章图片数据</returns>
    public static byte[] CreateSeal(string outerCircleText, string interiorText)
    {
        const int size = 400;
        using var image = new Image<SixLabors.ImageSharp.PixelFormats.Rgba32>(size, size);

        // 设置背景为白色
        image.Mutate(x => x.BackgroundColor(Color.White));

        // 绘制红色圆形边框
        var pen = new SolidPen(Color.Red, 8);
        var circle = new EllipsePolygon(size / 2, size / 2, size / 2 - 8);
        image.Mutate(x => x.Draw(pen, circle));

        // 绘制五角星
        DrawStar(image, size / 2, size / 2, 40);

        // 加载字体
        var fontCollection = new FontCollection();
        var fontPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "font", "SimSun.ttf");
        if (!File.Exists(fontPath))
        {
            throw new FileNotFoundException($"字体文件不存在: {fontPath}");
        }
        var fontFamily = fontCollection.Add(fontPath);
        var font = fontFamily.CreateFont(28);

        // 绘制中心文字
        var textOptions = new RichTextOptions(font)
        {
            Origin = new PointF(size / 2, size / 2 + 40),
            KerningMode = KerningMode.Standard
        };
        image.Mutate(x => x.DrawText(textOptions, interiorText, Color.Red));

        // 绘制弧形文字
        DrawArcText(image, outerCircleText + "专用章", size / 2, size / 2, size / 2 - 30, 180, 360, font);

        // 将图片转换为字节数组
        using var ms = new MemoryStream();
        image.SaveAsPng(ms);
        return ms.ToArray();
    }

    private static void DrawStar(Image<SixLabors.ImageSharp.PixelFormats.Rgba32> image, int centerX, int centerY, int outerRadius)
    {
        var points = new PointF[10];
        var angle = -Math.PI / 2;
        for (var i = 0; i < 10; i++)
        {
            var radius = (i % 2 == 0) ? outerRadius : outerRadius * 0.4f;
            points[i] = new PointF(
                centerX + (float)(radius * Math.Cos(angle)),
                centerY + (float)(radius * Math.Sin(angle))
            );
            angle += Math.PI / 5;
        }

        var star = new Polygon(points);
        image.Mutate(x => x.Fill(Color.Red, star));
    }

    private static void DrawArcText(Image<SixLabors.ImageSharp.PixelFormats.Rgba32> image, string text, int centerX, int centerY, int radius, double startAngle, double endAngle, Font font)
    {
        var sweepAngle = endAngle - startAngle;
        var charAngle = sweepAngle / text.Length;

        for (var i = 0; i < text.Length; i++)
        {
            var angle = (startAngle + i * charAngle) * Math.PI / 180;
            var x = centerX + (float)(radius * Math.Cos(angle));
            var y = centerY + (float)(radius * Math.Sin(angle));

            var textOptions = new RichTextOptions(font)
            {
                Origin = new PointF(x, y),
                KerningMode = KerningMode.Standard
            };

            image.Mutate(x => x.DrawText(textOptions, text[i].ToString(), Color.Red));
        }
    }
}