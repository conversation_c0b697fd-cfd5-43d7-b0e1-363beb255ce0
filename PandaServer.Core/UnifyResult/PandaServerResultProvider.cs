using Furion.Authorization;
using Furion.UnifyResult;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using Furion.DataValidation;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace PandaServer.Core;

/// <summary>
///     规范化RESTful风格返回值
/// </summary>
[SuppressSniffer]
[UnifyModel(typeof(PandaServerResult<>))]
public class PandaServerResultProvider : IUnifyResultProvider
{
    /// <summary>
    ///     异常返回
    /// </summary>
    /// <param name="context"></param>
    /// <param name="metadata"></param>
    /// <returns></returns>
    public IActionResult OnException(ExceptionContext context, ExceptionMetadata metadata)
    {
        return new JsonResult(RESTfulResult(metadata.StatusCode, data: metadata.Data, errors: metadata.Errors));
    }

    /// <summary>
    ///     成功返回
    /// </summary>
    /// <param name="context"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    public IActionResult OnSucceeded(ActionExecutedContext context, object data)
    {
        return new JsonResult(RESTfulResult(StatusCodes.Status200OK, true, data));
    }

    /// <summary>
    ///     验证失败返回
    /// </summary>
    /// <param name="context"></param>
    /// <param name="metadata"></param>
    /// <returns></returns>
    public IActionResult OnValidateFailed(ActionExecutingContext context, ValidationMetadata metadata)
    {
        return new JsonResult(RESTfulResult(metadata.StatusCode ?? StatusCodes.Status400BadRequest, data: metadata.Data,
            errors: metadata.FirstErrorMessage ?? metadata.Message));
    }

    /// <summary>
    ///     状态码响应拦截
    /// </summary>
    /// <param name="context"></param>
    /// <param name="statusCode"></param>
    /// <param name="unifyResultSettings"></param>
    /// <returns></returns>
    public async Task OnResponseStatusCodes(HttpContext context, int statusCode, UnifyResultSettingsOptions unifyResultSettings = null)
    {
        // 设置响应状态码
        UnifyContext.SetResponseStatusCodes(context, statusCode, unifyResultSettings);

        switch (statusCode)
        {
            // 处理 401 状态码
            case StatusCodes.Status401Unauthorized:
                var result401 = new PandaServerResult<object>
                {
                    Code = statusCode,
                    Success = false,
                    Message = "会话超时，请重试",
                    Data = null,
                    Extras = UnifyContext.Take(),
                    Time = DateTime.Now
                };
                await context.Response.WriteAsJsonAsync(result401,
                    App.GetOptions<JsonOptions>()?.JsonSerializerOptions);
                break;
            // 处理 403 状态码
            case StatusCodes.Status403Forbidden:
                var result403 = new PandaServerResult<object>
                {
                    Code = statusCode,
                    Success = false,
                    Message = "禁止访问，没有权限",
                    Data = null,
                    Extras = UnifyContext.Take(),
                    Time = DateTime.Now
                };
                await context.Response.WriteAsJsonAsync(result403,
                    App.GetOptions<JsonOptions>()?.JsonSerializerOptions);
                break;
        }
    }

    /// <summary>
    /// 简化版的 RESTful 风格结果集
    /// </summary>
    /// <param name="code">状态码</param>
    /// <param name="message">消息</param>
    /// <returns></returns>
    public static dynamic RESTfulResult(int code, string message)
    {
        var result = new PandaServerResult<object>
        {
            Code = code,
            Success = code == StatusCodes.Status200OK,
            Message = string.IsNullOrEmpty(message) ? GetStatusCodeMessage(code) : message,
            Data = null,
            Extras = UnifyContext.Take(),
            Time = DateTime.Now
        };

        return result;
    }


    /// <summary>
    /// 简化版的 RESTful 风格结果集
    /// </summary>
    /// <param name="code">状态码</param>
    /// <param name="succeeded"></param>
    /// <param name="message">消息</param>
    /// <returns></returns>
    public static dynamic RESTfulResult(int code, bool succeeded, string message)
    {
        var result = new PandaServerResult<object>
        {
            Code = code,
            Success = succeeded,
            Message = string.IsNullOrEmpty(message) ? GetStatusCodeMessage(code) : message,
            Data = null,
            Extras = UnifyContext.Take(),
            Time = DateTime.Now
        };

        return result;
    }

    /// <summary>
    /// 获取状态码对应的默认消息
    /// </summary>
    private static string GetStatusCodeMessage(int statusCode)
    {
        return statusCode switch
        {
            StatusCodes.Status200OK => "请求成功",
            StatusCodes.Status401Unauthorized => "登录已过期，请重新登录",
            StatusCodes.Status403Forbidden => "禁止访问，没有权限",
            StatusCodes.Status400BadRequest => "请求参数错误",
            StatusCodes.Status404NotFound => "未找到资源",
            StatusCodes.Status500InternalServerError => "服务器内部错误",
            StatusCodes.Status502BadGateway => "网关错误",
            StatusCodes.Status503ServiceUnavailable => "服务不可用",
            StatusCodes.Status504GatewayTimeout => "网关超时",
            _ => "未知错误"
        };
    }

    /// <summary>
    ///     返回 RESTful 风格结果集
    /// </summary>
    /// <param name="statusCode">状态码</param>
    /// <param name="succeeded">是否成功</param>
    /// <param name="data">数据</param>
    /// <param name="errors">错误信息</param>
    /// <returns></returns>
    private static PandaServerResult<object> RESTfulResult(int statusCode, bool succeeded = default,
        object data = default, object errors = default)
    {
        string message;
        if (statusCode == StatusCodes.Status200OK)
        {
            message = (data != null && data.GetType() == typeof(string) && !string.IsNullOrEmpty(data.ToString()) && data.ToString()!.Length < 30)
                ? data.ToString()!
                : "请求成功!";
        }
        else
        {
            message = errors?.ToString() ?? GetErrorMessage(statusCode);
        }

        return new PandaServerResult<object>
        {
            Code = statusCode,
            Success = succeeded,
            Message = message,
            Data = data,
            Extras = UnifyContext.Take(),
            Time = DateTime.Now
        };
    }

    /// <summary>
    /// 获取错误信息
    /// </summary>
    private static string GetErrorMessage(int statusCode)
    {
        return statusCode switch
        {
            400 => "请求参数错误",
            401 => "未授权",
            403 => "禁止访问",
            404 => "未找到资源",
            500 => "服务器内部错误",
            502 => "网关错误",
            503 => "服务不可用",
            504 => "网关超时",
            _ => "未知错误"
        };
    }

    /// <summary>
    /// JWT 授权异常返回值
    /// </summary>
    /// <param name="context"></param>
    /// <param name="metadata"></param>
    /// <returns></returns>
    public IActionResult OnAuthorizeException(DefaultHttpContext context, ExceptionMetadata metadata)
    {
        return new JsonResult(new PandaServerResult<object>
        {
            Code = StatusCodes.Status401Unauthorized,
            Success = false,
            Data = null,
            Message = "未经授权的访问",
            Extras = UnifyContext.Take(),
            Time = DateTime.Now
        });
    }

}