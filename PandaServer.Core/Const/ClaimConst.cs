﻿namespace PandaServer.Core;

/// <summary>
///     授权用户常量
/// </summary>
public class ClaimConst
{
    /// <summary>
    ///     用户Id
    /// </summary>
    public const string UserId = "UserId";

    /// <summary>
    ///     学员Id
    /// </summary>
    public const string StudentId = "StudentId";

    // /// <summary>
    // ///     学生租户Id
    // /// </summary>
    // public const string StudentTenantId = "StudentTenantId";

    // /// <summary>
    // ///     学生租户名称
    // /// </summary>
    // public const string StudentTenantName = "StudentTenantName";

    /// <summary>
    ///     关联考场的 Id
    /// </summary>
    public const string FieldId = "FieldId";

    /// <summary>
    /// 用户的 系统的 Id
    /// </summary>
    public const string UserSysId = "UserSysId";


    /// <summary>
    /// 头像
    /// </summary>
    public const string Avatar = "Avatar";


    /// <summary>
    ///     用户 电话
    /// </summary>
    public const string Phone = "Phone";

    /// <summary>
    ///     账号
    /// </summary>
    public const string Account = "Account";

    /// <summary>
    ///     真实姓名
    /// </summary>
    public const string RealName = "RealName";

    /// <summary>
    ///     公司名称  UserEntity  标记的 公司的名称
    /// </summary>
    public const string CompanyName = "CompanyName";

    /// <summary>
    /// 
    /// </summary>
    public const string CompanyId = "CompanyId";


    /// <summary>
    ///     昵称
    /// </summary>
    public const string NickName = "NickName";

    /// <summary>
    ///     租户的编号
    /// </summary>
    public const string TenantId = "TenantId";

    /// <summary>
    ///     租户所在的城市
    /// </summary>
    public const string CityId = "CityId";

    /// <summary>
    ///     账号类型
    /// </summary>
    public const string IsSuperAdmin = "IsSuperAdmin";

    /// <summary>
    ///     是否是租户的管理员
    /// </summary>
    public const string IsTenantAdmin = "IsTenantAdmin";

    /// <summary>
    ///     租户的 公司名称
    /// </summary>
    public const string TenantName = "TenantName";

    /// <summary>
    ///     租户的 公司类型
    /// </summary>
    public const string TenantType = "TenantType";

    /// <summary>
    ///     租户的 公司类型
    /// </summary>
    public const string IdCard = "IdCard";

    /// <summary>
    ///     Plus 服务的结束时间
    /// </summary>
    public const string PlusEndTime = "PlusEndTime";

    /// <summary>
    ///     系统服务的结束时间
    /// </summary>
    public const string ServiceEndTime = "ServiceEndTime";

    /// <summary>
    ///     微信用户 OpenId
    /// </summary>
    public const string OpenId = "OpenId";

    /// <summary>
    ///     微信用户 UnionId
    /// </summary>
    public const string UnionId = "UnionId";
}
