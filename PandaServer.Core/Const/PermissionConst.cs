namespace PandaServer.Core;

/// <summary>
/// 系统权限常量
/// </summary>
public class PermissionConst
{
    /// <summary>
    /// 审批流程-学员信息-修改审核
    /// </summary>
    public const int STUDENT_MODIFY_AUDIT = 141;

    /// <summary>
    /// 财务支出-支出编辑
    /// </summary>
    public const int FINANCE_EXPENDITURE_EDIT = 696;

    /// <summary>
    /// 财务入账-收入编辑
    /// </summary>
    public const int FINANCE_INCOME_EDIT = 695;

    /// <summary>
    /// 财务支出-支出删除
    /// </summary>
    public const int FINANCE_EXPENDITURE_DELETE = 694;

    /// <summary>
    /// 财务入账-收入删除
    /// </summary>
    public const int FINANCE_INCOME_DELETE = 693;

    /// <summary>
    /// 审批流程-学员财务-退款审核
    /// </summary>
    public const int FINANCE_REFUND_AUDIT = 682;

    /// <summary>
    /// 审批流程-学员财务-退学审核
    /// </summary>
    public const int STUDENT_STATUS_CHANGE_AUDIT = 681;

    /// <summary>
    /// 审批流程-学员财务-优惠审核
    /// </summary>
    public const int STUDENT_DISCOUNT_AUDIT = 680;

    /// <summary>
    /// 审批流程-学员财务-退学审核
    /// </summary>
    public const int STUDENT_QUIT_AUDIT = 684;

    /// <summary>
    /// 审批流程-学员财务-退款审核
    /// </summary>
    public const int STUDENT_REFUND_AUDIT = 685;

    /// <summary>
    /// 审批流程-学员分车-分配审核
    /// </summary>
    public const int STUDENT_CAR_ASSIGNMENT_AUDIT = 660;

    /// <summary>
    /// 审批流程-学员分车-修改审核
    /// </summary>
    public const int STUDENT_CAR_ASSIGNMENT_MODIFY_AUDIT = 662;

    /// <summary>
    /// 系统权限-账号信息-编辑
    /// </summary>
    public const int ACCOUNT_INFO_CHANGE_AUDIT = 664;

    /// <summary>
    /// 系统权限-账号权限-编辑
    /// </summary>
    public const int ACCOUNT_ROLE_CHANGE_AUDIT = 665;

    /// <summary>
    /// 系统权限-账号价格-编辑
    /// </summary>
    public const int ACCOUNT_PRICE_CHANGE_AUDIT = 667;

    /// <summary>
    /// 系统权限-账号关联车辆-编辑
    /// </summary>
    public const int ACCOUNT_CAR_CHANGE_AUDIT = 670;

    /// <summary>
    /// 审批流程-学员财务-考场退款审核
    /// </summary>
    public const int FINANCE_EXAM_REFUND_AUDIT = 690;

    /// <summary>
    /// 系统权限-用户分类编辑
    /// </summary>
    public const int SYSTEM_USER_CATEGORY_EDIT = 692;

    /// <summary>
    /// 修改协单人
    /// </summary>
    public const int STUDENT_MODIFY_USERSALE3 = 704;

    /// <summary>
    /// 修改责任人
    /// </summary>
    public const int STUDENT_MODIFY_USERSALE2 = 703;

    /// <summary>
    /// 修改推荐人
    /// </summary>
    public const int STUDENT_MODIFY_USERSALE1 = 702;

    /// <summary>
    /// 查看收入
    /// </summary>
    public const int FINANCE_PAY_VIEW = 705;

    /// <summary>
    /// 学员管理-学员搜索-学员财务-挂账
    /// </summary>
    public const int STUDENT_COST = 654;

    /// <summary>
    /// 学员管理-学员搜索-学员财务-支出
    /// </summary>
    public const int STUDENT_EXPENSE = 655;

    /// <summary>
    /// 查看支出
    /// </summary>
    public const int FINANCE_COST_VIEW = 656;

    /// <summary>
    /// 删除成绩
    /// </summary>
    public const int STUDENT_EXAM_DELETE = 706;

    /// <summary>
    /// 学员管理-学员搜索-学员财务-缴费
    /// </summary>
    public const int STUDENT_PAY = 617;

    /// <summary>
    /// 学员管理-学员搜索-学员财务-报名
    /// </summary>
    public const int STUDENT_CREATE = 616;

    /// <summary>
    /// 返现审核-考试预约-模拟返现-考场财务-返现审核
    /// </summary>
    public const int CASH_COUPON_AUDIT = 710;

    /// <summary>
    /// 员工添加-学员管理-学员搜索-学员财务-员工添加
    /// </summary>
    public const int USER_ADD = 712;

    /// <summary>
    /// 批量支出挂账并缴费
    /// </summary>
    public const int STUDENT_BATCH_COST_AND_PAY = 720;

    /// <summary>
    /// 批量支出挂账
    /// </summary>
    public const int STUDENT_BATCH_COST = 719;

    /// <summary>
    /// 批量已挂账项目平账
    /// </summary>
    public const int STUDENT_BATCH_COST_BALANCE = 718;

    /// <summary>
    /// 批量收入挂账并缴费
    /// </summary>
    public const int STUDENT_BATCH_INCOME_AND_PAY = 717;

    /// <summary>
    /// 批量收入挂账
    /// </summary>
    public const int STUDENT_BATCH_INCOME = 716;

    /// <summary>
    /// 批量设置学费并缴费
    /// </summary>
    public const int STUDENT_BATCH_SET_TUITION_AND_PAY = 715;

    /// <summary>
    /// 批量设置学费
    /// </summary>
    public const int STUDENT_BATCH_SET_TUITION = 714;

    /// <summary>
    /// 考场财务导出权限
    /// </summary>
    public const int EXAM_APPOINTMENT_EXAM_FINANCE_EXPORT = 723;

    /// <summary>
    /// 模拟返现导出权限
    /// </summary>
    public const int EXAM_APPOINTMENT_SIMULATE_CASHBACK_EXAM_FINANCE_EXPORT = 724;

    /// <summary>
    /// 消券列表导出权限
    /// </summary>
    public const int EXAM_APPOINTMENT_EXAM_FINANCE_STUDY_EXPORT = 725;

    /// <summary>
    /// 考试开闸
    /// </summary>
    public const int EXAM_SITE_OPEN_GATE = 679;

    /// <summary>
    /// 账号审核
    /// </summary>
    public const int ACCOUNT_REVIEW = 727;

    #region 微信小程序权限

    /// <summary>
    /// 车辆监控报警
    /// </summary>
    public const int WECHAT_VEHICLE_MONITOR = 183;

    /// <summary>
    /// 扫码管理
    /// </summary>
    public const int WECHAT_SCAN_MANAGEMENT = 489;

    /// <summary>
    /// 账户充值
    /// </summary>
    public const int WECHAT_ACCOUNT_RECHARGE = 497;

    /// <summary>
    /// 招生统计
    /// </summary>
    public const int WECHAT_ENROLLMENT_STATISTICS = 295;

    /// <summary>
    /// 财务统计
    /// </summary>
    public const int WECHAT_FINANCIAL_STATISTICS = 296;

    /// <summary>
    /// 考试统计
    /// </summary>
    public const int WECHAT_EXAM_STATISTICS = 297;

    /// <summary>
    /// 微信日报消息推送
    /// </summary>
    public const int WECHAT_DAILY_MESSAGE_PUSH = 298;

    /// <summary>
    /// 人脸识别
    /// </summary>
    public const int WECHAT_FACE_RECOGNITION = 458;

    /// <summary>
    /// 场地登记
    /// </summary>
    public const int WECHAT_SITE_REGISTRATION = 492;

    /// <summary>
    /// 新增账户审核
    /// </summary>
    public const int WECHAT_NEW_ACCOUNT_AUDIT = 467;

    /// <summary>
    /// 考试名单
    /// </summary>
    public const int WECHAT_EXAM_LIST = 480;

    /// <summaryimage.png>
    /// 考场财务
    /// </summary>
    public const int WECHAT_EXAM_FINANCE = 481;

    /// <summary>
    /// 现场退费
    /// </summary>
    public const int WECHAT_ONSITE_REFUND = 482;

    /// <summary>
    /// 返现管理
    /// </summary>
    public const int WECHAT_CASHBACK_MANAGEMENT = 483;

    /// <summary>
    /// 用户积分消费
    /// </summary>
    public const int WECHAT_USER_POINTS_CONSUMPTION = 548;

    #endregion
}