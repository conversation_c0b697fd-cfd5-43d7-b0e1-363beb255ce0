﻿using Newtonsoft.Json;

namespace PandaServer.Core;

/// <summary>
///     全局分页查询输入参数
/// </summary>
public class BasePageInput : IValidatableObject
{
    /// <summary>
    ///     当前页码
    /// </summary>
    [DataValidation(ValidationTypes.Numeric)]
    [JsonProperty("current")]
    public virtual int Current { get; set; } = 1;

    /// <summary>
    ///     每页条数
    /// </summary>
    // [Range(1, 100, ErrorMessage = "页码容量超过最大限制")]
    [DataValidation(ValidationTypes.Numeric)]
    [JsonProperty("pageSize")]
    public virtual int Size { get; set; } = 15;

    /// <summary>
    ///     排序字段
    /// </summary>
    [JsonProperty("SortField")]
    public virtual string SortField { get; set; }

    /// <summary>
    ///     排序方式，升序：ascend；降序：descend"
    /// </summary>
    [JsonProperty("SortOrder")]
    public virtual string SortOrder { get; set; }

    /// <summary>
    ///     关键字
    /// </summary>
    [StringLength(10000, ErrorMessage = "关键字词的总长度不能超过 10000")]
    public virtual string SearchKey { get; set; } = "";

    /// <summary>
    ///     关键字
    /// </summary>
    [StringLength(10000, ErrorMessage = "关键字词的总长度不能超过 10000")]
    public virtual string KeyWord { get; set; } = "";

    // /// <summary>
    // /// 关键字（即将废弃，请使用 SearchKey）
    // /// </summary>
    // [Obsolete("此属性即将废弃，请使用 SearchKey 替代")]
    // public virtual string KeyWord { get; set; } = "";

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        //配合小诺排序参数
        if (SortOrder == "descend")
            SortOrder = "desc";
        else if (SortOrder == "ascend")
            SortOrder = "asc";

        if (!string.IsNullOrEmpty(SearchKey))
            SearchKey = SearchKey.Trim();

        if (!string.IsNullOrEmpty(KeyWord))
            SearchKey = KeyWord.Trim();

        if (!string.IsNullOrEmpty(SortField))
        {
            //分割排序字段
            var fields = SortField.Split(" ");
            if (fields.Length > 1)
                yield return new ValidationResult("排序字段错误", new[]
                {
                    nameof(SortField)
                });
        }
    }

    /// <summary>
    ///     设计的 报表的 DesignId
    /// </summary>
    public Guid DesignId { get; set; } = Guid.Empty;

    /// <summary>
    ///     报表的 TableId
    /// </summary>
    public Guid TableId { get; set; } = Guid.Empty;

    /// <summary>
    /// 
    /// </summary>
    public Guid TenantId { get; set; } = Guid.Empty;
}