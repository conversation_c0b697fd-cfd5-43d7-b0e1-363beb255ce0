﻿namespace PandaServer.Core;

/// <summary>
///     主键Id输入参数
/// </summary>
public class BaseIdInput : BasePageInput
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [IdNotNull(ErrorMessage = "Id不能为空")]
    [DataValidation(ValidationTypes.GUID_OR_UUID)]
    public virtual Guid Id { get; set; }


    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public virtual long SysId { get; set; } = 0;


    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public virtual Guid OldId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <value></value>
    public virtual Guid ParentId { get; set; }


    /// <summary>
    ///     需要用到 租户的 编号
    /// </summary>
    public Guid TenantId { get; set; } = Guid.Empty;


    /// <summary>
    ///     分表的表 传入这个创建时间  SplitTable 开始时间就用这个
    /// </summary>
    public DateTime CreateTime { get; set; } = Convert.ToDateTime("2000-01-01");

    /// <summary>
    /// </summary>
    public string xm { get; set; }

    /// <summary>
    /// </summary>
    public string sfzmhm { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public string code { get; set; }
}