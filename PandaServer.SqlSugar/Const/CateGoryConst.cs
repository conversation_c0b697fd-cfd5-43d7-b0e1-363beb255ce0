﻿namespace PandaServer.SqlSugar;

/// <summary>
///     分类常量
/// </summary>
public class CateGoryConst
{
    #region Mqtt配置

    /// <summary>
    ///     MQTT配置
    /// </summary>
    public const string Config_MQTT_BASE = "MQTT_BASE";

    #endregion Mqtt配置

    #region 系统配置

    /// <summary>
    ///     系统基础
    /// </summary>
    public const string Config_SYS_BASE = "SYS_BASE";

    /// <summary>
    ///     业务定义
    /// </summary>
    public const string Config_BIZ_DEFINE = "BIZ_DEFINE";

    /// <summary>
    ///     文件-本地
    /// </summary>
    public const string Config_FILE_LOCAL = "FILE_LOCAL";

    /// <summary>
    ///     文件-MINIO
    /// </summary>
    public const string Config_FILE_MINIO = "FILE_MINIO";

    #endregion 系统配置

    #region 驾校相关的配置

    /// <summary>
    ///     人脸识别的距离  长沙驾协也用这个做认证
    /// </summary>
    public const string Config_JX_FACEMAXDISTANCE = "JX_FACEMAXDISTANCE";


    /// <summary>
    ///     科一初考退费
    /// </summary>
    public const string Config_JX_SUBJECT1_FIRST_EXAM_REFUND = "JX_SUBJECT1_FIRST_EXAM_REFUND";

    /// <summary>
    ///     科一初考退费成本类型ID
    /// </summary>
    public const string Config_JX_SUBJECT1_FIRST_EXAM_REFUND_COSTTYPEID = "JX_SUBJECT1_FIRST_EXAM_REFUND_COSTTYPEID";

    /// <summary>
    ///     科一补考退费
    /// </summary>
    public const string Config_JX_SUBJECT1_RETAKE_EXAM_REFUND = "JX_SUBJECT1_RETAKE_EXAM_REFUND";

    /// <summary>
    ///     科一补考退费成本类型ID
    /// </summary>
    public const string Config_JX_SUBJECT1_RETAKE_EXAM_REFUND_COSTTYPEID = "JX_SUBJECT1_RETAKE_EXAM_REFUND_COSTTYPEID";

    /// <summary>
    ///     科二初考退费
    /// </summary>
    public const string Config_JX_SUBJECT2_FIRST_EXAM_REFUND = "JX_SUBJECT2_FIRST_EXAM_REFUND";

    /// <summary>
    ///     科二初考退费成本类型ID
    /// </summary>
    public const string Config_JX_SUBJECT2_FIRST_EXAM_REFUND_COSTTYPEID = "JX_SUBJECT2_FIRST_EXAM_REFUND_COSTTYPEID";

    /// <summary>
    ///     科二补考退费
    /// </summary>
    public const string Config_JX_SUBJECT2_RETAKE_EXAM_REFUND = "JX_SUBJECT2_RETAKE_EXAM_REFUND";

    /// <summary>
    ///     科二补考退费成本类型ID
    /// </summary>
    public const string Config_JX_SUBJECT2_RETAKE_EXAM_REFUND_COSTTYPEID = "JX_SUBJECT2_RETAKE_EXAM_REFUND_COSTTYPEID";

    /// <summary>
    ///     科三初考退费
    /// </summary>
    public const string Config_JX_SUBJECT3_FIRST_EXAM_REFUND = "JX_SUBJECT3_FIRST_EXAM_REFUND";

    /// <summary>
    ///     科三初考退费成本类型ID
    /// </summary>
    public const string Config_JX_SUBJECT3_FIRST_EXAM_REFUND_COSTTYPEID = "JX_SUBJECT3_FIRST_EXAM_REFUND_COSTTYPEID";

    /// <summary>
    ///     科三补考退费
    /// </summary>
    public const string Config_JX_SUBJECT3_RETAKE_EXAM_REFUND = "JX_SUBJECT3_RETAKE_EXAM_REFUND";

    /// <summary>
    ///     科三补考退费成本类型ID
    /// </summary>
    public const string Config_JX_SUBJECT3_RETAKE_EXAM_REFUND_COSTTYPEID = "JX_SUBJECT3_RETAKE_EXAM_REFUND_COSTTYPEID";

    /// <summary>
    ///     初考可退班别ID列表
    /// </summary>
    public const string Config_JX_FIRST_EXAM_JX_CLASS_IDS = "JX_FIRST_EXAM_JX_CLASS_IDS";

    /// <summary>
    ///     补考可退班别ID列表
    /// </summary>
    public const string Config_JX_SECOND_EXAM_JX_CLASS_IDS = "JX_SECOND_EXAM_JX_CLASS_IDS";

    /// <summary>
    ///     制证费金额
    /// </summary>
    public const string Config_JX_MAKE_CARD_FEE = "JX_MAKE_CARD_FEE";

    /// <summary>
    ///     制证费挂账支出费用类型ID
    /// </summary>
    public const string Config_JX_MAKE_CARD_FEE_COST_TYPE_ID = "JX_MAKE_CARD_FEE_COST_TYPE_ID";

    /// <summary>
    ///     银行转账截图数量
    /// </summary>
    public const string Config_JX_BANK_SCREENSHOT_COUNT = "JX_BANK_SCREENSHOT_COUNT";

    /// <summary>
    ///     银行转账PDF数量
    /// </summary>
    public const string Config_JX_BANK_SCREEN_PDF_COUNT = "JX_BANK_SCREEN_PDF_COUNT";

    /// <summary>
    ///     银行转账内容
    /// </summary>
    public const string Config_JX_BANK_SCREEN_CONTENT = "JX_BANK_SCREEN_CONTENT";

    /// <summary>
    ///     是否允许学员线上申请退款
    /// </summary>
    public const string Config_JX_ENABLE_ONLINE_APPLICATION = "JX_ENABLE_ONLINE_APPLICATION";

    /// <summary>
    ///     退款方式配置
    /// </summary>
    public const string Config_JX_REFUND_METHOD = "JX_REFUND_METHOD";

    /// <summary>
    ///     结算账户
    /// </summary>
    public const string Config_JX_REFUND_COMPUTER_ACCOUNT_ID = "JX_COMPUTER_ACCOUNT_ID";

    /// <summary>
    ///     必选介绍人
    /// </summary>
    public const string Config_JX_MUSTSALEID = "JX_MUSTSALEID";

    /// <summary>
    ///     显示介绍人
    /// </summary>
    public const string Config_JX_SHOWSALEID = "JX_SHOWSALEID";

    /// <summary>
    ///     必须绑定电脑
    /// </summary>
    public const string Config_JX_MUSTMAC = "JX_MUSTMAC";


    /// <summary>
    ///     身份证 的正面在下
    /// </summary>
    public const string Config_JX_IDCARDDOWN = "JX_IDCARDDOWN";


    /// <summary>
    ///     关闭业务信息
    /// </summary>
    public const string Config_JX_CLOSEBUSINESS = "JX_CLOSEBUSINESS";

    /// <summary>
    ///     收款跟着推荐人的店面走
    /// </summary>
    public const string Config_JX_SALEJXDEPTPAY = "JX_SALEJXDEPTPAY";

    /// <summary>
    ///     可以修改学费
    /// </summary>
    public const string Config_JX_MODIFYTUITION = "JX_MODIFYTUITION";

    /// <summary>
    ///     新录进去的学员的状态
    /// </summary>
    public const string Config_JX_CREATESTATUS = "JX_CREATESTATUS";

    /// <summary>
    ///     默认的报名的时候 学费的  费用类型
    /// </summary>
    public const string Config_JX_TUITIONCOSTTYPEID = "JX_TUITIONCOSTTYPEID";

    /// <summary>
    ///     默认的报名的时候 学费补缴的  费用类型
    /// </summary>
    public const string Config_JX_ADDTUITIONCOSTTYPEID = "JX_ADDTUITIONCOSTTYPEID";

    /// <summary>
    ///     录入以后 不能修改 任何 财务信息
    /// </summary>
    public const string Config_JX_CREATEDFORBIDDENMODIFY = "JX_CREATEDFORBIDDENMODIFY";

    /// <summary>
    ///     付款以后 不能修改 任何 财务信息
    /// </summary>
    public const string Config_JX_AFTERPAYFORBIDDENMODIFY = "JX_AFTERPAYFORBIDDENMODIFY";

    /// <summary>
    ///     录入以后第二天 不能修改 任何 财务信息
    /// </summary>
    public const string Config_JX_AFTERDAYFORBIDDENMODIFY = "JX_AFTERDAYFORBIDDENMODIFY";

    /// <summary>
    ///     录入的时候 可以修改学费信息
    /// </summary>
    public const string Config_JX_CREATEMODIFYTUITION = "JX_CREATEMODIFYTUITION";

    /// <summary>
    ///     付款账户 跟随着推荐人的账户 走
    /// </summary>
    public const string Config_JX_ACCOUNTFOLLOWSALEUSER = "JX_ACCOUNTFOLLOWSALEUSER";

    /// <summary>
    ///     付款账户 跟随着缴费店面走
    /// </summary>
    public const string Config_JX_ACCOUNTFOLLOWPAYJXDEPT = "JX_ACCOUNTFOLLOWPAYJXDEPT";

    /// <summary>
    ///     挂账的时候 可以修改金额
    /// </summary>
    public const string Config_JX_MODIFYADDSHOULDPAYMOENY = "JX_MODIFYADDSHOULDPAYMOENY";

    /// <summary>
    ///     约车方式  Car  或者  Coach
    /// </summary>
    public const string Config_JX_ORDERCARMETHOD = "Config_JX_ORDERCARMETHOD";


    /// <summary>
    ///     打印的票据的 上面
    /// </summary>
    public const string Config_JX_PRINTER_TOP = "Config_JX_PRINTER_TOP";

    /// <summary>
    ///     打印的票据的 底部
    /// </summary>
    public const string Config_JX_PRINTER_BOTTOM = "Config_JX_PRINTER_BOTTOM";

    /// <summary>
    ///     打印的票据的 左侧
    /// </summary>
    public const string Config_JX_PRINTER_LEFT = "Config_JX_PRINTER_LEFT";

    /// <summary>
    ///     打印的票据的 右侧
    /// </summary>
    public const string Config_JX_PRINTER_RIGHT = "Config_JX_PRINTER_RIGHT";

    /// <summary>
    ///     打印的票据的 不打印学员的手机号码
    /// </summary>
    public const string Config_JX_PRINTER_NO_PHONE = "Config_JX_PRINTER_NO_PHONE";

    /// <summary>
    ///     打印票据的 纸张
    /// </summary>
    public const string Config_JX_PRINTER_PAPER = "Config_JX_PRINTER_PAPER";

    /// <summary>
    ///     打印票据的 印章
    /// </summary>
    public const string Config_JX_PRINTER_STAMP = "Config_JX_PRINTER_STAMP";

    /// <summary>
    ///     收款账号的 规则
    /// </summary>
    public const string Config_JX_PAYID_RULE = "Config_JX_PAYID_RULE";

    /// <summary>
    ///     更新考试 关联的数据来源
    /// </summary>
    public const string Config_JX_UPDATE_BY_JXMCS = "Config_JX_UPDATE_BY_JXMCS";

    /// <summary>
    ///     报名的时候必须要选择训练场地
    /// </summary>
    public const string Config_JX_MUST_SELECT_JXFILEDID = "Config_JX_MUST_SELECT_JXFILEDID";

    /// <summary>
    ///     分车以后 要更新 还未考试的考试记录  就算有分车 也要更新
    /// </summary>
    public const string Config_JX_ASSIGN_COACH_UPDATE_RESULT_EXAM = "Config_JX_ASSIGN_COACH_UPDATE_RESULT_EXAM";

    /// <summary>
    ///     考试以后不能更新考试的教练信息
    /// </summary>
    public const string Config_JX_DONT_UPDATE_EXAM_COACH_AFTER_EXAM = "Config_JX_DONT_UPDATE_EXAM_COACH_AFTER_EXAM";

    /// <summary>
    ///     欠费不能分配教练
    /// </summary>
    public const string Config_JX_DEBT_FORBIDDEN_ASSIGN_COACH = "Config_JX_DEBT_FORBIDDEN_ASSIGN_COACH";


    /// <summary>
    ///     业务状态显示上一次的考试
    /// </summary>
    public const string Config_JX_YWZT_SHOW_LAST_EXAM = "Config_JX_YWZT_SHOW_LAST_EXAM";

    /// <summary>
    /// 手机上 不显示备注
    /// </summary>
    public const string Config_JX_WX_NO_SHOW_REMARK = "Config_JX_WX_NO_SHOW_REMARK";

    /// <summary>
    /// 手机上 不显示收入
    /// </summary>
    public const string Config_JX_WX_NO_SHOW_PAY = "Config_JX_WX_NO_SHOW_PAY";

    /// <summary>
    /// 手机上 不显示支出
    /// </summary>
    // public const string Config_JX_WX_NO_SHOW_COST = "Config_JX_WX_NO_SHOW_COST";

    /// <summary>
    /// 手机上 不显示场地
    /// </summary>
    public const string Config_JX_WX_NO_SHOW_JXFIELD = "Config_JX_WX_NO_SHOW_JXFIELD";

    /// <summary>
    /// 手机上 不显示科目一教练
    /// </summary>
    public const string Config_JX_WX_NO_SHOW_TEACH_ONE = "Config_JX_WX_NO_SHOW_TEACH_ONE";

    /// <summary>
    /// 手机上 不显示科目二教练
    /// </summary>
    public const string Config_JX_WX_NO_SHOW_TEACH_TWO = "Config_JX_WX_NO_SHOW_TEACH_TWO";

    /// <summary>
    /// 手机上 不显示科目三教练
    /// </summary>
    public const string Config_JX_WX_NO_SHOW_TEACH_THREE = "Config_JX_WX_NO_SHOW_TEACH_THREE";

    /// <summary>
    /// 手机上 不推送 约考信息
    /// </summary>
    public const string Config_JX_WX_NO_SEND_ORDER_EXAM = "Config_JX_WX_NO_SEND_ORDER_EXAM";

    /// <summary>
    /// 手机上 不推送 考试信息
    /// </summary>
    public const string Config_JX_WX_NO_SEND_EXAM_RESULT = "Config_JX_WX_NO_SEND_EXAM_RESULT";

    /// <summary>
    /// 手报名的时候不限制选择报名点
    /// </summary>
    public const string Config_JX_NO_LOCK_JXDEPT_CREATE_STUDENT = "Config_JX_NO_LOCK_JXDEPT_CREATE_STUDENT";

    /// <summary>
    ///    用户手机隐藏
    /// </summary>
    public const string Config_JX_USERPHONENOMASK = "Config_JX_USERPHONENOMASK";

    /// <summary>
    /// 默认不显示财务数据
    /// </summary>
    public const string Config_JX_HIDE_FINANCE_DATA = "Config_JX_HIDE_FINANCE_DATA";

    #endregion 驾校相关的配置

    #region 关系表

    /// <summary>
    ///     用户有哪些角色
    /// </summary>
    public const string Relation_SYS_USER_HAS_ROLE = "SYS_USER_HAS_ROLE";

    /// <summary>
    ///     角色有哪些资源
    /// </summary>
    public const string Relation_SYS_ROLE_HAS_RESOURCE = "SYS_ROLE_HAS_RESOURCE";

    /// <summary>
    ///     用户有哪些资源
    /// </summary>
    public const string Relation_SYS_USER_HAS_RESOURCE = "SYS_USER_HAS_RESOURCE";

    /// <summary>
    ///     角色有哪些权限
    /// </summary>
    public const string Relation_SYS_ROLE_HAS_PERMISSION = "SYS_ROLE_HAS_PERMISSION";

    /// <summary>
    ///     角色有哪些权限
    /// </summary>
    public const string Relation_SYS_USER_HAS_PERMISSION = "SYS_USER_HAS_PERMISSION";

    /// <summary>
    ///     用户工作台数据
    /// </summary>
    public const string Relation_SYS_USER_WORKBENCH_DATA = "SYS_USER_WORKBENCH_DATA";

    /// <summary>
    ///     用户日程数据
    /// </summary>
    public const string Relation_SYS_USER_SCHEDULE_DATA = "SYS_USER_SCHEDULE_DATA";

    /// <summary>
    ///     站内信与接收用户
    /// </summary>
    public const string Relation_MSG_TO_USER = "MSG_TO_USER";

    #endregion 关系表

    #region 数据范围

    /// <summary>
    ///     本人
    /// </summary>
    public const string SCOPE_SELF = "SCOPE_SELF";

    /// <summary>
    ///     所有
    /// </summary>
    public const string SCOPE_ALL = "SCOPE_ALL";

    /// <summary>
    ///     仅所属组织
    /// </summary>
    public const string SCOPE_ORG = "SCOPE_ORG";

    /// <summary>
    ///     所属组织及以下
    /// </summary>
    public const string SCOPE_ORG_CHILD = "SCOPE_ORG_CHILD";

    /// <summary>
    ///     自定义
    /// </summary>
    public const string SCOPE_ORG_DEFINE = "SCOPE_ORG_DEFINE";

    #endregion 数据范围

    #region 资源表

    /// <summary>
    ///     模块
    /// </summary>
    public const string Resource_MODULE = "MODULE";

    /// <summary>
    ///     菜单
    /// </summary>
    public const string Resource_MENU = "MENU";

    /// <summary>
    ///     单页
    /// </summary>
    public const string Resource_SPA = "SPA";

    /// <summary>
    ///     按钮
    /// </summary>
    public const string Resource_BUTTON = "BUTTON";

    #endregion 资源表

    #region 日志表

    /// <summary>
    ///     登录
    /// </summary>
    public const string Log_LOGIN = "LOGIN";

    /// <summary>
    ///     登出
    /// </summary>
    public const string Log_LOGOUT = "LOGOUT";

    /// <summary>
    ///     操作
    /// </summary>
    public const string Log_OPERATE = "OPERATE";

    /// <summary>
    ///     异常
    /// </summary>
    public const string Log_EXCEPTION = "EXCEPTION";

    #endregion 日志表

    #region 字典表

    /// <summary>
    ///     框架
    /// </summary>
    public const string Dict_FRM = "FRM";

    /// <summary>
    ///     业务
    /// </summary>
    public const string Dict_BIZ = "BIZ";

    #endregion 字典表

    #region 组织表

    /// <summary>
    ///     部门
    /// </summary>
    public const string Org_DEPT = "DEPT";

    /// <summary>
    ///     公司
    /// </summary>
    public const string Org_COMPANY = "COMPANY";

    #endregion 组织表

    #region 站内信表

    /// <summary>
    ///     通知
    /// </summary>
    public const string Message_INFORM = "INFORM";

    /// <summary>
    ///     公告
    /// </summary>
    public const string Message_NOTICE = "NOTICE";

    #endregion 站内信表


    #region 短信配置

    /// <summary>
    ///     接口所属公司
    /// </summary>
    public const string Config_SMS_Company = "Config_SMS_Company";

    /// <summary>
    ///     账号
    /// </summary>
    public const string Config_SMS_Account = "Config_SMS_Account";


    /// <summary>
    ///     密码
    /// </summary>
    public const string Config_SMS_PWD = "Config_SMS_PWD";


    /// <summary>
    ///     签名
    /// </summary>
    public const string Config_SMS_Sign = "Config_SMS_Sign";

    #endregion 短信配置
}