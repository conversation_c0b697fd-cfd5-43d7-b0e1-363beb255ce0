using System.ComponentModel;

namespace PandaServer.SqlSugar;

/// <summary>
///     系统配置常量
/// </summary>
public class DevConfigConst
{
    /// <summary>
    ///     登录验证码开关
    /// </summary>
    [Description("登录验证码开关")]
    public const string SYS_DEFAULT_CAPTCHA_OPEN = "SYS_DEFAULT_CAPTCHA_OPEN";

    /// <summary>
    ///     单用户登录开关
    /// </summary>
    [Description("单用户登录开关")]
    public const string SYS_DEFAULT_SINGLE_OPEN = "SYS_DEFAULT_SINGLE_OPEN";

    /// <summary>
    ///     默认用户密码
    /// </summary>
    [Description("默认用户密码")]
    public const string SYS_DEFAULT_PASSWORD = "SYS_DEFAULT_PASSWORD";

    /// <summary>
    ///     系统默认工作台
    /// </summary>
    [Description("系统默认工作台")]
    public const string SYS_DEFAULT_WORKBENCH_DATA = "SYS_DEFAULT_WORKBENCH_DATA";

    /// <summary>
    ///     湖南122 更新的数据会话
    /// </summary>
    [Description("湖南122 更新的数据会话")]
    public const string SYS_UPDATE_HN122_GOV_CN_SESSION = "SYS_UPDATE_HN122_GOV_CN_SESSION";

    /// <summary>
    ///     百度人脸识别的 V2 的 Secret Key
    /// </summary>
    [Description("全局百度 V2 人脸的 App Id")]
    public const string SYS_UPDATE_BAIDU_FACEV2_APP_ID = "SYS_UPDATE_BAIDU_FACEV2_APP_ID";

    /// <summary>
    ///     百度人脸识别的 V2 的 api Key
    /// </summary>
    [Description("全局百度 V2 人脸的 App Key")]
    public const string SYS_UPDATE_BAIDU_FACEV2_API_KEY = "SYS_UPDATE_BAIDU_FACEV2_API_KEY";

    /// <summary>
    ///     百度人脸识别的 V2 的 Secret Key
    /// </summary>
    [Description("全局百度 V2 人脸的 Secret Key")]
    public const string SYS_UPDATE_BAIDU_FACEV2_SECRET_KEY = "SYS_UPDATE_BAIDU_FACEV2_SECRET_KEY";

    /// <summary>
    ///     全局虹软 Android App Id
    /// </summary>
    [Description("全局虹软 Android App Id")]
    public const string SYS_ARC_SOFT_ANDROID_APP_ID = "SYS_ARC_SOFT_ANDROID_APP_ID";

    /// <summary>
    ///     全局虹软 Android App Key
    /// </summary>
    [Description("全局虹软 Android App Key")]
    public const string SYS_ARC_SOFT_ANDROID_APP_KEY = "SYS_ARC_SOFT_ANDROID_APP_KEY";



    /// <summary>
    ///     全局嘉联支付的SM2私钥
    /// </summary>
    [Description("全局嘉联支付的SM2私钥")]
    public const string SYS_JLPAY_SM2_PRIVATE_KEY = "SYS_JLPAY_SM2_PRIVATE_KEY";


    /// <summary>
    ///     全局嘉联支付的SM2公钥
    /// </summary>
    [Description("全局嘉联支付的SM2公钥")]
    public const string SYS_JLPAY_SM2_PUBLIC_KEY = "SYS_JLPAY_SM2_PUBLIC_KEY";


    /// <summary>
    ///     全局嘉联支付的v5公钥
    /// </summary>
    [Description("全局嘉联支付的v5公钥")]
    public const string SYS_JLPAY_V5_PUBLIC_KEY = "SYS_JLPAY_V5_PUBLIC_KEY";


    /// <summary>
    ///     全局嘉联支付的RSA私钥
    /// </summary>
    [Description("全局嘉联支付的RSA私钥")]
    public const string SYS_JLPAY_RSA_PRIVATE_KEY = "SYS_JLPAY_RSA_PRIVATE_KEY";


    /// <summary>
    ///     全局嘉联支付的RSA公钥
    /// </summary>
    [Description("全局嘉联支付的RSA公钥")]
    public const string SYS_JLPAY_RSA_PUBLIC_KEY = "SYS_JLPAY_RSA_PUBLIC_KEY";

    /// <summary>
    ///     全局嘉联支付的AppId
    /// </summary>
    [Description("全局嘉联支付的AppId")]
    public const string SYS_JLPAY_ORG_APPID = "SYS_JLPAY_ORG_APPID";


    /// <summary>
    ///     全局嘉联支付的服务商号
    /// </summary>
    [Description("全局嘉联支付的服务商号")]
    public const string SYS_JLPAY_ORG_CODE = "SYS_JLPAY_ORG_CODE";


    /// <summary>
    ///     PNADA DOOR 版本号
    /// </summary>
    [Description("PNADA DOOR 版本号")]
    public const string SYS_PANDADOOR_VERSIONCODE = "SYS_PANDADOOR_VERSIONCODE";


    /// <summary>
    ///     PNADA DOOR 版本号
    /// </summary>
    [Description("PNADA DOOR 版本名称")]
    public const string SYS_PANDADOOR_VERSIONNAME = "SYS_PANDADOOR_VERSIONNAME";


    /// <summary>
    ///     PNADA DOOR 版本更新地址
    /// </summary>
    [Description("PNADA DOOR 版本更新地址")]
    public const string SYS_PANDADOOR_UPDATE_URL = "SYS_PANDADOOR_UPDATE_URL";


    /// <summary>
    ///     PNADA DOOR V2 版本号
    /// </summary>
    [Description("PNADA DOOR V2 版本号")]
    public const string SYS_PANDADOOR_V2_VERSIONCODE = "SYS_PANDADOOR_V2_VERSIONCODE";


    /// <summary>
    ///     PNADA DOOR V2 版本名称
    /// </summary>
    [Description("PNADA DOOR V2 版本名称")]
    public const string SYS_PANDADOOR_V2_VERSIONNAME = "SYS_PANDADOOR_V2_VERSIONNAME";


    /// <summary>
    ///     PNADA DOOR V2 版本更新地址
    /// </summary>
    [Description("PNADA DOOR V2 版本更新地址")]
    public const string SYS_PANDADOOR_V2_UPDATE_URL = "SYS_PANDADOOR_V2_UPDATE_URL";

    #region MQTT

    /// <summary>
    ///     mqtt连接地址
    /// </summary>
    [Description("mqtt连接地址")]
    public const string MQTT_PARAM_URL = "MQTT_PARAM_URL";

    /// <summary>
    ///     mqtt连接用户名
    /// </summary>
    [Description("mqtt连接用户名")]
    public const string MQTT_PARAM_USERNAME = "MQTT_PARAM_USERNAME";

    /// <summary>
    ///     mqtt连接密码
    /// </summary>
    [Description("mqtt连接密码")]
    public const string MQTT_PARAM_PASSWORD = "MQTT_PARAM_PASSWORD";

    #endregion MQTT

    #region 存储引擎

    /// <summary>
    ///     windows系统本地目录
    /// </summary>
    [Description("windows系统本地目录")]
    public const string FILE_LOCAL_FOLDER_FOR_WINDOWS = "FILE_LOCAL_FOLDER_FOR_WINDOWS";

    /// <summary>
    ///     Unix系统本地目录
    /// </summary>
    [Description("Unix系统本地目录")]
    public const string FILE_LOCAL_FOLDER_FOR_UNIX = "FILE_LOCAL_FOLDER_FOR_UNIX";

    /// <summary>
    ///     MINIO文件AccessKey
    /// </summary>
    [Description("MINIO文件AccessKey")]
    public const string FILE_MINIO_ACCESS_KEY = "FILE_MINIO_ACCESS_KEY";

    /// <summary>
    ///     MINIO文件SecetKey
    /// </summary>
    [Description("MINIO文件SecetKey")]
    public const string FILE_MINIO_SECRET_KEY = "FILE_MINIO_SECRET_KEY";

    /// <summary>
    ///     MINIO文件EndPoint
    /// </summary>
    [Description("MINIO文件EndPoint")]
    public const string FILE_MINIO_END_POINT = "FILE_MINIO_END_POINT";

    /// <summary>
    ///     MINIO文件默认存储桶
    /// </summary>
    [Description("MINIO文件默认存储桶")]
    public const string FILE_MINIO_DEFAULT_BUCKET_NAME = "FILE_MINIO_DEFAULT_BUCKET_NAME";

    #endregion 存储引擎

    #region 三方配置

    /// <summary>
    ///     百度 人脸的 AppId
    /// </summary>
    [Description("百度 人脸的 AppId")]
    public const string THIRDCONFIG_BAIDU_FACE_APPID = "THIRDCONFIG_BAIDU_FACE_APPID";

    /// <summary>
    ///     百度 人脸的 Api Key
    /// </summary>
    [Description("百度 人脸的 Api Key")]
    public const string THIRDCONFIG_BAIDU_FACE_APIKEY = "THIRDCONFIG_BAIDU_FACE_APIKEY";

    /// <summary>
    ///     百度 人脸的 Secret Key
    /// </summary>
    [Description("百度 人脸的 Secret Key")]
    public const string THIRDCONFIG_BAIDU_FACE_SECRETKEY = "THIRDCONFIG_BAIDU_FACE_SECRETKEY";


    /// <summary>
    ///     短信 平台公司  Tinree
    /// </summary>
    [Description("短信 平台公司")]
    public const string THIRDCONFIG_SMS_COMPANY = "THIRDCONFIG_SMS_COMPANY";


    /// <summary>
    ///     短信 平台公司
    /// </summary>
    [Description("短信 平台公司 账号 或者 appid")]
    public const string THIRDCONFIG_SMS_ACCOUNT = "THIRDCONFIG_SMS_ACCOUNT";


    /// <summary>
    ///     短信 平台公司 密码 或者 appsecret
    /// </summary>
    [Description("短信 平台公司 密码 或者 appsecret")]
    public const string THIRDCONFIG_SMS_PWD = "THIRDCONFIG_SMS_PWD";


    /// <summary>
    ///     短信 发送的签名
    /// </summary>
    [Description("短信 发送的签名")]
    public const string THIRDCONFIG_SMS_SIGN = "THIRDCONFIG_SMS_SIGN";
    #endregion 三方配置

    #region E签宝配置
    /// <summary>
    ///     e签宝应用ID
    /// </summary>
    [Description("e签宝应用ID")]
    public const string THIRDCONFIG_ESIGN_APPID = "THIRDCONFIG_ESIGN_APPID";

    /// <summary>
    ///     e签宝应用Secret
    /// </summary>
    [Description("e签宝应用Secret")]
    public const string THIRDCONFIG_ESIGN_SECRET = "THIRDCONFIG_ESIGN_SECRET";

    /// <summary>
    ///     e签宝RSA公钥
    /// </summary>
    [Description("e签宝RSA公钥")]
    public const string THIRDCONFIG_ESIGN_RSA_PUBLIC = "THIRDCONFIG_ESIGN_RSA_PUBLIC";

    /// <summary>
    ///     e签宝AES密钥
    /// </summary>
    [Description("e签宝AES密钥")]
    public const string THIRDCONFIG_ESIGN_AES = "THIRDCONFIG_ESIGN_AES";

    /// <summary>
    ///     e签宝AES密钥Key
    /// </summary>
    [Description("e签宝AES密钥Key")]
    public const string THIRDCONFIG_ESIGN_AES_KEY = "THIRDCONFIG_ESIGN_AES_KEY";
    #endregion E签宝配置


    public static string GetDescription(Type type, string constantValue)
    {
        // 获取该类中的所有字段
        FieldInfo[] fields = type.GetFields(BindingFlags.Public | BindingFlags.Static);

        foreach (FieldInfo field in fields)
        {
            // 检查字段值是否匹配
            if (field.GetValue(null).ToString() == constantValue)
            {
                // 获取 Description 特性
                var attributes = (DescriptionAttribute[])field.GetCustomAttributes(typeof(DescriptionAttribute), false);
                if (attributes.Length > 0)
                {
                    return attributes[0].Description;
                }
            }
        }

        return null;
    }
}