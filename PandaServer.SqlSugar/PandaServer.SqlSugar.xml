<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PandaServer.SqlSugar</name>
    </assembly>
    <members>
        <member name="T:PandaServer.SqlSugar.CateGoryConst">
            <summary>
                分类常量
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_MQTT_BASE">
            <summary>
                MQTT配置
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_SYS_BASE">
            <summary>
                系统基础
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_BIZ_DEFINE">
            <summary>
                业务定义
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_FILE_LOCAL">
            <summary>
                文件-本地
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_FILE_MINIO">
            <summary>
                文件-MINIO
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_FACEMAXDISTANCE">
            <summary>
                人脸识别的距离  长沙驾协也用这个做认证
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT1_FIRST_EXAM_REFUND">
            <summary>
                科一初考退费
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT1_FIRST_EXAM_REFUND_COSTTYPEID">
            <summary>
                科一初考退费成本类型ID
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT1_RETAKE_EXAM_REFUND">
            <summary>
                科一补考退费
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT1_RETAKE_EXAM_REFUND_COSTTYPEID">
            <summary>
                科一补考退费成本类型ID
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT2_FIRST_EXAM_REFUND">
            <summary>
                科二初考退费
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT2_FIRST_EXAM_REFUND_COSTTYPEID">
            <summary>
                科二初考退费成本类型ID
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT2_RETAKE_EXAM_REFUND">
            <summary>
                科二补考退费
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT2_RETAKE_EXAM_REFUND_COSTTYPEID">
            <summary>
                科二补考退费成本类型ID
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT3_FIRST_EXAM_REFUND">
            <summary>
                科三初考退费
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT3_FIRST_EXAM_REFUND_COSTTYPEID">
            <summary>
                科三初考退费成本类型ID
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT3_RETAKE_EXAM_REFUND">
            <summary>
                科三补考退费
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SUBJECT3_RETAKE_EXAM_REFUND_COSTTYPEID">
            <summary>
                科三补考退费成本类型ID
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_FIRST_EXAM_JX_CLASS_IDS">
            <summary>
                初考可退班别ID列表
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SECOND_EXAM_JX_CLASS_IDS">
            <summary>
                补考可退班别ID列表
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_MAKE_CARD_FEE">
            <summary>
                制证费金额
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_MAKE_CARD_FEE_COST_TYPE_ID">
            <summary>
                制证费挂账支出费用类型ID
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_BANK_SCREENSHOT_COUNT">
            <summary>
                银行转账截图数量
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_BANK_SCREEN_PDF_COUNT">
            <summary>
                银行转账PDF数量
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_BANK_SCREEN_CONTENT">
            <summary>
                银行转账内容
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_ENABLE_ONLINE_APPLICATION">
            <summary>
                是否允许学员线上申请退款
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_REFUND_METHOD">
            <summary>
                退款方式配置
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_REFUND_COMPUTER_ACCOUNT_ID">
            <summary>
                结算账户
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_MUSTSALEID">
            <summary>
                必选介绍人
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SHOWSALEID">
            <summary>
                显示介绍人
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_MUSTMAC">
            <summary>
                必须绑定电脑
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_IDCARDDOWN">
            <summary>
                身份证 的正面在下
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_CLOSEBUSINESS">
            <summary>
                关闭业务信息
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_SALEJXDEPTPAY">
            <summary>
                收款跟着推荐人的店面走
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_MODIFYTUITION">
            <summary>
                可以修改学费
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_CREATESTATUS">
            <summary>
                新录进去的学员的状态
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_TUITIONCOSTTYPEID">
            <summary>
                默认的报名的时候 学费的  费用类型
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_ADDTUITIONCOSTTYPEID">
            <summary>
                默认的报名的时候 学费补缴的  费用类型
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_CREATEDFORBIDDENMODIFY">
            <summary>
                录入以后 不能修改 任何 财务信息
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_AFTERPAYFORBIDDENMODIFY">
            <summary>
                付款以后 不能修改 任何 财务信息
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_AFTERDAYFORBIDDENMODIFY">
            <summary>
                录入以后第二天 不能修改 任何 财务信息
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_CREATEMODIFYTUITION">
            <summary>
                录入的时候 可以修改学费信息
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_ACCOUNTFOLLOWSALEUSER">
            <summary>
                付款账户 跟随着推荐人的账户 走
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_ACCOUNTFOLLOWPAYJXDEPT">
            <summary>
                付款账户 跟随着缴费店面走
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_MODIFYADDSHOULDPAYMOENY">
            <summary>
                挂账的时候 可以修改金额
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_ORDERCARMETHOD">
            <summary>
                约车方式  Car  或者  Coach
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_PRINTER_TOP">
            <summary>
                打印的票据的 上面
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_PRINTER_BOTTOM">
            <summary>
                打印的票据的 底部
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_PRINTER_LEFT">
            <summary>
                打印的票据的 左侧
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_PRINTER_RIGHT">
            <summary>
                打印的票据的 右侧
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_PRINTER_NO_PHONE">
            <summary>
                打印的票据的 不打印学员的手机号码
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_PRINTER_PAPER">
            <summary>
                打印票据的 纸张
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_PRINTER_STAMP">
            <summary>
                打印票据的 印章
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_PAYID_RULE">
            <summary>
                收款账号的 规则
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_UPDATE_BY_JXMCS">
            <summary>
                更新考试 关联的数据来源
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_MUST_SELECT_JXFILEDID">
            <summary>
                报名的时候必须要选择训练场地
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_ASSIGN_COACH_UPDATE_RESULT_EXAM">
            <summary>
                分车以后 要更新 还未考试的考试记录  就算有分车 也要更新
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_DONT_UPDATE_EXAM_COACH_AFTER_EXAM">
            <summary>
                考试以后不能更新考试的教练信息
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_DEBT_FORBIDDEN_ASSIGN_COACH">
            <summary>
                欠费不能分配教练
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_YWZT_SHOW_LAST_EXAM">
            <summary>
                业务状态显示上一次的考试
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_WX_NO_SHOW_REMARK">
            <summary>
            手机上 不显示备注
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_WX_NO_SHOW_PAY">
            <summary>
            手机上 不显示收入
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_WX_NO_SHOW_JXFIELD">
            <summary>
            手机上 不显示场地
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_WX_NO_SHOW_TEACH_ONE">
            <summary>
            手机上 不显示科目一教练
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_WX_NO_SHOW_TEACH_TWO">
            <summary>
            手机上 不显示科目二教练
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_WX_NO_SHOW_TEACH_THREE">
            <summary>
            手机上 不显示科目三教练
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_WX_NO_SEND_ORDER_EXAM">
            <summary>
            手机上 不推送 约考信息
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_WX_NO_SEND_EXAM_RESULT">
            <summary>
            手机上 不推送 考试信息
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_NO_LOCK_JXDEPT_CREATE_STUDENT">
            <summary>
            手报名的时候不限制选择报名点
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_USERPHONENOMASK">
            <summary>
               用户手机隐藏
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_JX_HIDE_FINANCE_DATA">
            <summary>
            默认不显示财务数据
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Relation_SYS_USER_HAS_ROLE">
            <summary>
                用户有哪些角色
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Relation_SYS_ROLE_HAS_RESOURCE">
            <summary>
                角色有哪些资源
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Relation_SYS_USER_HAS_RESOURCE">
            <summary>
                用户有哪些资源
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Relation_SYS_ROLE_HAS_PERMISSION">
            <summary>
                角色有哪些权限
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Relation_SYS_USER_HAS_PERMISSION">
            <summary>
                角色有哪些权限
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Relation_SYS_USER_WORKBENCH_DATA">
            <summary>
                用户工作台数据
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Relation_SYS_USER_SCHEDULE_DATA">
            <summary>
                用户日程数据
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Relation_MSG_TO_USER">
            <summary>
                站内信与接收用户
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.SCOPE_SELF">
            <summary>
                本人
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.SCOPE_ALL">
            <summary>
                所有
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.SCOPE_ORG">
            <summary>
                仅所属组织
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.SCOPE_ORG_CHILD">
            <summary>
                所属组织及以下
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.SCOPE_ORG_DEFINE">
            <summary>
                自定义
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Resource_MODULE">
            <summary>
                模块
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Resource_MENU">
            <summary>
                菜单
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Resource_SPA">
            <summary>
                单页
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Resource_BUTTON">
            <summary>
                按钮
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Log_LOGIN">
            <summary>
                登录
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Log_LOGOUT">
            <summary>
                登出
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Log_OPERATE">
            <summary>
                操作
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Log_EXCEPTION">
            <summary>
                异常
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Dict_FRM">
            <summary>
                框架
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Dict_BIZ">
            <summary>
                业务
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Org_DEPT">
            <summary>
                部门
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Org_COMPANY">
            <summary>
                公司
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Message_INFORM">
            <summary>
                通知
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Message_NOTICE">
            <summary>
                公告
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_SMS_Company">
            <summary>
                接口所属公司
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_SMS_Account">
            <summary>
                账号
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_SMS_PWD">
            <summary>
                密码
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.CateGoryConst.Config_SMS_Sign">
            <summary>
                签名
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.DevConfigConst">
            <summary>
                系统配置常量
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_DEFAULT_CAPTCHA_OPEN">
            <summary>
                登录验证码开关
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_DEFAULT_SINGLE_OPEN">
            <summary>
                单用户登录开关
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_DEFAULT_PASSWORD">
            <summary>
                默认用户密码
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_DEFAULT_WORKBENCH_DATA">
            <summary>
                系统默认工作台
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_UPDATE_HN122_GOV_CN_SESSION">
            <summary>
                湖南122 更新的数据会话
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_UPDATE_BAIDU_FACEV2_APP_ID">
            <summary>
                百度人脸识别的 V2 的 Secret Key
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_UPDATE_BAIDU_FACEV2_API_KEY">
            <summary>
                百度人脸识别的 V2 的 api Key
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_UPDATE_BAIDU_FACEV2_SECRET_KEY">
            <summary>
                百度人脸识别的 V2 的 Secret Key
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_ARC_SOFT_ANDROID_APP_ID">
            <summary>
                全局虹软 Android App Id
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_ARC_SOFT_ANDROID_APP_KEY">
            <summary>
                全局虹软 Android App Key
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_JLPAY_SM2_PRIVATE_KEY">
            <summary>
                全局嘉联支付的SM2私钥
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_JLPAY_SM2_PUBLIC_KEY">
            <summary>
                全局嘉联支付的SM2公钥
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_JLPAY_V5_PUBLIC_KEY">
            <summary>
                全局嘉联支付的v5公钥
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_JLPAY_RSA_PRIVATE_KEY">
            <summary>
                全局嘉联支付的RSA私钥
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_JLPAY_RSA_PUBLIC_KEY">
            <summary>
                全局嘉联支付的RSA公钥
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_JLPAY_ORG_APPID">
            <summary>
                全局嘉联支付的AppId
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_JLPAY_ORG_CODE">
            <summary>
                全局嘉联支付的服务商号
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_PANDADOOR_VERSIONCODE">
            <summary>
                PNADA DOOR 版本号
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_PANDADOOR_VERSIONNAME">
            <summary>
                PNADA DOOR 版本号
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_PANDADOOR_UPDATE_URL">
            <summary>
                PNADA DOOR 版本更新地址
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_PANDADOOR_V2_VERSIONCODE">
            <summary>
                PNADA DOOR V2 版本号
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_PANDADOOR_V2_VERSIONNAME">
            <summary>
                PNADA DOOR V2 版本名称
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.SYS_PANDADOOR_V2_UPDATE_URL">
            <summary>
                PNADA DOOR V2 版本更新地址
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.MQTT_PARAM_URL">
            <summary>
                mqtt连接地址
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.MQTT_PARAM_USERNAME">
            <summary>
                mqtt连接用户名
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.MQTT_PARAM_PASSWORD">
            <summary>
                mqtt连接密码
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.FILE_LOCAL_FOLDER_FOR_WINDOWS">
            <summary>
                windows系统本地目录
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.FILE_LOCAL_FOLDER_FOR_UNIX">
            <summary>
                Unix系统本地目录
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.FILE_MINIO_ACCESS_KEY">
            <summary>
                MINIO文件AccessKey
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.FILE_MINIO_SECRET_KEY">
            <summary>
                MINIO文件SecetKey
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.FILE_MINIO_END_POINT">
            <summary>
                MINIO文件EndPoint
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.FILE_MINIO_DEFAULT_BUCKET_NAME">
            <summary>
                MINIO文件默认存储桶
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_BAIDU_FACE_APPID">
            <summary>
                百度 人脸的 AppId
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_BAIDU_FACE_APIKEY">
            <summary>
                百度 人脸的 Api Key
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_BAIDU_FACE_SECRETKEY">
            <summary>
                百度 人脸的 Secret Key
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_SMS_COMPANY">
            <summary>
                短信 平台公司  Tinree
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_SMS_ACCOUNT">
            <summary>
                短信 平台公司
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_SMS_PWD">
            <summary>
                短信 平台公司 密码 或者 appsecret
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_SMS_SIGN">
            <summary>
                短信 发送的签名
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_ESIGN_APPID">
            <summary>
                e签宝应用ID
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_ESIGN_SECRET">
            <summary>
                e签宝应用Secret
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_ESIGN_RSA_PUBLIC">
            <summary>
                e签宝RSA公钥
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_ESIGN_AES">
            <summary>
                e签宝AES密钥
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevConfigConst.THIRDCONFIG_ESIGN_AES_KEY">
            <summary>
                e签宝AES密钥Key
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.DevDictConst">
            <summary>
                字典常量
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.GENDER">
            <summary>
                性别
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.NATION">
            <summary>
                名族
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.IDCARD_TYPE">
            <summary>
                用户证件类型
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.CULTURE_LEVEL">
            <summary>
                通用文化程度
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.COMMON_STATUS_ENABLE">
            <summary>
                启用
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.COMMON_STATUS_DISABLED">
            <summary>
                停用
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.ONLINE_STATUS_ONLINE">
            <summary>
                在线
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.ONLINE_STATUS_OFFLINE">
            <summary>
                离线
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.FILE_ENGINE_LOCAL">
            <summary>
                本地
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevDictConst.FILE_ENGINE_MINIO">
            <summary>
                MINIO
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.DevLogConst">
            <summary>
                日志常量
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevLogConst.SUCCESS">
            <summary>
                成功
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DevLogConst.FAIL">
            <summary>
                成功
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.EffTypeConst">
            <summary>
                前端控件作用类型常量
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.INPUT">
            <summary>
                输入框
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.TEXTAREA">
            <summary>
                文本框
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.SELECT">
            <summary>
                下拉框
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.RADIO">
            <summary>
                单选框
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.CHECKBOX">
            <summary>
                复选框
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.DATEPICKER">
            <summary>
                日期选择器
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.TIMEPICKER">
            <summary>
                时间选择器
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.INPUTNUMBER">
            <summary>
                数字输入框
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.SLIDER">
            <summary>
                滑动数字条
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.EffTypeConst.SWITCH">
            <summary>
                下拉框
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.JiaXiaoConst.JxConfig_MustSaleId">
            <summary>
                必选介绍人
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.JiaXiaoConst.JxConfig_ShowSaleId">
            <summary>
                显示 介绍人
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.JiaXiaoConst.JxConfig_MustMac">
            <summary>
                必须绑定电脑
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.JiaXiaoConst.JxConfig_IdCardUp">
            <summary>
                身份证 正面在上
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.JiaXiaoConst.JxConfig_SaleJxPay">
            <summary>
                收款 跟着 推荐人的 信息走
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.JiaXiaoConst.JxConfig_CreatedModifyTuition">
            <summary>
                报名以后 可以修改学费
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.JiaXiaoConst.JxConfig_BeforePayModify">
            <summary>
                付款前 可以修改信息
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.ResourceConst">
            <summary>
                资源表常量
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.ResourceConst.CATALOG">
            <summary>
                目录
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.ResourceConst.MENU">
            <summary>
                组件
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.ResourceConst.IFRAME">
            <summary>
                内链
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.ResourceConst.LINK">
            <summary>
                外链
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.ResourceConst.System">
            <summary>
                系统内置单页面编码
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.RoleConst">
            <summary>
                角色常量
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.RoleConst.SuperAdmin">
            <summary>
                超级管理员
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.RoleConst.BizAdmin">
            <summary>
                业务管理员
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.SqlsugarConst">
            <summary>
                Sqlsugar系统常量类
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlsugarConst.DB_Default">
            <summary>
                默认库ConfigId
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlsugarConst.DB_JiaXiaoOA">
            <summary>
                JiaXiaoOA 数据库
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlsugarConst.DB_JiaXiaoOAParking">
            <summary>
                JiaXiaoOAParking 数据库
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlsugarConst.DB_JiaXiaoOAImage">
            <summary>
                JiaXiaoOAImage 数据库
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlsugarConst.DB_SupervisionPlatform">
            <summary>
                SupervisionPlatform  数据库
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlsugarConst.DB_PrimaryKey">
            <summary>
                默认表主键
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlsugarConst.NVarCharMax">
            <summary>
                varchar(max)
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlsugarConst.LongText">
            <summary>
                mysql的longtext
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlsugarConst.Text">
            <summary>
                sqlite的text
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.TenantIdConst">
            <summary>
                固定 的几个 公司
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.TenantIdConst.CS">
            <summary>
                总账号
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.TenantIdConst.HnjxTenantId">
            <summary>
                湖南驾协
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.TenantIdConst.CsjxTenantId">
            <summary>
                长沙驾协
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.TenantIdConst.CsjxWxAppId">
            <summary>
                长沙驾校的 小程序的 Appid
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetAllTables(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,System.Collections.Generic.List{SqlSugar.DbTableInfo})">
            <summary>
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="tableInfos"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetTableName(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo)">
            <summary>
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetTableName(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,SqlSugar.SplitType)">
            <summary>
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="splitType"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetTableName(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,SqlSugar.SplitType,System.Object)">
            <summary>
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="splitType"></param>
            <param name="fieldValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetFieldValue(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,SqlSugar.SplitType,System.Object)">
            <summary>
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="splitType"></param>
            <param name="entityValue"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.VerifySplitType(SqlSugar.SplitType)">
            <summary>
            </summary>
            <param name="splitType"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetTableNameByDate(SqlSugar.EntityInfo,SqlSugar.SplitType,System.DateTime)">
            <summary>
            </summary>
            <param name="EntityInfo"></param>
            <param name="splitType"></param>
            <param name="date"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetDate(System.String,System.String,System.String,System.String)">
            <summary>
            </summary>
            <param name="group1"></param>
            <param name="group2"></param>
            <param name="group3"></param>
            <param name="dbTableName"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.PadLeft2(System.String)">
            <summary>
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.CheckTableName(System.String)">
            <summary>
            </summary>
            <param name="dbTableName"></param>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.ConvertDateBySplitType(System.DateTime,SqlSugar.SplitType)">
            <summary>
            </summary>
            <param name="time"></param>
            <param name="type"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetMondayDate">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetSundayDate">
            <summary>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetMondayDate(System.DateTime)">
            <summary>
            </summary>
            <param name="someDate"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService.GetSundayDate(System.DateTime)">
            <summary>
            </summary>
            <param name="someDate"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.SqlSugar.CustomSplitService.SplitTableSort">
            <summary>
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService1.GetAllTables(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,System.Collections.Generic.List{SqlSugar.DbTableInfo})">
            <summary>
                返回数据库中所有分表
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="tableInfos"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService1.GetFieldValue(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,SqlSugar.SplitType,System.Object)">
            <summary>
                获取分表字段的值
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="splitType"></param>
            <param name="entityValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService1.GetTableName(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo)">
            <summary>
                默认表名
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService1.GetTableName(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,SqlSugar.SplitType)">
            <summary>
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSplitService1.GetTableName(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,SqlSugar.SplitType,System.Object)">
            <summary>
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="splitType"></param>
            <param name="fieldValue"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.SqlSugar.DbContext">
            <summary>
                数据库上下文对象
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DbContext.DbConfigs">
            <summary>
                读取配置文件中的 ConnectionStrings:Sqlsugar 配置节点
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.DbContext.Db">
            <summary>
                SqlSugar 数据库实例
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.DbContext.ExternalServicesSetting(SqlSugar.SqlSugarScopeProvider,PandaServer.SqlSugar.SqlSugarConfig)">
            <summary>
                实体拓展配置,自定义类型多库兼容
            </summary>
            <param name="db"></param>
            <param name="config"></param>
        </member>
        <member name="M:PandaServer.SqlSugar.DbContext.AopSetting(SqlSugar.SqlSugarScopeProvider)">
            <summary>
                Aop设置
            </summary>
            <param name="db"></param>
        </member>
        <member name="M:PandaServer.SqlSugar.DbContext.MoreSetting(SqlSugar.SqlSugarScopeProvider)">
            <summary>
                实体更多配置
            </summary>
            <param name="db"></param>
        </member>
        <member name="M:PandaServer.SqlSugar.DbContext.FilterSetting(SqlSugar.SqlSugarScopeProvider)">
            <summary>
                过滤器设置
            </summary>
            <param name="db"></param>
        </member>
        <member name="M:PandaServer.SqlSugar.DbContext.LogicDeletedEntityFilter(SqlSugar.SqlSugarScopeProvider)">
            <summary>
                假删除过滤器
            </summary>
            <param name="db"></param>
        </member>
        <member name="T:PandaServer.SqlSugar.DbRepository`1">
            <summary>
                仓储模式对象
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.DbRepository`1.InsertOrBulkCopy(System.Collections.Generic.List{`0},System.Int32)">
            <summary>
                批量插入判断走普通导入还是大数据
            </summary>
            <param name="data">数据</param>
            <param name="threshold">阈值</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.DbRepository`1.GetListAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,`0}})">
            <summary>
                获取列表指定多个字段
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="selectExpression">查询字段</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.DbRepository`1.GetListAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.String}})">
            <summary>
                获取列表指定单个字段
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="selectExpression">查询字段</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.DbRepository`1.GetListAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Int64}})">
            <summary>
                获取列表指定单个字段
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="selectExpression">查询字段</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.DbRepository`1.GetFirstAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.String}})">
            <summary>
                获取指定表的单个字段
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="selectExpression">查询字段</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.DbRepository`1.GetFirstAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Guid}})">
            <summary>
                获取指定表的单个字段
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="selectExpression">查询字段</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.MySplitService.GetAllTables(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,System.Collections.Generic.List{SqlSugar.DbTableInfo})">
            <summary>
                返回数据库中所有分表
            </summary>
            <param name="db"></param>
            <param name="EntityInfo"></param>
            <param name="tableInfos"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.MySplitService.GetFieldValue(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,SqlSugar.SplitType,System.Object)">
            <summary>
                获取分表字段的值
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="splitType"></param>
            <param name="entityValue"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.MySplitService.GetTableName(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo)">
            <summary>
                默认表名
            </summary>
            <param name="db"></param>
            <param name="EntityInfo"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.MySplitService.GetTableName(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,SqlSugar.SplitType)">
            <summary>
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.MySplitService.GetTableName(SqlSugar.ISqlSugarClient,SqlSugar.EntityInfo,SqlSugar.SplitType,System.Object)">
            <summary>
            </summary>
            <param name="db"></param>
            <param name="entityInfo"></param>
            <param name="splitType"></param>
            <param name="fieldValue"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.SqlSugar.SqlSugarConfig">
            <summary>
                sqlsugar数据库配置
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarConfig.IsUnderLine">
            <summary>
                是否驼峰转下划线
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.LinqPagedList`1">
            <summary>
                Linq分页泛型集合
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:PandaServer.SqlSugar.PrimaryKeyEntity">
            <summary>
                主键实体基类
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.PrimaryKeyEntity.Id">
            <summary>
                主键Id
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.BaseEntity">
            <summary>
                框架实体基类
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.BaseEntity.CreateTime">
            <summary>
                创建时间
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.BaseEntity.CreateUserId">
            <summary>
                创建者Id
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.BaseEntity.UpdateTime">
            <summary>
                更新时间
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.BaseEntity.UpdateUserId">
            <summary>
                修改者Id
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.BaseEntity.IsDelete">
            <summary>
                软删除
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.BaseEntity.DeleteTime">
            <summary>
                更新时间
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.BaseEntity.DeleteUserId">
            <summary>
                修改者Id
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.BaseEntity.Create">
            <summary>
                初始化
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.BaseEntity.Modify">
            <summary>
                修改
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.BaseEntity.Delete">
            <summary>
                假删除
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.DataEntityBase">
            <summary>
                业务数据实体基类(数据权限)
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.DataEntityBase.TenantId">
            <summary>
                所在的租户的 Id
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetSingleAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            </summary>
            <param name="whereExpression"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetSingleAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.DateTime)">
            <summary>
            </summary>
            <param name="whereExpression"></param>
            <param name="startTime"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetSingleAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Guid)">
            <summary>
            </summary>
            <param name="whereExpression"></param>
            <param name="tenantId"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetSingleAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32)">
            <summary>
                分页的 快速的 查询的方法
            </summary>
            <param name="whereExpression"></param>
            <param name="splitTake"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetSingleAsync``1(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            </summary>
            <param name="whereExpression"></param>
            <param name="column"></param>
            <typeparam name="TResult"></typeparam>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetFirstAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},SqlSugar.OrderByType)">
            <summary>
                GetFirstAsync 取回 整个一行数据
            </summary>
            <param name="whereExpression"></param>
            <param name="orderExpression"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetFirstAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},SqlSugar.OrderByType,System.DateTime)">
            <summary>
                GetFirstAsync 取回 整个一行数据
            </summary>
            <param name="whereExpression"></param>
            <param name="orderExpression"></param>
            <param name="type"></param>
            <param name="startTime"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetFirstAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},SqlSugar.OrderByType,System.Guid)">
            <summary>
                GetFirstAsync 取回 整个一行数据
            </summary>
            <param name="whereExpression"></param>
            <param name="orderExpression"></param>
            <param name="type"></param>
            <param name="tenantId"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetFirst(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},SqlSugar.OrderByType,System.Guid)">
            <summary>
                GetFirst 取回 整个一行数据
            </summary>
            <param name="whereExpression"></param>
            <param name="orderExpression"></param>
            <param name="type"></param>
            <param name="tenantId"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetFirstAsync``1(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},SqlSugar.OrderByType,System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
                GetFirstAsync 取回 指定的 列
            </summary>
            <param name="whereExpression"></param>
            <param name="orderExpression"></param>
            <param name="type"></param>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.IsAnyAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
                查询条件 是否存在
            </summary>
            <param name="whereExpression"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.IsAnyAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32)">
            <summary>
                查询条件 是否存在
            </summary>
            <param name="whereExpression"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.IsAnyAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Guid)">
            <summary>
                查询条件 是否存在
            </summary>
            <param name="whereExpression"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetListAsync">
            <summary>
                不带 Where 的 ToListAsync()
            </summary>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetListAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
                带 Where 的 ToListAsync()
            </summary>
            <param name="whereExpression">Where 条件</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetListAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Guid)">
            <summary>
                带 Where 的 ToListAsync()
            </summary>
            <param name="whereExpression">Where 条件</param>
            <param name="tenantId">所属的 公司的 Id </param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetListAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32)">
            <summary>
                带 Where 的 ToListAsync() 只针对分表的表，要带上 Take 表的数量
            </summary>
            <param name="whereExpression"></param>
            <param name="splitTake"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetListAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},SqlSugar.OrderByType)">
            <summary>
                带 Where 和 Order 的 ToListAsync()
            </summary>
            <param name="whereExpression">Where 条件</param>
            <param name="orderExpression">Order 的条件</param>
            <param name="type">Order 的顺序</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.GetListAsync``1(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},SqlSugar.OrderByType,System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
                带 Where 和 Order 的 ToListAsync()  返回 固定字段
            </summary>
            <param name="whereExpression"></param>
            <param name="orderExpression"></param>
            <param name="type"></param>
            <param name="column"></param>
            <typeparam name="TResult"></typeparam>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.SumAsync``1(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
                针对某一个字段 计算 Sum 的表达式
            </summary>
            <param name="whereExpression">Where 的条件</param>
            <param name="column">要计算 Sum 的字段</param>
            <typeparam name="TResult"></typeparam>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.SumAsync``1(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Guid)">
            <summary>
                针对某一个字段 计算 Sum 的表达式
            </summary>
            <param name="whereExpression"></param>
            <param name="column"></param>
            <param name="tenantId"></param>
            <typeparam name="TResult"></typeparam>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
                求总的 条数  重写
            </summary>
            <param name="whereExpression"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Guid)">
            <summary>
                求总的 条数  重写
            </summary>
            <param name="whereExpression"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.InsertOrUpdateAsync(System.Collections.Generic.List{`0})">
            <summary>
                Insert  或者  Update 数组
            </summary>
            <param name="datas"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.UpdateAsync(`0)">
            <summary>
                Update 单个对象
            </summary>
            <param name="updateObj"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.UpdateAsync(System.Collections.Generic.List{`0})">
            <summary>
                Update 数组
            </summary>
            <param name="updateObj"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CustomSimpleClient`1.UpdateAsync(System.Linq.Expressions.Expression{System.Func{`0,`0}},System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
                字段的更新
            </summary>
            <param name="columns"></param>
            <param name="whereExpression"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.SqlSugar.ISqlSugarEntitySeedData`1">
            <summary>
                实体种子数据接口
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="M:PandaServer.SqlSugar.ISqlSugarEntitySeedData`1.SeedData">
            <summary>
                种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.SqlSugar.SqlSugarPageExtension">
            <summary>
                Sqlsugar分页拓展类
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarPageExtension.ToPagedList``1(SqlSugar.ISugarQueryable{``0},System.Int32,System.Int32)">
            <summary>
                SqlSugar分页扩展
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="queryable"></param>
            <param name="current"></param>
            <param name="size"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarPageExtension.ToPagedListAsync``1(SqlSugar.ISugarQueryable{``0},System.Int32,System.Int32)">
            <summary>
                SqlSugar分页扩展
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="queryable"></param>
            <param name="current"></param>
            <param name="size"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarPageExtension.ToPagedList``2(SqlSugar.ISugarQueryable{``0},System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
                SqlSugar分页扩展
            </summary>
            <typeparam name="TEntity"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="queryable"></param>
            <param name="pageIndex"></param>
            <param name="pageSize"></param>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarPageExtension.ToPagedListAsync``2(SqlSugar.ISugarQueryable{``0},System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
                SqlSugar分页扩展
            </summary>
            <typeparam name="TEntity"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="queryable"></param>
            <param name="pageIndex"></param>
            <param name="pageSize"></param>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarPageExtension.LinqPagedList``1(System.Collections.Generic.List{``0},System.Int32,System.Int32)">
            <summary>
                分页查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="list">数据列表</param>
            <param name="pageIndex">当前页</param>
            <param name="pageSize">每页数量</param>
            <returns>分页集合</returns>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarPageExtension.ToSqlSugarPagedList``1(System.Collections.Generic.List{``0},System.Int32,System.Int32,System.Int32)">
            <summary>
                List 数据 和相关页面的参数 直接转成 SqlSugarPagedList 结果
            </summary>
            <param name="list"></param>
            <param name="current"></param>
            <param name="size"></param>
            <param name="totalNumber"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="T:PandaServer.SqlSugar.SqlSugarPagedList`1">
            <summary>
                SqlSugar 分页泛型集合
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarPagedList`1.Current">
            <summary>
                页码
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarPagedList`1.Size">
            <summary>
                数量
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarPagedList`1.Total">
            <summary>
                总条数
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarPagedList`1.Pages">
            <summary>
                总页数
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarPagedList`1.Records">
            <summary>
                当前页集合
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarPagedList`1.HasPrevPages">
            <summary>
                是否有上一页
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarPagedList`1.HasNextPages">
            <summary>
                是否有下一页
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.SqlSugarUnitOfWork">
            <summary>
                SqlSugar 事务和工作单元
            </summary>
        </member>
        <member name="F:PandaServer.SqlSugar.SqlSugarUnitOfWork._sqlSugarClient">
            <summary>
                SqlSugar 对象
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUnitOfWork.#ctor(SqlSugar.ISqlSugarClient,Microsoft.Extensions.Logging.ILogger{PandaServer.SqlSugar.SqlSugarUnitOfWork})">
            <summary>
                构造函数
            </summary>
            <param name="sqlSugarClient"></param>
            <param name="logger"></param>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUnitOfWork.BeginTransaction(Microsoft.AspNetCore.Mvc.Filters.FilterContext,Furion.DatabaseAccessor.UnitOfWorkAttribute)">
            <summary>
                开启工作单元处理
            </summary>
            <param name="context"></param>
            <param name="unitOfWork"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUnitOfWork.CommitTransaction(Microsoft.AspNetCore.Mvc.Filters.FilterContext,Furion.DatabaseAccessor.UnitOfWorkAttribute)">
            <summary>
                提交工作单元处理
            </summary>
            <param name="resultContext"></param>
            <param name="unitOfWork"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUnitOfWork.RollbackTransaction(Microsoft.AspNetCore.Mvc.Filters.FilterContext,Furion.DatabaseAccessor.UnitOfWorkAttribute)">
            <summary>
                回滚工作单元处理
            </summary>
            <param name="resultContext"></param>
            <param name="unitOfWork"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUnitOfWork.OnCompleted(Microsoft.AspNetCore.Mvc.Filters.FilterContext,Microsoft.AspNetCore.Mvc.Filters.FilterContext)">
            <summary>
                执行完毕（无论成功失败）
            </summary>
            <param name="context"></param>
            <param name="resultContext"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="T:PandaServer.SqlSugar.Startup">
            <summary>
                AppStartup启动类
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.Startup.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
                ConfigureServices中不能解析服务，比如App.GetService()，尤其是不能在ConfigureServices中获取诸如缓存等数据进行初始化，应该在Configure中进行
                服务都还没初始化完成，会导致内存中存在多份 IOC 容器！！
                正确应该在 Configure 中，这个时候服务（IServiceCollection 已经完成 BuildServiceProvider() 操作了
            </summary>
            <param name="services"></param>
        </member>
        <member name="M:PandaServer.SqlSugar.Startup.CheckSameConfigId">
            <summary>
                检查是否有相同的ConfigId
            </summary>
            <returns></returns>
        </member>
        <member name="T:PandaServer.SqlSugar.CodeFirstUtils">
            <summary>
                CodeFirst功能类
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.CodeFirstUtils.CodeFirst(PandaServer.Core.BaseOptions,System.String)">
            <summary>
                CodeFirst生成数据库表结构和种子数据
            </summary>
            <param name="options">codefirst选项</param>
            <param name="assemblyName">程序集名称</param>
        </member>
        <member name="M:PandaServer.SqlSugar.CodeFirstUtils.InitTable(System.String)">
            <summary>
                初始化数据库表结构
            </summary>
            <param name="assemblyName">程序集名称</param>
        </member>
        <member name="M:PandaServer.SqlSugar.CodeFirstUtils.InitSeedData(System.String)">
            <summary>
                初始化种子数据
            </summary>
            <param name="assemblyName">程序集名称</param>
        </member>
        <member name="M:PandaServer.SqlSugar.CodeFirstUtils.HasImplementedRawGeneric(System.Type,System.Type)">
            <summary>
                判断类型是否实现某个泛型
            </summary>
            <param name="type">类型</param>
            <param name="generic">泛型类型</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CodeFirstUtils.ToDataTable``1(System.Collections.Generic.List{``0})">
            <summary>
                List转DataTable
            </summary>
            <typeparam name="T"></typeparam>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.CodeFirstUtils.IsIgnoreColumn(System.Reflection.PropertyInfo)">
            <summary>
                排除SqlSugar忽略的列
            </summary>
            <param name="pi"></param>
            <returns></returns>
        </member>
        <member name="T:PandaServer.SqlSugar.SqlsugarColumnInfo">
            <summary>
                Sqlsugar字段信息
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlsugarColumnInfo.ColumnName">
            <summary>
                字段名称
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlsugarColumnInfo.IsPrimarykey">
            <summary>
                是否主键
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlsugarColumnInfo.DataType">
            <summary>
                字段类型
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlsugarColumnInfo.ColumnDescription">
            <summary>
                字段注释
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.SqlSugarTableInfo">
            <summary>
                Sqlsugar表信息
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarTableInfo.ConfigId">
            <summary>
                所属库
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarTableInfo.TableName">
            <summary>
                表名称
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarTableInfo.EntityName">
            <summary>
                实体名
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarTableInfo.TableDescription">
            <summary>
                表注释
            </summary>
        </member>
        <member name="P:PandaServer.SqlSugar.SqlSugarTableInfo.Columns">
            <summary>
                表字段
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.SeedDataUtil">
            <summary>
                种子数据工具类
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.SeedDataRecords`1">
            <summary>
                种子数据格式实体类,遵循Navicat导出json格式
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:PandaServer.SqlSugar.SeedDataRecords`1.Records">
            <summary>
                数据
            </summary>
        </member>
        <member name="T:PandaServer.SqlSugar.SqlSugarUtils">
            <summary>
                Sqlusgar通用功能
            </summary>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUtils.GetTablesByAttribute``1">
            <summary>
                根据特性获取所有表信息
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUtils.GetTableColumns(System.String,System.String)">
            <summary>
                获取字段信息
            </summary>
            <param name="configId"></param>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUtils.ConvertDataType(System.String)">
            <summary>
                数据库字段类型转.NET类型
            </summary>
            <param name="dataType">字段类型</param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUtils.DataTypeToEff(System.String)">
            <summary>
                数据类型转显示类型
            </summary>
            <param name="dataType"></param>
            <returns></returns>
        </member>
        <member name="M:PandaServer.SqlSugar.SqlSugarUtils.IsCommonColumn(System.String)">
            <summary>
                是否通用字段
            </summary>
            <param name="columnName">字段名</param>
            <returns></returns>
        </member>
    </members>
</doc>
